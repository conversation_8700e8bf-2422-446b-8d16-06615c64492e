package api

import (
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/migrate"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/tool"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

func MigrateSendEmailOrderDelivering(req sdk.APIRequest, resp sdk.APIResponder) error {
	orderID := sdk.ParseInt64(req.GetParam("orderID"), 0)
	return resp.Respond(action.MigrateSendEmailOrderDelivering(orderID))
}

func OrderCopy(req sdk.APIRequest, resp sdk.APIResponder) error {
	orderID := sdk.ParseInt64(req.GetParam("orderID"), 0)
	withNoVoucher, withNoExtraFee, withCodMethod, withNoSplitDO := req.GetParam("withNoVoucher") == "true", req.GetParam("withNoExtraFee") == "true", req.GetParam("withCodMethod") == "true", req.GetParam("withNoSplitDO") == "true"
	tag := req.GetParam("tag")
	noteCopy := req.GetParam("noteCopy")
	return resp.Respond(action.CopyNewOrder(orderID, withNoVoucher, withNoExtraFee, withCodMethod, withNoSplitDO, noteCopy, tag))
}

func MigrateOrderPoint(req sdk.APIRequest, resp sdk.APIResponder) error {
	orderID := sdk.ParseInt64(req.GetParam("orderID"), 0)
	point, err := strconv.ParseFloat(req.GetParam("point"), 64)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}

	return resp.Respond(action.MigrateOrderPoint(orderID, point))
}

func MigrateReconcileSendInvoice(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		migrate.MigrateReconcileSendInvoice()
	})
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateReconcileSendInvoice running...",
	})
}

func MigrateInvoiceMailProcessing(req sdk.APIRequest, resp sdk.APIResponder) error {
	type Input struct {
		StartTime time.Time `json:"startTime"`
		StopTime  time.Time `json:"stopTime"`
	}
	var input Input
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}

	go sdk.Execute(func() {
		migrate.MigrateInvoiceMailProcessing(input.StartTime, input.StopTime)
	})

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateInvoiceMailProcessing running...",
	})
}

func MigrateOrderDetails(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		migrate.MigrateOrderDetails()
	})
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateOrderDetails running...",
	})
}

func MigrateReconcileInboundPenalty(req sdk.APIRequest, resp sdk.APIResponder) error {
	type Input struct {
		RemoveIndexes   []string `json:"removeIndexes,omitempty"`
		RemoveTimeRange []int    `json:"removeTimeRange,omitempty"`
	}

	var input Input
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}

	if len(input.RemoveIndexes) <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "removeIndexes is empty",
		})
	}

	go sdk.Execute(func() {
		res := migrate.MigrateReconcileInboundPenalty(input.RemoveIndexes, input.RemoveTimeRange)
		fmt.Printf("MigrateReconcileInboundPenalty res: %#v\n", res)
	})
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateReconcileInboundPenalty running...",
	})
}

func MigrateReconcileInvoiceOverdue(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		res := migrate.MigrateReconcileInvoiceOverdue()
		fmt.Printf("MigrateReconcileInvoiceOverdue res: %#v\n", res)
	})
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateReconcileInvoiceOverdue running...",
	})
}

func MigrateReconcile3DaysReconcile(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		res := migrate.MigrateReconcile3DaysReconcile()
		fmt.Printf("MigrateReconcile3DaysReconcile res: %#v\n", res)
	})
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateReconcile3DaysReconcile running...",
	})
}

func MigrateUpdateOrdersForCreatedByAccountID(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		migrate.MigrateUpdateOrdersForCreatedByAccountID()
	})

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateUpdateOrdersForCreatedByAccountID running...",
	})
}

func MigrateInvoiceAutoChangeStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		migrate.MigrateInvoiceAutoChangeStatus()
	})

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateInvoiceAutoChangeStatus running...",
	})
}

func MigrateOrderItemLevel(req sdk.APIRequest, resp sdk.APIResponder) error {

	type Input struct {
		Sku        string `json:"sku"`
		SellerCode string `json:"sellerCode"`
		Time       string `json:"time"`
	}

	var input Input
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}

	if input.Sku == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "sku is empty",
		})
	}

	if input.SellerCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "sellerCode is empty",
		})
	}

	return resp.Respond(migrate.MigrateUpdateOrderItemLevel(input.Sku, input.SellerCode, input.Time))
}

func MigrateInvoiceDeliveredTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		res := migrate.MigrateInvoiceDeliveredTime()
		fmt.Printf("MigrateInvoiceDeliveredTime res: %#v\n", res)
	})
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateInvoiceDeliveredTime running...",
	})
}

func MigrateRemoveBizHouseholdTax(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		migrate.MigrateRemoveBizHouseholdTax()
	})

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateRemoveBizHouseholdTax running...",
	})
}

func MigrateMissingBizHouseHoldTax(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		reconcile_action.ProcessMigrateDeliveryOrderTax()
	})

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateMissingBizHouseHoldTax running...",
	})
}

func MigrateReconcileFulfillmentPenalty(req sdk.APIRequest, resp sdk.APIResponder) error {
	type InputMigrateFulfillment struct {
		RemoveIndexes []string `json:"removeIndexes"`
	}

	var input InputMigrateFulfillment
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}

	if len(input.RemoveIndexes) <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "removeIndexes is empty",
		})
	}

	go sdk.Execute(func() {
		res := migrate.MigrateReconcileUnFulfillment(input.RemoveIndexes)
		fmt.Printf("MigrateReconcileUnFulfillment res: %#v\n", res)
	})
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate is running...",
	})
}

func MigrateOrderItemVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderItem
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}

	if input.OrderID <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "orderID is empty",
		})

	}

	res := tool.MigrateOrderItemVoucher(input.OrderID)

	return resp.Respond(res)
}

func MigrateCreateInvoiceDO(req sdk.APIRequest, resp sdk.APIResponder) error {
	type Input struct {
		Status enum.OrderStateValue `json:"status"`
	}

	var input Input
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}
	if !utils.Contains(string(input.Status), []string{
		string(enum.OrderState.WaitConfirm),
		string(enum.OrderState.Confirmed),
		string(enum.OrderState.Processing),
		string(enum.OrderState.WaitToDeliver),
	}) {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Status is invalid %s", input.Status),
		})
	}

	go sdk.Execute(func() {
		res := migrate.MigrateCreateInvoiceDO(input.Status)
		fmt.Printf("MigrateCreateInvoiceDO res: %#v\n", res)
	})
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateCreateInvoiceDO running...",
	})
}

func MigrateRecCodeForReconcile(req sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(func() {
		action.MigrateRecCodeForReconcile()
	})

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateRecCodeForReconcile running...",
	})
}

func RemoveReconciliationInboundPenaltyLog(req sdk.APIRequest, res sdk.APIResponder) error {
	id := req.GetParam("id")
	if id == "" {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid ID",
		})
	}

	return res.Respond(migrate.RemoveReconciliationInboundPenalty(id))
}
