package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func warmupDealLimitCache(data *model.DealLimit, customerID int64, key string) {
	if data == nil {
		qDealLimit := model.DealLimitDB.QueryOne(model.DealLimit{
			Key:        key,
			CustomerID: customerID,
		})
		if qDealLimit.Status != common.APIStatus.Ok {
			return
		}
		data = qDealLimit.Data.([]*model.DealLimit)[0]
	}
	model.DealLimitCacheDB.Upsert(model.DealLimit{Key: key, CustomerID: customerID}, data)
}
