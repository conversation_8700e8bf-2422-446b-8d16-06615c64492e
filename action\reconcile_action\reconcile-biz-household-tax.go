package reconcile_action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func CreateBizHouseHoldTaxItem(items []model.BizHouseholdTaxItem) *common.APIResponse {
	for _, item := range items {
		if item.ReconcileScheduleTimeIndex == "" || item.SellerCode == "" {
			continue
		}

		model.CalculateBizHouseholdTax.Push(item, &job.JobItemMetadata{
			Topic: "default",
			Keys: []string{
				item.SellerCode,
				item.ReconcileScheduleTimeIndex,
			},
		})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "push to queue success",
	}
}
