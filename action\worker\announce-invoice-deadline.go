package worker

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	fromOneDayHour = 23
	toOneDayHour   = 24

	fromTwoDayHour = 47
	toTwoDayHour   = 48
)

func ProcessAnnounceInvoiceDeadline(now *time.Time, config *schedule.Config) (err error, note string, nextRun *time.Time) {
	fmt.Println("Start process", config.Topic)
	defer fmt.Println("Finish process", config.Topic)

	if now == nil {
		_t := time.Now()
		now = &_t
	}
	vnNow := now.In(utils.VNTimeZone)

	AnnounceInvoiceDeadline(vnNow)

	timeNextRun := vnNow.Add(time.Hour)
	nextRun = &timeNextRun

	return
}

func AnnounceInvoiceDeadline(now time.Time) {
	beforeDeadlineOneDay := bson.M{
		"$and": []bson.M{
			{"invoice_deadline": bson.M{"$gt": now.Add(fromOneDayHour * time.Hour)}},
			{"invoice_deadline": bson.M{"$lte": now.Add(toOneDayHour * time.Hour)}},
		},
	}

	beforeDeadlineTwoDays := bson.M{
		"$and": []bson.M{
			{"invoice_deadline": bson.M{"$gt": now.Add(fromTwoDayHour * time.Hour)}},
			{"invoice_deadline": bson.M{"$lte": now.Add(toTwoDayHour * time.Hour)}},
		},
	}

	// query invoice before deadline 23->24 hours or 47->48 hours
	filter := model.Invoice{
		Request:       utils.ParseBoolToPointer(true),
		InvoiceStatus: model.InvoiceStatus.Waiting,
		OperationOr: []bson.M{
			beforeDeadlineOneDay,
			beforeDeadlineTwoDays,
		},
	}

	var offset, limit int64
	limit = 100
	for {
		invoicesRes := model.InvoiceDB.Query(filter, offset, limit, &primitive.M{"_id": 1})
		if invoicesRes.Status != common.APIStatus.Ok {
			break
		}

		offset += limit
		invoices := invoicesRes.Data.([]*model.Invoice)

		for _, invoice := range invoices {
			model.PushNotificationJob.Push(model.PushNotificationItem{
				SellerCode:       invoice.SellerCode,
				InvoiceId:        invoice.InvoiceID,
				InvoiceCode:      invoice.InvoiceCode,
				InvoiceDeadline:  *invoice.InvoiceDeadline,
				OrderId:          invoice.OrderID,
				NotificationType: enum.NotificationType.INVOICE_DEADLINE,
			}, &job.JobItemMetadata{
				Keys: []string{
					"INVOICE_DEADLINE",
				},
				Topic: "default",
			})
		}
	}
}
