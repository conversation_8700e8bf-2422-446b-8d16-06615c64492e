package client

import (
	"encoding/json"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

const (
	pathCreateNotify = "/integration/messaging/v1/notify"
)

// Client is model define Notify client
type notifyClient struct {
	Notify  *client.RestClient
	headers map[string]string
}

// NewClient is func define new Notification client
func NewNotifyClient(apiHost, apiKey, logName string, session *mongo.Database) *notifyClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	c := &notifyClient{
		Notify: client.NewRESTClient(
			apiHost,
			logName,
			3*time.Second,
			1,
			3*time.Second,
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	c.Notify.SetDBLog(session)
	return c
}

// CreateNotify is func create new Notify
func (cli *notifyClient) CreateNotify(in *Notify) (*Notify, error) {

	params := map[string]string{}
	res, err := cli.Notify.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathCreateNotify, nil)
	if err != nil {
		return nil, err
	}

	var result *NotifyResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}
