package enum

type TransactionStatusValue string
type transactionStatus struct {
	SUCCESS TransactionStatusValue //
	FAILED  TransactionStatusValue //
}

// TransactionStatus mapping
var TransactionStatus = &transactionStatus{
	SUCCESS: "SUCCESS",
	FAILED:  "FAILED",
}

type TransactionActionTypeValue string
type transactionActionType struct {
	DEPOSIT    TransactionActionTypeValue // nạp tiền
	WITHDRAWAL TransactionActionTypeValue // rút tiền

	PAY        TransactionActionTypeValue // thanh toán
	CANCEL_PAY TransactionActionTypeValue // hủy thanh toán

	REFUND        TransactionActionTypeValue // hoàn trả
	CANCEL_REFUND TransactionActionTypeValue // hủy hoàn trả

	BUY TransactionActionTypeValue // mua hàng

	INTEREST            TransactionActionTypeValue // tính lãi
	PAY_INTEREST        TransactionActionTypeValue // trả lãi
	CANCEL_PAY_INTEREST TransactionActionTypeValue // hủy trả lãi

	BONUS        TransactionActionTypeValue // cộng tiền(rebate)
	UPDATE_LIMIT TransactionActionTypeValue // cập nhật hạn mức
}

// TransactionActionType mapping
var TransactionActionType = &transactionActionType{
	WITHDRAWAL: "WITHDRAWAL",
	DEPOSIT:    "DEPOSIT",

	PAY:        "PAY",
	CANCEL_PAY: "CANCEL_PAY",

	REFUND:        "REFUND",
	CANCEL_REFUND: "CANCEL_REFUND",

	BUY: "BUY",

	INTEREST:            "INTEREST",     // tính lãi
	PAY_INTEREST:        "PAY_INTEREST", // trả lãi
	CANCEL_PAY_INTEREST: "CANCEL_PAY_INTEREST",

	UPDATE_LIMIT: "UPDATE_LIMIT",
	BONUS:        "BONUS",
}

type TransactionRefTypeValue string
type transactionRefType struct {
	ORDER       TransactionRefTypeValue //
	CREDIT_NOTE TransactionRefTypeValue //
	PAYMENT     TransactionRefTypeValue //
	INVOICE     TransactionRefTypeValue //
}

// TransactionRefType mapping
var TransactionRefType = &transactionRefType{
	ORDER:       "ORDER",
	CREDIT_NOTE: "CREDIT_NOTE",
	PAYMENT:     "PAYMENT",
	INVOICE:     "INVOICE",
}
