package client

import (
	"time"
)

type SkuMainResponse struct {
	BaseAPIResponse
	Data []*SkuMain `json:"data"`
}

type SkuMain struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty"`
	CreateBy        *string    `json:"createBy,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty"`

	// basic info
	Code        string        `json:"code,omitempty"` // sku code
	ProductCode string        `json:"productCode,omitempty"`
	ProductID   int64         `json:"productID,omitempty"`
	SellerCode  string        `json:"sellerCode,omitempty"`
	Slug        string        `json:"slug,omitempty"`
	Type        *SkuTypeValue `json:"type,omitempty"` // normal/deal/combo

	HashTag string `json:"hashTag,omitempty"` // json:"hashTag,omitempty" hiển thị HashTag cho ticket

	// combo
	IsCombo *bool         `json:"isCombo,omitempty"`
	SKUs    *[]SubSkuMain `json:"skus,omitempty"`

	Level            *LevelSKUValue `json:"level,omitempty"`
	LevelSpecial     *LevelSpecial  `json:"levelSpecial,omitempty"`
	TimeChangeStatus *time.Time     `json:"timeChangeStatus,omitempty"`

	RequiredCertificates *[]CustomerCertificateValue `json:"requiredCertificates,omitempty"`
}

type SkuTypeValue string

type skuType struct {
	NORMAL       SkuTypeValue
	COMBO        SkuTypeValue
	DEAL         SkuTypeValue
	SKU_CONTRACT SkuTypeValue
}

var SKUType = &skuType{
	NORMAL:       "NORMAL",
	COMBO:        "COMBO",
	DEAL:         "DEAL",
	SKU_CONTRACT: "SKU_CONTRACT",
}

type SubSkuMain struct {
	SKU         string `json:"sku" bson:"sku,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	Quantity    int    `json:"quantity" bson:"quantity,omitempty"`
}

type LevelSKUValue string

type levelSKUValue struct {
	LEVEL_1 LevelSKUValue
	LEVEL_2 LevelSKUValue
	LEVEL_3 LevelSKUValue
	MARKET  LevelSKUValue
}

var LevelSKU = &levelSKUValue{
	LEVEL_1: "LEVEL_1",
	LEVEL_2: "LEVEL_2",
	LEVEL_3: "LEVEL_3",
	MARKET:  "MARKET",
}

type CustomerCertificateValue string

type customerCertificateType struct {
	GPPCertificate      CustomerCertificateValue // GPP
	BusinessCertificate CustomerCertificateValue // giấy đủ điều kiện kinh doanh dược
	BusinessLicense     CustomerCertificateValue // giấy hoạt động phòng khám
}

var CustomerCertificateType = customerCertificateType{
	GPPCertificate:      "GPP",                  // GPP
	BusinessCertificate: "BUSINESS_CERTIFICATE", // giấy đủ điều kiện kinh doanh dược
	BusinessLicense:     "BUSINESS_LICENSE",     // giấy hoạt động phòng khám
}

type Manufacturer struct {
	Code      string `json:"code" bson:"code"`
	Name      string `json:"name,omitempty" bson:"name,omitempty"`
	ShortName string `json:"shortName,omitempty" bson:"short_name,omitempty"`
}
type ManufacturerResponse struct {
	BaseAPIResponse
	Data []*Manufacturer `json:"data"`
}

type ProductFeeConfigConcurrentlyQuery struct {
	AppliedTime        *time.Time `json:"appliedTime,omitempty" bson:"applied_time,omitempty"`
	LocationCode       string     `json:"locationCode"`
	ProductIDs         []int64    `json:"productIDs"`
	AutoFillDefaultFee bool       `json:"autoFillDefaultFee"`
}

type ProductFeeConfigConcurrentlyResp struct {
	FeeMap map[int64]*ProductFeeConfig `json:"feeMap"`
}

type ProductFeeConfig struct {
	ProductID    int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	LocationCode string `json:"locationCode,omitempty" bson:"location_code,omitempty"`

	SaleFeePercent        *float64 `json:"saleFeePercent,omitempty" bson:"sale_fee_percent,omitempty"`               // phí bán hàng
	FulfillmentFeePercent *float64 `json:"fulfillmentFeePercent,omitempty" bson:"fulfillment_fee_percent,omitempty"` // phí xử lý đơn hàng

	// FromTimeIndex: Nếu là __INITIAL thì config này do hệ thống tự động tạo ra
	FromTimeIndex string `json:"fromTimeIndex,omitempty" bson:"from_time_index,omitempty"` // thời gian bắt đầu áp dụng
}

type ProductFeeConfigConcurrentlyRes struct {
	Status    string `json:"status"`
	Message   string `json:"message"`
	ErrorCode string `json:"errorCode,omitempty"`
	Total     int64  `json:"total,omitempty"`

	Data []*ProductFeeConfigConcurrentlyResp `json:"data"`
}
