package api

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

// CartAddItem ...
func CartAddItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartAddItem

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "<PERSON>ui lòng kiểm tra lại thông tin " + err.<PERSON>rror(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if input.Type != "NORMAL" && input.Type != "DEAL" && input.Type != "CAMPAIGN" {
		input.Type = "NORMAL"
	}
	if input.Metadata != nil {
		input.BlockCode = input.Metadata["block_code"]
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := wrapActionSource(req); acc != nil {
		accountId := sdk.ParseInt64(req.GetParam("accountId"), acc.AccountID)
		event := &client.AddToCartEvent{
			Event: client.ADD_TO_CART,
			Metadata: map[string]string{
				"sku":               input.Sku,
				"source":            input.EventSource,
				"screen":            input.EventScreen,
				"host":              input.Host,
				"search_index":      input.SearchIndex,
				"search_page_index": input.SearchPageIndex,
				"recommend_skus":    input.RecommendSKUs,
				"metric":            input.Metric,
			},
			UserAgent:   req.GetHeader("User-Agent"),
			IP:          req.GetIP(),
			AccountType: acc.Type,
			AccountID:   accountId,
			CreatedTime: utils.ParseTimeToPointer(time.Now()),
		}
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.AddCartItem(acc, &input, event, req))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartRemoveItem ...
func CartRemoveItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartRemoveItem
	if input.Type == "" {
		input.Type = "NORMAL"
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := wrapActionSource(req); acc != nil {
		event := &client.RemoveFromCartEvent{
			Event: client.REMOVE_FROM_CART,
			Metadata: map[string]string{
				"sku":        input.Sku,
				"host":       input.Host,
				"block_code": input.BlockCode,
			},
			UserAgent:   req.GetHeader("User-Agent"),
			IP:          req.GetIP(),
			CreatedTime: utils.ParseTimeToPointer(time.Now()),
			AccountType: acc.Type,
			AccountID:   acc.AccountID,
		}
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.RemoveCartItem(acc, &input, event))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartUpdateInfo is handler update current cart information
func CartUpdateInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartUpdateInfo
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		if acc.Type == enum.AccountType.EMPLOYEE {
			return resp.Respond(action.UpdateCartInfo(&model.Account{
				AccountID: sdk.ParseInt64(req.GetParam("accountId"), 0),
				Type:      enum.AccountType.CUSTOMER,
			}, &input))
		}
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.UpdateCartInfo(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartUpdateVoucher is handler update cart voucher code
func CartUpdateVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartUpdateVoucher
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		if acc.Type == enum.AccountType.EMPLOYEE {
			return resp.Respond(action.UpdateCartApplyVoucher(&model.Account{
				AccountID:    sdk.ParseInt64(req.GetParam("accountId"), 0),
				Type:         enum.AccountType.CUSTOMER,
				SourceDetail: acc.SourceDetail,
			}, &input))
		}
		return resp.Respond(action.UpdateCartApplyVoucher(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartRemoveVoucher is handler remove cart voucher code
func CartRemoveVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.RemoveVoucherRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		if acc.Type == enum.AccountType.EMPLOYEE {
			return resp.Respond(action.RemoveVoucherCode(&model.Account{
				AccountID:    sdk.ParseInt64(req.GetParam("accountId"), 0),
				Type:         enum.AccountType.CUSTOMER,
				SourceDetail: acc.SourceDetail,
			}, &input))
		}
		return resp.Respond(action.RemoveVoucherCode(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartAddVoucher is handler add cart voucher code
func CartAddVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.RemoveVoucherRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		if acc.Type == enum.AccountType.EMPLOYEE {
			return resp.Respond(action.RemoveVoucherCode(&model.Account{
				AccountID:    sdk.ParseInt64(req.GetParam("accountId"), 0),
				Type:         enum.AccountType.CUSTOMER,
				SourceDetail: acc.SourceDetail,
			}, &input))
		}
		return resp.Respond(action.AddVoucherCode(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartGetInfo is handler get current cart
func CartGetInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var getVoucherAuto = req.GetParam("getVoucherAuto") == "true"
	var screen = req.GetParam("screen")
	var redeemCodeRemovedStr = req.GetParam("redeemCodeRemovedStr")
	var autoRemoveVoucherAutoInvalid = true
	redeemCodeRemovedArr := make([]string, 0)
	if len(redeemCodeRemovedStr) > 0 {
		redeemCodeRemovedArr = strings.Split(redeemCodeRemovedStr, ",")
	}
	getVoucherAuto = screen != "Payment"
	if acc := wrapActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.GetCartCacheWithVoucher(acc, getVoucherAuto, redeemCodeRemovedArr, autoRemoveVoucherAutoInvalid, screen))
		//return resp.Respond(action.GetCartInfo(acc, getVoucherAuto, redeemCodeRemovedArr, autoRemoveVoucherAutoInvalid, screen))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartCheckout is handler checkout current cart
func CartCheckout(req sdk.APIRequest, resp sdk.APIResponder) error {
	var start = time.Now()
	var result *common.APIResponse
	var acc *model.Account
	var session *model.Session
	var ip = req.GetIP()
	var ua = req.GetHeader("User-Agent")

	defer sdk.Execute(func() {
		var end = time.Now()
		event := client.CreateOrderEvent{
			Event: client.CREATE_ORDER,
			Metadata: map[string]string{
				"function": "CartCheckout",
				"start":    start.Format(time.RFC3339),
				"end":      end.Format(time.RFC3339),
				"exec_ms":  strconv.FormatInt(end.Sub(start).Milliseconds(), 10),
			},
			CreatedTime: &start,
			IP:          ip,
			UserAgent:   ua,
		}
		if result != nil {
			event.ResultStatus = result.Status
			event.ResultErrorCode = result.ErrorCode
		}
		if acc != nil {
			event.AccountID = acc.AccountID
			event.AccountType = acc.Type
		}

		go client.Services.Collector.SendEventCreateOrder(event)
	})

	var input model.CartCheckoutData
	req.GetContent(&input)
	usInfo := getUAInfo(req.GetHeader("User-Agent"))
	input.SourceDetail = model.OrderSourceDetail{
		Os:             usInfo.OSName,
		OsVersion:      usInfo.OSVersion,
		Browser:        usInfo.ClientName,
		BrowserVersion: usInfo.ClientVersion,
		Platform:       usInfo.Platform,
		IP:             req.GetIP(),
	}

	if acc, session = wrapActionSourceAllInfo(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		result = action.CheckoutCart(acc, session, &input, false, req.GetHeaders())
		return resp.Respond(result)
	}

	//return resp.Respond(&common.APIResponse{
	//	Status:    common.APIStatus.Unauthorized,
	//	Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
	//	ErrorCode: "ACTION_NOT_FOUND",
	//})

	result = &common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	}
	return resp.Respond(result)
}

// CartDelete ...
func CartDelete(req sdk.APIRequest, resp sdk.APIResponder) error {
	if acc := wrapActionSource(req); acc != nil {
		return resp.Respond(action.DeleteCart(acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartRevert2 is func to edit order to cart
func CartRevert2(req sdk.APIRequest, resp sdk.APIResponder) error {
	var orderId = sdk.ParseInt64(req.GetParam("orderId"), 0)
	if acc := getActionSource(req); acc != nil {
		if acc.Type == enum.AccountType.EMPLOYEE {
			return resp.Respond(action.RevertCart2(&model.Account{
				AccountID: sdk.ParseInt64(req.GetParam("accountId"), 0),
				Type:      enum.AccountType.CUSTOMER,
			}, orderId))
		}
		return resp.Respond(action.RevertCart2(acc, orderId))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// ReOrder is func to re order to cart
func ReOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var start = time.Now()
	var result *common.APIResponse
	var acc *model.Account
	var ip = req.GetIP()
	var ua = req.GetHeader("User-Agent")
	var byEmployee = false

	defer sdk.Execute(func() {
		var end = time.Now()
		event := client.CreateOrderEvent{
			Event: client.CREATE_ORDER,
			Metadata: map[string]string{
				"function":   "ReOrder",
				"start":      start.Format(time.RFC3339),
				"end":        end.Format(time.RFC3339),
				"exec_ms":    strconv.FormatInt(end.Sub(start).Milliseconds(), 10),
				"byEmployee": strconv.FormatBool(byEmployee),
			},
			CreatedTime: &start,
			IP:          ip,
			UserAgent:   ua,
		}
		if result != nil {
			event.ResultStatus = result.Status
			event.ResultErrorCode = result.ErrorCode
		}
		if acc != nil {
			event.AccountID = acc.AccountID
			event.AccountType = acc.Type
		}

		go client.Services.Collector.SendEventCreateOrder(event)
	})

	var input model.ReOrderRequest
	if err := req.GetContent(&input); err != nil {
		result = &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		}
		return resp.Respond(result)
	}

	acc = getActionSource(req)
	if acc == nil {
		result = &common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
			ErrorCode: "ACTION_NOT_FOUND",
		}
		return resp.Respond(result)
	}

	if acc.Type == enum.AccountType.EMPLOYEE {
		byEmployee = true
		acc.AccountID = sdk.ParseInt64(req.GetParam("accountId"), 0)
		acc.Type = enum.AccountType.CUSTOMER
	}
	acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())

	return resp.Respond(action.ReOrder(acc, input.OrderID))
}

// CartItemUpdate ...
func CartItemUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartItemUpdate
	if input.Type == "" {
		input.Type = "NORMAL"
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := wrapActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.UpdateCartItem(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartConfirm ...
func CartConfirm(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartConfirm
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := wrapActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.ConfirmCart(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CartGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q          = req.GetParam("q")
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		skuCode    = req.GetParam("skuCode")
		getTotal   = req.GetParam("getTotal") == "true"
		hasVoucher = req.GetParam("hasVoucher") == "true"
	)
	if limit < 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}
	var query = model.Cart{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}
	return resp.Respond(action.GetCartList(&query, skuCode, offset, limit, getTotal, hasVoucher))
}

// CartItemGetList is handler get list cart item with pagination
func CartItemGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q            = req.GetParam("q")
		offset       = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit        = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal     = req.GetParam("getTotal") == "true"
		cartIds      = req.GetParam("cartIds")
		onlyQuantity = req.GetParam("onlyQuantity") == "true"
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 1000 {
		limit = 1000
	}

	if offset < 0 {
		offset = 0
	}

	var query = model.CartItem{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không thể đọc dữ liệu",
				ErrorCode: "PARAM_INVALID",
			})
		}
	}
	if len(cartIds) > 0 {
		idsArr := strings.Split(cartIds, ",")
		listIDs := []int64{}
		for _, id := range idsArr {
			intID, _ := strconv.Atoi(id)
			if intID > 0 {
				listIDs = append(listIDs, int64(intID))
			}
		}
		query.ComplexQuery = []*bson.M{
			{
				"cart_id": &bson.M{
					"$in": listIDs,
				},
			},
		}
	}

	return resp.Respond(action.GetCartItemList(getActionSource(req), &query, offset, limit, getTotal, onlyQuantity))
}

func SelectCartItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.SelectCartItemRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := wrapActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.SelectCartItem(acc, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// VerifyCart ...
func VerifyCart(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.VerifyCartRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := wrapActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.VerifyCart(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CartGetActiveVoucher ...
func CartGetActiveVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset      = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit       = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal    = req.GetParam("getTotal") == "true"
		getValidate = req.GetParam("getValidate") == "true"
		search      = req.GetParam("search")
		scope       = req.GetParam("scope")

		systemDisplay = req.GetParam("systemDisplay")
	)
	sourceDetail := getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
	if acc := getActionSource(req); acc != nil {
		if acc.Type == enum.AccountType.EMPLOYEE {
			return resp.Respond(action.CartGetActiveVoucher(&model.Account{
				AccountID: sdk.ParseInt64(req.GetParam("accountId"), 0),
				Type:      enum.AccountType.CUSTOMER,
			}, offset, limit, getTotal, getValidate, search, scope, systemDisplay, sourceDetail))
		}
		return resp.Respond(action.CartGetActiveVoucher(acc, offset, limit, getTotal, getValidate, search, scope, systemDisplay,
			sourceDetail))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func getOrderSourceDetail(ua string, ip string) *model.OrderSourceDetail {
	usInfo := getUAInfo(ua)
	return &model.OrderSourceDetail{
		Os:             usInfo.OSName,
		OsVersion:      usInfo.OSVersion,
		Browser:        usInfo.ClientName,
		BrowserVersion: usInfo.ClientVersion,
		Platform:       usInfo.Platform,
		IP:             ip,
	}
}

// CartGetInfo is handler get current cart
func CartGetInfoLite(req sdk.APIRequest, resp sdk.APIResponder) error {
	if acc := wrapActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.GetCartInfoLite(acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CartItemLiteGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartItemRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := wrapActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.GetCartItemLite(acc, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CartItemRemoveImportantItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartRemoveItemImportant
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := wrapActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.RemoveCartItemImportant(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func CartUncheckRefuseSplitOrder(req sdk.APIRequest, resp sdk.APIResponder) error {

	return resp.Respond(action.CartUncheckRefuseSplitOrder())
}
