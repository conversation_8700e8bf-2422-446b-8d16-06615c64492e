package consumer

import (
	"fmt"
	"runtime/debug"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func HandleChargePoorQualityProduct(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return nil
	}

	var input model.PoorQualityProductRequest
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return nil
	}

	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic: %s", string(debug.Stack()))
			fmt.Printf("[%d] %s\n", input.OrderId, err.Error())
		}
	}()

	processRes := reconcile_action.ProcessChargePoorQualityProduct(&input)
	if processRes.Status == common.APIStatus.Error {
		return fmt.Errorf("process charge poor quality product error: %s, %s",
			processRes.ErrorCode, processRes.Message)
	}

	return nil
}
