package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

// Invoice ...
type InvoiceRequest struct {
	CompanyName       string `json:"companyName,omitempty" bson:"company_name,omitempty"`
	TaxCode           string `json:"taxCode,omitempty" bson:"tax_code,omitempty"`
	CompanyAddress    string `json:"companyAddress,omitempty" bson:"company_address,omitempty"`
	Email             string `json:"email,omitempty" bson:"email,omitempty"`
	RequestInvoice    *bool  `json:"invoiceRequest,omitempty" bson:"invoice_request,omitempty"`
	IsIncidentTaxCode *bool  `json:"isIncidentTaxCode,omitempty" bson:"is_incident_tax_code,omitempty"`
}

// Cart ...
type Cart struct {
	// Note: set cart.ID=nil if you clone the cart (E.g. Before calling `CartDeletedDB.Create(cart)`)
	ID primitive.ObjectID `json:"-" bson:"_id,omitempty"`

	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	LastActionTime  *time.Time `json:"lastActionTime,omitempty" bson:"last_action_time,omitempty"`

	// reference data
	CartID int64  `json:"cartId,omitempty" bson:"cart_id,omitempty"` //
	CartNo string `json:"cartNo,omitempty" bson:"cart_no,omitempty"` //

	// customer info
	AccountID               int64   `json:"accountId,omitempty" bson:"account_id,omitempty"`                              // mã tài khoản
	CustomerID              int64   `json:"customerId,omitempty" bson:"customer_id,omitempty"`                            //
	CustomerCode            string  `json:"customerCode,omitempty" bson:"customer_code,omitempty"`                        // mã khách hàng
	CustomerName            string  `json:"customerName,omitempty" bson:"customer_name,omitempty"`                        // tên người nhận
	CustomerPhone           string  `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`                      // điện thoại người nhận
	CustomerEmail           *string `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`                      // email người nhận
	CustomerShippingAddress string  `json:"customerShippingAddress,omitempty" bson:"customer_shipping_address,omitempty"` // địa chỉ người nhận
	CustomerDistrictCode    string  `json:"customerDistrictCode,omitempty" bson:"customer_district_code,omitempty"`       // khu vực nhận
	CustomerWardCode        string  `json:"customerWardCode,omitempty" bson:"customer_ward_code,omitempty"`               //
	CustomerProvinceCode    string  `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`       //
	CustomerAddressCode     string  `json:"customerAddressCode,omitempty" bson:"customer_address_code,omitempty"`         //
	CustomerRegionCode      string  `json:"customerRegionCode,omitempty" bson:"customer_region_code,omitempty"`           //
	CustomerScope           string  `json:"customerScope,omitempty" bson:"customer_scope,omitempty"`                      //

	FlattenLocation []string `json:"flattenLocation,omitempty" bson:"flatten_location,omitempty"`
	WardCode        string   `json:"wardCode" bson:"ward_code,omitempty"`                          //
	DistrictCode    string   `json:"districtCode" bson:"district_code,omitempty"`                  //
	ProvinceCode    string   `json:"provinceCode,omitempty" bson:"province_code,omitempty"`        //
	RegionCode      string   `json:"regionCode,omitempty" bson:"region_code,omitempty"`            //
	RegionCodes     []string `json:"regionCodes,omitempty" bson:"region_codes,omitempty"`          //
	SaleRegionCodes []string `json:"saleRegionCodes,omitempty" bson:"sale_region_codes,omitempty"` //

	// fee
	// payment fee
	PaymentMethod           string   `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"` // phương thức thanh toán cod/chuyển khoản
	PaymentMethodFee        int64    `json:"paymentMethodFee,omitempty" bson:"-"`                     // phí phương thức thanh toán cod/chuyển khoản
	PaymentMethodPercentage *float64 `json:"paymentMethodPercentage,omitempty" bson:"-"`              // phần trăm giảm giá cho hình thức thanh toán

	PartnerPaymentMethod *PartnerPaymentMethod `json:"partnerPaymentMethod,omitempty" bson:"-"` // Phương thức thanh toán qua cổng thanh toán online

	// // PartnerPaymentMethod *PartnerPaymentMethod `json:"partnerPaymentMethod,omitempty" bson:"-"`
	// PartnerPaymentMethodFixedFee          *int64   `json:"partnerPaymentMethodFee,omitempty" bson:"-"`               // Phí thanh toán cố định qua cổng thanh toán online
	// PartnerPaymentMethodDynamicFee        int64    `json:"partnerPaymentMethodDynamicFee,omitempty" bson:"-"`        // Phí thanh toán cố định qua cổng thanh toán online
	// PartnerPaymentMethodDynamicPercentage *float64 `json:"partnerPaymentMethodDynamicPercentage,omitempty" bson:"-"` // Phần trăm phí thanh toán cố định qua cổng thanh toán online
	// TotalPartnerPaymentMethodFee          int64    `json:"totalPartnerPaymentMethodFee,omitempty" bson:"-"`          // Tổng phí thanh toán qua cổng thanh toán online

	// delivery fee
	DeliveryMethod    string `json:"deliveryMethod,omitempty" bson:"delivery_method,omitempty"` // hình thức giao hàng
	DeliveryMethodFee int64  `json:"deliveryMethodFee,omitempty" bson:"-"`                      // phí hình thức giao hàng
	ExtraFee          int64  `json:"extraFee,omitempty" bson:"-"`                               // phụ phí
	ExtraFeeNote      string `json:"extraFeeNote,omitempty" bson:"-"`                           // phụ phí ghi chú
	UseFreeDelivery   bool   `json:"useFreeDelivery,omitempty" bson:"-"`

	// delivery date and note
	DeliveryDate *time.Time `json:"deliveryDate,omitempty" bson:"delivery_date,omitempty"` // ngày giao mong muốn
	Note         *string    `json:"note,omitempty" bson:"note,omitempty"`                  // ghi chú giỏ hàng

	// promotion
	RedeemCode        *[]*string          `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"` // mã giảm giá
	RedeemCodeMap     []*RedeemApply      `json:"redeemCodeMap,omitempty" bson:"redeem_code_map,omitempty"`
	AllRedeemCode     *[]*string          `json:"-" bson:"-"`                                                       // mã giảm giá
	RedeemApplyResult []*PromoApplyResult `json:"redeemApplyResult,omitempty" bson:"redeem_apply_result,omitempty"` //
	RedeemCodeRemoved []string            `json:"redeemCodeRemoved,omitempty" bson:"redeem_code_removed,omitempty"` // mã giảm giá bị xóa, không auto add lại

	// price
	TotalPrice                        int `json:"totalPrice,omitempty" bson:"total_price,omitempty"` // tổng tiền giỏ hàng sau cùng
	Price                             int `json:"price,omitempty" bson:"price,omitempty"`            // tổng tiển chưa trừ các khoản khác
	SubPrice                          int `json:"subPrice,omitempty" bson:"sub_price,omitempty"`     // tổng tiển sau khi discount = price - discount
	TotalFee                          int `json:"totalFee,omitempty" bson:"total_fee,omitempty"`     //
	Discount                          int `json:"discount,omitempty" bson:"discount,omitempty"`      //
	DiscountRemain                    int `json:"discountRemain,omitempty" bson:"-"`
	TotalPriceAllItem                 int `json:"totalPriceAllItem,omitempty" bson:"-"`
	TotalPriceBeforePartnerPaymentFee int `json:"totalPriceBeforePartnerPaymentFee,omitempty" bson:"total_price_before_partner_payment_fee,omitempty"` // tổng tiền trước khi trừ phí thanh toán online

	IsRefuseSplitOrder *bool `json:"isRefuseSplitOrder,omitempty" bson:"is_refuse_split_order,omitempty"`

	// status
	Status enum.CartStateValue `json:"status,omitempty" bson:"status,omitempty"` //
	Source *enum.SourceValue   `json:"source,omitempty" bson:"source,omitempty"` //

	Invoice *InvoiceRequest `json:"invoice,omitempty" bson:"invoice,omitempty"`

	// items
	RefCartItem int64       `json:"-" bson:"ref_cart_item,omitempty"`
	Items       []*CartItem `json:"cartItems,omitempty" bson:"-"` //

	CustomerOrderIndex int `json:"-" bson:"customer_order_index,omitempty"` //

	SystemDisplay string `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`

	SourceDetail              *OrderSourceDetail `json:"sourceDetail,omitempty" bson:"-"`
	DealLimitPerCustomerCodes []string           `json:"-" bson:"-"`

	//statistic
	TotalItem             *int    `json:"totalItem,omitempty" bson:"total_item,omitempty"`
	TotalQuantity         *int    `json:"totalQuantity,omitempty" bson:"total_quantity,omitempty"`
	TotalQuantitySelected *int    `json:"totalQuantitySelected,omitempty" bson:"-"`
	TotalItemSelected     *int    `json:"totalItemSelected,omitempty" bson:"-"`
	TotalWeight           float64 `json:"totalWeight,omitempty" bson:"-"`
	TotalVolume           float64 `json:"totalVolume,omitempty" bson:"-"`
	MaxVolume             float64 `json:"maxVolume,omitempty" bson:"-"`
	MaxWidth              float64 `json:"maxWidth,omitempty" bson:"-"`
	MaxHeight             float64 `json:"maxHeight,omitempty" bson:"-"`
	MaxLength             float64 `json:"maxLength,omitempty" bson:"-"`
	MaxWeight             float64 `json:"maxWeight,omitempty" bson:"-"`

	ErrorCode    string `json:"errorCode,omitempty" bson:"-"`
	ErrorMessage string `json:"errorMessage,omitempty" bson:"-"`

	// invoice
	CanExportInvoice *bool `json:"canExportInvoice,omitempty" bson:"-"`

	// cache sku map
	SkuMap       interface{}    `json:"-" bson:"-"`
	DealLimitMap map[string]int `json:"-" bson:"-"`

	/// used only for query, not for save data
	ComplexQuery      []*bson.M  `json:"-" bson:"$and,omitempty"`
	PriceFrom         *int       `json:"priceFrom,omitempty" bson:"-"`
	PriceTo           *int       `json:"priceTo,omitempty" bson:"-"`
	DateFrom          *time.Time `json:"timeFrom,omitempty" bson:"-"`
	DateTo            *time.Time `json:"timeTo,omitempty" bson:"-"`
	TotalQuantityFrom *int       `json:"totalQuantityFrom,omitempty" bson:"-"`
	TotalQuantityTo   *int       `json:"totalQuantityTo,omitempty" bson:"-"`
	TotalItemFrom     *int       `json:"totalItemFrom,omitempty" bson:"-"`
	TotalItemTo       *int       `json:"totalItemTo,omitempty" bson:"-"`
	ValidateOrder     *bool      `json:"validateOrder,omitempty" bson:"-"`

	TagMap                       map[string]*OrderTagConfig `json:"-" bson:"-"`
	GetVoucherAutoApply          bool                       `json:"-" bson:"-"`
	RedeemCodeRemovedArr         []string                   `json:"-" bson:"-"`
	AutoRemoveVoucherAutoInvalid bool                       `json:"-" bson:"-"`
	Screen                       string                     `json:"-" bson:"-"`

	// brand
	Key                string       `json:"key,omitempty" bson:"key,omitempty"`
	CreatedByAccountID int64        `json:"createdByAccountId,omitempty" bson:"created_by_account_id,omitempty"`
	BrandCode          string       `json:"brandCode,omitempty" bson:"brand_code,omitempty"`
	BrandGifts         *[]BrandGift `json:"brandGifts,omitempty" bson:"brand_gifts,omitempty"`
	BrandNames         []string     `json:"brandNames,omitempty" bson:"brand_names,omitempty"`
	SalesType          string       `json:"salesType,omitempty" bson:"sales_type,omitempty"`
	SalesTypeCode      string       `json:"salesTypeCode,omitempty" bson:"sales_type_code,omitempty"`
}

// PromoApply ...
type PromoApply struct {
	CanUse            bool              `json:"canUse" bson:"-"`
	DiscountValue     int               `json:"discountValue"`
	VoucherCode       string            `json:"voucherCode"`
	Data              []*PromoApplyItem `json:"items" bson:"items"`
	Gifts             []Gift            `json:"gifts" bson:"gifts"`
	ErrorMessage      string            `json:"errorMessage,omitempty"`
	AutoApply         bool              `json:"autoApply,omitempty"`
	MatchSeller       string            `json:"matchSeller,omitempty"`
	MatchProducts     []MatchProduct    `json:"matchProducts,omitempty"`
	DiscountInfos     []DiscountInfo    `json:"discountInfos,omitempty"`
	SellerCode        string            `json:"sellerCode,omitempty"`
	SellerCodes       []string          `json:"sellerCodes,omitempty"`
	ApplySkus         []string          `json:"applySkus,omitempty"`
	NotApplySkus      []string          `json:"notApplySkus,omitempty"`
	StoreCode         string            `json:"storeCode,omitempty"`
	PaymentMethod     string            `json:"paymentMethod,omitempty"`
	ErrorCode         string            `json:"errorCode,omitempty"`
	VoucherName       string            `json:"voucherName,omitempty"`
	NumberOfAutoApply int               `json:"numberOfAutoApply,omitempty"`
	ChargeFee         string            `json:"chargeFee,omitempty"`
}

type MatchProduct struct {
	Sku           string `json:"sku,omitempty" bson:"sku,omitempty"`
	ProductID     int64  `json:"productId,omitempty" bson:"product_id,omitempty"`
	ProductCode   string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	SellerCode    string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	MinQuantity   int    `json:"minQuantity,omitempty" bson:"min_quantity,omitempty"`
	MinTotalPrice *int   `json:"minTotalPrice,omitempty" bson:"min_total_price,omitempty"`
}

type DiscountInfo struct {
	Discount    int      `json:"discount,omitempty"`
	Sku         string   `json:"sku,omitempty"`
	IsApply     bool     `json:"isApply,omitempty"`
	SellerCodes []string `json:"sellerCodes,omitempty"`
	Message     string   `json:"message,omitempty"`
}

// Gift ...
type Gift struct {
	ProductID        int64  `json:"productId,omitempty" bson:"product_id,omitempty"`
	Sku              string `json:"sku,omitempty" bson:"sku,omitempty"`
	Quantity         int    `json:"quantity,omitempty" bson:"quantity,omitempty"`
	QuantityPerApply int64  `json:"quantityPerApply,omitempty" bson:"quantity_per_apply,omitempty"`
}

// PromoApplyItem ...
type PromoApplyItem struct {
	Price    int    `json:"price"`
	Quantity int    `json:"quantity"`
	Total    int    `json:"total"`
	Sku      string `json:"sku"`
}

// PromoApplyResult ...
type PromoApplyResult struct {
	Code              string         `json:"code" bson:"code,omitempty"`
	CanUse            bool           `json:"canUse" bson:"can_use,omitempty"`
	Message           string         `json:"message" bson:"message,omitempty"`
	Gift              []Gift         `json:"gifts" bson:"gifts,omitempty"`
	AutoApply         bool           `json:"autoApply,omitempty" bson:"auto_apply,omitempty"`
	SellerCode        string         `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerCodes       []string       `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	MatchSeller       string         `json:"matchSeller,omitempty" bson:"match_seller,omitempty"`
	DiscountValue     int            `json:"discountValue,omitempty" bson:"discount_value,omitempty"`
	MatchProducts     []MatchProduct `json:"matchProducts,omitempty" bson:"match_products,omitempty"`
	DiscountInfos     []DiscountInfo `json:"discountInfos,omitempty" bson:"discount_infos,omitempty"`
	ApplySkus         []string       `json:"applySkus,omitempty" bson:"apply_skus,omitempty"`
	NotApplySkus      []string       `json:"notApplySkus,omitempty" bson:"not_apply_skus,omitempty"`
	StoreCode         string         `json:"storeCode,omitempty" bson:"store_code,omitempty"`
	PaymentMethod     string         `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	Name              string         `json:"name,omitempty" bson:"name,omitempty"`
	NumberOfAutoApply int            `json:"numberOfAutoApply,omitempty" bson:"number_of_auto_apply,omitempty"`
	ChargeFee         string         `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"`
}

type ActualDiscount struct {
	Code          string   `json:"code" bson:"code,omitempty"`
	SellerCodes   []string `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	DiscountValue int      `json:"discountValue,omitempty" bson:"discount_value,omitempty"`
}

// RedeemApply ...
type RedeemApply struct {
	Code      string `json:"code" bson:"code,omitempty"`
	AutoApply bool   `json:"autoApply,omitempty" bson:"auto_apply,omitempty"`
}

// CartDB ...
var CartDB = &db.Instance{
	ColName:        "cart",
	TemplateObject: &Cart{},
}

// CartDeletedDB ...
var CartDeletedDB = &db.Instance{
	ColName:        "cart_deleted",
	TemplateObject: &Cart{},
}

// InitCartModel is func init model cart
func InitCartModel(s *mongo.Database) {
	CartDB.ApplyDatabase(s)
	// t := true
	// _ = CartDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "customer_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
}

// InitCartModel is func init model cart
func InitCartDeletedModel(s *mongo.Database) {
	CartDeletedDB.ApplyDatabase(s)
}

// VoucherActive ....
type VoucherActive struct {
	Code                 string     `json:"code,omitempty" bson:"code,omitempty"`
	PromotionID          int64      `json:"promotionId,omitempty" bson:"promotion_id,omitempty"`
	PromotionName        string     `json:"promotionName,omitempty" bson:"-"`
	StartTime            *time.Time `json:"startTime,omitempty" bson:"-"`
	PublicTime           *time.Time `json:"publicTime,omitempty" bson:"-"`
	EndTime              *time.Time `json:"endTime,omitempty" bson:"-"`
	Description          string     `json:"description,omitempty" bson:"-"`
	ConditionDescription string     `json:"conditionDescription,omitempty" bson:"-"`
	CanUse               bool       `json:"canUse,omitempty" bson:"-"`
	ErrorMessage         string     `json:"errorMessage,omitempty" bson:"-"`
	Discount             int        `json:"discount,omitempty" bson:"-"`
	Gifts                []Gift     `json:"gifts,omitempty" bson:"-"`
	Reward               Reward     `json:"reward,omitempty" bson:"-"`
	Voucher              struct {   // app old version
		Code             string     `json:"code,omitempty"`
		AppliedCustomers []int64    `json:"appliedCustomers" bson:"-"`
		PromotionName    string     `json:"promotionName,omitempty" bson:"-"`
		StartTime        *time.Time `json:"startTime,omitempty" bson:"-"`
		EndTime          *time.Time `json:"endTime,omitempty" bson:"-"`
		Promotion        struct {
			Description string `json:"description,omitempty"`
		} `json:"promotion,omitempty"`
	} `json:"voucher,omitempty" bson:"-"`
}

type Reward struct {
	Type               string `json:"type,omitempty" bson:"type,omitempty"`
	PercentageDiscount int64  `json:"percentageDiscount,omitempty" bson:"percentage_discount,omitempty"`
	AbsoluteDiscount   int64  `json:"absoluteDiscount,omitempty" bson:"absolute_discount,omitempty"`
	MaxDiscount        int64  `json:"maxDiscount,omitempty" bson:"max_discount,omitempty"`
	PointValue         int64  `json:"pointValue,omitempty" bson:"point_value,omitempty"`
	Gifts              []Gift `json:"gifts,omitempty" bson:"gifts,omitempty"`
}
