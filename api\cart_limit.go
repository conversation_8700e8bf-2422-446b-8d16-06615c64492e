package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

// GetSingleCartLimit is func get category by slug
func GetSingleCartLimit(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")
	// fill query
	query := model.CartLimit{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	return resp.Respond(action.GetCartLimit(&query))
}

// GetCartLimitList iss func get list category
func GetCartLimitList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.CartLimit{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	return resp.Respond(action.GetCartLimitList(&query, offset, limit, getTotal))
}

// CreateCartLimit is func create a category with payload
func CreateCartLimit(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartLimit
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})

	}
	return resp.Respond(action.CreateCartLimit(&input))
}

// UpdateCartLimit is func update a category
func UpdateCartLimit(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartLimitUpdate
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không thể đọc dữ liệu",
		})
	}
	return resp.Respond(action.UpdateCartLimit(input.Code, &input))
}
