package client

import "gitlab.buymed.tech/sdk/go-sdk/sdk/common"

type WarehouseRes struct {
	common.APIResponse `json:",inline"`
	Data               []*Warehouse `json:"data"`
}

type Warehouse struct {
	WarehouseID int64  `json:"warehouseId"`
	Code        string `json:"code"`

	Address      string              `json:"address"`
	Area         int                 `json:"area"`
	Areas        []string            `json:"areas"`
	DistrictCode string              `json:"districtCode"`
	DistrictName string              `json:"districtName"`
	Name         string              `json:"name"`
	Status       WarehouseStatusType `json:"status"`
	Type         string              `json:"type"`
	WardCode     string              `json:"wardCode"`
	WardName     string              `json:"wardName"`
	Width        int64               `json:"width"`
}

type WarehouseStatusType string

var WarehouseStatusValue = struct {
	ACTIVE   WarehouseStatusType
	INACTIVE WarehouseStatusType
}{
	ACTIVE:   "ACTIVE",
	INACTIVE: "INACTIVE",
}
