package reconcile_action

import (
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func CompleteReconciliation(input *model.Reconciliation, as *model.ActionSource) *common.APIResponse {
	reconciliationF := model.Reconciliation{ID: input.ID}
	result := model.ReconciliationDB.QueryOne(reconciliationF)
	if result.Status != common.APIStatus.Ok {
		return result
	}
	reconciliation := result.Data.([]*model.Reconciliation)[0]

	model.ReconcileLogReqDb.Create(&model.ReconcileLogReq{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		ReqType:                    model.ReconcileLogReqType.Completed,
		ActionSource:               as.Account,
	})

	if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Confirmed &&
		reconciliation.ReconciliationStatus == model.ReconciliationStatus.Completed {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      result.Data,
			Message:   "Lượt đối soát chưa được xác nhận",
			ErrorCode: "UNCONFIRMED_YET",
		}
	}

	completedTime := time.Now()

	if input.ReconciledTime != nil {
		completedTime = *input.ReconciledTime
	}

	if reconciliation.ReconciliationStatus == model.ReconciliationStatus.Completed && reconciliation.ReconciledTime != nil {
		completedTime = *reconciliation.ReconciledTime
	}

	reconciliationItemF := model.ReconciliationItem{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		OperationAnd: []bson.M{
			{
				"order_id": bson.M{
					"$exists": true,
				},
			},
		},
	}
	orderIDsRes := model.ReconciliationItemDB.Distinct(reconciliationItemF, "order_id")
	if orderIDsRes.Status != common.APIStatus.Ok {
		return orderIDsRes
	}

	orderIDsResData := orderIDsRes.Data.([]interface{})
	triggerRes := triggerCompleteReconcileOrderSeller(
		reconciliation.SellerCode,
		orderIDsResData,
		&completedTime,
		input.SessionPayment,
	)
	if triggerRes.Status != common.APIStatus.Ok {
		log.Printf("trigger CompleteReconciliation failed: %#v\n", triggerRes)
	}

	result = model.ReconciliationDB.UpdateOne(reconciliationF, model.Reconciliation{
		ReconciliationStatus: model.ReconciliationStatus.Completed,
		ReconciledTime:       &completedTime,
		SessionPayment:       input.SessionPayment,
		PaidFor:              input.PaidFor,
		PaidForReason:        input.PaidForReason,
	})

	return result
}

func triggerCompleteReconcileOrderSeller(
	sellerCode string,
	orderIds []interface{},
	ReconciledTime *time.Time,
	sessionPayment *string,
) *common.APIResponse {
	orderIDs := make([]int64, len(orderIds))
	for i, v := range orderIds {
		orderIDs[i] = v.(int64)
	}

	result := client.Services.SellerMis.
		CompleteReconcileOrderSeller(sellerCode, orderIDs, ReconciledTime, sessionPayment)
	return result
}
