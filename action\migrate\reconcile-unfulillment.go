package migrate

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateReconcileUnFulfillment(indexes []string) *common.APIResponse {

	var offset, limit int64 = 0, 1000
	recItemDelCount := 0
	recInboundDelCount := 0
	reconcileUpdated := 0

	type sellerEffected struct {
		sellerCode         string
		reconcileTimeIndex string
	}
	sellersEffected := map[string]sellerEffected{}

	for {
		queryRes := model.ReconciliationItemDB.Query(&model.ReconciliationItem{
			OperationAnd: []bson.M{
				{"reconcile_schedule_time_index": bson.M{"$in": indexes}},
				{"fee_type": enum.FeeType.FULFILLMENT_PENALTY},
			},
		}, offset, limit, &primitive.M{"_id": 1})
		if queryRes.Status != common.APIStatus.Ok {
			break
		}

		reconciliationItems := queryRes.Data.([]*model.ReconciliationItem)
		for _, reconciliationItem := range reconciliationItems {

			key := fmt.Sprintf("%s_%s", reconciliationItem.SellerCode, reconciliationItem.ReconcileScheduleTimeIndex)
			if _, ok := sellersEffected[key]; !ok {
				sellersEffected[key] = sellerEffected{
					sellerCode:         reconciliationItem.SellerCode,
					reconcileTimeIndex: reconciliationItem.ReconcileScheduleTimeIndex,
				}
			}

			recItemDeletedRes := model.ReconciliationItemDB.Delete(&model.ReconciliationItem{
				ID: reconciliationItem.ID,
			})
			if recItemDeletedRes.Status == common.APIStatus.Ok {
				recItemDelCount++
			}

			time.Sleep(30 * time.Millisecond)
		}
	}

	for _, sellerEffected := range sellersEffected {
		recRes := model.ReconciliationDB.QueryOne(&model.Reconciliation{
			SellerCode:                 sellerEffected.sellerCode,
			ReconcileScheduleTimeIndex: sellerEffected.reconcileTimeIndex,
		})
		if recRes.Status != common.APIStatus.Ok {
			continue
		}

		reconciliation := recRes.Data.([]*model.Reconciliation)[0]
		if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
			continue
		}

		recCalValue, err := reconcile_action.CalculateReconciliation(sellerEffected.sellerCode, sellerEffected.reconcileTimeIndex)
		if err != nil {
			continue
		}

		updater := model.Reconciliation{
			TotalBuyerFee:  &recCalValue.TotalBuyerFee,
			TotalRevenue:   &recCalValue.TotalRevenue,
			ListingFee:     &recCalValue.ListingFee,
			FulfillmentFee: &recCalValue.FulfillmentFee,
			PenaltyFee:     &recCalValue.PenaltyFee,
			BonusAmount:    &recCalValue.BonusAmount,
			TotalPayment:   &recCalValue.TotalPayment,
		}
		result := model.ReconciliationDB.UpdateOne(&model.Reconciliation{
			SellerCode:                 sellerEffected.sellerCode,
			ReconcileScheduleTimeIndex: sellerEffected.reconcileTimeIndex,
		}, updater)
		if result.Status == common.APIStatus.Ok {
			reconcileUpdated++
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate finish",
		Data: []interface{}{
			map[string]interface{}{
				"recItemDelCount":    recItemDelCount,
				"recInboundDelCount": recInboundDelCount,
				"sellersEffected":    len(sellersEffected),
				"reconcileUpdated":   reconcileUpdated,
			},
		},
	}
}
