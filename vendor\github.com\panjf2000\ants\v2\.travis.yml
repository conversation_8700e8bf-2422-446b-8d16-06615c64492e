language: go

os:
  - linux
  - osx
  - windows

dist: bionic

go:
#  - 1.11.x
#  - 1.12.x
  - 1.13.x
  - 1.14.x

go_import_path: github.com/panjf2000/ants

before_install:
  - if [[ "${GO111MODULE}" = "on" ]]; then mkdir "${HOME}/go"; export GOPATH="${HOME}/go";
    export PATH="$GOPATH/bin:$PATH"; fi
  - mkdir -p ~/bin/ && export PATH="~/bin/:$PATH"

install:
  - go get -u golang.org/x/lint/golint
  - curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.26.0
  - curl -sfL https://raw.githubusercontent.com/reviewdog/reviewdog/master/install.sh | sh -s -- -b ~/bin

script:
  - |-
    case $TRAVIS_OS_NAME in
      linux|osx)
        golint ./... | reviewdog -f=golint -reporter=github-check
        golangci-lint run --out-format=line-number -E goimports -E misspell -E godot -E lll | reviewdog -f=golangci-lint -reporter=github-check
        golint ./... | reviewdog -f=golint -reporter=github-pr-review
        golangci-lint run --out-format=line-number -E goimports -E misspell -E godot -E lll | reviewdog -f=golangci-lint -reporter=github-pr-review
        ;;
    esac
  - go test -race -coverprofile=coverage.txt -covermode=atomic -v

after_success:
  - if [[ "$TRAVIS_OS_NAME" == "windows" ]]; then curl -s https://codecov.io/bash >
    .codecov && chmod +x .codecov && ./.codecov; else bash <(curl -s https://codecov.io/bash);
    fi

env:
  global:
    - GO111MODULE=on
    - secure: ajez2Mpm5Txz1bg1eG7qxZinuyC99IUTXDzlAHHqkkPxai8Pa95Zv5v540aRz3oC+TF4Nu9r+qxkn4VIgOc6GxE8j9ySQs5QflbvH74loNlkpWkOrSsimnYaFYvJQQZjbb1P0L29L9p+bZ72ZlDAjFaEBYeFH4MwG4qotVxepLmyOghaCsXzQNt9a0ETJ9o/0gnKpdd7W2Tqz1LBEwEZrunF0z8HQtHS1/HK9YA5Anxv4S5hchkKAQtaXg6y26dDW5ZEPAd1Uw3dTv1CHn35WBeI/l2wKNq1Xfq1gl2mKzKyxWSk1sbeHOYMMICB64YakAZWv6FYPuK1qiFGe0/LYrFc+//I0T7+1zuY9ZV692TbuPB+qkNp325Ma6eadU3tD9rhaXSizLFiLch5W7V1VYORRoeNXq+SThXr3gAJ+PwLNKca++jbiFi/WotO9/3RhiBtJHWIXTUejD028C6hGV0xgt5GtWyktdBITOkWCr7vZjM0oQEeSpXqySbnRkIuBsInEDW4bUbsyXpeZf/Ftj8OaEJFKtAgJkj8uR6ErfianorEIgGfmydyInooIxViW2C4eC3IrS3EgdMCdXsg1bxC6j5p5z7Nz7eQIRYk+ClhVSrGV7XJS6N/HmQw5dpK51iAp+rLjJsRHdprGHxZeaPSIYrGuBERwb0mvncCwFk=
