package enum

type MailProcessingStatusValue string

type mailProcessingStatus struct {
	PROCESSING MailProcessingStatusValue
	REJECTED   MailProcessingStatusValue
	SENT       MailProcessingStatusValue
}

var MailProcessingStatus = mailProcessingStatus{
	PROCESSING: "PROCESSING",
	REJECTED:   "REJECTED",
	SENT:       "SENT",
}

type RejectedReasonValue string

type rejectedReason struct {
	NoAttachment       RejectedReasonValue
	DomainNotSupported RejectedReasonValue
}

var RejectedReason = rejectedReason{
	NoAttachment:       "NO_ATTACHMENT",
	DomainNotSupported: "DOMAIN_NOT_SUPPORTED",
}

type InvoiceExcludeTagValue string
type invoiceExcludeTag struct {
	FailedDelivery InvoiceExcludeTagValue
}

var InvoiceExcludeTag = &invoiceExcludeTag{
	FailedDelivery: "FAILED_DELIVERY",
}

type CustomerTaxStatusType string

type customerTaxStatus struct {
	INVALID   CustomerTaxStatusType
	VALID     CustomerTaxStatusType
	DIFF_INFO CustomerTaxStatusType
}

var CustomerTaxStatus = &customerTaxStatus{
	INVALID:   "INVALID",   // MST không hợp lệ
	VALID:     "VALID",     // MST hợp lệ
	DIFF_INFO: "DIFF_INFO", // MST hợp lệ nhưng thông tin trên CQT khác với KH nhập
}

type InvoiceStatusType string

type invoiceStatusEnv struct {
	Init       InvoiceStatusType
	Processing InvoiceStatusType
	Processed  InvoiceStatusType
	Error      InvoiceStatusType
}

var InvoiceStatus = &invoiceStatusEnv{
	Init:       "INIT",       // Đã tạo dữ liệu invoice
	Processing: "PROCESSING", // Đang xử lý: đã sync invoice sang cho đối tác & chờ lấy thông tin PDF
	Processed:  "PROCESSED",  // Đã xử lý
	Error:      "ERROR",      // Lỗi(mã số thuế không hợp lệ)
}

type InvoiceTypeEnum string

type invoiceTypeEnv struct {
	SaleInvoice InvoiceTypeEnum
}

var InvoiceType = &invoiceTypeEnv{
	SaleInvoice: "SALE_INVOICE",
}
