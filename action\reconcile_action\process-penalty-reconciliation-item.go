package reconcile_action

import (
	"errors"
	"fmt"
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

func ProcessPenaltyReconciliationItem(
	upsertData model.ReconciliationItem,
	reconciliation *model.Reconciliation,
) *common.APIResponse {
	if penaltyAlready(upsertData) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Already penalty",
			ErrorCode: "ALREADY_PENALTY",
		}
	}

	err := markAlreadyPenalty(upsertData)
	if err != nil {
		fmt.Println("Err upsertReconciliationItem, markAlreadyPenalty:", err)
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MARK_ALREADY_PENALTY_ERROR",
		}
	}

	riFilter := model.ReconciliationItem{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,

		AutoPenaltyFee: true,
		FeeType:        enum.FeeType.INBOUND_OVERDUE_PENALTY,

		Sku:           upsertData.Sku,
		ProductId:     upsertData.ProductId,
		WarehouseCode: upsertData.WarehouseCode,
	}

	fromTime := utils.FormatTimeString(reconciliation.FromTime)
	toTime := utils.FormatTimeString(reconciliation.ToTime)
	description := fmt.Sprintf(
		"Phí lưu kho quá hạn sản phẩm #%d tại kho %s đợt %s ~ %s",
		upsertData.ProductId,
		upsertData.WarehouseCode,
		fromTime, toTime,
	)

	// move fee from penalty fee to penalty bml fee
	// use case for reconcile ticket from seller/mis
	if upsertData.PenaltyFee != 0 &&
		upsertData.PenaltyBMLFee == 0 {
		upsertData.PenaltyBMLFee = upsertData.PenaltyFee
		upsertData.PenaltyFee = 0 // omitempty
	}

	result := model.ReconciliationItemDB.Upsert(riFilter, model.ReconciliationItem{
		PenaltyDescription: description,

		AvailableQuantity: upsertData.AvailableQuantity,
		PenaltyTimeline:   upsertData.PenaltyTimeline,
		PenaltyFee:        upsertData.PenaltyFee,
		PenaltyBMFee:      upsertData.PenaltyBMFee,
		PenaltyBMLFee:     upsertData.PenaltyBMLFee,
		PenaltyRate:       upsertData.PenaltyRate,

		InboundCode: upsertData.InboundCode,
		InboundTime: upsertData.InboundTime,
		DaysInbound: upsertData.DaysInbound,
		RetailPrice: upsertData.RetailPrice,
	})

	return result
}

func penaltyAlready(upsertData model.ReconciliationItem) bool {
	if upsertData.InboundCode != "" {
		iPenRes := model.ReconciliationInboundPenaltyDB.QueryOne(model.ReconciliationInboundPenalty{
			Sku:             upsertData.Sku,
			WarehouseCode:   upsertData.WarehouseCode,
			InboundCode:     upsertData.InboundCode,
			PenaltyTimeline: upsertData.PenaltyTimeline,
		})

		return iPenRes.Status == common.APIStatus.Ok
	}

	if upsertData.InboundTime == nil {
		log.Println("check penalty already nil inbound time", upsertData.Sku, upsertData.WarehouseCode)
		return true
	}

	iPenRes := model.ReconciliationInboundPenaltyDB.QueryOne(model.ReconciliationInboundPenalty{
		Sku:             upsertData.Sku,
		WarehouseCode:   upsertData.WarehouseCode,
		InboundTime:     upsertData.InboundTime,
		PenaltyTimeline: upsertData.PenaltyTimeline,
	})

	return iPenRes.Status == common.APIStatus.Ok
}

func markAlreadyPenalty(upsertData model.ReconciliationItem) error {
	res := model.ReconciliationInboundPenaltyDB.Create(model.ReconciliationInboundPenalty{
		Sku:             upsertData.Sku,
		WarehouseCode:   upsertData.WarehouseCode,
		InboundCode:     upsertData.InboundCode,
		InboundTime:     upsertData.InboundTime,
		PenaltyTimeline: upsertData.PenaltyTimeline,

		ReconcileTimeIndex: upsertData.ReconcileScheduleTimeIndex,
		AvailableQuantity:  upsertData.AvailableQuantity,
		PenaltyFee:         upsertData.PenaltyFee,
	})
	if res.Status != common.APIStatus.Ok {
		return errors.New(res.Message)
	}

	return nil
}
