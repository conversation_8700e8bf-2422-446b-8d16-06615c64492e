package worker

import (
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ProcessInvoiceFail(now *time.Time, config *schedule.Config) (err error, note string, nextRun *time.Time) {
	if now == nil {
		_t := time.Now()
		now = &_t
	}

	AlertInvoiceFail(*now)

	timeNextRun := now.Add(4 * time.Hour)
	nextRun = &timeNextRun

	return
}

func AlertInvoiceFail(now time.Time) {
	defer func() {
		if r := recover(); r != nil {
			log.Println("Panic: ", r)
		}
	}()

	fromTime := now.Add(-4 * time.Hour)
	historyDb := model.ConfigSchedule.GetHistoryDB()

	res := historyDb.Query(schedule.History{
		Topic: "SCHEDULE_ALERT_INVOICE_FAIL",
	}, 0, 1, &primitive.M{"_id": -1})

	if res.Status == common.APIStatus.Ok {
		history := res.Data.([]*schedule.History)[0]
		if history != nil && history.CreatedTime != nil {
			fromTime = *history.CreatedTime
		}
	}

	count := model.LogOrderWrongInvoiceDb.Count(bson.M{
		"created_time": bson.M{
			"$gt": fromTime,
		},
	}).Total

	if count > 0 {
		log.Println("AlertInvoiceError: ", count)
		client.Services.SellerPurchasing.AlertInvoiceError(count)
	}
}
