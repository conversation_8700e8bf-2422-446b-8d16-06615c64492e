package action

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func GetActionSource(req sdk.APIRequest) *model.ActionSource {
	var source *model.ActionSource
	sourceAttr := req.GetAttribute("X-Source")
	if sourceAttr != nil {
		source = sourceAttr.(*model.ActionSource)
		return source
	}
	sourceStr := req.GetHeader("X-Source")
	if sourceStr == "" {
		return nil
	}

	err := json.Unmarshal([]byte(sourceStr), &source)

	if err != nil {
		return nil
	}

	return source
}
