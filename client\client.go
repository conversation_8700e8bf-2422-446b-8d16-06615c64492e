package client

import (
	"fmt"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

// Promo ..
type gatewayClient struct {
	Customer *customerClient
	Invoice  *invoiceClient
	Bill     *billClient

	Seller           *sellerClient
	SellerWis        *sellerWisClient
	SellerMis        *sellerMisClient
	SellerPurchasing *sellerPurchasingClient
	MailService      *sendGridClient

	SellerReporting *sellerReportingClient

	Warehouse *warehouseClient
	Picking   *pickingClient
	Inventory *inventoryClient

	Promotion      *promotionClient
	Notification   *notificationClient
	SMS            *smsClient
	Product        *productClient
	Pricing        *pricingClient
	Ticket         *ticketClient
	Location       *locationClient
	EmailFile      *pdfClient
	Message        *MessageClient
	Reconcile      *reconcileClient
	Collector      *collectorClient
	AccountingDebt *accountingDebtClient
	ConfigManager  *configManager
	Billing        *billingCenterSvc
	BrandSales     *brandSalesClient
	Notify         *notifyClient

	OnepayClient *onepayClient

	Zns *ZnsClient

	Kiotviet *KiotvietClient
}

// Services ...
// ErrUnavailableService ...
var (
	Services              *gatewayClient
	ErrUnavailableService = fmt.Errorf("%s", "Service product unavailable")
)

// InitClient ...
func InitClient(log *mongo.Database) {
	Services = &gatewayClient{
		Customer: NewCustomerServiceClient(conf.Config.APIHost, conf.Config.APIKey, "customer_client", log),
		Invoice:  NewInvoiceServiceClient(conf.Config.APIHost, conf.Config.APIKey, "invoice_client", log),
		Bill:     NewBillServiceClient(conf.Config.APIHost, conf.Config.APIKey, "bill_client", log),

		Seller:           NewSellerServiceClient(conf.Config.APIHost, conf.Config.APIKey, "seller_client"),
		SellerWis:        NewSellerWisClient(conf.Config.APIHost, conf.Config.APIKey, "seller_wis_client", log),
		SellerMis:        NewSellerMisServiceClient(conf.Config.APIHost, conf.Config.APIKey, "seller_mis_client", log),
		SellerPurchasing: NewSellerPurchasingClient(conf.Config.APIHost, conf.Config.APIKey, "seller_purchasing_client", log),
		MailService:      NewSendGridClient(log),

		SellerReporting: NewSellerReportingClient(conf.Config.APIHost, conf.Config.APIKey, "seller_reporting_client", log),

		Warehouse: NewWarehouseServiceClient(conf.Config.APIHost, conf.Config.APIKey, "warehouse_client", log),
		Picking:   NewPickingServiceClient(conf.Config.APIHost, conf.Config.APIKey, "picking_client", log),
		Inventory: NewInventoryServiceClient(conf.Config.APIHost, conf.Config.APIKey, "inventory_client", log),

		Promotion:      NewPromotionServiceClient(conf.Config.APIHost, conf.Config.APIKey, "promotion_client", log),
		Notification:   NewNotificationServiceClient(conf.Config.APIHost, conf.Config.APIKey, "notification_client"),
		SMS:            NewSMSClient(conf.Config.APIHost, conf.Config.APIKey, "sms_client"),
		Product:        NewProductServiceClient(conf.Config.APIHost, conf.Config.APIKey, "product_client", log),
		Pricing:        NewPricingServiceClient(conf.Config.APIHost, conf.Config.APIKey, "pricing_client", log),
		Ticket:         NewTicketServiceClient(conf.Config.APIHost, conf.Config.APIKey, "ticket_cs_client", log),
		Location:       NewLocationServiceClient(conf.Config.APIHost, conf.Config.APIKey, "location_client"),
		EmailFile:      NewPdfServiceClient(conf.Config.APIHost, conf.Config.APIKey, "pdf-processor", log),
		Message:        NewMessageClient(conf.Config.APIHost, conf.Config.APIKey, "message", log),
		Reconcile:      NewReconcileServiceClient(conf.Config.APIHost, conf.Config.APIKey, "reconcile", log),
		Collector:      NewCollectorServiceClient(conf.Config.APIHost, conf.Config.APIKey, "collector", log),
		AccountingDebt: NewAccountingDebtServiceClient(conf.Config.APIHost, conf.Config.APIKey, "accounting_debt", log),
		ConfigManager:  NewConfigManagerClient(conf.Config.APIHost, conf.Config.APIKey, "config_manager", log),
		Billing:        NewBillingCenterSvcClient(conf.Config.BuymedAPIHost, conf.Config.BuymedAPIKey, conf.Config.SSOHost, conf.Config.SSOToken, "billing_center", log),

		BrandSales: NewBrandSalesServiceClient(conf.Config.APIHost, conf.Config.APIKey, "brand_sales", log),

		OnepayClient: NewOnepayClient(conf.Config.APIHost, conf.Config.APIKey, "onepay_adapter", log),

		Notify: NewNotifyClient(conf.Config.APIHost, conf.Config.APIKey, "notify_client", log),

		Zns: NewZnsClient(conf.Config.BuymedAPIHost, conf.Config.BuymedAPIKey, "zns_client", log),

		Kiotviet: NewKiotvietClient(conf.Config.KiotvietAPIHost, conf.Config.KiotvietAPIKey, "kiotviet_client", log),
	}
}
