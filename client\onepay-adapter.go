package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetPaymentLink = "/payment/onepay-adapter/v1/payment"
)

type onepayClient struct {
	client  *client.RestClient
	headers map[string]string
}

func NewOnepayClient(apiHost, apiKey, logName string, session *mongo.Database) *onepayClient {
	cli := &onepayClient{
		client: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	cli.client.SetDBLog(session)
	return cli
}

// GetPaymentLink ...
func (cli *onepayClient) GetPaymentLink(params map[string]interface{}, headers map[string]string) *common.APIResponse {

	// Call API
	cloneHeaders := map[string]string{}
	for k, v := range headers {
		cloneHeaders[k] = v
	}
	cloneHeaders["X-Forwardkey"] = conf.Config.OnepayForwardFromKey
	cli.client.AddMaskHeader("X-Forwardkey")

	res, err := cli.client.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cloneHeaders, nil, params, pathGetPaymentLink, &[]string{fmt.Sprintf("%d", params["orderId"])})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: common.APIStatus.Error,
		}
	}

	var result *model.OnepayGetListResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: common.APIStatus.Error,
		}
	}
	return &common.APIResponse{
		Status:    result.Status,
		Message:   result.Message,
		ErrorCode: result.ErrorCode,
		Data:      result.Data,
	}
}
