package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type Address struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	Code            string             `json:"code,omitempty" bson:"code,omitempty"`
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CustomerID int64 `json:"customerID,omitempty" bson:"customer_id,omitempty"`

	Name         *string  `json:"name,omitempty" bson:"name,omitempty" validate:"required"`
	Phone        *string  `json:"phone,omitempty" bson:"phone,omitempty" validate:"required,numeric,isPhone,min=10,max=11"`
	Email        *string  `json:"email,omitempty" bson:"email,omitempty" validate:"omitempty"`
	IsDefault    *bool    `json:"isDefault,omitempty" bson:"is_default,omitempty"`
	ProvinceCode *string  `json:"provinceCode,omitempty" bson:"province_code,omitempty" validate:"required"`
	DistrictCode *string  `json:"districtCode,omitempty" bson:"district_code,omitempty" validate:"required"`
	WardCode     *string  `json:"wardCode,omitempty" bson:"ward_code,omitempty" validate:"required"`
	Address      *string  `json:"address,omitempty" bson:"address,omitempty" validate:"required"`
	FullAddress  *string  `json:"fullAddress,omitempty" bson:"-"`
	Tags         []string `json:"tags,omitempty" bson:"tags,omitempty"`
}
