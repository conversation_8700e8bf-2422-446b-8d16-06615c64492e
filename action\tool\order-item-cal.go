package tool

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

func CalTotalFeeValue(item *model.OrderItem, orderStatus enum.OrderStateValue) {
	if item.Fee == nil {
		return
	}

	for _, fee := range item.Fee.Result {
		if fee == nil {
			continue
		}

		totalFeeValue := fee.FeeValue * float64(getDeliveredQty(item, orderStatus))
		fee.TotalFeeValueDisplay = &totalFeeValue
	}
}

func getDeliveredQty(item *model.OrderItem, orderStatus enum.OrderStateValue) int {
	switch orderStatus {
	case enum.OrderState.Completed:
		if item.CompletedQuantity == nil {
			return 0
		}

		return *item.CompletedQuantity
	case enum.OrderState.Delivered:
		if item.DeliveredQuantity == nil {
			return 0
		}

		return *item.DeliveredQuantity
	}

	return 0
}

func CalTotalSellerRevenueDisplay(item *model.OrderItem, orderStatus enum.OrderStateValue) {
	if item.SellerRevenueDisplay == nil {
		return
	}

	if orderStatus == enum.OrderState.Completed {
		if item.CompletedQuantity == nil {
			return
		}

		sellerRevenueDisplay := *item.SellerRevenueDisplay * *item.CompletedQuantity
		item.TotalSellerRevenueDisplay = &sellerRevenueDisplay
	} else if orderStatus == enum.OrderState.Delivered {
		if item.DeliveredQuantity == nil {
			return
		}

		sellerRevenueDisplay := *item.SellerRevenueDisplay * *item.DeliveredQuantity
		item.TotalSellerRevenueDisplay = &sellerRevenueDisplay
	}
}

func CalSellerRevenueDisplay(item *model.OrderItem) {
	pricingTime := time.Date(2021, 9, 21, 22, 15, 0, 0, time.UTC)
	if item.CreatedTime != nil && item.CreatedTime.Before(pricingTime) {
		calculateSellerRevenueDisplayBeforePricingTime(item)
	} else {
		calculateSellerRevenueDisplay(item)
	}
}

func calculateSellerRevenueDisplayBeforePricingTime(item *model.OrderItem) {
	switch getChargeDealType(item) {
	case "SELLER":
		if item.DealPricingType != nil && *item.DealPricingType == "ABSOLUTE" {
			item.SellerRevenueDisplay = utils.ParseIntToPointer(item.Price + getTotalFee(item))
		} else {
			item.SellerRevenueDisplay = utils.ParseIntToPointer(item.SellerPrice - getChargeDealOnSeller(item))
		}
	case "SELLER_MARKETPLACE":
		item.SellerRevenueDisplay = utils.ParseIntToPointer(item.SellerPrice - getChargeDealOnSeller(item))
	default:
		item.SellerRevenueDisplay = utils.ParseIntToPointer(item.SellerPrice)
	}
}

func calculateSellerRevenueDisplay(item *model.OrderItem) {
	switch getChargeDealType(item) {
	case "SELLER":
		item.SellerRevenueDisplay = utils.ParseIntToPointer(item.Price + getTotalFee(item))
		skuPriceType := item.SkuPriceType
		if skuPriceType == nil {
			skuPriceType = &enum.PriceType.FIXED_PRICE
		}

		if *skuPriceType == enum.PriceType.FIXED_REVENUE && item.DealPricingType != nil && *item.DealPricingType == "ABSOLUTE" {
			item.SellerRevenueDisplay = &item.SellerRevenue
			return
		}
		return
	case "SELLER_MARKETPLACE":
		item.SellerRevenueDisplay = utils.ParseIntToPointer(item.SellerPrice - getChargeDealOnSeller(item))
		return
	}

	if item.SkuPriceType == nil {
		return
	}

	switch *item.SkuPriceType {
	case enum.PriceType.FIXED_REVENUE:
		item.SellerRevenueDisplay = utils.ParseIntToPointer(item.SellerPrice)
	case enum.PriceType.FIXED_PRICE:
		item.SellerRevenueDisplay = utils.ParseIntToPointer(item.SellerPrice + getTotalFee(item))
	}
}

// total fee is zero or negative number
func getTotalFee(item *model.OrderItem) int {
	if item.Fee == nil {
		return 0
	}

	total := 0.0
	for _, fee := range item.Fee.Result {
		if fee == nil {
			continue
		}

		total -= fee.FeeValue
	}

	return int(total)
}

func getChargeDealOnSeller(item *model.OrderItem) int {
	switch getChargeDealType(item) {
	case "SELLER":
		return item.ChargeDealFeeValue
	case "SELLER_MARKETPLACE":
		return item.ChargeDealFeeValue / 2
	default:
		return 0
	}
}

func getChargeDealType(item *model.OrderItem) string {
	if item.Type == enum.ItemType.DEAL || item.Type == enum.ItemType.CAMPAIGN {
		if item.ChargeDealFee == "" {
			return "SELLER"
		}
		return item.ChargeDealFee
	}

	return ""
}
