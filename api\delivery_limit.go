package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func UpdateDeliveryLimitation(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.DeliveryLimitation
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateDeliveryLimitation(&input))
}

func GetDeliveryLimitation(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GetDeliveryLimitation())
}
