package client

import (
	"strconv"
	"strings"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

// CheckVoucherRequest is model define payload call promo internal
type CheckVoucherRequest struct {
	Cart                 interface{} `json:"cart"`
	VoucherCode          []*string   `json:"voucherCodes"`
	AccountID            int64       `json:"accountId"`
	Offset               int64       `json:"offset"`
	Limit                int64       `json:"limit"`
	GetTotal             bool        `json:"getTotal"`
	GetValidate          bool        `json:"getValidate"`
	Search               string      `json:"search"`
	Scope                string      `json:"scope"`
	GetVoucherAutoApply  bool        `json:"getVoucherAutoApply"` // to apply voucher auto
	GetVoucherAuto       bool        `json:"getVoucherAuto"`      // for get list only
	RedeemCodeRemovedArr []string    `json:"redeemCodeRemovedArr"`

	SystemDisplay string `json:"systemDisplay,omitempty" bson:"-"`

	Source string `json:"source,omitempty" bson:"-"` // trace

	Customer                   *model.Customer          `json:"customer"`
	SourceDetail               *model.OrderSourceDetail `json:"sourceDetail,omitempty" bson:"-"`
	KeepVoucherInCart          bool                     `json:"keepVoucherInCart,omitempty"`
	SkipVoucherByPaymentMethod bool                     `json:"skipVoucherByPaymentMethod"`
	GetVoucherAutoByPayment    bool                     `json:"getVoucherAutoByPayment"`
}

// CheckVoucherActiveRequest ...
type CheckVoucherActiveRequest struct {
	Cart        interface{}
	VoucherCode []*string `json:"voucherCode"`
	AccountID   int64     `json:"accountId"`
}

// UseVoucherRequest is model define payload call promo internal
type UseVoucherRequest struct {
	VoucherCode       string         `json:"voucherCode"`
	VoucherCodes      []*string      `json:"voucherCodes"`
	AccountID         int64          `json:"accountId"`
	OrderID           int64          `json:"orderId"`
	ApplyVoucherCount map[string]int `json:"applyVoucherCount"`
}

// UpdatePointAndOrderCountRequest ...
type UpdatePointAndOrderCountRequest struct {
	Point         *float64             `json:"point"`
	OrderCount    *int                 `json:"orderCount"`
	CustomerID    int                  `json:"customerId"`
	SaleOrderCode string               `json:"saleOrderCode"`
	OrderID       int64                `json:"orderId"`
	OrderStatus   enum.OrderStateValue `json:"orderStatus"`
	Note          string               `json:"note"`
	LastOrderAt   *time.Time           `json:"lastOrderAt,omitempty" bson:"last_order_at,omitempty"`
}

// Notification ...
type Notification struct {
	Code         string  `json:"code,omitempty" bson:"code,omitempty" `
	UserID       int64   `json:"userId,omitempty" bson:"user_id,omitempty" `
	Username     string  `json:"username,omitempty" bson:"username,omitempty"`
	ReceiverType *string `json:"receiverType,omitempty" bson:"receiver_type,omitempty"`
	IsRead       *bool   `json:"isRead,omitempty" bson:"is_read,omitempty" `
	Topic        string  `json:"topic,omitempty" bson:"topic,omitempty"`
	Title        string  `json:"title,omitempty" bson:"title,omitempty"`
	Description  string  `json:"description,omitempty" bson:"description,omitempty" `
	Link         string  `json:"link,omitempty" bson:"link,omitempty" `
	Onwer        string  `json:"owner,omitempty" bson:"owner,omitempty"`

	Tags []enum.NotificationTagEnum `json:"tags,omitempty" bson:"tags,omitempty"`
}

// LookupSKUPriceRequest ...
type LookupSKUPriceRequest struct {
	CustomerLevel string           `json:"customerLevel" validate:"required"`
	ProvinceCode  string           `json:"locationCode" validate:"required"`
	DistrictCode  string           `json:"districtCode"`
	WardCode      string           `json:"wardCode"`
	Skus          []SKUInformation `json:"skus" validate:"required,dive"`
}

// SkuInformation ...
type SKUInformation struct {
	Type                  string    `json:"type" validate:"required"`
	RetailPriceType       string    `json:"retailPriceType" validate:"required"`
	Code                  string    `json:"code" validate:"required"`
	Price                 int       `json:"price" validate:"required"`
	SalePrice             int       `json:"salePrice"`
	IsDynamicPricingLevel *bool     `json:"isDynamicPricingLevel"`
	DynamicPricingLevel   *int64    `json:"dynamicPricingLevel,omitempty"`
	PricingStrategy       *float64  `json:"pricingStrategy,omitempty"`
	FeeCodes              *[]string `json:"feeCodes,omitempty"`
	Weight                float64   `json:"weight,omitempty"`

	// combo
	UseSKUsPrice          *bool       `json:"useSKUsPrice"`
	ComboDiscountType     string      `json:"comboDiscountType"`
	ComboDiscountValue    *int64      `json:"comboDiscountValue"`
	ComboMaxDiscountValue *int64      `json:"comboMaxDiscountValue"`
	ComboSKUs             []*ComboSKU `json:"skus"`
	ChargeDealFee         string      `json:"chargeDealFee"`
}

// ComboSKU ...
type ComboSKU struct {
	Type                  string               `json:"type"`
	RetailPriceType       string               `json:"retailPriceType"`
	Status                *enum.SkuStatusValue `json:"status,omitempty"`
	Code                  string               `json:"code" validate:"required"`
	Price                 int                  `json:"price"`
	IsDynamicPricingLevel *bool                `json:"isDynamicPricingLevel"`
	DynamicPricingLevel   *int64               `json:"dynamicPricingLevel"`
	PricingStrategy       *float64             `json:"pricingStrategy,omitempty"`
	FeeCodes              *[]string            `json:"feeCodes"`
	Weight                float64              `json:"weight"`
	Quantity              int                  `json:"quantity"`
}

// UpdateDealQuantityRequest ...
type UpdateDealQuantityRequest struct {
	Code     string `json:"code"`
	Quantity int    `json:"quantity"`
	Action   string `json:"action"`
	OrderID  int64  `json:"orderID"`
}

// UpdateSkuQuantityRequest ...
type UpdateSkuQuantityRequest struct {
	ItemCode      string               `json:"itemCode"`
	StatusData    *model.SkuStatusData `json:"statusData,omitempty" bson:"status_data,omitempty"`
	Status        *enum.SkuStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	IncreQuantity *int                 `json:"increQuantity,omitempty"`
}

// SMS ...
type SMS struct {
	Code     string   `json:"code,omitempty" bson:"code,omitempty"`
	Topic    string   `json:"topic" bson:"topic,omitempty"`
	Content  string   `json:"content" bson:"content,omitempty"`
	Receiver []string `json:"receiver" bson:"receiver,omitempty"`
}

// InvoiceRequest
type InvoiceRequest struct {
	OrderID       int64      `json:"orderId,omitempty"`
	ReconcileDate *time.Time `json:"reconcileDate,omitempty"`
	OutboundDate  *time.Time `json:"outboundDate,omitempty"`
	DoCode        string     `json:"doCode,omitempty"`
}

// InvoiceRequest
type CancelOrderRequest struct {
	OrderID        int64 `json:"orderId,omitempty"`
	CancelRemainDO bool  `json:"cancelRemainDO,omitempty"`
}

// UpdateInvoiceRequest
type UpdateInvoiceRequest struct {
	OrderID        int64 `json:"orderId,omitempty"`
	RequestInvoice *bool `json:"requestInvoice,omitempty"`
}

type UpdateSoldQuantityCampaign struct {
	OrderId          int64  `json:"orderId,omitempty"`
	CustomerId       int64  `json:"customerId,omitempty"`
	CampaignCode     string `json:"campaignCode,omitempty"`
	Sku              string `json:"sku,omitempty"`
	Quantity         int64  `json:"quantity,omitempty"`
	CheckoutQuantity int64  `json:"checkoutQuantity,omitempty"`
}

type SellerRevenue struct {
	Code    string `json:"code, omitempty"`
	Revenue int    `json:"revenue, omitempty"`
}

type ReqSellerList struct {
	Offset              int
	Limit               int
	GetTotal            bool
	GetStoreInfo        bool
	GetInvoiceInfo      bool
	GetPaymentInfo      bool
	GetConfigInfo       bool
	Q                   string
	Status              string
	SellerClass         string
	SellerType          string
	SellerCode          string
	SellerCodes         []string
	SellerID            int64
	LocationCodes       string
	AccountIds          string
	Search              string
	GetTopRevenue       bool
	GetIgnoreListingSku bool
	GetSellerListingSku bool
}

func (params *ReqSellerList) ToParamsString() map[string]string {
	paramsStr := make(map[string]string)

	if params.Offset != 0 {
		paramsStr["offset"] = strconv.Itoa(params.Offset)
	}
	if params.Limit != 0 {
		paramsStr["limit"] = strconv.Itoa(params.Limit)
	}
	if params.GetTotal {
		paramsStr["getTotal"] = "true"
	}
	if params.GetStoreInfo {
		paramsStr["getStoreInfo"] = "true"
	}
	if params.GetInvoiceInfo {
		paramsStr["getInvoiceInfo"] = "true"
	}
	if params.GetPaymentInfo {
		paramsStr["getPaymentInfo"] = "true"
	}
	if params.GetConfigInfo {
		paramsStr["getConfigInfo"] = "true"
	}
	if params.Q != "" {
		paramsStr["q"] = params.Q
	}
	if params.Status != "" {
		paramsStr["status"] = params.Status
	}
	if params.SellerClass != "" {
		paramsStr["sellerClass"] = params.SellerClass
	}
	if params.SellerType != "" {
		paramsStr["sellerType"] = params.SellerType
	}
	if params.SellerCode != "" {
		paramsStr["sellerCode"] = params.SellerCode
	}
	if len(params.SellerCodes) > 0 {
		paramsStr["sellerCodes"] = strings.Join(params.SellerCodes, ",")
	}
	if params.SellerID != 0 {
		paramsStr["sellerID"] = strconv.Itoa(int(params.SellerID))
	}
	if params.LocationCodes != "" {
		paramsStr["locationCodes"] = params.LocationCodes
	}
	if params.AccountIds != "" {
		paramsStr["accountIds"] = params.AccountIds
	}
	if params.Search != "" {
		paramsStr["search"] = params.Search
	}
	if params.GetTopRevenue {
		paramsStr["getTopRevenue"] = "true"
	}
	if params.GetIgnoreListingSku {
		paramsStr["getIgnoreListingSku"] = "true"
	}
	if params.GetSellerListingSku {
		paramsStr["getSellerListingSku"] = "true"
	}

	return paramsStr
}

type CreateSaleOrderRequest struct {
	WarehouseCode           string             `json:"warehouseCode,omitempty"`
	OrderID                 int64              `json:"orderId,omitempty"`
	ReferenceCode           string             `json:"referenceCode,omitempty"`
	Type                    string             `json:"type,omitempty"`
	AdditionalTime          int64              `json:"additionalTime,omitempty"`
	OrderTime               *time.Time         `json:"orderTime,omitempty"`
	Priority                int64              `json:"priority,omitempty"`
	Price                   float64            `json:"price"`
	TotalPrice              float64            `json:"totalPrice"`
	TotalAmount             float64            `json:"totalAmount,omitempty"`
	VoucherAmount           float64            `json:"voucherAmount,omitempty"`
	PaymentDiscountAmount   float64            `json:"paymentDiscountAmount,omitempty"`
	DeliveryMethod          string             `json:"deliveryMethod,omitempty"`
	DeliveryMethodFee       float64            `json:"deliveryMethodFee,omitempty"`
	ExtraFee                float64            `json:"extraFee,omitempty"`
	CODAmount               *float64           `json:"codAmount,omitempty"`
	TotalAmountWithoutVat   float64            `json:"totalAmountWithoutVat,omitempty"`
	PartnerPaymentMethodFee int64              `json:"partnerPaymentMethodFee,omitempty"`
	VatAmount               float64            `json:"vatAmount,omitempty"`
	PaymentMethod           string             `json:"paymentMethod,omitempty"`
	CustomerInfo            *CustomerSaleOrder `json:"customer,omitempty"`
	OrderLines              []*SaleOrderItem   `json:"orderLines,omitempty"`
	OrderNote               string             `json:"orderNote,omitempty"`
	CreateSource            string             `json:"createSource,omitempty"`
	PrivateNote             string             `json:"privateNote,omitempty"`
	ProvinceCode            string             `json:"provinceCode,omitempty"`
	CustomerOrderIndex      int                `json:"customerOrderIndex,omitempty"`
	CallbackURL             string             `json:"callbackUrl,omitempty"`
	Tags                    []enum.TagValue    `json:"tags,omitempty"`
	InvoiceRequest          *bool              `json:"invoiceRequest,omitempty"`
	SourceLocation          string             `json:"sourceLocation,omitempty" bson:"source_location,omitempty"`
}

type SaleOrderItem struct {
	ProductId           int                `json:"productId,omitempty"`
	ProductCode         string             `json:"productCode,omitempty"`
	SKU                 string             `json:"sku,omitempty"`
	ProductType         string             `json:"productType,omitempty"`
	IsRedTag            bool               `json:"isRedTag"`
	IsImportant         bool               `json:"isImportant"`
	IsExternalSeller    bool               `json:"isExternalSeller"`
	ImageURL            string             `json:"imageUrl,omitempty"`
	ProductName         string             `json:"productName,omitempty"`
	SellerCode          string             `json:"sellerCode,omitempty"`
	Packaging           string             `json:"packaging,omitempty"`
	Weight              float64            `json:"weight,omitempty"`
	Quantity            float64            `json:"quantity"`
	UnitPrice           float64            `json:"unitPrice,omitempty"`
	VatAmount           float64            `json:"vatAmount,omitempty"`
	RateTax             int64              `json:"rateTax,omitempty"`
	IsFragile           bool               `json:"isFragile"`
	IsFrozen            bool               `json:"isFrozen"`
	IsNearExp           bool               `json:"isNearExp"`
	MarketplaceItemType enum.ItemTypeValue `json:"marketplaceItemType,omitempty"`
	LinkQuantity        int                `json:"linkQuantity,omitempty"`
	LinkItemQuantity    int                `json:"linkItemQuantity"`
	SubItems            []*SaleOrderItem   `json:"subItems,omitempty"`
	Tags                []string           `json:"tags,omitempty"`
	Details             []OrderDetailItem  `json:"details,omitempty"`
	PriceAfterDiscount  float64            `json:"priceAfterDiscount,omitempty"`
}

type OrderDetailItem struct {
	Lot      string `json:"lot,omitempty" bson:"lot,omitempty"`
	ExpDate  string `json:"expDate,omitempty" bson:"exp_date,omitempty"`
	Quantity int64  `json:"quantity,omitempty" bson:"quantity,omitempty"`
}

type CustomerSaleOrder struct {
	Name         string             `json:"name,omitempty" bson:"name,omitempty"`
	Code         int64              `json:"code,omitempty" bson:"code,omitempty"`
	BusinessName string             `json:"businessName,omitempty" bson:"business_name,omitempty"`
	Address      string             `json:"address,omitempty" bson:"address,omitempty"`
	Phone        string             `json:"phone,omitempty" bson:"phone,omitempty"`
	Email        string             `json:"email,omitempty" bson:"email,omitempty"`
	Ward         string             `json:"ward,omitempty" bson:"ward,omitempty"`
	WardCode     string             `json:"wardCode,omitempty" bson:"ward_code,omitempty"`
	District     string             `json:"district,omitempty" bson:"district,omitempty"`
	DistrictCode string             `json:"districtCode,omitempty" bson:"district_code,omitempty"`
	TaxCode      interface{}        `json:"taxCode,omitempty" bson:"tax_code,omitempty"`
	Province     string             `json:"province,omitempty" bson:"province,omitempty"`
	ProvinceCode string             `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	Delivery     *CustomerSaleOrder `json:"delivery,omitempty" bson:"delivery,omitempty"`
	Invoice      *CustomerSaleOrder `json:"invoice,omitempty" bson:"invoice,omitempty"`

	// shipping method
	IsDropOffAtWarehouse *bool `json:"isDropOffAtWarehouse,omitempty" bson:"is_drop_off_at_warehouse,omitempty"`
}

// AddToCartEvent dùng cho collector send event
type AddToCartEvent struct {
	Event           string            `json:"event,omitempty"`    // will be overwritten, can be blank
	Metadata        map[string]string `json:"metadata,omitempty"` // required
	ResultStatus    string            `json:"resultStatus,omitempty"`
	ResultErrorCode string            `json:"resultErrorCode,omitempty"`
	UserAgent       string            `json:"userAgent,omitempty"` // required
	CreatedTime     *time.Time        `json:"createdTime,omitempty"`
	IP              string            `json:"ip,omitempty"` // required
	AccountType     string            `json:"accountType,omitempty"`
	AccountID       int64             `json:"accountID,omitempty"`
}

type AddToCartEventMetadata struct {
	Sku             string `json:"sku,omitempty"`
	SellerID        string `json:"seller_id,omitempty"`
	SellerCode      string `json:"seller_code,omitempty"`
	ProductID       string `json:"product_id,omitempty"`
	Source          string `json:"source,omitempty"`
	Screen          string `json:"screen,omitempty"`
	Host            string `json:"host,omitempty"`
	SearchIndex     string `json:"search_index,omitempty"`
	SearchPageIndex string `json:"search_page_index,omitempty"`
	RecommendSKUs   string `json:"recommend_skus,omitempty"`
	Metric          string `json:"metric,omitempty"`
	BlockCode       string `json:"block_code,omitempty"`
}

// RemoveFromCartEvent dùng cho collector send event
type RemoveFromCartEvent struct {
	Event           string            `json:"event,omitempty"`    // will be overwritten, can be blank
	Metadata        map[string]string `json:"metadata,omitempty"` // required
	ResultStatus    string            `json:"resultStatus,omitempty"`
	ResultErrorCode string            `json:"resultErrorCode,omitempty"`
	UserAgent       string            `json:"userAgent,omitempty"` // required
	CreatedTime     *time.Time        `json:"createdTime,omitempty"`
	IP              string            `json:"ip,omitempty"` // required
	AccountType     string            `json:"accountType,omitempty"`
	AccountID       int64             `json:"accountID,omitempty"`
}

type DeliveryInformation struct {
	Name         *string `json:"name,omitempty"`
	BusinessName *string `json:"businessName,omitempty"`
	Address      *string `json:"address,omitempty" `
	Phone        *string `json:"phone,omitempty"`
	WardCode     *string `json:"wardCode,omitempty"`
	DistrictCode *string `json:"districtCode,omitempty"`
	ProvinceCode *string `json:"provinceCode,omitempty"`
}

type UpdateDeliveryInfoRequest struct {
	SaleOrderCode string               `json:"saleOrderCode"`
	PaymentMethod *enum.MethodValue    `json:"paymentMethod,omitempty"`
	DeliveryInfo  *DeliveryInformation `json:"deliveryInfo,omitempty"`
}

type CheckoutOrderInput struct {
	CustomerID  int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	OrderCode   string `json:"orderCode,omitempty" bson:"order_code,omitempty"`
	OrderID     int64  `json:"orderId,omitempty" bson:"order_id,omitempty"`
	OrderAmount int64  `json:"orderAmount,omitempty" bson:"order_amount,omitempty"`
	IsVerify    bool   `json:"isVerify,omitempty" bson:"is_verify,omitempty"`
	Action      string `json:"action,omitempty" bson:"-"`
	RequestID   int64  `json:"requestId,omitempty"`
}

type RefundOrderInput struct {
	CustomerID       int64                          `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	OrderID          int64                          `json:"orderId,omitempty" bson:"order_id,omitempty"`
	Type             enum.RefundOrderInputTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	TotalOrderAmount int64                          `json:"totalOrderAmount,omitempty" bson:"total_order_amount,omitempty"` // tổng số tiền đơn hàng
	IsVerify         bool                           `json:"isVerify,omitempty" bson:"is_verify,omitempty"`
	RequestID        int64                          `json:"requestId,omitempty"`
}

type WarehouseUpdateInvoiceRequest struct {
	AdminID        int64  `json:"adminId,omitempty"`
	SaleOrderCode  string `json:"saleOrderCode,omitempty"`
	InvoiceRequest *bool  `json:"invoiceRequest,omitempty"`
}

type Notify struct {
	CustomerID int64 `json:"customerID,omitempty" bson:"customer_id,omitempty"`

	Topic    string   `json:"topic,omitempty" bson:"topic,omitempty"`
	Source   string   `json:"source,omitempty" bson:"source,omitempty"`
	Receiver []string `json:"receiver,omitempty" bson:"-"`

	Title    string `json:"title,omitempty" bson:"title,omitempty"`
	Content  string `json:"content,omitempty" bson:"content,omitempty"`
	ImageUrl string `json:"imageUrl,omitempty" bson:"image_url,omitempty"`
	DeepLink string `json:"deepLink,omitempty" bson:"deep_link,omitempty"`
}

type NotificationPartner struct {
	NotificationPartnerID int64      `json:"notificationPartnerID,omitempty" bson:"notification_partner_id,omitempty"`
	CreatedTime           *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime       *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// customerID
	CustomerID int64 `json:"customerID,omitempty" bson:"customer_id,omitempty"`

	// notification partner
	NotificationType string `json:"notificationType,omitempty" bson:"notification_type,omitempty"`
	Token            string `json:"token,omitempty" bson:"token,omitempty"`
	Status           string `json:"status,omitempty" bson:"status,omitempty"`
	DeviceID         string `json:"deviceID,omitempty" bson:"device_id,omitempty"`
}

type GetLatestPutTicketReq struct {
	WarehouseCode string `json:"warehouseCode,omitempty"`
	Sku           string `json:"sku,omitempty"`
}

type CreateVoucherReuseOnOrderCancelReq struct {
	RedeemCodes *[]*string `json:"redeemCodes,omitempty"`
	AccountID   int64      `json:"accountId,omitempty"`
	CustomerID  int64      `json:"customerId,omitempty"`
	OrderID     int64      `json:"orderId,omitempty"`
}

// Hilo Invoice
type ExportInvoiceRequest struct {
	InvoiceTemplateCode string `json:"invoiceTemplateCode,omitempty"`

	RequestDraft bool `json:"requestDraft,omitempty"`

	OrderID       int    `json:"orderId,omitempty"`
	OrderCode     string `json:"orderCode,omitempty"`
	SaleOrderCode string `json:"saleOrderCode,omitempty"`
	DoCode        string `json:"doCode,omitempty"`

	OrgId        int    `json:"orgId,omitempty"`
	OriginSource string `json:"originSource,omitempty"`
	Source       string `json:"source,omitempty"`
	CompanyCode  string `json:"companyCode,omitempty"`
	Provider     string `json:"provider,omitempty"`

	PaymentMethod    string `json:"paymentMethod,omitempty"`
	AmountWithoutVAT int    `json:"amountWithoutVat,omitempty"`
	Amount           int    `json:"amount,omitempty"`

	PurchaserName       string `json:"purchaserName,omitempty"`       // Họ tên người mua hàng
	SellerPhoneNumber   string `json:"sellerPhoneNumber,omitempty"`   // Số điện thoại người bán hàng
	SellerFax           string `json:"sellerFax,omitempty"`           // Số fax người bán hàng
	SellerBankName      string `json:"sellerBankName,omitempty"`      // Tên ngân hàng người bán hàng
	SellerAccountNumber string `json:"sellerAccountNumber,omitempty"` // Số tài khoản người bán hàng
	SellerCode          string `json:"sellerCode,omitempty"`          // Mã Seller
	SellerEmail         string `json:"sellerEmail,omitempty"`         // Email người bán hàng

	BuyerTaxCode                   string `json:"buyerTaxCode,omitempty"`                   // MST người mua hàng
	BuyerPhone                     string `json:"buyerPhone,omitempty"`                     // SDT người mua hàng
	BuyerAddress                   string `json:"buyerAddress,omitempty"`                   // Địa chỉ người mua hàng
	BuyerEmail                     string `json:"buyerEmail,omitempty"`                     // Email người mua hàng
	BuyerName                      string `json:"buyerName,omitempty"`                      // Tên người mua hàng
	BuyerRequest                   bool   `json:"buyerRequest,omitempty"`                   // Người mua hàng yêu cầu xuất hóa đơn
	BuyerRequestWhenInvalidTaxCode bool   `json:"buyerRequestWhenInvalidTaxCode,omitempty"` // Người mua hàng yêu cầu xuất hóa đơn khi mã số thuế không hợp lệ
	BuyerNameWhenInvalidTaxCode    string `json:"buyerNameWhenInvalidTaxCode,omitempty"`    // Tên người mua hàng khi mã số thuế không hợp lệ

	OutboundTime *time.Time `json:"outboundTime,omitempty"` // Thời gian xuất kho
	BoughtTime   *time.Time `json:"boughtTime,omitempty"`   // Thời gian mua hàng

	Details []*ExportInvoiceItem `json:"details,omitempty"`
}

type ExportInvoiceItem struct {
	Characteristic   int     `json:"characteristic,omitempty"`
	Quantity         int     `json:"quantity,omitempty"`
	Price            int     `json:"price,omitempty"`
	Amount           int     `json:"amount,omitempty"`
	VATAmount        int     `json:"vatAmount,omitempty"`
	PriceWithoutVAT  int     `json:"priceWithoutVat,omitempty"`
	AmountWithoutVAT int     `json:"amountWithoutVat,omitempty"`
	VAT              float64 `json:"vat,omitempty"`

	ProductID     int64  `json:"productId,omitempty"`
	ProductCode   string `json:"productCode,omitempty"`
	ProductName   string `json:"productName,omitempty"`
	SKUCode       string `json:"skuCode,omitempty"`
	OrderItemType string `json:"orderItemType,omitempty"`
	Unit          string `json:"unit,omitempty"`
	Lot           string `json:"lot,omitempty"`
	ExpiryDate    string `json:"expiryDate,omitempty"`
}

type QueryOption struct {
	Total     bool `json:"total,omitempty"`
	Items     bool `json:"items,omitempty"`
	Translate bool `json:"translate,omitempty"`
}

type ReqListInvoice struct {
	Offset int64           `json:"offset,omitempty"`
	Limit  int64           `json:"limit,omitempty"`
	Query  *BillingInvoice `json:"q,omitempty"`
	Option QueryOption     `json:"option,omitempty"`
}
