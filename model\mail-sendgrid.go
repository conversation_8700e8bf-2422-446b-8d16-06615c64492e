package model

type ReqSendMail struct {
	Personalizations []Personalization `json:"personalizations"`
	From             Email             `json:"from"`
	ReplyTo          Email             `json:"reply_to"`
	Content          []Content         `json:"content"`
}

type Personalization struct {
	To      []Email `json:"to"`
	Cc      []Email `json:"cc"`
	Bcc     []Email `json:"bcc"`
	Subject string  `json:"subject"`
}

type Email struct {
	Email string `json:"email"`
	Name  string `json:"name"`
}

type Content struct {
	Type  string `json:"type"`
	Value string `json:"value"`
}

type ResSendMail struct {
	Errors []SgError `json:"errors"`
}

type SgError struct {
	Message string      `json:"message"`
	Field   interface{} `json:"field"`
	Help    interface{} `json:"help"`
}
