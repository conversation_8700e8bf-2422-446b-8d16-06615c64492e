package action

import (
	"math"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func BuildTreeLocation(provinces []*model.ProvinceInfo) *Tree {
	root := &Tree{
		Children: make(map[string]*Tree),
		Code:     "00",
		Level:    country,
	}

	for _, provinceInfo := range provinces {
		provinceTree := &Tree{
			Children: make(map[string]*Tree),
			Code:     provinceInfo.ProvinceCode,
			Level:    province,
		}

		if provinceInfo.Districts == nil {
			root.Children[provinceInfo.ProvinceCode] = provinceTree
			continue
		}

		for _, districtInfo := range *provinceInfo.Districts {
			districtTree := &Tree{
				Children: make(map[string]*Tree),
				Code:     districtInfo.DistrictCode,
				Level:    district,
			}

			if districtInfo.WardCodes == nil {
				provinceTree.Children[districtInfo.DistrictCode] = districtTree
				continue
			}

			for _, wardCode := range *districtInfo.WardCodes {
				wardTree := &Tree{
					Children: make(map[string]*Tree),
					Code:     wardCode,
					Level:    ward,
				}

				districtTree.Children[wardCode] = wardTree
			}

			provinceTree.Children[districtInfo.DistrictCode] = districtTree
		}

		root.Children[provinceInfo.ProvinceCode] = provinceTree
	}

	return root
}

type countryLevel int64

const (
	country countryLevel = iota
	province
	district
	ward
)

type Tree struct {
	Children map[string]*Tree
	Code     string
	Level    countryLevel
}

// Depth First Search a tree but only add the deepest leaf to result or stop at a level specified
func DFSTree(root *Tree, levelOpt ...countryLevel) (codes []string) {
	if root == nil {
		return nil
	}

	level := countryLevel(math.MaxInt64)
	if len(levelOpt) > 0 {
		level = (levelOpt[0])
	}

	for _, child := range root.Children {
		codes = append(codes, dfsTree(child, level)...)
	}

	return codes
}

func dfsTree(t *Tree, stopLevel countryLevel) (codes []string) {
	if t == nil {
		return nil
	}
	if t.Level == stopLevel {
		return []string{t.Code}
	}

	if len(t.Children) == 0 || t.Children == nil {
		return []string{t.Code}
	}

	for _, child := range t.Children {
		codes = append(codes, dfsTree(child, stopLevel)...)
	}

	return codes
}
