package enum

type NotificationTypeValue string

type notificationType struct {
	INVOICE_DEADLINE        NotificationTypeValue
	SKU_FIRST_ORDER         NotificationTypeValue
	RECONCILIATION_COMPLETE NotificationTypeValue
}

var NotificationType = &notificationType{
	INVOICE_DEADLINE:        "INVOICE_DEADLINE",
	SKU_FIRST_ORDER:         "SKU_FIRST_ORDER",
	RECONCILIATION_COMPLETE: "RECONCILIATION_COMPLETE",
}

type NotificationTagEnum string
type notificationTag struct {
	ACCOUNT   NotificationTagEnum
	ORDER     NotificationTagEnum
	PRICE     NotificationTagEnum
	PRODUCT   NotificationTagEnum
	TICKET    NotificationTagEnum
	PROMOTION NotificationTagEnum
	IMPORTANT NotificationTagEnum

	// seller tags
	MARKETING     NotificationTagEnum
	ORDER_INVOICE NotificationTagEnum
	INBOUND       NotificationTagEnum
	WITHDRAW      NotificationTagEnum
	WAREHOUSE     NotificationTagEnum
	STORE         NotificationTagEnum
}

var NotificationTag = &notificationTag{
	ACCOUNT:   "ACCOUNT",
	ORDER:     "ORDER",
	PRICE:     "PRICE",
	PRODUCT:   "PRODUCT",
	TICKET:    "TICKET",
	PROMOTION: "PROMOTION",
	IMPORTANT: "IMPORTANT",

	MARKETING:     "MARKETING",
	ORDER_INVOICE: "ORDER_INVOICE",
	INBOUND:       "INBOUND",
	WITHDRAW:      "WITHDRAW",
	WAREHOUSE:     "WAREHOUSE",
	STORE:         "STORE",
}
