package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type CartCache struct {
	CartID int64  `json:"cartId,omitempty" bson:"cart_id,omitempty"` //
	CartNo string `json:"cartNo,omitempty" bson:"cart_no,omitempty"` //

	// customer info
	AccountID               int64   `json:"accountId,omitempty" bson:"account_id,omitempty"`                              // mã tài khoản
	CustomerID              int64   `json:"customerId,omitempty" bson:"customer_id,omitempty"`                            //
	CustomerCode            string  `json:"customerCode,omitempty" bson:"customer_code,omitempty"`                        // mã khách hàng
	CustomerName            string  `json:"customerName,omitempty" bson:"customer_name,omitempty"`                        // tên người nhận
	CustomerPhone           string  `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`                      // điện thoại người nhận
	CustomerEmail           *string `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`                      // email người nhận
	CustomerShippingAddress string  `json:"customerShippingAddress,omitempty" bson:"customer_shipping_address,omitempty"` // địa chỉ người nhận
	CustomerDistrictCode    string  `json:"customerDistrictCode,omitempty" bson:"customer_district_code,omitempty"`       // khu vực nhận
	CustomerWardCode        string  `json:"customerWardCode,omitempty" bson:"customer_ward_code,omitempty"`               //
	CustomerProvinceCode    string  `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`       //
	CustomerAddressCode     string  `json:"customerAddressCode,omitempty" bson:"customer_address_code,omitempty"`         //
	CustomerRegionCode      string  `json:"customerRegionCode,omitempty" bson:"customer_region_code,omitempty"`           //
	CustomerScope           string  `json:"customerScope,omitempty" bson:"customer_scope,omitempty"`                      //

	FlattenLocation []string `json:"flattenLocation,omitempty" bson:"flatten_location,omitempty"`
	WardCode        string   `json:"wardCode" bson:"ward_code,omitempty"`                          //
	DistrictCode    string   `json:"districtCode" bson:"district_code,omitempty"`                  //
	ProvinceCode    string   `json:"provinceCode,omitempty" bson:"province_code,omitempty"`        //
	RegionCode      string   `json:"regionCode,omitempty" bson:"region_code,omitempty"`            //
	RegionCodes     []string `json:"regionCodes,omitempty" bson:"region_codes,omitempty"`          //
	SaleRegionCodes []string `json:"saleRegionCodes,omitempty" bson:"sale_region_codes,omitempty"` //

	// fee
	// payment fee
	PaymentMethod           string   `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`                      // phương thức thanh toán cod/chuyển khoản
	PaymentMethodFee        int64    `json:"paymentMethodFee,omitempty" bson:"payment_method_fee,omitempty"`               // phí phương thức thanh toán cod/chuyển khoản
	PaymentMethodPercentage *float64 `json:"paymentMethodPercentage,omitempty" bson:"payment_method_percentage,omitempty"` // phần trăm giảm giá cho hình thức thanh toán

	PartnerPaymentMethod *PartnerPaymentMethod `json:"partnerPaymentMethod,omitempty" bson:"partner_payment_method,omitempty"` // Phương thức thanh toán qua cổng thanh toán online
	// delivery fee
	DeliveryMethod    string `json:"deliveryMethod,omitempty" bson:"delivery_method,omitempty"`        // hình thức giao hàng
	DeliveryMethodFee int64  `json:"deliveryMethodFee,omitempty" bson:"delivery_method_fee,omitempty"` // phí hình thức giao hàng
	ExtraFee          int64  `json:"extraFee,omitempty" bson:"extra_fee,omitempty"`                    // phụ phí
	ExtraFeeNote      string `json:"extraFeeNote,omitempty" bson:"extra_fee_note,omitempty"`           // phụ phí ghi chú

	// delivery date and note
	Note *string `json:"note,omitempty" bson:"note,omitempty"` // ghi chú giỏ hàng

	// promotion
	RedeemCode        *[]*string          `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"` // mã giảm giá
	RedeemCodeMap     []*RedeemApply      `json:"redeemCodeMap,omitempty" bson:"redeem_code_map,omitempty"`
	RedeemApplyResult []*PromoApplyResult `json:"redeemApplyResult,omitempty" bson:"redeem_apply_result,omitempty"` //
	RedeemCodeRemoved []string            `json:"redeemCodeRemoved,omitempty" bson:"redeem_code_removed,omitempty"`

	// price
	TotalPrice                        int `json:"totalPrice,omitempty" bson:"total_price,omitempty"` // tổng tiền giỏ hàng sau cùng
	Price                             int `json:"price,omitempty" bson:"price,omitempty"`            // tổng tiển chưa trừ các khoản khác
	SubPrice                          int `json:"subPrice,omitempty" bson:"sub_price,omitempty"`     // tổng tiển sau khi discount = price - discount
	TotalFee                          int `json:"totalFee,omitempty" bson:"total_fee,omitempty"`     //
	Discount                          int `json:"discount,omitempty" bson:"discount,omitempty"`      //
	DiscountRemain                    int `json:"discountRemain,omitempty" bson:"discount_remain,omitempty"`
	TotalPriceAllItem                 int `json:"totalPriceAllItem,omitempty" bson:"total_price_all_item,omitempty"`                                   // tổng tiền tất cả sản phẩm
	TotalPriceBeforePartnerPaymentFee int `json:"totalPriceBeforePartnerPaymentFee,omitempty" bson:"total_price_before_partner_payment_fee,omitempty"` // tổng tiền trước khi trừ phí thanh toán online

	IsRefuseSplitOrder *bool `json:"isRefuseSplitOrder,omitempty" bson:"is_refuse_split_order,omitempty"`

	// status
	Status enum.CartStateValue `json:"status,omitempty" bson:"status,omitempty"` //
	Source *enum.SourceValue   `json:"source,omitempty" bson:"source,omitempty"` //

	Invoice *InvoiceRequest `json:"invoice,omitempty" bson:"invoice,omitempty"`

	// items
	Items []*CartItemCache `json:"cartItems,omitempty" bson:"-"` //

	GetVoucherAutoApply          bool     `json:"-" bson:"-"`
	RedeemCodeRemovedArr         []string `json:"-" bson:"-"`
	AutoRemoveVoucherAutoInvalid bool     `json:"-" bson:"-"`
	Screen                       string   `json:"-" bson:"-"`

	SourceDetail *OrderSourceDetail `json:"sourceDetail,omitempty" bson:"source_detail,omitempty"`

	SystemDisplay string `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`

	//statistic
	TotalItem             *int    `json:"totalItem,omitempty" bson:"total_item,omitempty"`
	TotalQuantity         *int    `json:"totalQuantity,omitempty" bson:"total_quantity,omitempty"`
	TotalQuantitySelected *int    `json:"totalQuantitySelected,omitempty" bson:"total_quantity_selected,omitempty"`
	TotalItemSelected     *int    `json:"totalItemSelected,omitempty" bson:"total_item_selected,omitempty"`
	TotalWeight           float64 `json:"totalWeight,omitempty" bson:"total_weight,omitempty"`
	TotalVolume           float64 `json:"totalVolume,omitempty" bson:"total_volume,omitempty"`
	MaxVolume             float64 `json:"maxVolume,omitempty" bson:"max_volume,omitempty"`
	MaxWidth              float64 `json:"maxWidth,omitempty" bson:"max_width,omitempty"`
	MaxHeight             float64 `json:"maxHeight,omitempty" bson:"max_height,omitempty"`
	MaxLength             float64 `json:"maxLength,omitempty" bson:"max_length,omitempty"`
	MaxWeight             float64 `json:"maxWeight,omitempty" bson:"max_weight,omitempty"`

	ErrorCode    *string `json:"errorCode,omitempty" bson:"error_code,omitempty"`
	ErrorMessage *string `json:"errorMessage,omitempty" bson:"error_message,omitempty"`

	// invoice
	CanExportInvoice *bool `json:"canExportInvoice,omitempty" bson:"can_export_invoice,omitempty"`

	/// used only for query, not for save data
	ComplexQuery      []*bson.M  `json:"-" bson:"$and,omitempty"`
	PriceFrom         *int       `json:"priceFrom,omitempty" bson:"-"`
	PriceTo           *int       `json:"priceTo,omitempty" bson:"-"`
	DateFrom          *time.Time `json:"timeFrom,omitempty" bson:"-"`
	DateTo            *time.Time `json:"timeTo,omitempty" bson:"-"`
	TotalQuantityFrom *int       `json:"totalQuantityFrom,omitempty" bson:"-"`
	TotalQuantityTo   *int       `json:"totalQuantityTo,omitempty" bson:"-"`
	TotalItemFrom     *int       `json:"totalItemFrom,omitempty" bson:"-"`
	TotalItemTo       *int       `json:"totalItemTo,omitempty" bson:"-"`
	ValidateOrder     *bool      `json:"validateOrder,omitempty" bson:"-"`

	// brand
	Key                string       `json:"key,omitempty" bson:"key,omitempty"`
	CreatedByAccountID int64        `json:"createdByAccountId,omitempty" bson:"created_by_account_id,omitempty"`
	BrandCode          string       `json:"brandCode,omitempty" bson:"brand_code,omitempty"`
	BrandGifts         *[]BrandGift `json:"brandGifts,omitempty" bson:"brand_gifts,omitempty"`
	BrandNames         []string     `json:"brandNames,omitempty" bson:"brand_names,omitempty"`
	SalesType          string       `json:"salesType,omitempty" bson:"sales_type,omitempty"`
	SalesTypeCode      string       `json:"salesTypeCode,omitempty" bson:"sales_type_code,omitempty"`
}

// CartItemCache ...
type CartItemCache struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// Last time add to cart use for sorting by the last time user add item to cart
	LastAdded *time.Time `json:"lastAdded,omitempty" bson:"last_added,omitempty"`

	// reference data
	RefCart int64  `json:"-" bson:"ref_cart,omitempty"`
	CartID  int64  `json:"cartId,omitempty" bson:"cart_id,omitempty"`
	CartNo  string `json:"cartNo,omitempty" bson:"cart_no,omitempty"`

	Sku         string             `json:"sku,omitempty" bson:"sku,omitempty"`
	ItemCode    string             `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	Quantity    int                `json:"quantity,omitempty" bson:"quantity,omitempty"`
	MaxQuantity int                `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`
	IsImportant *bool              `json:"isImportant,omitempty" bson:"is_important,omitempty"`
	Type        enum.ItemTypeValue `json:"type,omitempty" bson:"type,omitempty"`

	Price      int                `json:"price,omitempty" bson:"price,omitempty"`                // save order item & view web
	CurPrice   int                `json:"currentPrice,omitempty" bson:"current_price,omitempty"` // save order item & view web
	OldType    enum.ItemTypeValue `json:"-" bson:"old_type,omitempty"`                           //
	OldLevel   string             `json:"-" bson:"old_level,omitempty"`                          //
	IsFirstAdd bool               `json:"-" bson:"isFirstAdd,omitempty"`                         // save order item & view web
	SalePrice  int                `json:"salePrice,omitempty" bson:"sale_price,omitempty"`       // save order item & view web
	Weight     float64            `json:"weight,omitempty" bson:"weight,omitempty"`
	Volume     float64            `json:"volume,omitempty" bson:"volume,omitempty"`

	TotalPrice   int                  `json:"total,omitempty" bson:"total,omitempty"`                 // save order item & view web
	IsAvailable  bool                 `json:"isAvailable,omitempty" bson:"is_available,omitempty"`    // view web
	SkuStatus    *enum.SkuStatusValue `json:"skuStatus,omitempty" bson:"sku_status,omitempty"`        // view web
	SkuPriceType *enum.PriceTypeValue `json:"skuPriceType,omitempty" bson:"sku_price_type,omitempty"` // save order item
	SkuVersion   string               `json:"skuVersion,omitempty" bson:"sku_version,omitempty"`
	Owner        string               `json:"owner,omitempty" bson:"owner,omitempty"`
	SkuLevel     *enum.LevelSKUValue  `json:"skuLevel,omitempty" bson:"sku_level,omitempty"`
	TotalWeight  float64              `json:"totalWeight,omitempty" bson:"total_weight,omitempty"`
	TotalVolume  float64              `json:"totalVolume,omitempty" bson:"total_volume,omitempty"`

	CampaignCode        string `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`                // save order item
	CampaignProductCode string `json:"campaignProductCode,omitempty" bson:"campaign_product_code,omitempty"` // save order item
	SellerCode          string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`                    // save order item
	ProductCode         string `json:"productCode,omitempty" bson:"product_code,omitempty"`                  // save order item
	ProductID           int64  `json:"productID,omitempty" bson:"product_id,omitempty"`                      // save order item
	ProductName         string `json:"productName,omitempty" bson:"product_name,omitempty"`                  // save order item
	StoreCode           string `json:"storeCode,omitempty" bson:"store_code,omitempty"`                      // save order item

	ChargeDealFeeValue     int `json:"chargeDealFeeValue,omitempty" bson:"charge_deal_fee_value,omitempty"`         // số tiền được giảm khi mua deal
	ChargeCampaignFeeValue int `json:"chargeCampaignFeeValue,omitempty" bson:"charge_campaign_fee_value,omitempty"` // số tiền được giảm khi mua campaign

	// source
	Source enum.SourceValue `json:"source,omitempty" bson:"source,omitempty"`

	Page      string `json:"page,omitempty" bson:"page,omitempty"`
	SearchKey string `json:"searchKey,omitempty" bson:"search_key,omitempty"`

	ErrorCode      *string `json:"errorCode,omitempty" bson:"error_code,omitempty"`
	WarningCode    *string `json:"warningCode,omitempty" bson:"warning_code,omitempty"`
	WarningMessage *string `json:"warningMessage,omitempty" bson:"warning_message,omitempty"`
	ErrorMessage   *string `json:"errorMessage,omitempty" bson:"error_message,omitempty"`

	// tags: use json tag: productTags for api check voucher
	Tags         []string  `json:"productTags,omitempty" bson:"product_tags,omitempty"`
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`

	Point                 int64          `json:"point,omitempty" bson:"point,omitempty"`
	PointMultiplier       int64          `json:"pointMultiplier,omitempty" bson:"point_multiplier,omitempty"`
	SkuContractCode       *string        `json:"skuContractCode,omitempty" bson:"sku_contract_code,omitempty"`
	SkuContractDetailCode *string        `json:"skuContractDetailCode,omitempty" bson:"sku_contract_detail_code,omitempty"`
	IsSelected            *bool          `json:"isSelected,omitempty" bson:"is_selected,omitempty"`
	VoucherCode           string         `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	LotDates              *[]LotDates    `json:"lotDates,omitempty" bson:"lot_dates,omitempty"`
	IsNearExpired         bool           `json:"isNearExpired,omitempty" bson:"is_near_expired,omitempty"`
	EventSource           string         `json:"eventSource,omitempty" bson:"event_source,omitempty"`
	EventScreen           string         `json:"eventScreen,omitempty" bson:"event_screen,omitempty"`
	Notification          map[string]int `json:"notification,omitempty" bson:"notification,omitempty"`

	Unit    string `json:"unit,omitempty" bson:"unit,omitempty"`        // order unit
	SkuUnit string `json:"skuUnit,omitempty" bson:"sku_unit,omitempty"` // standard unit

	//Brand
	LimitQuantityPerMonth int    `json:"limitQuantityPerMonth" bson:"limit_quantity_per_month,omitempty"`
	QtyOrdered            int    `json:"qtyOrdered,omitempty" bson:"qty_ordered,omitempty"`
	ManufacturerCode      string `json:"manufacturerCode,omitempty" bson:"manufacturer_code,omitempty"`

	// discount detail
	DiscountDetail *DiscountDetail `json:"discountDetail,omitempty" bson:"-"`
	SellerClass    string          `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`

	ProductData *ProductData      `json:"productData,omitempty" bson:"product_data,omitempty"`
	SubItems    *[]*CartItemCache `json:"subItems,omitempty" bson:"sub_items,omitempty"` // save order item
}

type ProductData struct {
	SKU                    *Sku             `json:"sku,omitempty"`
	Deal                   *Deal            `json:"deal,omitempty"`
	Product                *Product         `json:"product,omitempty"`
	Campaign               *ProductCampaign `json:"campaign,omitempty"`
	IsAvailable            bool             `json:"isAvailable,omitempty"`
	Owner                  string           `json:"owner,omitempty"`
	QuantityPurchasedToday int64            `json:"quantityPurchasedToday"`
}

// Sku ...
type Sku struct {
	// basic info
	SKU         string               `json:"sku,omitempty" bson:"sku,omitempty"` // sku code
	Code        string               `json:"code,omitempty"`
	ItemCode    string               `json:"itemCode,omitempty"`
	ProductCode string               `json:"productCode,omitempty"`
	ProductID   int64                `json:"productID"`
	SellerCode  string               `json:"sellerCode,omitempty"`
	Type        *enum.SkuTypeValue   `json:"type,omitempty"`
	Status      *enum.SkuStatusValue `json:"status,omitempty"`
	StatusData  *SkuStatusData       `json:"statusData,omitempty"`
	IsActive    *bool                `json:"isActive,omitempty"`

	// configuration
	DealCode              string               `json:"dealCode,omitempty"`
	MaxQuantityPerOrder   int64                `json:"maxQuantityPerOrder,omitempty"`
	RetailPriceType       *enum.PriceTypeValue `json:"retailPriceType,omitempty"`
	RetailPriceValue      int64                `json:"retailPriceValue,omitempty"`
	VAT                   *float64             `json:"vat,omitempty"`
	IsDynamicPricingLevel *bool                `json:"isDynamicPricingLevel,omitempty"`
	DynamicPricingLevel   *int64               `json:"dynamicPricingLevel,omitempty"`
	PricingStrategy       *float64             `json:"pricingStrategy,omitempty"`
	FeeCodes              *[]string            `json:"feeCodes,omitempty"`
	SKUs                  *[]*SubSku           `json:"skus,omitempty"`
	Tags                  []string             `json:"tags,omitempty"`
	NoneVat               *bool                `json:"noneVat,omitempty"`
	Version               string               `json:"version,omitempty"`
	Level                 enum.LevelSKUValue   `json:"level,omitempty" bson:"level,omitempty"`
	LevelSpecial          *LevelSpecial        `json:"levelSpecial,omitempty" bson:"level_special,omitempty"`

	// combo
	IsCombo *bool `json:"isCombo,omitempty"`
	// combo price
	UseSKUsPrice          *bool       `json:"useSKUsPrice,omitempty"`
	ComboDiscountType     string      `json:"comboDiscountType,omitempty"`
	ComboDiscountValue    *int64      `json:"comboDiscountValue,omitempty"`
	ComboMaxDiscountValue *int64      `json:"comboMaxDiscountValue,omitempty"`
	Point                 int64       `json:"point" bson:"point"`
	PointMultiplier       int64       `json:"pointMultiplier" bson:"point_multiplier"`
	SkuContractCode       string      `json:"skuContract,omitempty" bson:"-"`
	SkuContractDetailCode string      `json:"skuContractDetailCode,omitempty" bson:"-"`
	LotDates              *[]LotDates `json:"lotDates,omitempty" bson:"lot_dates,omitempty"`

	// location
	LocationCodes *[]string `json:"locationCodes,omitempty" bson:"location_codes,omitempty"`

	ApprovedDate *time.Time `json:"approvedDate,omitempty" bson:"approved_date,omitempty"` // the time that sku was approved to sell on the app
	CampaignCode *string    `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`

	RequiredCertificates []string `json:"requiredCertificates,omitempty" bson:"-"`

	IsTrading   *bool    `json:"isTrading,omitempty" bson:"is_trading,omitempty"`
	SellerClass string   `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`
	Slug        string   `json:"slug,omitempty" bson:"slug,omitempty"`
	ImageUrls   []string `json:"imageUrls,omitempty" bson:"image_urls,omitempty"`
}

type LevelSpecial struct {
	Level               *enum.LevelSKUValue `json:"level,omitempty" bson:"level,omitempty"`
	TimeStartApplyLevel *time.Time          `json:"timeStartApplyLevel,omitempty" bson:"time_start_apply_level,omitempty"`
	TimeEndApplyLevel   *time.Time          `json:"timeEndApplyLevel,omitempty" bson:"time_end_apply_level,omitempty"`
	TimeApplyLevel      *int                `json:"timeApplyLevel,omitempty" bson:"time_apply_level,omitempty"`
}

// Deal is model define struct Deal
type Deal struct {
	Code     string                `json:"code,omitempty"`
	Name     string                `json:"name,omitempty"`
	DealType *enum.DealTypeValue   `json:"dealType"`
	Status   *enum.DealStatusValue `json:"status"`

	MaxQuantity            int `json:"maxQuantity"`
	MaxQuantityPerCustomer int `json:"maxQuantityPerCustomer"`
	TotalDealQuantity      int `json:"totalDealQuantity"`
	CurrentQuantity        int `json:"quantity"`
	Price                  int `json:"price,omitempty"`

	StartTime        *time.Time `json:"startTime"`
	EndTime          *time.Time `json:"endTime"`
	ReadyTime        *time.Time `json:"-"`
	DiscountPercent  *float64   `json:"discountPercent,omitempty"`
	MaxDiscountValue *int       `json:"maxDiscountValue,omitempty"`
	ChargeDealFee    string     `json:"chargeDealFee,omitempty"` // MARKETPLACE . SELLER . SELLER_MARKETPLACE
	Owner            string     `json:"owner,omitempty"`
	PricingType      *string    `json:"pricingType,omitempty"`

	VendorPromoInfo *VendorPromoInfo `json:"vendorPromoInfo,omitempty" bson:"vendor_promo_info,omitempty"`
}

type VendorPromoInfo struct {
	PromoCode string  `json:"promoCode,omitempty" bson:"promo_code,omitempty"`
	PromoName string  `json:"promoName,omitempty" bson:"promo_name,omitempty"`
	Price     float64 `json:"price,omitempty" bson:"price,omitempty"`
}

type Campaign struct {
	CampaignCode          string           `json:"campaignCode,omitempty"`
	CampaignType          string           `json:"campaignType,omitempty"`
	Status                string           `json:"status,omitempty"`
	CampaignName          string           `json:"campaignName,omitempty"`
	RegistrationStartTime time.Time        `json:"registrationStartTime,omitempty"`
	RegistrationEndTime   time.Time        `json:"registrationEndTime,omitempty"`
	StartTime             time.Time        `json:"startTime,omitempty"`
	EndTime               time.Time        `json:"endTime,omitempty"`
	FlashSaleTimes        *[]FlashSaleTime `json:"flashSaleTimes,omitempty"`
	Reward                *CampaignReward  `json:"reward,omitempty"`

	SaleType       string    `json:"saleType,omitempty"`
	IsActive       *bool     `json:"isActive,omitempty"`
	CustomerScopes *[]string `json:"customerScopes,omitempty"`
	Regions        *[]string `json:"regions,omitempty"`
	SubsidyType    string    `json:"subsidyType,omitempty" bson:"subsidy_type,omitempty"`
	SubsidyValue   int64     `json:"subsidyValue,omitempty" bson:"subsidy_value,omitempty"`
	Slug           string    `json:"slug,omitempty" bson:"slug,omitempty"`
}

type FlashSaleTime struct {
	Code       string    `json:"code,omitempty"`
	StartTime  time.Time `json:"startTime,omitempty"`
	EndTime    time.Time `json:"endTime,omitempty"`
	ProductIDs *[]int64  `json:"productIDs,omitempty"`
}

type CampaignReward struct {
	PercentageDiscount *int64 `json:"percentageDiscount,omitempty"`
	AbsoluteDiscount   *int64 `json:"absoluteDiscount,omitempty"`
	MaxDiscount        *int64 `json:"maxDiscount,omitempty"`
}

type ProductCampaign struct {
	CampaignID             int64                `json:"campaignID,omitempty"`
	CampaignCode           string               `json:"campaignCode,omitempty"`
	CampaignProductCode    string               `json:"campaignProductCode,omitempty"`
	ProductID              int64                `json:"productID,omitempty"`
	ProductCode            string               `json:"productCode,omitempty"`
	Sku                    string               `json:"sku,omitempty"`
	SellerCode             string               `json:"sellerCode,omitempty"`
	SalePrice              int64                `json:"retailPriceValue,omitempty"`
	CampaignPrice          int64                `json:"campaignPrice,omitempty"`
	PercentageDiscount     *int64               `json:"percentageDiscount"`
	AbsoluteDiscount       *int64               `json:"absoluteDiscount"`
	SaleType               string               `json:"saleType,omitempty"`
	MaxDiscount            *int64               `json:"maxDiscount,omitempty"`
	ChargeFee              string               `json:"chargeFee,omitempty"`
	Quantity               int64                `json:"quantity,omitempty"`
	Price                  int64                `json:"price,omitempty"`
	SoldQuantity           int64                `json:"soldQuantity,omitempty"`
	MaxQuantityPerOrder    int64                `json:"maxQuantityPerOrder,omitempty"`
	MaxQuantityPerCustomer int                  `json:"maxQuantityPerCustomer"`
	TotalQuantity          int                  `json:"totalQuantity"`
	FlashSaleTime          *[]*CampaignSaleTime `json:"flashSaleTime,omitempty"`
	IsActive               bool                 `json:"isActive,omitempty"`
	Campaign               *Campaign            `json:"campaign,omitempty"`
	Slug                   string               `json:"slug,omitempty"`
}

type CampaignSaleTime struct {
	StartTime    *time.Time           `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime      *time.Time           `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Code         string               `json:"code,omitempty" bson:"code,omitempty"`
	NumDay       int                  `json:"numDay,omitempty" bson:"num_day,omitempty"`
	Kind         string               `json:"kind,omitempty" bson:"kind,omitempty"`
	CampaignID   int64                `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode string               `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	ProductID    *int64               `json:"productID,omitempty" bson:"product_id,omitempty"`
	SaleTime     []*FlashSaleTimeItem `json:"saleTime,omitempty" bson:"sale_time,omitempty"`
}

type FlashSaleTimeItem struct {
	Code      string     `json:"code,omitempty" bson:"code,omitempty"`
	Ref       string     `json:"ref,omitempty" bson:"ref,omitempty"`
	Name      string     `json:"name,omitempty" bson:"name,omitempty"`
	StartTime *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
}

type Product struct {
	ProductID             int64     `json:"productID" bson:"product_id,omitempty"`
	Code                  string    `json:"code,omitempty" bson:"code,omitempty"`
	Name                  string    `json:"name,omitempty"`
	SellerCategoryCode    string    `json:"sellerCategoryCode,omitempty" bson:"seller_category_code,omitempty"`
	SellerSubCategoryCode string    `json:"sellerSubCategoryCode,omitempty" bson:"seller_sub_category_code,omitempty"`
	ImageUrls             *[]string `json:"imageUrls,omitempty"`
	IsFragile             *bool     `json:"isFragile,omitempty"` // hàng dễ vỡ
	IsFrozen              *bool     `json:"isFrozen,omitempty"`

	ManufacturerCode string `json:"manufacturerCode,omitempty" bson:"manufacturer_code,omitempty"`

	Weight float64 `json:"weight,omitempty"`
	Length float64 `json:"length,omitempty"`
	Width  float64 `json:"width,omitempty"`
	Height float64 `json:"height,omitempty"`
	Volume string  `json:"volume,omitempty"`
	Unit   string  `json:"unit,omitempty"` // order unit

	SubstituteProductConfig *SubstituteProductConfig `json:"substituteProductConfig,omitempty" bson:"substitute_product_config,omitempty"`
}
type SubstituteProductConfig struct {
	ProductID             int64       `json:"productId,omitempty" bson:"product_id,omitempty"`
	ProductCode           string      `json:"productCode,omitempty" bson:"product_code,omitempty"`
	InStockDisplayPage    DisplayPage `json:"inStockDisplayPage,omitempty" bson:"in_stock_display_page,omitempty"`
	OutOfStockDisplayPage DisplayPage `json:"outOfStockDisplayPage,omitempty" bson:"out_of_stock_display_page,omitempty"`
}
type DisplayPage struct {
	All               bool `json:"all,omitempty" bson:"all,omitempty"`
	ProductListPage   bool `json:"productListPage,omitempty" bson:"product_list_page,omitempty"`
	ProductDetailPage bool `json:"productDetailPage,omitempty" bson:"product_detail_page,omitempty"`
	CartPage          bool `json:"cartPage,omitempty" bson:"cart_page,omitempty"`
	QuickOrderPage    bool `json:"quickOrderPage,omitempty" bson:"quick_order_page,omitempty"`
}

// CartCacheDB ...
var CartCacheDB = &db.Instance{
	ColName:        "cart_raw",
	TemplateObject: &CartCache{},
}

// CartCacheDB ...
var CartItemCacheDB = &db.Instance{
	ColName:        "cart_item_raw",
	TemplateObject: &CartItemCache{},
}

// InitCartCacheModel is func init model cart
func InitCartCacheModel(s *mongo.Database) {
	CartCacheDB.ApplyDatabase(s)
	CartItemCacheDB.ApplyDatabase(s)

	t := true

	// CartCache
	_ = CartCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "account_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = CartCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "cart_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	// CartItemCache
	_ = CartItemCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "cart_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = CartItemCacheDB.CreateIndex(bson.D{
		primitive.E{Key: "cart_id", Value: 1},
		primitive.E{Key: "sku", Value: 1},
		primitive.E{Key: "type", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

}

type SubSkuCache struct {
	SKU         string `json:"sku" bson:"sku,omitempty"`
	Quantity    int    `json:"quantity" bson:"quantity,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`

	OldSKU      string       `json:"-" bson:"old_sku,omitempty"`
	ProductData *ProductData `json:"productData,omitempty"`
}
