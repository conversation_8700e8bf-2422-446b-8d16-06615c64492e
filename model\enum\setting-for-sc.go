package enum

type SettingForSCCodeValue string
type settingForSCCodeValue struct {
	INBOUND_FORMULA                      SettingForSCCodeValue
	INBOUND_LOCK_WAREHOUSE               SettingForSCCodeValue
	INBOUND_LOCK_ESTIMATE_TIME           SettingForSCCodeValue
	INBOUND_LOCK_MAX_NUMBER_OF_QUANTITY_ SettingForSCCodeValue
	INBOUND_ADDRESS_WAREHOUSE_           SettingForSCCodeValue
	EXCLUSIVE_PRODUCTS                   SettingForSCCodeValue
	RECONCILIATION_SCHEDULE              SettingForSCCodeValue
	UNLOCK_GAMIFICATION                  SettingForSCCodeValue
}

var SettingForSCCode = &settingForSCCodeValue{
	INBOUND_FORMULA:                      "INBOUND_FORMULA",
	INBOUND_LOCK_WAREHOUSE:               "INBOUND_LOCK_WAREHOUSE",
	INBOUND_LOCK_ESTIMATE_TIME:           "INBOUND_LOCK_ESTIMATE_TIME",
	INBOUND_LOCK_MAX_NUMBER_OF_QUANTITY_: "INBOUND_LOCK_MAX_NUMBER_OF_QUANTITY_",
	INBOUND_ADDRESS_WAREHOUSE_:           "INBOUND_ADDRESS_WAREHOUSE_",
	EXCLUSIVE_PRODUCTS:                   "EXCLUSIVE_PRODUCTS",
	RECONCILIATION_SCHEDULE:              "RECONCILIATION_SCHEDULE",
	UNLOCK_GAMIFICATION:                  "UNLOCK_GAMIFICATION",
}

type ScheduleSettingTypeValue string
type scheduleSettingTypeValue struct {
	SKIP_TO_NEXT ScheduleSettingTypeValue
	CHANGE_TIME  ScheduleSettingTypeValue
}

var ScheduleSettingType = &scheduleSettingTypeValue{
	SKIP_TO_NEXT: "SKIP_TO_NEXT",
	CHANGE_TIME:  "CHANGE_TIME",
}
