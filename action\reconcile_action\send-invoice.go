package reconcile_action

import (
	"log"
	"sort"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func SendInvoiceForConfirmedOrder(reconciliation *model.Reconciliation) *common.APIResponse {
	reconciliationItemF := model.ReconciliationItem{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		OperationAnd: []bson.M{
			{
				"order_id": bson.M{
					"$exists": true,
				},
			},
		},
	}
	orderIDsRes := model.ReconciliationItemDB.Distinct(reconciliationItemF, "order_id")
	if orderIDsRes.Status != common.APIStatus.Ok {
		return orderIDsRes
	}

	orderIDsResData := orderIDsRes.Data.([]interface{})
	orderIDs := make([]int64, len(orderIDsResData))
	for i, v := range orderIDsResData {
		orderIDs[i] = v.(int64)
	}

	now := time.Now()
	return SendInvoiceForReconciledOrder(orderIDs, &now)
}

// For reconciled order (each order-seller are reconciled), trigger send invoice to buyer
// orderIDs: 			List of orders to be verified if orderSeller are completed reconciled
// completedTime: Time of the last reconciliation completed
func SendInvoiceForReconciledOrder(orderIDs []int64, completedTime *time.Time) *common.APIResponse {
	for _, orderID := range orderIDs {
		sellerOrders := GetOrderSellers(orderID)
		if len(sellerOrders) == 0 {
			continue
		}

		sellerReconcileMap := map[string]bool{}
		for _, sellerOrder := range sellerOrders {
			if sellerOrder.ReconciliationStatus == model.ReconciliationStatus.Completed ||
				sellerOrder.ReconciliationStatus == model.ReconciliationStatus.Confirmed {
				sellerReconcileMap[sellerOrder.SellerCode] = true
			}
		}

		// Get all reconciliation items belonged to orderID
		itemRes := model.ReconciliationItemDB.Query(model.ReconciliationItem{OrderID: orderID}, 0, 0, nil)
		if itemRes.Status != common.APIStatus.Ok {
			return itemRes
		}

		// Prepare the `reconciliations` params
		// use map to avoid duplicate
		reconciliationItems := itemRes.Data.([]*model.ReconciliationItem)
		reconciliationsMap := make(map[model.BuyerServiceInvoiceReconciliation]int)
		for _, item := range reconciliationItems {
			if !sellerReconcileMap[item.SellerCode] {
				continue
			}

			// Check if not confirmed or completed -> skip
			reconciliationRes := model.ReconciliationDB.QueryOne(model.Reconciliation{
				SellerCode:                 item.SellerCode,
				ReconcileScheduleTimeIndex: item.ReconcileScheduleTimeIndex,
			})
			if reconciliationRes.Status != common.APIStatus.Ok {
				log.Printf("Query reconciliationRes error: %#v\n", reconciliationRes)
				continue
			}

			reconciliation := reconciliationRes.Data.([]*model.Reconciliation)[0]
			if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Completed &&
				reconciliation.ReconciliationStatus != model.ReconciliationStatus.Confirmed {
				continue
			}

			reconciliationsMap[model.BuyerServiceInvoiceReconciliation{
				SellerCode:                 item.SellerCode,
				ReconcileScheduleTimeIndex: item.ReconcileScheduleTimeIndex,
			}] = 1
		}
		// get keys from map
		var reconciliations []model.BuyerServiceInvoiceReconciliation
		for key := range reconciliationsMap {
			reconciliations = append(reconciliations, key)
		}

		sort.Slice(reconciliations, func(i, j int) bool {
			return reconciliations[i].ReconcileScheduleTimeIndex < reconciliations[j].ReconcileScheduleTimeIndex
		})

		invoiceRes := client.Services.Invoice.SendInvoiceForReconciledOrder(&model.BuyerServiceInvoice{
			OrderID:         orderID,
			ReconcileTime:   completedTime,
			Reconciliations: reconciliations,
		})
		if invoiceRes.Status != common.APIStatus.Ok {
			return invoiceRes
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Trigger sending invoices for fully reconciled orders successfully",
	}
}
