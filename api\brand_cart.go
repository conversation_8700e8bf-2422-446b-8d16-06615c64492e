package api

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

// CartGetInfo is handler get current cart
func BrandCartGetInfo(req sdk.APIRequest, resp sdk.APIResponder) error {

	var customerID = sdk.ParseInt64(req.GetParam("customerID"), 0)
	var salesTypeCode = req.GetParam("salesTypeCode")
	acc, session := wrapActionSourceAllInfo(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "T<PERSON><PERSON> khoản của bạn không thể thực hiện thao tác này",
			ErrorCode: "ACTION_NOT_FOUND",
		})
	}

	acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
	return resp.Respond(action.GetBrandCartInfo(acc, session, customerID, salesTypeCode))
}

// BrandCartAddItem ...
func BrandCartAddItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartAddItem
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if input.Type == "" {
		input.Type = "NORMAL"
	}

	acc, session := wrapActionSourceAllInfo(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
			ErrorCode: "ACTION_NOT_FOUND",
		})
	}

	event := &client.AddToCartEvent{
		Event: client.ADD_TO_CART,
		Metadata: map[string]string{
			"sku":               input.Sku,
			"source":            input.EventSource,
			"screen":            input.EventScreen,
			"host":              input.Host,
			"search_index":      input.SearchIndex,
			"search_page_index": input.SearchPageIndex,
			"recommend_skus":    input.RecommendSKUs,
			"metric":            input.Metric,
		},
		UserAgent:   req.GetHeader("User-Agent"),
		IP:          req.GetIP(),
		AccountType: acc.Type,
		AccountID:   acc.AccountID,
		CreatedTime: utils.ParseTimeToPointer(time.Now()),
	}

	return resp.Respond(action.AddBrandCartItem(acc, &input, event, session))
}

// BrandCartRemoveItem ...
func BrandCartRemoveItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartRemoveItem
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if input.Type == "" {
		input.Type = "NORMAL"
	}

	acc, session := wrapActionSourceAllInfo(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
			ErrorCode: "ACTION_NOT_FOUND",
		})
	}

	event := &client.RemoveFromCartEvent{
		Event: client.REMOVE_FROM_CART,
		Metadata: map[string]string{
			"sku":  input.Sku,
			"host": input.Host,
		},
		UserAgent:   req.GetHeader("User-Agent"),
		IP:          req.GetIP(),
		CreatedTime: utils.ParseTimeToPointer(time.Now()),
		AccountType: acc.Type,
		AccountID:   acc.AccountID,
	}

	return resp.Respond(action.BrandRemoveCartItem(acc, &input, event, session))
}

// CartUpdateInfo is handler update current cart information
func BrandCartUpdateInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartUpdateInfo
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	acc, session := wrapActionSourceAllInfo(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
			ErrorCode: "ACTION_NOT_FOUND",
		})
	}

	return resp.Respond(action.UpdateBrandCartInfo(acc, &input, session))
}

// BrandCartRevert is func to edit order to cart
func BrandCartRevert(req sdk.APIRequest, resp sdk.APIResponder) error {

	var orderID = sdk.ParseInt64(req.GetParam("orderID"), 0)
	var customerID = sdk.ParseInt64(req.GetParam("customerID"), 0)

	if orderID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "OrderID không được để trống",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	acc, session := wrapActionSourceAllInfo(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
			ErrorCode: "ACTION_NOT_FOUND",
		})

	}

	return resp.Respond(action.RevertBrandCart(acc, orderID, session, customerID))
}

// CartCheckout is handler checkout current cart
func SalesCartCheckout(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CartCheckoutData
	req.GetContent(&input)

	usInfo := getUAInfo(req.GetHeader("User-Agent"))
	input.SourceDetail = model.OrderSourceDetail{
		Os:             usInfo.OSName,
		OsVersion:      usInfo.OSVersion,
		Browser:        usInfo.ClientName,
		BrowserVersion: usInfo.ClientVersion,
		Platform:       usInfo.Platform,
		IP:             req.GetIP(),
	}

	acc, session := wrapActionSourceAllInfo(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
			ErrorCode: "ACTION_NOT_FOUND",
		})
	}

	acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
	return resp.Respond(action.CheckoutCart(acc, session, &input, true, req.GetHeaders()))
}

// BrandReOrder is func to re order to cart
func BrandReOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.BrandReOrderRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	acc, session := wrapActionSourceAllInfo(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
			ErrorCode: "ACTION_NOT_FOUND",
		})
	}

	return resp.Respond(action.BrandReOrder(acc, input.OrderID, input.CustomerID, session))
}

// RecountQuantityCustomerBoughtInThisMonth
func RecountQuantityCustomerBoughtInThisMonth(req sdk.APIRequest, resp sdk.APIResponder) error {

	var customerID = sdk.ParseInt64(req.GetParam("customerID"), 0)

	if customerID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "CustomerID không được để trống",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	return resp.Respond(action.RecountQuantityCustomerBoughtInThisMonth(customerID))
}

// BrandCartUpdateVoucher is handler update cart voucher code
func BrandCartUpdateVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.BrandCartUpdateVoucher
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc, session := wrapActionSourceAllInfo(req); acc != nil && session != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		if acc.Type == enum.AccountType.EMPLOYEE {
			return resp.Respond(action.BrandUpdateCartApplyVoucher(&model.Account{
				AccountID:    sdk.ParseInt64(req.GetParam("accountId"), 0),
				Type:         enum.AccountType.CUSTOMER,
				SourceDetail: acc.SourceDetail,
			}, &input, session))
		}
		return resp.Respond(action.BrandUpdateCartApplyVoucher(acc, &input, session))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// BrandCartRemoveVoucher is handler remove cart voucher code
func BrandCartRemoveVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.RemoveBrandVoucherRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc, session := wrapActionSourceAllInfo(req); acc != nil && session != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		if acc.Type == enum.AccountType.EMPLOYEE {
			return resp.Respond(action.BrandRemoveVoucherCode(&model.Account{
				AccountID:    sdk.ParseInt64(req.GetParam("accountId"), 0),
				Type:         enum.AccountType.CUSTOMER,
				SourceDetail: acc.SourceDetail,
			}, &input, session))
		}
		return resp.Respond(action.BrandRemoveVoucherCode(acc, &input, session))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// BrandCartAddVoucher is handler add cart voucher code
func BrandCartAddVoucher(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.RemoveBrandVoucherRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc, session := wrapActionSourceAllInfo(req); acc != nil && session != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		if acc.Type == enum.AccountType.EMPLOYEE {
			return resp.Respond(action.BrandAddVoucherCode(&model.Account{
				AccountID:    sdk.ParseInt64(req.GetParam("accountId"), 0),
				Type:         enum.AccountType.CUSTOMER,
				SourceDetail: acc.SourceDetail,
			}, &input, session))
		}
		return resp.Respond(action.BrandAddVoucherCode(acc, &input, session))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// func BrandCartItemLiteGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
// 	var input struct {
// 		SKUs          []string `json:"skus,omitempty"`
// 		CustomerID    int64    `json:"customerID,omitempty"`
// 		SalesTypeCode string   `json:"salesTypeCode,omitempty"`
// 	}
// 	if err := req.GetContent(&input); err != nil {
// 		return resp.Respond(&common.APIResponse{
// 			Status:    common.APIStatus.Invalid,
// 			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
// 			ErrorCode: "PAYLOAD_INVALID",
// 		})
// 	}
// 	if acc, session := wrapActionSourceAllInfo(req); acc != nil && session != nil {
// 		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
// 		return resp.Respond(action.GetBrandCartItemLite(acc, input.SKUs, input.CustomerID, session))
// 	}
// 	return resp.Respond(&common.APIResponse{
// 		Status:    common.APIStatus.Unauthorized,
// 		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
// 		ErrorCode: "ACTION_NOT_FOUND",
// 	})
// }
