package enum

type DealStatusValue string

type DealStatusEnum struct {
	ACTIVE   DealStatusValue
	INACTIVE DealStatusValue
}

var DealStatus = &DealStatusEnum{
	"ACTIVE",
	"INACTIVE",
}

type DealTypeValue string

type DealTypeEnum struct {
	DEAL  DealTypeValue
	COMBO DealTypeValue
}

var DealType = &DealTypeEnum{
	"DEAL",
	"COMBO",
}

type SkuTypeValue string
type skuType struct {
	NORMAL SkuTypeValue
	COMBO  SkuTypeValue
	DEAL   SkuTypeValue
}

var SKUType = &skuType{
	"NORMAL",
	"COMBO",
	"DEAL",
}

// SkuStatusValue ..
type SkuStatusValue string

// nolint
type skuStatus struct {
	NORMAL         SkuStatusValue
	LIMIT          SkuStatusValue
	OUT_OF_STOCK   SkuStatusValue // tam het hang
	SUSPENDED      SkuStatusValue // ngung kinh doanh
	STOP_PRODUCING SkuStatusValue // Ngưng sản xuất
	// NEAR_EXPIRATION SkuStatusValue // can date
}

// SkuStatus ...
var SkuStatus = &skuStatus{
	"NORMAL",
	"LIMIT",
	"OUT_OF_STOCK",
	"SUSPENDED",
	"STOP_PRODUCING",
	// "NEAR_EXPIRATION",
}

// PriceTypeValue ..
type PriceTypeValue string

type priceType struct {
	FIXED_REVENUE PriceTypeValue
	FIXED_PRICE   PriceTypeValue
}

// PriceType ...
var PriceType = &priceType{
	"FIXED_REVENUE",
	"FIXED_PRICE",
}

type LevelSKUValue string
type levelSKUValue struct {
	LEVEL_1 LevelSKUValue
	LEVEL_2 LevelSKUValue
	LEVEL_3 LevelSKUValue
	MARKET  LevelSKUValue
	VIP     LevelSKUValue
}

var LevelSKU = &levelSKUValue{
	LEVEL_1: "LEVEL_1",
	LEVEL_2: "LEVEL_2",
	LEVEL_3: "LEVEL_3",
	MARKET:  "MARKET",
	VIP:     "VIP",
}

type CustomerCertificateValue string

type customerCertificateType struct {
	GDP_GPP_GSPCertificate CustomerCertificateValue // GDP_GPP_GSP
	GPPCertificate         CustomerCertificateValue // GPP
	GDPCertificate         CustomerCertificateValue // GDP
	GSPCertificate         CustomerCertificateValue // GSP
	BusinessCertificate    CustomerCertificateValue // giấy đủ điều kiện kinh doanh dược
	BusinessLicense        CustomerCertificateValue // giấy hoạt động phòng khám
	BasicBusinessLicense   CustomerCertificateValue // giấy phép kinh doanh
}

var CustomerCertificateType = customerCertificateType{
	GDP_GPP_GSPCertificate: "GPP",
	GDPCertificate:         "GDP",
	GPPCertificate:         "GPP_NEW", // GPP
	GSPCertificate:         "GSP",
	BusinessCertificate:    "BUSINESS_CERTIFICATE",   // giấy đủ điều kiện kinh doanh dược
	BusinessLicense:        "BUSINESS_LICENSE",       // giấy hoạt động phòng khám
	BasicBusinessLicense:   "BASIC_BUSINESS_LICENSE", // giấy phép kinh doanh
}
