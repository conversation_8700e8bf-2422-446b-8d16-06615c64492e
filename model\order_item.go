package model

import (
	"context"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db_partition"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// OrderItem ...
type OrderItem struct {
	ID                  *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime         *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime     *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CartItemCreatedTime *time.Time          `json:"cartItemCreatedTime,omitempty" bson:"cart_item_created_time,omitempty"`

	// reference data
	OrderID         int64                  `json:"orderId,omitempty" bson:"order_id,omitempty"`
	OrderCode       string                 `json:"orderCode,omitempty" bson:"order_code,omitempty"`
	Sku             string                 `json:"sku,omitempty" bson:"sku,omitempty"`
	ItemCode        string                 `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	IsImportant     *bool                  `json:"isImportant,omitempty" bson:"is_important,omitempty"`
	Type            enum.ItemTypeValue     `json:"type,omitempty" bson:"type,omitempty"`
	DealCode        *string                `json:"dealCode,omitempty" bson:"deal_code,omitempty"`
	DealPricingType *string                `json:"dealPricingType,omitempty" bson:"deal_pricing_type,omitempty"`
	DealVendorPrice float64                `json:"dealVendorPrice,omitempty" bson:"deal_vendor_price,omitempty"`
	SellerCode      string                 `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerClass     *enum.SellerClassValue `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`
	ProductCode     string                 `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID       int64                  `json:"productID,omitempty" bson:"product_id,omitempty"`
	Skus            *[]*SubSku             `json:"skus" bson:"skus,omitempty"`                                // save for combo
	SkuExpiredDate  *time.Time             `json:"skuExpiredDate,omitempty" bson:"skuexpired_date,omitempty"` // expired date
	SkuStatus       *enum.SkuStatusValue   `json:"skuStatus,omitempty" bson:"sku_status,omitempty"`           // view web
	SkuPriceType    *enum.PriceTypeValue   `json:"skuPriceType,omitempty" bson:"sku_price_type,omitempty"`    // save order item
	SkuStatusData   *SkuStatusData         `json:"skuStatusData,omitempty" bson:"sku_status_data,omitempty"`  // save order item
	SkuVersion      string                 `json:"skuVersion,omitempty" bson:"sku_version,omitempty"`
	SkuLevel        *enum.LevelSKUValue    `json:"skuLevel,omitempty" bson:"sku_level,omitempty"`
	StoreCode       string                 `json:"storeCode,omitempty" bson:"store_code,omitempty"`

	Fee *FeesApply `json:"fee,omitempty" bson:"fee,omitempty"`
	VAT *float64   `json:"vat,omitempty" bson:"vat,omitempty"`

	Price               int             `json:"price,omitempty" bson:"price,omitempty,omitempty"`
	SellerPrice         int             `json:"sellerPrice,omitempty" bson:"seller_price,omitempty"`
	SellerRevenue       int             `json:"sellerRevenue,omitempty" bson:"seller_revenue,omitempty"`
	TotalPrice          int             `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
	TotalSellerPrice    int             `json:"totalSellerPrice,omitempty" bson:"total_seller_price,omitempty"`
	TotalSellerRevenue  int             `json:"totalSellerRevenue,omitempty" bson:"total_seller_revenue,omitempty"`
	ActualPrice         *int            `json:"actualPrice,omitempty" bson:"actual_price,omitempty"`
	ActualSellerPrice   *int            `json:"actualSellerPrice,omitempty" bson:"actual_seller_price,omitempty"`
	ActualSellerRevenue *int            `json:"actualSellerRevenue,omitempty" bson:"actual_seller_revenue,omitempty"`
	NotionalPrice       *int            `json:"notionalPrice,omitempty" bson:"notional_price,omitempty"`
	DiscountDetail      *DiscountDetail `json:"discountDetail,omitempty" bson:"discount_detail,omitempty"`

	Quantity          int  `json:"quantity,omitempty" bson:"quantity,omitempty"`
	NotionalQuantity  *int `json:"notionalQuantity,omitempty" bson:"notional_quantity,omitempty"`
	ReservedQuantity  *int `json:"reservedQuantity,omitempty" bson:"reserved_quantity,omitempty"`
	DeliveredQuantity *int `json:"deliveredQuantity,omitempty" bson:"deliveredQuantity,omitempty"`
	OutboundQuantity  *int `json:"outboundQuantity,omitempty" bson:"outbound_quantity,omitempty"`
	CompletedQuantity *int `json:"completedQuantity,omitempty" bson:"completed_quantity,omitempty"`
	ReturnedQuantity  *int `json:"returnedQuantity,omitempty" bson:"returned_quantity,omitempty"` // This field is use of the case SO
	DamageQuantity    *int `json:"damageQuantity,omitempty" bson:"damage_quantity,omitempty"`
	MaxQuantity       int  `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`

	SellerTotalPriceDisplay   *int `json:"sellerTotalPriceDisplay,omitempty" bson:"-"`   // Tổng cộng: [Giá bán] x [SL giao]
	SellerTotalPriceSADisplay *int `json:"sellerTotalPriceSADisplay,omitempty" bson:"-"` // Tổng cộng: [Giá bán] x [SL giao - SL trả]
	SellerRevenueDisplay      *int `json:"sellerRevenueDisplay,omitempty" bson:"-"`      // Doanh thu người bán nhận được
	TotalSellerRevenueDisplay *int `json:"totalSellerRevenueDisplay,omitempty" bson:"-"` // Tổng cộng Doanh thu người bán nhận được

	UpdatedProcessingQuantity *bool           `json:"-" bson:"updated_processing_quantity,omitempty"` // tracking if missing processing-status
	ReserveInfos              []*ReserveInfo  `json:"reserveInfos,omitempty" bson:"reserve_infos,omitempty"`
	OutboundInfos             []*OutboundInfo `json:"outboundInfos,omitempty" bson:"outbound_infos,omitempty"`
	ReturnInfos               []*ReturnInfo   `json:"returnInfos,omitempty" bson:"return_infos,omitempty"`
	NoneVat                   *bool           `json:"noneVat,omitempty" bson:"none_vat,omitempty"`
	InvoiceExportedTime       *time.Time      `json:"invoiceExportedTime,omitempty" bson:"invoice_exported_time,omitempty"`
	DealOwner                 string          `json:"dealOwner,omitempty" bson:"deal_owner,omitempty"`
	SubItems                  *[]*OrderItem   `json:"subItems,omitempty" bson:"sub_items,omitempty"`                       // combo
	ChargeDealFee             string          `json:"chargeDealFee,omitempty" bson:"charge_deal_fee,omitempty"`            // MARKETPLACE . SELLER . SELLER_MARKETPLACE
	ChargeDealFeeValue        int             `json:"chargeDealFeeValue,omitempty" bson:"charge_deal_fee_value,omitempty"` // số tiền được giảm khi mua deal

	CampaignCode           string `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	ChargeCampaignFeeValue int    `json:"chargeCampaignFeeValue,omitempty" bson:"charge_campaign_fee_value,omitempty"` // số tiền được giảm khi mua campaign
	CampaignPricingType    string `json:"campaignPricingType,omitempty" bson:"campaign_pricing_type,omitempty"`

	SubsidyPrice int64 `json:"subsidyPrice,omitempty" bson:"subsidy_price,omitempty"`

	// source
	Page       string `json:"page,omitempty" bson:"page,omitempty"`
	SearchKey  string `json:"searchKey,omitempty" bson:"search_key,omitempty"`
	CustomerID int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`

	Source enum.SourceValue `json:"source,omitempty" bson:"source,omitempty"`

	Tags           []string  `json:"tags,omitempty" bson:"tags,omitempty"`
	ComplexQuery   []*bson.M `json:"-" bson:"$and,omitempty"`
	ComplexQueryOr []*bson.M `json:"-" bson:"$or,omitempty"`

	DateFrom             *time.Time `json:"timeFrom,omitempty" bson:"-"`
	DateTo               *time.Time `json:"timeTo,omitempty" bson:"-"`
	DateFromAny          *time.Time `json:"timeFromAny,omitempty" bson:"-"`
	DateToAny            *time.Time `json:"timeToAny,omitempty" bson:"-"`
	StartLastUpdatedTime *time.Time `json:"startLastUpdatedTime,omitempty" bson:"-"`
	EndLastUpdatedTime   *time.Time `json:"endLastUpdatedTime,omitempty" bson:"-"`
	SubSkuCode           string     `json:"subSkuCode,omitempty" bson:"-"`
	SubItemCode          string     `json:"subItemCode,omitempty" bson:"-"`

	Point                 int64       `json:"point,omitempty" bson:"point,omitempty"`
	PointMultiplier       int64       `json:"pointMultiplier,omitempty" bson:"point_multiplier,omitempty"`
	TotalPoint            float64     `json:"totalPoint,omitempty" bson:"total_point,omitempty"`
	SkuContractCode       *string     `json:"skuContractCode,omitempty" bson:"sku_contract_code,omitempty"`
	SkuContractDetailCode *string     `json:"skuContractDetailCode,omitempty" bson:"sku_contract_detail_code,omitempty"`
	IsSkuLimitExisted     bool        `json:"isSkuLimitExisted,omitempty" bson:"is_sku_limit_existed,omitempty"`
	SkuLimitQuantity      int         `json:"-" bson:"-"`
	VoucherCode           string      `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	LotDates              *[]LotDates `json:"lotDates,omitempty" bson:"lot_dates,omitempty"`
	IsNearExpired         bool        `json:"isNearExpired,omitempty" bson:"is_near_expired,omitempty"`
	EventSource           string      `json:"eventSource,omitempty" bson:"event_source,omitempty"`
	EventScreen           string      `json:"eventScreen,omitempty" bson:"event_screen,omitempty"`
	BlockCode             string      `json:"blockCode,omitempty" bson:"block_code,omitempty"`
	ItemCodeIn            []string    `json:"itemCodeIn,omitempty" bson:"-"`

	OldData *OrderItem `json:"oldData,omitempty" bson:"old_data,omitempty"`

	UnitPrice                       int64 `json:"unitPrice,omitempty" bson:"unit_price,omitempty"`
	DiscountIncludedVAT             int64 `json:"discountIncludedVAT,omitempty" bson:"discount_included_vat,omitempty"`
	VoucherAmountBySKUUnit          int   `json:"voucherAmountBySKUUnit,omitempty" bson:"voucher_amount_by_sku_unit,omitempty"`
	ActualInvoiceVoucherAmountBySKU int64 `json:"actualInvoiceVoucherAmountBySKU,omitempty" bson:"actual_invoice_voucher_amount_by_sku,omitempty"`

	ReturnedQuantityInfosByDO []*ReturnedQuantityInfoByDO     `json:"returnedQuantityInfosByDO,omitempty" bson:"returned_quantity_infos_by_do,omitempty"` // This field is use of the case DO
	ReceiveInvoiceInfoBy      *enum.ReceiveInvoiceInfoByValue `json:"receiveInvoiceInfoBy,omitempty" bson:"receive_invoice_info_by,omitempty"`
	InvoiceQuantity           int                             `json:"invoiceQuantity" bson:"-"` // Just field for invoice cuz invoice detail using this model

	Unit    string `json:"unit,omitempty" bson:"unit,omitempty"`        // order unit
	SkuUnit string `json:"skuUnit,omitempty" bson:"sku_unit,omitempty"` // standard unit

	PriceAfterDiscount *int               `json:"priceAfterDiscount,omitempty" bson:"price_after_discount,omitempty"`
	DealInfo           *DiscountPromoInfo `json:"dealInfo,omitempty" bson:"deal_info,omitempty"`
	ContractInfo       *ContractInfo      `json:"contractInfo,omitempty" bson:"contract_info,omitempty"`
	CampaignInfo       *DiscountPromoInfo `json:"campaignInfo,omitempty" bson:"campaign_info,omitempty"`

	ProductFees []*ProductFee `json:"productFees,omitempty" bson:"product_fees,omitempty"`

	// For query replica
	IsQueryReplica bool `json:"isQueryReplica,omitempty" bson:"-"`
}

func (item *OrderItem) AutoSetSellerClass() {
	internalSeller := SELLER_INTERNALS
	if utils.IsContains(internalSeller, item.SellerCode) {
		item.SellerClass = &enum.SellerClass.INTERNAL
	} else {
		item.SellerClass = &enum.SellerClass.EXTERNAL
	}
}

type ProductFee struct {
	ProductID int64 `json:"productId,omitempty" bson:"product_id,omitempty"`

	SaleFeePercent        float64 `json:"saleFeePercent,omitempty" bson:"sale_fee_percent,omitempty"`               // phí bán hàng
	FulfillmentFeePercent float64 `json:"fulfillmentFeePercent,omitempty" bson:"fulfillment_fee_percent,omitempty"` // phí xử lý đơn hàng
}

// ReserveInfo ...
type ReserveInfo struct {
	Quantity int   `json:"quantity,omitempty" bson:"quantity,omitempty"`
	PickID   int64 `json:"pickId,omitempty" bson:"pick_id,omitempty"`
}

// OutboundInfo ...
type OutboundInfo struct {
	Lot           string `json:"lot,omitempty" bson:"lot,omitempty"`
	ExpDate       string `json:"expDate,omitempty" bson:"exp_date,omitempty"`
	Quantity      int    `json:"quantity,omitempty" bson:"quantity,omitempty"`
	VAT           int    `json:"vat,omitempty" bson:"vat,omitempty"`
	PoCode        string `json:"poCode,omitempty" bson:"po_code,omitempty"`
	DoCode        string `json:"doCode,omitempty" bson:"do_code,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
}

// ReturnInfo ...
type ReturnInfo struct {
	Lot      string `json:"lot,omitempty" bson:"lot,omitempty"`
	ExpDate  string `json:"expDate,omitempty" bson:"exp_date,omitempty"`
	Quantity int    `json:"quantity,omitempty" bson:"quantity,omitempty"`
	VAT      int    `json:"vat,omitempty" bson:"vat,omitempty"`
	DoCode   string `json:"doCode,omitempty" bson:"do_code,omitempty"`
}

type ReturnedQuantityInfoByDO struct {
	SKU      string `json:"sku,omitempty" bson:"sku,omitempty"`
	Quantity int    `json:"quantity,omitempty" bson:"quantity,omitempty"`
	DoCode   string `json:"doCode,omitempty" bson:"do_code,omitempty"`
}

// OrderItemDB ...
var OrderItemDB = &db.Instance{
	ColName:        "order_item",
	TemplateObject: &OrderItem{},
}

// OrderItemDeletedDB ...
var OrderItemDeletedDB = &db.Instance{
	ColName:        "order_item_deleted",
	TemplateObject: &OrderItem{},
}

// InitOrderItemModel is func init model sale order
func InitOrderItemModel(s *mongo.Database) {
	OrderItemDB.ApplyDatabase(s)

	// t := true
	// _ = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_code", Value: 1},
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "type", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "last_updated_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "completed_quantity", Value: 1},
	// 	primitive.E{Key: "last_updated_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "completed_quantity", Value: 1},
	// 	primitive.E{Key: "last_updated_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "none_vat", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "outbound_quantity", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "outbound_quantity", Value: 1},
	// 	primitive.E{Key: "sub_items", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "deal_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// err := OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// if err != nil {
	// 	fmt.Printf("OrderItemDB.CreateIndex order_id, sku %v\n", err.Error())
	// }

	// err = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// if err != nil {
	// 	fmt.Printf("OrderItemDB.CreateIndex product_id, seller_code %v\n", err.Error())
	// }

	// err = OrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "sub_items.sku", Value: 1},
	// 	primitive.E{Key: "completed_quantity", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// if err != nil {
	// 	fmt.Printf("OrderItemDB.CreateIndex sub_items.sku, completed_quantity, created_time %v\n", err.Error())
	// }
}

// InitOrderItemDeletedModel is func init model sale order deleted
func InitOrderItemDeletedModel(s *mongo.Database) {
	OrderItemDeletedDB.ApplyDatabase(s)
}

// ===========================================================================

// orderItemPartitionDB ...
var orderItemPartitionDB = &db_partition.PartitionInstance{
	ColName:        "order_item",
	TemplateObject: &OrderItem{},
	PartType:       db_partition.PartType.Month,
}

// order item replica
var orderItemPartitionDBReplica = &db_partition.PartitionInstance{
	ColName:        "order_item",
	TemplateObject: &OrderItem{},
	PartType:       db_partition.PartType.Month,
}

// PartitionErrorDB ...
var PartitionErrorDB = &db.Instance{
	ColName:        "partition_error",
	TemplateObject: &bson.M{},
}

// InitOrderItemModel is func init model sale order
func InitOrderItemPartitionModel(s *mongo.Database, onNewHandler db_partition.OnNewHandler) {
	orderItemPartitionDB.OnNewCollection = onNewHandler
	orderItemPartitionDB.RunScheduleCreateCollection = true
	orderItemPartitionDB.ApplyDatabase(s)
	PartitionErrorDB.ApplyDatabase(s)
}

func InitOrderItemPartitionReadModel(s *mongo.Database) {
	orderItemPartitionDBReplica.OnNewCollection = func(ins *db.Instance) error {
		return nil
	}
	orderItemPartitionDBReplica.RunScheduleCreateCollection = false
	orderItemPartitionDBReplica.ApplyDatabase(s)
}

var PARTITION_NOT_FOUND_RESPONSE *common.APIResponse = &common.APIResponse{
	Status:    common.APIStatus.Invalid,
	Message:   "Partition not found",
	ErrorCode: "PARTITION_NOT_FOUND",
}

func GetOrderItemPartitionDB(order *Order, source string) *db.Instance {
	return getOrderItemPartitionDB(order, source, false)
}

func GetOrderItemPartitionDBReplica(order *Order, source string) *db.Instance {
	return getOrderItemPartitionDB(order, source, true)
}

func getOrderItemPartitionDB(order *Order, source string, isRep bool) *db.Instance {

	dbOrder := OrderDB
	dbOrderItem := orderItemPartitionDB
	if isRep {
		dbOrder = OrderDBReplica
		dbOrderItem = orderItemPartitionDBReplica
	}

	// by CreatedTime
	if order.CreatedTime != nil {

		// GetCol
		ins, err := dbOrderItem.GetCol(*order.CreatedTime)
		if err != nil {
			fmt.Println(source + ": " + err.Error())
			PartitionErrorDB.Create(bson.M{
				"keys": []string{"GetOrderItemPartitionDB", source},
				"messages": []string{
					source,
					err.Error(),
				},
				"body": bson.M{
					"created_time": order.CreatedTime,
				},
			})
		}

		return ins

		// by OrderID || OrderCode
	} else if order.OrderID != 0 || order.OrderCode != "" {
		// by OrderID || OrderCode
		checkResp := dbOrder.QueryOne(Order{OrderID: order.OrderID, OrderCode: order.OrderCode})
		if checkResp.Status != common.APIStatus.Ok {
			PartitionErrorDB.Create(bson.M{
				"keys": []string{"GetOrderItemPartitionDB", source, order.OrderCode, fmt.Sprint(order.OrderID)},
				"messages": []string{
					source,
					checkResp.Message,
				},
				"body": bson.M{
					"order_id":   order.OrderID,
					"order_code": order.OrderCode,
				},
			})
			return nil
		}
		check := checkResp.Data.([]*Order)[0]

		// GetCol
		ins, err := dbOrderItem.GetCol(*check.CreatedTime)
		if err != nil {
			fmt.Println(source + ": " + err.Error())
			PartitionErrorDB.Create(bson.M{
				"keys": []string{"GetOrderItemPartitionDB", source, check.OrderCode, fmt.Sprint(check.OrderID)},
				"messages": []string{
					source,
					err.Error(),
				},
				"body": bson.M{
					"created_time": check.CreatedTime,
				},
			})
		}

		return ins
	}

	fmt.Println(source + ": Invalid partition input")
	PartitionErrorDB.Create(bson.M{
		"keys": []string{"GetOrderItemPartitionDB", source},
		"messages": []string{
			source,
			"Invalid partition input",
		},
	})
	return nil
}

func GetLatestOrderItemPartitionDB(source string) *db.Instance {
	ins, err := orderItemPartitionDB.GetCol()
	if err != nil {
		fmt.Println(source + ": " + err.Error())
	}

	return ins
}

func GetLatestOrderItemPartitionDBReplica(source string) *db.Instance {
	ins, err := orderItemPartitionDBReplica.GetCol()
	if err != nil {
		fmt.Println(source + ": " + err.Error())
	}

	return ins
}

func InitOnNewCollectionOrderItem(newCol *db.Instance) error {
	time.Sleep(time.Second * 3)

	previous := time.Now().AddDate(0, -1, 0)
	oldCol, err := orderItemPartitionDB.GetCol(previous)
	if err != nil {
		fmt.Println("InitOnNewCollectionOrderItem: " + err.Error())
		return err
	}

	err = CopyIndexes(oldCol, newCol)
	if err != nil {
		fmt.Println("InitOnNewCollectionOrderItem: " + err.Error())
		return err
	}

	return nil
}

func CopyIndexes(oldCol *db.Instance, newCol *db.Instance) error {
	queryIndex, err := oldCol.GetClient().Database(oldCol.DBName).Collection(oldCol.ColName).Indexes().List(context.Background())
	if err != nil {
		// log.Fatal(err)
		return err
	}
	var listIndexRaw []bson.D
	queryIndex.All(context.TODO(), &listIndexRaw)
	for _, v := range listIndexRaw {
		temp := v.Map()
		newIndex := mongo.IndexModel{}
		indexOptions := options.IndexOptions{}
		newIndex.Keys = temp["key"]
		for key, value := range temp {
			switch key {
			case "background":
				indexOptions.Background = utils.WithBool(value.(bool))
			case "expireAfterSeconds":
				indexOptions.ExpireAfterSeconds = utils.WithInt32(value.(int32))
			case "name":
				indexOptions.Name = utils.WithString(value.(string))
			case "sparse":
				indexOptions.Sparse = utils.WithBool(value.(bool))
			case "storageEngine":
				indexOptions.StorageEngine = &value
			case "unique":
				indexOptions.Unique = utils.WithBool(value.(bool))
			case "v":
				indexOptions.Version = utils.WithInt32(value.(int32))
			case "default_language":
				indexOptions.DefaultLanguage = utils.WithString(value.(string))
			case "language_override":
				indexOptions.LanguageOverride = utils.WithString(value.(string))
			case "textIndexVersion":
				indexOptions.TextVersion = utils.WithInt32(value.(int32))
			case "weights":
				indexOptions.Weights = &value
			case "2dsphereIndexVersion":
				indexOptions.SphereVersion = utils.WithInt32(value.(int32))
			case "bits":
				indexOptions.Bits = utils.WithInt32(value.(int32))
			case "max":
				indexOptions.Max = utils.WithFloat64(value.(float64))
			case "min":
				indexOptions.Min = utils.WithFloat64(value.(float64))
			case "bucketSize":
				indexOptions.BucketSize = utils.WithInt32(value.(int32))
			case "partialFilterExpression":
				indexOptions.PartialFilterExpression = &value
			case "wildcardProjection":
				indexOptions.WildcardProjection = &value
			case "hidden":
				indexOptions.Hidden = utils.WithBool(value.(bool))
			default:
				continue
			}

		}
		newIndex.Options = &indexOptions
		_, err = newCol.GetClient().Database(newCol.DBName).Collection(newCol.ColName).Indexes().CreateOne(context.Background(), newIndex)
		if err != nil {
			// log.Fatal(err)
			return err
		}
	}

	return nil
}
