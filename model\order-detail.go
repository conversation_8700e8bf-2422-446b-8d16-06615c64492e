package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var OrderDetailDB = &db.Instance{
	ColName:        "order_detail",
	TemplateObject: &OrderDetail{},
}

var OrderDetailDeletedDB = &db.Instance{
	ColName:        "order_detail_deleted",
	TemplateObject: &OrderDetail{},
}

type OrderDetail struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// reference
	OrderID      int64  `json:"orderId,omitempty" bson:"order_id,omitempty"`
	OrderCode    string `json:"orderCode" bson:"order_code,omitempty"`
	AccountID    int64  `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CustomerID   int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	CustomerCode string `json:"customerCode,omitempty" bson:"customer_code,omitempty"`

	Skus        []string `json:"-" bson:"skus,omitempty"`         // skus exist in order
	SellerCodes []string `json:"_" bson:"seller_codes,omitempty"` // sellers participate in order
	ItemCodes   []string `json:"-" bson:"item_codes,omitempty"`   // itemCodes exist in order
	ProductIds  []int64  `json:"-" bson:"product_ids,omitempty"`  // productIds exist in order
	DealCodes   []string `json:"-" bson:"deal_codes,omitempty"`

	// For combo
	SubSkus       *[]string `json:"-" bson:"sub_skus,omitempty"`
	SubProductIds *[]int64  `json:"-" bson:"sub_product_ids,omitempty"`

	ComplexQuery   []*bson.M `json:"-" bson:"$and,omitempty"`
	ComplexQueryOr []*bson.M `json:"-" bson:"$or,omitempty"`
}

func InitOrderDetailModel(s *mongo.Database) {
	OrderDetailDB.ApplyDatabase(s)
	OrderDetailDeletedDB.ApplyDatabase(s)
}

type OrderDetailQuery struct {
	OrderID      int64  `json:"orderId,omitempty" bson:"order_id,omitempty"`
	OrderCode    string `json:"orderCode" bson:"order_code,omitempty"`
	AccountID    int64  `json:"accountId,omitempty" bson:"account_id,omitempty"`
	CustomerID   int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	CustomerCode string `json:"customerCode,omitempty" bson:"customer_code,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`

	OrderIDIn []int64 `json:"orderIDIn,omitempty" bson:"-"` // orderID exist in order

	// NOTE
	Sku      string `json:"sku,omitempty" bson:"-"`      // skus exist in order
	ItemCode string `json:"itemCode,omitempty" bson:"-"` // itemCodes exist in order
	DealCode string `json:"dealCode,omitempty" bson:"-"` // dealCode exist in order
	//
	SkuIn      []string `json:"skuIn,omitempty" bson:"-"`      // skus exist in order
	ItemCodeIn []string `json:"itemCodeIn,omitempty" bson:"-"` // itemCodes exist in order
	// DealCodeIn []string `json:"dealCodeIn,omitempty" bson:"-"` // dealCode exist in order

	DateFrom    *time.Time `json:"timeFrom,omitempty" bson:"-"`
	DateTo      *time.Time `json:"timeTo,omitempty" bson:"-"`
	DateFromAny *time.Time `json:"timeFromAny,omitempty" bson:"-"`
	DateToAny   *time.Time `json:"timeToAny,omitempty" bson:"-"`

	Offset   int64 `json:"offset,omitempty" bson:"-"`
	Limit    int64 `json:"limit,omitempty" bson:"-"`
	GetTotal bool  `json:"getTotal,omitempty" bson:"-"`
}
