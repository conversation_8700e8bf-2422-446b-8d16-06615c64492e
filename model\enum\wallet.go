package enum

type WalletStatusValue string
type walletStatus struct {
	ACTIVE   WalletStatusValue //
	INACTIVE WalletStatusValue //
}

// WalletStatus mapping
var WalletStatus = &walletStatus{
	ACTIVE:   "ACTIVE",
	INACTIVE: "INACTIVE",
}

type WalletOptionValue string
type walletOption struct {
	CREDIT WalletOptionValue
	// DEBIT    WalletOptionValue
	INTEREST WalletOptionValue
}

// WalletOption mapping
var WalletOption = &walletOption{
	CREDIT: "CREDIT",
	// DEBIT:  "DEBIT",
	INTEREST: "INTEREST",
}

type WalletCreditStatusValue string
type walletCreditStatus struct {
	WITHIN_LIMIT   WalletCreditStatusValue // trong hạn mức [0; 90%)
	CLOSE_TO_LIMIT WalletCreditStatusValue // gần hạn mức [90%; 100%)
	OVER_LIMIT     WalletCreditStatusValue // vượt hạn mức >= 100%
}

// WalletCreditStatus mapping
var WalletCreditStatus = &walletCreditStatus{
	WITHIN_LIMIT:   "WITHIN_LIMIT",
	CLOSE_TO_LIMIT: "CLOSE_TO_LIMIT",
	OVER_LIMIT:     "OVER_LIMIT",
}

type WalletTypeValue string
type walletType struct {
	CASH   WalletTypeValue //
	BANK   WalletTypeValue //
	WALLET WalletTypeValue //
}

// WalletType mapping
var WalletType = &walletType{
	CASH:   "CASH",
	BANK:   "BANK",
	WALLET: "WALLET",
}
