package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

// BrandCartDB ...
var BrandCartDB = &db.Instance{
	ColName:        "brand_cart",
	TemplateObject: &Cart{},
}

// InitCartModel is func init model cart
func InitBrandCartModel(s *mongo.Database) {
	BrandCartDB.ApplyDatabase(s)
	// t := true
	// _ = BrandCartDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "key", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// t := true
	// _ = BrandCartDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "customer_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// _ = BrandCartDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "cart_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// _ = BrandCartDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "cart_no", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
}

// BrandCartDeletedDB ...
var BrandCartDeletedDB = &db.Instance{
	ColName:        "brand_cart_deleted",
	TemplateObject: &Cart{},
}

// Init Brand Cart Delete
func InitBrandCartDeletedModel(s *mongo.Database) {
	BrandCartDeletedDB.ApplyDatabase(s)
}
