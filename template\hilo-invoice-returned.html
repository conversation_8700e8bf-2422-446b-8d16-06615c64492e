<html>
  <head>
    <meta charset="UTF-8" />
    <style>
      /* Reset */
      body {
        background-color: #e5e5e5;
        font-family: sans-serif;
        color: #595959;
        line-height: 1.5;
      }

      a {
        color: #0bd577;
        text-decoration: none;
      }

      a:hover {
        text-decoration: underline;
      }

      h1,
      h3 {
        margin-top: 0;
      }

      h1 {
        color: black;
      }

      h3 {
        margin-bottom: 0.5em;
      }

      table,
      th,
      td {
        border: 1px solid black;
        border-collapse: collapse;
      }

      th,
      td {
        padding: 8px;
      }

      /* Layout */
      .main-container {
        background-color: white;
        width: 600px;
        margin: 2em auto;
      }

      .sub-container {
        padding: 1.25em 1.5em;
      }

      .logo-container {
        border-bottom: 1px solid #e8e8e8;
        width: inherit;
        display: flex;
      }

      .footer-container {
        background-color: #0bd577;
        color: white;
        text-align: center;
        font-size: small;
        line-height: 1.25;
      }

      /* Spacing */
      .mt-0 {
        margin-top: 0;
      }

      .mb-0 {
        margin-bottom: 0;
      }

      .mb-3 {
        margin-bottom: 1.5em;
      }

      /* Text */
      .text-gray-7 {
        /* Grey / grey-7 */
        color: #8c8c8c;
      }

      /* Components */
      .btn {
        display: inline-block;
        padding: 0.5em 1em;
        border-radius: 4px;
        text-decoration: none;
      }

      .btn-primary {
        background-color: #00b46e;
        color: white;
      }

      .error {
        color: #ff0000;
      }
    </style>
  </head>

  <body>
    <div class="container main-container">
      <div class="sub-container logo-container">
        <img
          height="186"
          src="https://cdn-gcs.thuocsi.vn/thuocsi-live/images/20224b9861f37d5d2371c93519fca18d"
        />
      </div>

      <div class="sub-container">
        <h1>Dear Seller,</h1>
        <p>
          Trong quá trình xử lý đơn hàng, đơn hàng dưới đây đã phát sinh trả
          hàng. Buymed đề nghị Nhà Bán Hàng kiểm tra và điều chỉnh lại các hóa
          đơn điện tử đã xuất cho đơn hàng này:
        </p>

        <table>
          <tr>
            <th>Mã đơn hàng</th>
            <th>Mã hóa đơn đã xuất</th>
            <th>Ngày giao hàng</th>
            <th>Ngày phát sinh trả hàng</th>
          </tr>
          {{range $item := .Invoices}}
          <tr>
            <td>{{$item.OrderID}}</td>
            <td>{{$item.InvoiceCode}}</td>
            <td>{{$item.DeliveredTime.Format "02/01/2006"}}</td>
            <td>{{$item.ReturnedTime.Format "02/01/2006"}}</td>
          </tr>
          {{end}}
        </table>

        <p>
          Để đảm bảo tính chính xác và tuân thủ quy định về hóa đơn điện tử, Nhà
          Bán Hàng vui lòng thực hiện điều chỉnh hoặc xuất hóa đơn điều chỉnh
          theo đúng nghiệp vụ của nhà bán hàng.
        </p>

        <p>
          👉 Tài liệu hướng dẫn điều chỉnh hóa đơn điện tử trên Hilo:
          <a href="https://hddt78.hilo.com.vn/Account/HDSD" target="_blank"
            >Tài liệu hướng dẫn sử dụng hoá đơn điện tử</a
          >
        </p>

        <div class="logo-container mb-3"></div>

        <p>
          <b>Lưu ý:</b> Đây là email thông báo tự động từ hệ thống Buymed gửi
          đến Nhà bán hàng khi đơn hàng phát sinh trả hàng.
        </p>
        <p>
          Nếu phát sinh vấn đề bất kỳ cần hỗ trợ Nhà bán hàng vui lòng phản hồi
          qua Phiếu hỗ trợ trên Seller Center để được xử lý kịp thời.
        </p>

        <p>
          <b><u>CÁCH TẠO PHIẾU HỖ TRỢ</u></b>
        </p>

        <p>
          Hệ thống Sellercenter -> Vào Mục Hỗ trợ -> Chọn Phiếu Hỗ trợ -> Bấm
          Tạo yêu cầu -> Lựa chọn lý do cần hỗ trợ -> Điền đầy đủ thông tin vấn
          đề cần hỗ trợ
        </p>

        <p>Trân trọng,</p>
      </div>

      <div class="sub-container footer-container">
        <h3>Công ty TNHH Buymed</h3>
        <p class="mt-0 mb-0">
          Địa Chỉ: Tầng 8, Tòa nhà Vincom Center Đồng khởi, 72 Lê Thánh Tôn,
          Phường Bến Nghé, Quận 1, Thành Phố Hồ Chí Minh, Việt Nam <br />
          Số Giấy Phép Sàn Thương Mại Điện Tử: 0314758651/KD-0368<br />
          Số Chứng Nhận ĐKKD: 0314758651, Cấp Ngày 29/11/2017<br />
          Tại Sở Kế Hoạch Và Đầu Tư Thành Phố Hồ Chí Minh<br />
        </p>
      </div>
    </div>
  </body>
</html>
