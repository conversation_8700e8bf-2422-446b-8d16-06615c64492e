package migrate

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateReconcileInboundPenalty(indexes []string, removeTimeRange []int) *common.APIResponse {

	var offset, limit int64 = 0, 100
	recItemDelCount := 0
	recInboundDelCount := 0
	reconcileUpdated := 0

	type sellerEffected struct {
		sellerCode         string
		reconcileTimeIndex string
	}
	sellersEffected := map[string]sellerEffected{}

	query := bson.M{
		"reconcile_schedule_time_index": bson.M{"$in": indexes},
		"fee_type":                      enum.FeeType.INBOUND_OVERDUE_PENALTY,
	}

	if len(removeTimeRange) > 0 {
		query["penalty_timeline"] = bson.M{"$in": removeTimeRange}
	}

	for {
		queryRes := model.ReconciliationItemDB.Query(&query, offset, limit, &primitive.M{"_id": 1})
		if queryRes.Status != common.APIStatus.Ok {
			break
		}

		reconciliationItems := queryRes.Data.([]*model.ReconciliationItem)
		for _, reconciliationItem := range reconciliationItems {
			key := fmt.Sprintf("%s_%s", reconciliationItem.SellerCode, reconciliationItem.ReconcileScheduleTimeIndex)
			if _, ok := sellersEffected[key]; !ok {
				sellersEffected[key] = sellerEffected{
					sellerCode:         reconciliationItem.SellerCode,
					reconcileTimeIndex: reconciliationItem.ReconcileScheduleTimeIndex,
				}
			}

			recItemDeletedRes := model.ReconciliationItemDB.Delete(&model.ReconciliationItem{
				ID: reconciliationItem.ID,
			})
			if recItemDeletedRes.Status == common.APIStatus.Ok {
				recItemDelCount++
			}

			recInboundDelRes := model.ReconciliationInboundPenaltyDB.Delete(&model.ReconciliationInboundPenalty{
				Sku:                reconciliationItem.Sku,
				InboundCode:        reconciliationItem.InboundCode,
				ReconcileTimeIndex: reconciliationItem.ReconcileScheduleTimeIndex,
			})
			if recInboundDelRes.Status == common.APIStatus.Ok {
				recInboundDelCount++
			}
			time.Sleep(30 * time.Millisecond)
		}

		offset += limit
	}

	for _, sellerEffected := range sellersEffected {
		recRes := model.ReconciliationDB.QueryOne(&model.Reconciliation{
			SellerCode:                 sellerEffected.sellerCode,
			ReconcileScheduleTimeIndex: sellerEffected.reconcileTimeIndex,
		})
		if recRes.Status != common.APIStatus.Ok {
			continue
		}

		reconciliation := recRes.Data.([]*model.Reconciliation)[0]
		if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
			continue
		}

		reconcile_action.DeleteReconciliationIfEmpty(sellerEffected.sellerCode, sellerEffected.reconcileTimeIndex)

		recCalValue, err := reconcile_action.CalculateReconciliation(sellerEffected.sellerCode, sellerEffected.reconcileTimeIndex)
		if err != nil {
			continue
		}

		updater := model.Reconciliation{
			TotalBuyerFee:  &recCalValue.TotalBuyerFee,
			TotalRevenue:   &recCalValue.TotalRevenue,
			ListingFee:     &recCalValue.ListingFee,
			FulfillmentFee: &recCalValue.FulfillmentFee,
			PenaltyFee:     &recCalValue.PenaltyFee,
			BonusAmount:    &recCalValue.BonusAmount,
			TotalPayment:   &recCalValue.TotalPayment,
		}
		result := model.ReconciliationDB.UpdateOne(&model.Reconciliation{
			SellerCode:                 sellerEffected.sellerCode,
			ReconcileScheduleTimeIndex: sellerEffected.reconcileTimeIndex,
		}, updater)
		if result.Status == common.APIStatus.Ok {
			reconcileUpdated++
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate finish",
		Data: []interface{}{
			map[string]interface{}{
				"recItemDelCount":    recItemDelCount,
				"recInboundDelCount": recInboundDelCount,
				"sellersEffected":    len(sellersEffected),
				"reconcileUpdated":   reconcileUpdated,
			},
		},
	}
}

func RemoveReconciliationInboundPenalty(idStr string) *common.APIResponse {
	id, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Invalid ID",
		}
	}

	if id.IsZero() {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Invalid ID",
		}
	}

	return model.ReconciliationInboundPenaltyDB.Delete(&model.ReconciliationInboundPenalty{ID: &id})
}
