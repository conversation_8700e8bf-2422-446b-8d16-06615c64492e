package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CheckoutError struct {
	ID                primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime       *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime   *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	FirstCheckoutTime *time.Time         `json:"firstCheckoutTime,omitempty" bson:"first_checkout_time,omitempty"`
	LastCheckoutTime  *time.Time         `json:"lastCheckoutTime,omitempty" bson:"last_checkout_time,omitempty"`

	Code            string `json:"code,omitempty" bson:"code,omitempty"`
	CustomerID      int64  `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	CustomerPhone   string `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`
	CustomerAddress string `json:"customerAddress,omitempty" bson:"customer_address,omitempty"`
	AccountID       int64  `json:"accountID,omitempty" bson:"account_id,omitempty"`
	CartID          int64  `json:"cartID,omitempty" bson:"cart_id,omitempty"`
	FailCount       int64  `json:"failCount,omitempty" bson:"fail_count,omitempty"`
	ErrorDetail     string `json:"errorDetail,omitempty" bson:"error_detail,omitempty"`
	ErrorMessage    string `json:"errorMessage,omitempty" bson:"error_message,omitempty"`
	ErrorCode       string `json:"errorCode,omitempty" bson:"error_code,omitempty"`
}

// CheckoutErrorDB ...
var CheckoutErrorDB = &db.Instance{
	ColName:        "checkout_error",
	TemplateObject: &CheckoutError{},
}

// InitCheckoutErrorModel is func init
func InitCheckoutErrorModel(s *mongo.Database) {
	CheckoutErrorDB.ApplyDatabase(s)
}
