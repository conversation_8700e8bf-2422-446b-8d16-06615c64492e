package enum

// OrderStateValue ...
type OrderStateValue string

type orderStateValue struct {
	WaitConfirm       OrderStateValue
	Confirmed         OrderStateValue
	Canceled          OrderStateValue
	CanceledOrderPart OrderStateValue
	Processing        OrderStateValue
	WaitToPack        OrderStateValue
	WaitToDeliver     OrderStateValue
	Completed         OrderStateValue
	Delivering        OrderStateValue
	Returned          OrderStateValue
	Delivered         OrderStateValue
	Reserving         OrderStateValue
	Reverting         OrderStateValue
}

// OrderState ...
var OrderState = &orderStateValue{
	"WAIT_TO_CONFIRM",
	"CONFIRMED",
	"CANCEL",
	"CANCEL_ORDER_PART",
	"PROCESSING",
	"WAIT_TO_PACK",
	"WAIT_TO_DELIVER",
	"COMPLETED",
	"DELIVERING",
	"RETURNED",
	"DELIVERED",
	"RESERVING",
	"REVERTING",
}

// IsOrderStatus is func check customer type
func IsOrderStatus(val string) bool {
	switch OrderStateValue(val) {
	case OrderState.WaitConfirm, OrderState.Canceled,
		OrderState.Completed, OrderState.Delivering,
		OrderState.Processing, OrderState.Confirmed, OrderState.Delivered:
		{
			return true
		}
	default:
		return false
	}
}

// OrderConfirmTypeValue ...
type OrderConfirmTypeValue string

type orderConfirmTypeValue struct {
	Auto   OrderConfirmTypeValue
	Manual OrderConfirmTypeValue
}

// OrderConfirmType ...
var OrderConfirmType = &orderConfirmTypeValue{
	"AUTO",
	"MANUAL",
}

// ReconciliationStatusValue
type ReconciliationStatusValue string

type reconciliationStatus struct {
	Waiting   ReconciliationStatusValue
	Completed ReconciliationStatusValue
}

// ReconciliationStatus ...
var ReconciliationStatus = &reconciliationStatus{
	"WAITING",
	"COMPLETED",
}

// OrderSellerStateValue ...
type OrderSellerStateValue string

type orderSellerStateValue struct {
	Draft             OrderSellerStateValue
	WaitToConfirm     OrderSellerStateValue
	Confirmed         OrderSellerStateValue
	WaitingToPick     OrderSellerStateValue
	WaitingToPack     OrderSellerStateValue
	WaitingToDelivery OrderSellerStateValue
	PickedByCarrier   OrderSellerStateValue
	Delivering        OrderSellerStateValue
	Delivered         OrderSellerStateValue
	Completed         OrderSellerStateValue
	Cancelled         OrderSellerStateValue
}

// OrderSellerState ...
var OrderSellerState = &orderSellerStateValue{
	"DRAFT",
	"WAIT_TO_CONFIRM",
	"CONFIRMED",
	"WAITING_TO_PICK",
	"WAITING_TO_PACK",
	"WAITING_TO_DELIVERY",
	"PICKED_BY_CARRIER",
	"DELIVERING",
	"DELIVERED",
	"COMPLETED",
	"CANCELLED",
}

type SellerClassValue string

type sellerClassValue struct {
	INTERNAL SellerClassValue
	EXTERNAL SellerClassValue
}

var SellerClass = &sellerClassValue{
	INTERNAL: "INTERNAL",
	EXTERNAL: "EXTERNAL",
}

type TagValue string

type tagValue struct {
	VIP                      TagValue
	BUYDENTAL                TagValue
	MULTIPLE_DELIVERY_ORDER  TagValue
	ORDER_ASSIST             TagValue
	SPLIT_MULTIPLE_WAREHOUSE TagValue // giao nhiều kho
	REFUSE_SPLIT_ORDER       TagValue
	NON_INVOICE_CUSTOMER     TagValue
	CUSTOMER_COME_BACK       TagValue
	BRAND_ORDER              TagValue
	NO_DELIVERY_BY_BIN       TagValue
	BRANDGIFT                TagValue
	BRAND_PORTAL             TagValue
	CLINIC_PORTAL            TagValue
	SO_INTERNAL              TagValue
	CO_INTERNAL              TagValue
	LO_INTERNAL              TagValue
	CIRCA_RFID               TagValue
}

var Tag = &tagValue{
	"VIP",
	"BUYDENTAL",
	"MULTIPLE_DELIVERY_ORDER",
	"ORDER_ASSIST",
	"SPLIT_MULTIPLE_WAREHOUSE",
	"REFUSE_SPLIT_ORDER",
	"NON_INVOICE_CUSTOMER",
	"CUSTOMER_COME_BACK",
	"BRAND_ORDER",
	"NO_DELIVERY_BY_BIN",
	"BRANDGIFT",
	"BRAND_PORTAL",
	"CLINIC_PORTAL",
	"SO_INTERNAL",
	"CO_INTERNAL",
	"LO_INTERNAL",
	"CIRCA_RFID",
}

type SplitTypeValue string

type splitTypeValue struct {
	INSIDE_WAREHOUSE   SplitTypeValue
	MULTIPLE_WAREHOUSE SplitTypeValue
}

var SplitType = &splitTypeValue{
	"INSIDE_WAREHOUSE",
	"MULTIPLE_WAREHOUSE",
}

// SourceValue
type SourceValue string
type source struct {
	THUOCSI_WEB     SourceValue
	THUOCSI_MOBILE  SourceValue
	THUOCSI_LITE    SourceValue
	BUYDENTAL_WEB   SourceValue
	THUOCSI_PORTAL  SourceValue
	BRAND_PORTAL    SourceValue
	CLINIC_PORTAL   SourceValue
	INTERNAL_PORTAL SourceValue
}

var Source = &source{
	THUOCSI_WEB:     "thuocsi-web",
	THUOCSI_MOBILE:  "thuocsi-mobile",
	THUOCSI_LITE:    "thuocsi-lite",
	BUYDENTAL_WEB:   "buydental-web",
	THUOCSI_PORTAL:  "thuocsi-portal",
	BRAND_PORTAL:    "brand-portal",
	CLINIC_PORTAL:   "clinic-portal",
	INTERNAL_PORTAL: "internal-portal",
}

var SourceValueMap = map[SourceValue]string{
	Source.BRAND_PORTAL:  "BRAND_PORTAL",
	Source.CLINIC_PORTAL: "CLINIC_PORTAL",
}

type PartnerPaymentStatusValue string
type partnerPaymentStatusValue struct {
	WAIT_TO_PAY PartnerPaymentStatusValue
	PAID        PartnerPaymentStatusValue
}

// PartnerPaymentStatusValue mapping
var PartnerPaymentStatus = &partnerPaymentStatusValue{
	WAIT_TO_PAY: "WAIT_TO_PAY",
	PAID:        "PAID",
}

type InternalOrderTypeValue string
type InternalOrderTypeEnt struct {
	CO_INTERNAL InternalOrderTypeValue
	SO_INTERNAL InternalOrderTypeValue
	LO_INTERNAL InternalOrderTypeValue
}

var InternalOrderType = &InternalOrderTypeEnt{
	CO_INTERNAL: "CO_INTERNAL",
	SO_INTERNAL: "SO_INTERNAL",
	LO_INTERNAL: "LO_INTERNAL",
}
