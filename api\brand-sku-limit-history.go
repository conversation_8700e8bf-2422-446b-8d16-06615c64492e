package api

import (
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
)

func GetBrandSkuLimitHistoryList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		customerID = sdk.ParseInt64(req.GetParam("customerId"), 0)
		skuCodes   = req.GetParam("skuCodes")
		source     = req.GetParam("source")
	)

	skus := make([]string, 0)
	if len(skuCodes) > 0 {
		skus = strings.Split(skuCodes, ",")
	}
	return resp.Respond(action.GetBrandSkuLimitHistoryList(customerID, skus, source))
}
