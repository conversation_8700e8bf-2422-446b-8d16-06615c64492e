package enum

type ReturnTypeValue string
type ReturnTypeEnt struct {
	NORMAL  ReturnTypeValue
	DAMAGE  ReturnTypeValue
	MISSING ReturnTypeValue
	EMPTY   ReturnTypeValue
}

var ReturnType = &ReturnTypeEnt{
	"NORMAL",
	"DAMAGE",
	"MISSING",
	"",
}

type ProductReturnTypeValue string
type ProductReturnTypeEnt struct {
	MISSING ProductReturnTypeValue
	NORMAL  ProductReturnTypeValue
}

var ProductReturnType = &ProductReturnTypeEnt{
	"MISSING",
	"NORMAL",
}
