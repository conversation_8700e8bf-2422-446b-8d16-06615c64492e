package reconcile_action

import (
	"fmt"
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func ResyncCompletedReconciliation(input *model.ResyncCompletedReconciliationReq) *common.APIResponse {
	reconciliations := make([]*model.Reconciliation, 0)
	for offset, limit := int64(0), int64(1000); ; offset += limit {
		reconciliationRes := model.ReconciliationDB.Query(bson.M{
			"reconciled_time": bson.M{
				"$gt": input.ReconciledTimeFrom,
				"$lt": input.ReconciledTimeTo,
			},
			"reconciliation_status": "COMPLETED",
		}, offset, limit, &bson.M{"_id": -1})
		if reconciliationRes.Status == common.APIStatus.NotFound {
			break
		}
		if reconciliationRes.Status != common.APIStatus.Ok {
			log.Printf("reconciliationRes: %#v", reconciliationRes)
			continue
		}
		reconciliations = append(reconciliations, reconciliationRes.Data.([]*model.Reconciliation)...)
	}

	fmt.Println("START ResyncReconciliation")
	for _, reconciliation := range reconciliations {
		completedTime := time.Now()
		if reconciliation.ReconciledTime != nil {
			completedTime = *reconciliation.ReconciledTime
		}

		orderIDsRes := model.ReconciliationItemDB.Distinct(
			model.ReconciliationItem{
				SellerCode:                 reconciliation.SellerCode,
				ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
				OperationAnd: []bson.M{
					{"order_id": bson.M{"$exists": true}},
				},
			},
			"order_id",
		)
		if orderIDsRes.Status != common.APIStatus.Ok {
			log.Printf("orderIDsRes: %#v", orderIDsRes)
			continue
		}
		orderIDsResData := orderIDsRes.Data.([]interface{})
		orderIDs := make([]int64, len(orderIDsResData))
		for i, v := range orderIDsResData {
			orderIDs[i] = v.(int64)
		}

		SendInvoiceForReconciledOrder(orderIDs, &completedTime)
	}
	fmt.Println("END ResyncReconciliation")

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: fmt.Sprintf("ResyncCompletedReconciliation completed with %d reconciliations", len(reconciliations)),
		Data:    reconciliations,
	}
}
