package client

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathSendEmailWithPdf = "/integration/pdf-processor/v1/order"
)

type pdfClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewPdfServiceClient ...
func NewPdfServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *pdfClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	pdfClient := &pdfClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	pdfClient.svc.SetDBLog(session)
	return pdfClient
}

func (cli *pdfClient) SendEmailWithPdf(in *model.OrderNotDeliveryRequest) error {
	params := map[string]string{}
	_, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathSendEmailWithPdf, nil)
	if err != nil {
		return err
	}
	return nil
}
