package reconcile_action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func UpdateReconciliationItem(input *model.ReconciliationItem, as *model.ActionSource) *common.APIResponse {
	reconciliationItemF := model.ReconciliationItem{ID: input.ID}
	result := model.ReconciliationItemDB.QueryOne(reconciliationItemF)
	if result.Status != common.APIStatus.Ok {
		return result
	}
	rItem := result.Data.([]*model.ReconciliationItem)[0]

	reconciliationF := model.Reconciliation{
		SellerCode:                 rItem.SellerCode,
		ReconcileScheduleTimeIndex: rItem.ReconcileScheduleTimeIndex,
	}
	result = model.ReconciliationDB.QueryOne(reconciliationF)
	if result.Status != common.APIStatus.Ok {
		return result
	}
	reconciliation := result.Data.([]*model.Reconciliation)[0]

	if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      result.Data,
			Message:   "Trạng thái đối soát không phù hợp để chính sửa",
			ErrorCode: "INVALID_RECONCILIATION_STATUS",
		}
	}

	input.CreatedTime = nil
	input.LastUpdatedTime = nil
	input.OperationOr = nil
	input.OperationAnd = nil
	result = model.ReconciliationItemDB.UpdateOne(reconciliationItemF, input)
	return result
}

func UpdateReconciliation(input *model.Reconciliation) *common.APIResponse {
	if input.RecCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      nil,
			Message:   "Missing RecCode",
			ErrorCode: "INVALID_RECONCILIATION_REC_CODE",
		}
	}

	// query check exist
	qCheck := model.ReconciliationDB.QueryOne(model.Reconciliation{
		RecCode: input.RecCode,
	})
	if qCheck.Status != common.APIStatus.Ok {
		return qCheck
	}

	result := model.ReconciliationDB.UpdateOne(
		model.Reconciliation{RecCode: input.RecCode},
		model.Reconciliation{
			CreatedTransferRequestCode: input.CreatedTransferRequestCode,
			ReconciliationStatus:       input.ReconciliationStatus,
			ReconciledTime:             input.ReconciledTime,
			SessionPayment:             input.SessionPayment,
		},
	)
	return result
}

func CallbackTransferRequestHandler(transferRequest *model.TransferRequest) *common.APIResponse {
	if len(transferRequest.Items) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Items is required",
		}
	}

	for _, item := range transferRequest.Items {

		if item.RelateCode == "" {
			continue
		}

		if transferRequest.Type == string(model.TransferRequestType.THUOCSIVN_SELLER_RECONCILE) {
			// revoke reconciliation
			if transferRequest.Status == "REVOKE" || item.Status == "FAILED" {
				revokeTransferRequestReconciliation(item)

			} else if item.TransactionCode != "" && item.Status == "SUCCESS" { // update reconciliation
				timePaid := time.Now()
				sellerReconciliationUpdate := &model.Reconciliation{
					RecCode:              item.RelateCode,
					ReconciliationStatus: model.ReconciliationStatus.Completed,
					ReconciledTime:       &timePaid,
					SessionPayment:       &item.TransactionCode,
				}

				// call update reconciliation
				UpdateReconciliation(sellerReconciliationUpdate)
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
		Data:    transferRequest,
	}
}

func revokeTransferRequestReconciliation(transferRequestItem *model.TransferRequestItem) {
	// query reconciliation exist
	qCheck := model.ReconciliationDB.QueryOne(model.Reconciliation{
		RecCode: transferRequestItem.RelateCode,
	})
	if qCheck.Status != common.APIStatus.Ok {
		return
	}

	emptyString := ""
	model.ReconciliationDB.UpdateOne(
		model.Reconciliation{RecCode: transferRequestItem.RelateCode},
		model.Reconciliation{
			CreatedTransferRequestCode: &emptyString,
		},
	)
}
