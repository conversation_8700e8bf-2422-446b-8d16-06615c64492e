package enum

type DebtStatusValue string
type debtStatus struct {
	WITHIN_LIMIT   DebtStatusValue // trong hạn mức [0; 90%)
	CLOSE_TO_LIMIT DebtStatusValue // gần hạn mức [90%; 100%)
	OVER_LIMIT     DebtStatusValue // vượt hạn mức >= 100%
}

// DebtStatus mapping
var DebtStatus = &debtStatus{
	WITHIN_LIMIT:   "WITHIN_LIMIT",
	CLOSE_TO_LIMIT: "CLOSE_TO_LIMIT",
	OVER_LIMIT:     "OVER_LIMIT",
}

type RefundOrderInputTypeValue string

type refundOrderInputType struct {
	EDIT_ORDER          RefundOrderInputTypeValue // KH chỉnh sửa đơn hàng
	EDIT_ORDER_INTERNAL RefundOrderInputTypeValue // NV chỉnh sửa đơn hàng trên hệ thống internal
	DELIVERED           RefundOrderInputTypeValue // <PERSON>iao hàng thành công
	CANCEL              RefundOrderInputTypeValue // Hủy đơn hàng trước khi outbound
}

// RefundOrderInputType mapping
var RefundOrderInputType = &refundOrderInputType{
	EDIT_ORDER:          "EDIT_ORDER",
	EDIT_ORDER_INTERNAL: "EDIT_ORDER_INTERNAL",
	DELIVERED:           "DELIVERED",
	CANCEL:              "CANCEL",
}

type ContractStatusValue string
type contractStatus struct {
	ACTIVE   ContractStatusValue
	INACTIVE ContractStatusValue
}

// ContractStatus mapping
var ContractStatus = &contractStatus{
	ACTIVE:   "ACTIVE",
	INACTIVE: "INACTIVE",
}

type ContractTypeValue string

type contractType struct {
	Debt       ContractTypeValue
	Rebate     ContractTypeValue
	DebtRebate ContractTypeValue
}

var ContractType = &contractType{
	Debt:       "DEBT",
	Rebate:     "REBATE",
	DebtRebate: "DEBT_REBATE",
}