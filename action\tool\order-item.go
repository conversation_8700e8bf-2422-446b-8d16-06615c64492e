package tool

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func MigrateOrderItemVoucher(orderID int64) *common.APIResponse {
	orderQuery := model.Order{OrderID: orderID}

	orderRes := model.OrderDB.QueryOne(orderQuery)
	if orderRes == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Order is nil",
		}
	}

	order := orderRes.Data.([]*model.Order)[0]

	if order == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Order is nil",
		}
	}

	mapItemDiscount := make(map[string][]string, 0)
	for _, redeem := range order.RedeemApplyResult {
		if _, ok := mapItemDiscount[redeem.Code]; !ok {
			mapItemDiscount[redeem.Code] = redeem.SellerCodes
		}
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(&orderQuery, "migrate_discount_detail")
	if orderItemPartitionDB == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "OrderItemPartitionDB is nil",
		}
	}

	query := model.OrderItem{OrderID: orderID}

	res := orderItemPartitionDB.Query(query, 0, 0, nil)
	if res == nil || res.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "OrderItemPartitionDB.Query is nil",
		}
	}

	orderItems := res.Data.([]*model.OrderItem)

	for _, orderItem := range orderItems {
		if orderItem.DiscountDetail != nil {
			if len(orderItem.DiscountDetail.VoucherDetails) > 0 {
				newVoucherDetails := make([]*model.VoucherDetail, 0)
				for _, vd := range orderItem.DiscountDetail.VoucherDetails {
					newVoucherDetail := &model.VoucherDetail{
						VoucherCode:   vd.VoucherCode,
						DiscountValue: vd.DiscountValue,
						IsApply:       vd.IsApply,
						SellerCodes:   vd.SellerCodes,
					}
					if data, ok := mapItemDiscount[vd.VoucherCode]; ok {
						newVoucherDetail.SellerCodes = data
					}
					newVoucherDetails = append(newVoucherDetails, newVoucherDetail)
				}
				orderItem.DiscountDetail.VoucherDetails = newVoucherDetails
			}
			orderItemPartitionDB.UpdateOne(model.OrderItem{ID: orderItem.ID}, bson.M{"discount_detail": orderItem.DiscountDetail})
		}
	}

	res.Data = orderItems

	return res
}
