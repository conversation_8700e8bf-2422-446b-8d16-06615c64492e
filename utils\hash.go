package utils

import (
	"crypto/sha256"
	"encoding/hex"
)

// hashSHA256 hashes the input string and returns the hexadecimal representation of the hash
func HashSHA256(input string) string {
	hash := sha256.New()
	hash.Write([]byte(input))
	return hex.EncodeToString(hash.Sum(nil))
}

// verifySHA256 checks if a given hash corresponds to the input data
func VerifySHA256(input, hash string) bool {
	return HashSHA256(input) == hash
}
