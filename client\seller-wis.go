package client

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetInbound   = "/seller/wis/v1/inbounds"
	pathGetSkuDemand = "/seller/wis/v1/seller/demand"
)

type sellerWisClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewProductServiceClient ...
func NewSellerWisClient(apiHost, apiKey, logName string, session *mongo.Database) *sellerWisClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}

	sellerWisClient := &sellerWisClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": api<PERSON>ey,
		},
	}
	sellerWisClient.svc.SetDBLog(session)
	return sellerWisClient
}

func (cli *sellerWisClient) GetLatestInbounds(
	sellerCode, skuCode string,
	warehouseId int64,
) []*InboundTicket {
	q := map[string]interface{}{
		"warehouseId": warehouseId,
	}

	qBytes, err := json.Marshal(q)
	if err != nil {
		return nil
	}

	params := map[string]string{
		"q":          string(qBytes),
		"sellerCode": sellerCode,
		"sku":        skuCode,
		"sort":       "-checkInTime",
		"limit":      strconv.Itoa(20),
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetInbound, nil)
	if err != nil {
		fmt.Println("GetInbound failed: " + err.Error())
		return nil
	}

	var inbRes *InboundTicketRes
	err = json.Unmarshal([]byte(result.Body), &inbRes)
	if err != nil {
		fmt.Println("GetInbound failed: " + err.Error())
		return nil
	}

	if inbRes == nil || inbRes.Status != common.APIStatus.Ok {
		return nil
	}

	return inbRes.Data
}

func (cli *sellerWisClient) GetSkuDemand(
	skuCode,
	warehouseCode string,
) *StockQuantity {
	params := map[string]string{
		"skuCode":       skuCode,
		"warehouseCode": warehouseCode,
	}

	keys := []string{skuCode, warehouseCode}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSkuDemand, &keys)
	if err != nil {
		fmt.Println("GetSkuDemand failed: " + err.Error())
		return nil
	}

	var inbRes *StockQuantityRes
	err = json.Unmarshal([]byte(result.Body), &inbRes)
	if err != nil {
		fmt.Println("GetInbound failed: " + err.Error())
		return nil
	}

	if inbRes == nil || inbRes.Status != common.APIStatus.Ok {
		return nil
	}

	data := inbRes.Data

	return data[0]
}
