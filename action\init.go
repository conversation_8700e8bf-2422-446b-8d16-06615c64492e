package action

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

// TODO: Store this in db
var NEED_CREATE_PM_BANK_ACCOUNTS = []string{
	"*********",
}

var mapWard = map[string]client.Ward{}
var mapSplitOrderDistrictCode = map[string]string{}
var mapConfigWardSplitWebMobile = map[string]struct{}{}
var mapConfigDistricsSplitWebMobile = map[string]struct{}{}
var mapConfigProvinceSplitWebMobile = map[string]struct{}{}
var customerComebackDays = 0
var mapProvinceExtraFee = map[string]struct {
	MinPrice                int `json:"minPrice,omitempty"`
	ExtraFee                int `json:"extraFee,omitempty"`
	MaxPriceAppliedExtraFee int `json:"maxPriceAppliedExtraFee,omitempty"` // dưới giá này đơn hàng sẽ bị thu phụ phí
}{}

var mapLevelExtraFee = map[string]model.ExtraFeeUse{}

var mapCustomerIDCrossProvinces = map[int64]struct{}{}

var mapCustomerTagCheckoutOtherMethod = map[string]struct{}{}
var mapCustomerIdTagCheckoutOtherMethod = map[int64]struct{}{}

var mapCustomerTagCannotAutoConfirmDebtOrder = map[string]struct{}{}
var mapCustomerIdCannotAutoConfirmDebtOrder = map[int64]struct{}{}

var mapBrandPortalConfig = struct {
	MinPrice int `json:"minPrice,omitempty"`
}{}

var mapPaymentMethodName = map[string]string{}

var deliverySecondsByLocationCode = sdk.NewLCacheRefreshMode(1000, 5*60, false)

var mapPaymentMethodConfig = map[string]*client.PaymentFeeConfig{}

var customerSKUs = map[string]*model.CustomerSKUItem{}

var customerSKUConfigCache = struct {
	IsActive   bool  `json:"isActive,omitempty"`
	WarmupTime int64 `json:"warmupTime,omitempty"`
}{}

var syncCustomerTagForWarehouseConfig = []string{}

var sourcePreventSendTagToWarehouseConfig = make(map[string][]string) // prevent send tag

var kiotvietConfigs = model.PartnerKiotvietConfigs{}

var preventSendTagDoNotDeliveryBinConfig = []string{}

var productTagsConfig = []string{}

func isBrandOrClinic(source *enum.SourceValue) bool {
	return source != nil && (*source == enum.Source.BRAND_PORTAL || *source == enum.Source.CLINIC_PORTAL)
}

func isClinicPortal(source *enum.SourceValue) bool {
	return source != nil && *source == enum.Source.CLINIC_PORTAL
}

func isBrandPortal(source *enum.SourceValue) bool {
	return source != nil && *source == enum.Source.BRAND_PORTAL
}

func notBrandOrClinic(source *enum.SourceValue) bool {
	return source == nil || *source != enum.Source.BRAND_PORTAL && *source != enum.Source.CLINIC_PORTAL
}

func parserQ(q string) string {
	q = strings.Replace(utils.NormalizeString(q), " ", "-", -1)
	r, _ := regexp.Compile(`(\\W)`)
	qCheck := make(map[string]int)
	for i, v := range r.FindAllString(q, -1) {
		if v == "-" || qCheck[v] > 0 {
			continue
		}
		qCheck[v] = i + 1
		q = strings.ReplaceAll(q, v, `\`+v)
	}
	return q
}

func getCustomerProfile(acc *model.Account) (*model.Customer, *common.APIResponse) {
	if acc.AccountID <= 0 {
		return nil, AccountError
	}

	customer, err := client.Services.Customer.GetCustomerByAccountID(acc.AccountID, acc.Type)
	if err != nil {
		return nil, CustomerError
	}
	if customer == nil {
		return nil, CustomerNotFoundError
	}

	if customer.ProvinceCode == "" {
		return nil, CustomerProvinceError
	}
	if customer.Level == "" {
		return nil, CustomerLevelError
	}
	customer.CertificateMap = map[enum.CustomerCertificateValue]bool{}
	if customer.ExaminationAndTreatmentLicense != nil && len(*customer.ExaminationAndTreatmentLicense) > 0 {
		customer.CertificateMap[enum.CustomerCertificateType.BusinessLicense] = true
	}
	if customer.Gpp != nil && len(*customer.Gpp) > 0 {
		customer.CertificateMap[enum.CustomerCertificateType.GPPCertificate] = true
	}
	if customer.PharmacyEligibilityLicense != nil && len(*customer.PharmacyEligibilityLicense) > 0 {
		customer.CertificateMap[enum.CustomerCertificateType.BusinessCertificate] = true
	}
	if customer.Gdp != nil && len(*customer.Gdp) > 0 {
		customer.CertificateMap[enum.CustomerCertificateType.GDPCertificate] = true
	}
	if customer.Gsp != nil && len(*customer.Gsp) > 0 {
		customer.CertificateMap[enum.CustomerCertificateType.GSPCertificate] = true
	}
	if customer.Licenses != nil && len(*customer.Licenses) > 0 {
		customer.CertificateMap[enum.CustomerCertificateType.BasicBusinessLicense] = true
	}
	if customer.TagMap == nil && len(customer.Tags) > 0 {
		customer.TagMap = make(map[string]bool)
		for _, tag := range customer.Tags {
			customer.TagMap[tag] = true
		}
	}
	return customer, nil
}

func getCustomerProfileByCustomerID(customerID int64) (*model.Customer, *common.APIResponse) {
	if customerID <= 0 {
		return nil, AccountError
	}
	customer, err := client.Services.Customer.GetCustomerByCustomerID(customerID)
	if err != nil {
		return nil, CustomerError
	}
	if customer == nil {
		return nil, CustomerNotFoundError
	}

	if customer.ProvinceCode == "" {
		return nil, CustomerProvinceError
	}
	if customer.Level == "" {
		return nil, CustomerLevelError
	}
	// customer.CertificateMap = map[enum.CustomerCertificateValue]bool{}
	// if customer.ExaminationAndTreatmentLicense != nil && len(*customer.ExaminationAndTreatmentLicense) > 0 {
	// 	customer.CertificateMap[enum.CustomerCertificateType.BusinessLicense] = true
	// }
	// if customer.Gpp != nil && len(*customer.Gpp) > 0 {
	// 	customer.CertificateMap[enum.CustomerCertificateType.GPPCertificate] = true
	// 	customer.CertificateMap[enum.CustomerCertificateType.GDP_GPP_GSPCertificate] = true
	// }
	// if customer.PharmacyEligibilityLicense != nil && len(*customer.PharmacyEligibilityLicense) > 0 {
	// 	customer.CertificateMap[enum.CustomerCertificateType.BusinessCertificate] = true
	// }
	// if customer.Gdp != nil && len(*customer.Gdp) > 0 {
	// 	customer.CertificateMap[enum.CustomerCertificateType.GDPCertificate] = true
	// }
	// if customer.Gsp != nil && len(*customer.Gsp) > 0 {
	// 	customer.CertificateMap[enum.CustomerCertificateType.GSPCertificate] = true
	// }
	if customer.TagMap == nil && len(customer.Tags) > 0 {
		customer.TagMap = make(map[string]bool)
		for _, tag := range customer.Tags {
			customer.TagMap[tag] = true
		}
	}
	return customer, nil
}

func getSellerInfo(account *model.Account) (*client.Seller, *common.APIResponse) {
	if account.AccountID <= 0 {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing account id",
			ErrorCode: "ACCOUNT_ID_INVALID",
		}
	}
	seller, err := client.Services.Seller.GetSellerByAccountID(account.AccountID)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "GET_SELLER_INFO",
		}
	}
	if seller == nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Message:   "Vui lòng kiểm tra lại đăng nhập",
			ErrorCode: "AUTH_MISSING",
		}
	}

	return seller, nil
}

// InitAllConsume ...
func InitAllConsume() {
	initConfirmOrderJob()

}

// initConfirmOrderJob is func set topic to consume data order confirm
// scope: job
/*
	Input: none
	How to use: this is func private initConfirmOrderJob()
*/
func initConfirmOrderJob() {
	model.JobExecutor.SetTopicConsumer(TopicConfirmOrder, func(item *job.JobItem) error {
		data, err := bson.Marshal(item.Data)
		if err != nil {
			return nil
		}
		var input model.ConfirmOrderData
		err = bson.Unmarshal(data, &input)
		if err != nil {
			return nil
		}
		qResult := model.OrderDB.QueryOne(&model.Order{
			OrderID:     input.OrderID,
			CreatedTime: input.CreatedTime,
		})
		if qResult.Status != common.APIStatus.Ok {
			return nil
		}
		order := qResult.Data.([]*model.Order)[0]
		if order.Status == enum.OrderState.WaitConfirm {
			res := confirmOrder(order)
			if res.Status != common.APIStatus.Ok {
				return fmt.Errorf(res.Message)
			}
		}
		return nil
	})

	//model.JobExecutor.SetTopicConsumer(TopicCompleteOrder, )
}

func UpdateDeliveryConsumer(item *job.JobItem) (returnErr error) {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		returnErr = err
		return returnErr
	}

	var input *model.RequestUpdateDeliverStatus
	returnErr = bson.Unmarshal(data, &input)
	if returnErr != nil {
		return returnErr
	}

	func() {
		defer func() {
			if r := recover(); r != nil {
				returnErr = fmt.Errorf("panic: %s - %s", r, string(debug.Stack()))
				fmt.Printf("[%d - %s] %s\n", input.OrderID, input.Status, returnErr.Error())
			}
		}()

		response := UpdateProcessingStatus2(input)
		if response.Status != common.APIStatus.Ok {
			returnErr = fmt.Errorf("UpdateProcessingStatus: %s", response.Message)
			return
		}

		orderSellerRes := client.Services.SellerMis.UpdateDeliveryOrderSeller(input.OrderID)
		if orderSellerRes.Status != common.APIStatus.Ok {
			returnErr = fmt.Errorf("UpdateOrderSeller: %s", orderSellerRes.Message)
			return
		}
	}()

	time.Sleep(5 * time.Second)

	return returnErr
}

func CompleteOrderConsumer(item *job.JobItem) (err error) {
	if item.FailCount > 1 {
		keys := []string{}
		if item.Keys != nil {
			keys = *item.Keys
		}
		ready := time.Now().Add(30 * time.Minute)
		return model.CompleteOrderJobExecutor.Push(item.Data, &job.JobItemMetadata{
			Keys:      keys,
			Topic:     "default",
			ReadyTime: &ready,
		})
	}

	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input *model.Order
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}

	model.NotifyInvoiceCompleteOrderJob.Push(model.Order{
		OrderID:       input.OrderID,
		SaleOrderCode: input.SaleOrderCode,
	}, &job.JobItemMetadata{
		Keys: []string{input.SaleOrderCode, fmt.Sprintf("%d", input.OrderID)},
	})

	if input.ProcessingStatus != "" {
		return nil // skip old order
	}

	if input.DeliveryDate == nil && input.OrderID < 500000 {
		return nil
	} else if input.DeliveryDate == nil {
		return fmt.Errorf("delivery_date is nil")
	}

	return processOrderPoint(input.OrderID)
}

func processOrderPoint(orderID int64) error {
	qResult := model.OrderDB.QueryOne(&model.Order{
		OrderID: orderID,
	})
	if qResult.Status != common.APIStatus.Ok {
		return fmt.Errorf("%s: %s", strconv.Itoa(int(orderID)), qResult.Message)
	}
	order := qResult.Data.([]*model.Order)[0]

	// Ignore with BRAND_SALES
	if isBrandOrClinic(order.Source) {
		return nil
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "processOrderPoint")
	if orderItemPartitionDB == nil {
		return errors.New(model.PARTITION_NOT_FOUND_RESPONSE.Message)
	}

	if order.ProcessingStatus != "" && order.ProcessingStatus != enum.OrderState.Completed {
		return fmt.Errorf("order still wait update processing-status with status = COMPLETED")
	}

	orderItemsQueryResponse := orderItemPartitionDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
	if orderItemsQueryResponse.Status != common.APIStatus.Ok {
		return nil
	}
	orderItems := orderItemsQueryResponse.Data.([]*model.OrderItem)

	point := 0.0
	for idx, item := range orderItems {
		// item must to have complete qty
		if item.CompletedQuantity != nil && item.ActualPrice != nil {
			// prepare
			subPoint, curPoint, curMul, qty := 0.0, float64(0), float64(item.PointMultiplier), float64(*item.CompletedQuantity)
			if curMul <= 0 {
				curMul = 1
			}

			if item.Point > 0 {
				curPoint = float64(item.Point) * qty

			} else {
				// calc point again
				curPoint = float64(*item.ActualPrice) / 100000
			}
			subPoint = ceil(curPoint * curMul)
			orderItems[idx].TotalPoint = subPoint
			point += subPoint
		}

	}

	point = ceil(point)
	_ = client.Services.Customer.UpdatePointAndOrderCount(&client.UpdatePointAndOrderCountRequest{
		CustomerID:    int(order.CustomerID),
		OrderID:       order.OrderID,
		OrderStatus:   enum.OrderState.Completed,
		SaleOrderCode: order.SaleOrderCode,
		Point:         utils.ParseFloat64ToPointer(point),
		Note:          fmt.Sprintf("System increase point +%v with order id %d", point, order.OrderID),
	})

	updater := &model.Order{Point: utils.ParseFloat64ToPointer(point)}

	// caculate accumulated product count
	accumulatedProductCount, err := CalcAccumulateProductForOrderCompleted(order.OrderID, order.CustomerID)
	if err == nil {
		updater.AccumulateProductCount = utils.ParseIntToPointer(accumulatedProductCount)
	}

	qUpdateOrder := model.OrderDB.UpdateOne(&model.Order{OrderID: order.OrderID}, updater)
	if qUpdateOrder.Status == common.APIStatus.Ok {
		for _, item := range orderItems {
			if item.TotalPoint > 0.0 {
				orderItemPartitionDB.UpdateOne(&model.OrderItem{OrderID: order.OrderID, Sku: item.Sku}, &model.OrderItem{TotalPoint: item.TotalPoint})
			}
		}
	}

	return nil
}

func isNilOrDefaultValue(val interface{}) bool {
	switch v := val.(type) {
	case *int:
		if v == nil {
			return true
		} else {
			return *v == 0
		}
	case int:
		return v == 0
	case *float64:
		if v == nil {
			return true
		} else {
			return *v == 0
		}
	case float64:
		return v == 0
	case *int64:
		if v == nil {
			return true
		} else {
			return *v == 0
		}
	case int64:
		return v == 0
	case nil:
		return true
	case string:
		return v == ""
	case *string:
		if v == nil {
			return true
		} else {
			return *v == ""
		}
	}
	return false
}

func normalizePhoneNumber(phone string) string {
	phone = strings.Trim(phone, " ")
	if strings.HasPrefix(phone, "0") {
		phone = "84" + phone[1:]
	} else if strings.HasPrefix(phone, "84") {
		// TODO
	} else {
		return ""
	}

	if len(phone) != 11 {
		return ""
	}
	return phone
}

func CreateInvoiceDraftJob(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input *client.InvoiceRequest
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	if item.FailCount > 3 {
		jobItemMetadata := &job.JobItemMetadata{
			Topic: item.Topic,
		}
		if item.Keys != nil {
			jobItemMetadata.Keys = *item.Keys
		}
		// repush xuống cuối
		err := model.CreateInvoiceDraftJobExecutor.Push(
			item.Data,
			jobItemMetadata,
		)
		if err != nil {
			return err
		}
		return nil
	}
	resp := client.Services.Invoice.CreateInvoiceDraft(&client.InvoiceRequest{OrderID: input.OrderID, OutboundDate: input.OutboundDate, DoCode: input.DoCode})
	if resp.Status != common.APIStatus.Ok {
		return fmt.Errorf(resp.Message)
	}
	return nil
}

func ExportInvoiceJob(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input *client.InvoiceRequest
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	if item.FailCount > 3 {
		jobItemMetadata := &job.JobItemMetadata{
			Topic: item.Topic,
		}
		if item.Keys != nil {
			jobItemMetadata.Keys = *item.Keys
		}
		// repush xuống cuối
		err := model.ExportInvoiceJobExecutor.Push(
			item.Data,
			jobItemMetadata,
		)
		if err != nil {
			return err
		}
		return nil
	}
	resp := client.Services.Invoice.ExportInvoice(input)
	if resp.Status != common.APIStatus.Ok {
		return fmt.Errorf(resp.Message)
	}
	return nil
}

func ReplaceInvoiceJob(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input *client.InvoiceRequest
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	resp := client.Services.Invoice.ReplaceInvoice(&client.InvoiceRequest{
		OrderID: input.OrderID,
		DoCode:  input.DoCode,
	})
	if resp.Status != common.APIStatus.Ok {
		return fmt.Errorf(resp.Message)
	}
	return nil
}

// UpdateInvoiceInfoJob ...
func UpdateInvoiceInfoJob(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input *client.UpdateInvoiceRequest
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}

	if item.FailCount > 10 {
		jobItemMetadata := &job.JobItemMetadata{
			Topic: item.Topic,
		}
		if item.Keys != nil {
			jobItemMetadata.Keys = *item.Keys
		}
		// repush xuống cuối
		err := model.UpdateInvoiceInfoJobExecutor.Push(
			item.Data,
			jobItemMetadata,
		)
		if err != nil {
			return err
		}
		return nil
	}

	resp := client.Services.Invoice.UpdateInvoice(input)
	if resp.Status != common.APIStatus.Ok {
		if resp.ErrorCode == "ERR_UPDATE_INVOICE_MULTIPLE_TIMES" || resp.ErrorCode == "ERR_INVALID_CHANGE_INVOICE_INFO_TIME" {
			return nil
		}
		return fmt.Errorf(resp.Message)
	}

	err = client.Services.Warehouse.UpdateInvoieRequest(&client.WarehouseUpdateInvoiceRequest{
		AdminID:        input.OrderID,
		InvoiceRequest: input.RequestInvoice,
	})
	if err != nil {
		return err
	}
	return nil
}

type syncSkuLimitData struct {
	CustomerID   int64
	AccountID    int64
	Sku          string
	ItemCode     string
	Date         *time.Time
	NumberOfDays int
}

func SyncSkuLimitHistory(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input syncSkuLimitData
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	//now := time.Now()
	//input.Date = &now
	_, errRecount := recountQuantityOrdered(input)
	return errRecount
}

// TODO: improve performance: Use simple query to get order and order_item, then
// calculate the sum. Do not use DB for complex query
func getQuantityOrdered(input syncSkuLimitData) int {
	dateLocal := input.Date.Add(7 * time.Hour)
	input.Date = &dateLocal

	// start = start of the day in VN timezone
	start := time.Date(input.Date.Year(), input.Date.Month(), input.Date.Day(), 17, 0, 0, 0, input.Date.Location())
	start = start.AddDate(0, 0, -1)

	// end = input.Date in VN timezone
	end := input.Date

	queryOrder := bson.M{
		"customer_id": input.CustomerID,
		"$and": []*bson.M{
			{"created_time": bson.M{"$gte": start}},
			{"created_time": bson.M{"$lte": end}},
		},
		"status": bson.M{"$ne": enum.OrderState.Canceled},
	}
	qOrders := model.OrderDB.Query(queryOrder, 0, 0, nil)

	orderIDs := make([]int64, 0)
	orders := []*model.Order{}
	if qOrders.Status == common.APIStatus.Ok {
		orders = qOrders.Data.([]*model.Order)

		for i := range orders {
			order := orders[i]
			orderIDs = append(orderIDs, order.OrderID)
		}
	}

	query := bson.M{
		// "type": enum.ItemType.NORMAL,
		//"customer_id":       input.CustomerID,
		"item_code": input.ItemCode,
		"order_id":  bson.M{"$in": orderIDs},
		"$and": []*bson.M{
			{"created_time": bson.M{"$gte": start}},
			{"created_time": bson.M{"$lte": end}},
		},
	}
	listOrderItemDB := make(map[string]*db.Instance, 0)

	if len(orders) > 0 {
		months, _ := ListMonthByListOrder(orders)
		for _, date := range months {
			orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{CreatedTime: &date}, "recountQuantityOrdered")
			listOrderItemDB[orderItemPartitionDB.ColName] = orderItemPartitionDB
		}
	}

	type countData struct {
		Quantity   int    `json:"quantity" bson:"quantity"`
		ItemCode   string `json:"itemCode" bson:"item_code"`
		CustomerId int    `json:"customerId" bson:"_id"`
	}
	quantityUpdate := 0
	for _, db := range listOrderItemDB {
		var countDatas []*countData
		db.Aggregate([]primitive.M{
			{"$match": query},
			{"$group": primitive.M{
				"_id": "$customer_id",
				"quantity": primitive.M{
					"$sum": "$quantity",
				},
			}},
		}, &countDatas)
		if len(countDatas) > 0 {
			count := countDatas[0]
			quantityUpdate = quantityUpdate + count.Quantity
		}
	}

	return quantityUpdate
}

func recountQuantityOrdered(input syncSkuLimitData) (int, error) {
	quantityUpdate := getQuantityOrdered(input)

	query := &model.SkuLimitHistory{
		CustomerID: int64(input.CustomerID),
		ComplexQuery: []*bson.M{
			{"item_code": input.ItemCode},
			{"start_time": bson.M{"$lte": input.Date}},
			{"end_time": bson.M{"$gte": input.Date}},
		},
	}

	qLog := model.SkuLimitHistoryDB.QueryOne(query)
	if qLog.Status == common.APIStatus.Ok {
		go model.SkuLimitHistoryDB.UpdateOne(
			query,
			&model.SkuLimitHistory{Quantity: utils.ParseIntToPointer(quantityUpdate)},
		)

	} else {
		newLog := &model.SkuLimitHistory{
			Code:       model.GenCodeWithTime(),
			CustomerID: input.CustomerID,
			AccountID:  input.AccountID,
			Quantity:   utils.ParseIntToPointer(quantityUpdate),
			Sku:        input.Sku,
			ItemCode:   input.ItemCode,

			NumberOfDays: 1, // TODO: skuLimit is hardcode to 1 day. Need update
		}
		newLog.SetKey()

		now := time.Now().Add(time.Hour * 7)

		start := time.Date(now.Year(), now.Month(), now.Day(), 17, 0, 0, 0, now.Location())
		start = start.AddDate(0, 0, -1)
		newLog.StartTime = &start

		end := start.AddDate(0, 0, newLog.NumberOfDays)
		newLog.EndTime = &end

		go model.SkuLimitHistoryDB.Create(newLog)
	}

	return quantityUpdate, nil
}

func GetStartDayOfWeek(tm time.Time) time.Time { //get monday 00:00:00
	weekday := time.Duration(tm.Weekday())
	if weekday == 0 {
		weekday = 7
	}
	year, month, day := tm.Date()
	currentZeroDay := time.Date(year, month, day, 0, 0, 0, 0, time.Local)
	return currentZeroDay.Add(-1 * (weekday - 1) * 24 * time.Hour)
}

func calVolume(width, height, length float64) float64 {
	return width * height * length
}

func ceil(val float64) float64 {
	if val <= 0 {
		return 0.0
	}

	return float64(int(val*100)) / 100
}

func CalcTransferingDifferenceJob(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input model.CalcTransferingDifferenceRequest
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	resp := client.Services.Invoice.CalcTransferingDifference(input)
	if resp.Status != common.APIStatus.Ok {
		return fmt.Errorf(resp.Message)
	}
	return nil
}

type createSaleOrderData struct {
	OrderID       int64  `json:"orderId" bson:"order_id"`
	SenderName    string `json:"senderName" bson:"sender_name"`
	TransactionId string `json:"transactionId,omitempty" bson:"transaction_id,omitempty"` // FT code

	MasterAccountNumber string `json:"masterAccountNumber,omitempty" bson:"master_account_number,omitempty"` // Master account number
}

func CreateSaleOrderConsumer(item *job.JobItem) (err error) {
	// * read & unmarshal data
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input createSaleOrderData
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}

	note := ""
	isNotify := false
	defer func() {
		if len(note) > 0 && isNotify {
			model.OrderDB.UpdateOne(model.Order{OrderID: input.OrderID}, model.Order{CreateSaleOrderNote: utils.ParseStringToPointer(note)})
			go func() {
				_ = botNotifySyncFailTele(input.OrderID, note)
			}()
		}
		if item.FailCount > 20 {
			go func() {
				_ = model.CreateSaleOrderFailJob.Push(createSaleOrderData{OrderID: input.OrderID, TransactionId: input.TransactionId}, &job.JobItemMetadata{
					UniqueKey: fmt.Sprint(input.OrderID),
					Keys: []string{
						strconv.Itoa(int(input.OrderID)),
					},
					SortedKey: fmt.Sprint(input.OrderID),
					Topic:     "create_sale_order_fail",
				})
			}()
		}
	}()

	if item.FailCount > 25 && item.Topic == "default" {
		return nil
	}
	// * check flag
	qFlag := model.FlagDB.QueryOne(&model.Flag{})
	if qFlag.Status == common.APIStatus.Ok {
		flag := qFlag.Data.([]*model.Flag)[0]
		if flag.OrderTestSync != input.OrderID && flag.SyncSaleOrder != nil && *flag.SyncSaleOrder == false {
			note = "Hệ thống đang tắt đồng bộ đơn xuống kho"
			return fmt.Errorf("sync sale order from marketplace is turn off")
		}
	}
	// * query & check order by order id
	order, errRes := getOrder(input.OrderID)
	if errRes != nil && errRes.Status != common.APIStatus.Ok {
		note = "Không tìm thấy đơn hàng"
		return fmt.Errorf("%s_%s", errRes.ErrorCode, errRes.Message)
	}
	if len(order.SaleOrderCode) > 0 {
		note = "Đơn đã được đồng bộ xuống kho"
		return nil
	}
	if order.Status == enum.OrderState.Canceled {
		note = "Đơn hàng đã bị hủy"
		isNotify = order.CreateSaleOrderNote == nil || *order.CreateSaleOrderNote != note
		return nil
	}
	if order.Status != enum.OrderState.Confirmed {
		note = "Chỉ đồng bộ những đơn đã xác nhận, trạng thái hiện tại: " + string(order.Status)
		isNotify = order.CreateSaleOrderNote == nil || *order.CreateSaleOrderNote != note
		return fmt.Errorf("accept only order status = %s, current status = %s", enum.OrderState.Confirmed, order.Status)
	}

	customer, errGetCustomer := client.Services.Customer.GetCustomerByAccountID(order.AccountID, "")
	if errGetCustomer != nil {
		note = "Không tìm thấy thông tin KH"
		isNotify = order.CreateSaleOrderNote == nil || *order.CreateSaleOrderNote != note
		return errGetCustomer
	}
	wardCodes := []string{order.WardCode, customer.WardCode, order.CustomerWardCode}
	wardMap := make(map[string]*client.Ward)
	if wards, errGetWard := client.Services.Location.GetWardList(wardCodes, 0, 0); errGetWard == nil {
		for _, ward := range wards {
			wardMap[ward.Code] = ward
		}
	}
	if customer.DistrictCode == "" || customer.WardCode == "" {
		customer.DistrictCode = order.CustomerDistrictCode
		customer.WardCode = order.CustomerWardCode
		customer.ProvinceCode = order.CustomerProvinceCode
	}

	if len(customer.Tags) > 0 {
		tagInfo := make(map[string]bool)
		for _, tag := range customer.Tags {
			tagInfo[tag] = true
		}

		for _, customerTag := range syncCustomerTagForWarehouseConfig {
			if _, ok := tagInfo[customerTag]; ok {
				order.Tags = append(order.Tags, enum.TagValue(customerTag))

				// check tag CIRCA have payment method is credit then send tag CIRCA_DEBT to warehouse
				if customerTag == "CIRCA" {
					if order.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
						order.Tags = append(order.Tags, enum.TagValue("CIRCA_DEBT"))
					}
				}
			}
		}
	}

	createSaleOrderReq := &client.CreateSaleOrderRequest{
		OrderID:        order.OrderID,
		ReferenceCode:  order.OrderCode,
		Type:           "NORMAL",
		OrderTime:      order.ConfirmationDate,
		DeliveryMethod: order.DeliveryMethod,
		PaymentMethod:  "COD",
		CreateSource:   "MARKETPLACE",
		CustomerInfo: &client.CustomerSaleOrder{
			Name:         customer.Name,
			Code:         customer.CustomerID,
			BusinessName: customer.BusinessName,
			Address:      customer.Address,
			Phone:        customer.Phone,
			Email:        customer.Email,
			Ward:         getWardName(customer.WardCode, wardMap),
			WardCode: func() string {
				if customer.WardCode == "" || customer.WardCode == "0" {
					return ""
				}
				return customer.WardCode
			}(),
			District:     getDistrictName(customer.WardCode, wardMap),
			DistrictCode: customer.DistrictCode,
			TaxCode:      customer.TaxCode,
			Province:     getProvinceName(customer.WardCode, wardMap),
			ProvinceCode: customer.ProvinceCode,
			Delivery: &client.CustomerSaleOrder{
				Name:         order.CustomerName,
				Code:         customer.CustomerID,
				BusinessName: customer.BusinessName,
				Address:      order.CustomerShippingAddress,
				Phone:        order.CustomerPhone,
				Email: func() string {
					if order.CustomerEmail != nil {
						return *order.CustomerEmail
					}
					return ""
				}(),
				Ward: getWardName(order.CustomerWardCode, wardMap),
				WardCode: func() string {
					if order.CustomerWardCode == "" || order.CustomerWardCode == "0" {
						return ""
					}
					return order.CustomerWardCode
				}(),
				District:             getDistrictName(order.CustomerWardCode, wardMap),
				DistrictCode:         order.CustomerDistrictCode,
				TaxCode:              customer.TaxCode,
				Province:             getProvinceName(order.CustomerWardCode, wardMap),
				ProvinceCode:         order.CustomerProvinceCode,
				IsDropOffAtWarehouse: order.IsDropOffAtWarehouse,
			},
			Invoice: &client.CustomerSaleOrder{
				Name:         customer.Name,
				Code:         customer.CustomerID,
				BusinessName: customer.BusinessName,
				Address:      customer.Address,
				Phone:        customer.Phone,
				Email:        customer.Email,
				Ward:         getWardName(customer.WardCode, wardMap),
				WardCode: func() string {
					if customer.WardCode == "" || customer.WardCode == "0" {
						return ""
					}
					return customer.WardCode
				}(),
				District:     getDistrictName(customer.WardCode, wardMap),
				DistrictCode: customer.DistrictCode,
				TaxCode:      customer.TaxCode,
				Province:     getProvinceName(customer.WardCode, wardMap),
				ProvinceCode: customer.ProvinceCode,
			},
		},
		ProvinceCode:       order.ProvinceCode,
		CustomerOrderIndex: order.CustomerOrderIndex,
		Tags:               order.Tags,
		AdditionalTime:     order.AdditionalTime,
	}
	if order.Price != nil {
		createSaleOrderReq.Price = float64(*order.Price)
	}
	if order.TotalPrice != nil {
		createSaleOrderReq.TotalPrice = float64(*order.TotalPrice)
	}
	if order.TotalDiscount != nil {
		createSaleOrderReq.VoucherAmount = float64(*order.TotalDiscount)
	}
	if order.DeliveryMethodFee != nil {
		createSaleOrderReq.DeliveryMethodFee = float64(*order.DeliveryMethodFee)
	}
	if order.PartnerPaymentMethod != nil && order.PartnerPaymentMethod.TotalCustomerFee > 0 {
		createSaleOrderReq.PartnerPaymentMethodFee = order.PartnerPaymentMethod.TotalCustomerFee
	}
	if order.Note != nil {
		createSaleOrderReq.OrderNote = *order.Note
	}
	if order.Source != nil && *order.Source == enum.Source.INTERNAL_PORTAL &&
		order.InternalOrderType != nil {
		switch *order.InternalOrderType {
		case enum.InternalOrderType.SO_INTERNAL:
			createSaleOrderReq.SourceLocation = "WH-CONSIGNMENT"
		case enum.InternalOrderType.LO_INTERNAL:
			createSaleOrderReq.SourceLocation = "WH-LIQUIDATE"
			createSaleOrderReq.Type = "LIQUIDATION"
		}
	}

	//if order.Source != nil {
	//	createSaleOrderReq.CreateSource = *order.Source
	//}
	if order.DeliveryMethodFee != nil {
		createSaleOrderReq.DeliveryMethodFee = float64(*order.DeliveryMethodFee)
	}
	if order.ExtraFee != nil {
		createSaleOrderReq.ExtraFee = float64(*order.ExtraFee)
	}
	if order.TotalDiscount != nil {
		createSaleOrderReq.VoucherAmount = float64(*order.TotalDiscount)
	}
	if order.PaymentMethodFee != nil {
		createSaleOrderReq.PaymentDiscountAmount = -float64(*order.PaymentMethodFee)
	}
	if IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, order.PaymentMethod) || order.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
		createSaleOrderReq.PaymentMethod = "BANK"
	}
	if order.Invoice != nil {
		createSaleOrderReq.InvoiceRequest = order.Invoice.RequestInvoice
	}
	skus := make([]string, 0)
	mapItem := make(map[string]*model.OrderItem, 0)
	mapGiftItem := make(map[string]*model.OrderItem, 0)
	for _, item := range order.Items {
		skus = append(skus, item.Sku)
		if item.Type == enum.ItemType.GIFT {
			mapGiftItem[item.Sku] = item
		} else {
			mapItem[item.Sku] = item
		}
		if item.SubItems != nil && len(*item.SubItems) > 0 {
			for _, sub := range *item.SubItems {
				skus = append(skus, sub.Sku)
			}
		}
	}

	isBrandSales := isBrandOrClinic(order.Source)
	skuInfo, errGetSku := client.Services.Product.GetListSku(skus, order.ProvinceCode, order.CustomerID, isBrandSales, "")
	if errGetSku != nil {
		note = "Không thể lấy thông tin sản phẩm trong đơn hàng"
		isNotify = order.CreateSaleOrderNote == nil || *order.CreateSaleOrderNote != note
		return fmt.Errorf(errGetSku.Message)
	}
	skuMapInfo := make(map[string]*client.ProductData)
	for _, sku := range skuInfo {
		skuMapInfo[sku.SKU.Code] = sku
	}
	mapItemProductLink := make(map[string]bool)
	mapItemGiftProductLink := make(map[string]bool)
	if len(order.RedeemApplyResult) > 0 {
		for _, res := range order.RedeemApplyResult {
			if len(res.MatchProducts) > 0 {
				item := client.SaleOrderItem{
					ProductType: "link",
				}
				for _, product := range res.MatchProducts {
					sku := fmt.Sprintf("%s.%s", product.SellerCode, product.ProductCode)
					if data, ok := mapItem[sku]; ok && data != nil {
						subItem := orderItemToSaleOrderItem(data, skuMapInfo)
						subItem.LinkQuantity = res.NumberOfAutoApply
						subItem.LinkItemQuantity = product.MinQuantity
						item.SubItems = append(item.SubItems, &subItem)
						mapItemProductLink[sku] = true
					}
				}

				for _, gift := range res.Gift {
					sku := gift.Sku
					if data, ok := mapGiftItem[sku]; ok && data != nil {
						subItem := orderItemToSaleOrderItem(data, skuMapInfo)

						// If already added this gift SKU, increase qty for existing item
						var existingSubItem *client.SaleOrderItem
						for _, orderLine := range createSaleOrderReq.OrderLines {
							for _, soItem := range orderLine.SubItems {
								if soItem.SKU == sku {
									existingSubItem = soItem
									break
								}
							}
						}
						if existingSubItem != nil {
							existingSubItem.LinkQuantity += res.NumberOfAutoApply
							existingSubItem.Quantity += float64(gift.Quantity)
							existingSubItem.LinkItemQuantity += int(gift.QuantityPerApply)
							continue
						}

						// Else, add to subItems list
						subItem.LinkQuantity = res.NumberOfAutoApply
						subItem.Quantity = float64(gift.Quantity)
						subItem.LinkItemQuantity = int(gift.QuantityPerApply)
						item.SubItems = append(item.SubItems, &subItem)
						mapItemGiftProductLink[sku] = true
					}
				}
				if len(item.SubItems) > 0 {
					createSaleOrderReq.OrderLines = append(createSaleOrderReq.OrderLines, &item)
				}
			}
		}
	}
	for _, item := range order.Items {
		if item.Type == enum.ItemType.GIFT {
			if mapItemGiftProductLink[item.Sku] {
				continue
			}
		} else {
			if mapItemProductLink[item.Sku] {
				continue
			}
		}

		saleOrderItem := orderItemToSaleOrderItem(item, skuMapInfo)
		createSaleOrderReq.OrderLines = append(createSaleOrderReq.OrderLines, &saleOrderItem)
	}
	createSaleOrderRes, errCreateSaleOrder := client.Services.Warehouse.CreateSaleOrder(createSaleOrderReq)
	if errCreateSaleOrder != nil {
		note = "Không thể đồng bộ đơn hàng xuống kho - " + errCreateSaleOrder.Error()
		isNotify = order.CreateSaleOrderNote == nil || *order.CreateSaleOrderNote != note
		return errCreateSaleOrder
	}
	now := time.Now()
	privateNote := fmt.Sprintf("%s \n %v - %s", order.PrivateNote, now, "Tạo thành công đơn hàng "+createSaleOrderRes.SaleOrderCode)
	updateSaleOrderRes := model.OrderDB.UpdateOne(model.Order{OrderID: order.OrderID}, model.Order{SaleOrderCode: createSaleOrderRes.SaleOrderCode, SaleOrderCreatedTime: &now, PrivateNote: privateNote})
	if updateSaleOrderRes.Status != common.APIStatus.Ok {
		note = "Cập nhật đơn hàng không thành công"
		isNotify = order.CreateSaleOrderNote == nil || *order.CreateSaleOrderNote != note
		return fmt.Errorf(updateSaleOrderRes.Message)
	}

	if GetKiotvietConfigs().AllowSyncCreateOrderKiotviet {
		if updateSaleOrderRes.Status == common.APIStatus.Ok {
			if utils.IsContains(order.CustomerTags, "KIOTVIET") {
				go sdk.Execute(func() {
					SyncCreateOrderInfoToKiotvietPartner(order)
				})
			}
		}
	}

	// TODO: Check all sender name
	needCreatePayment := true
	if input.SenderName == "VPB" || input.SenderName == "VPBANK_ADAPTER" {
		// Need create payment if master account number is empty or in list NEED_CREATE_PM_BANK_ACCOUNTS
		needCreatePayment = input.MasterAccountNumber == "" ||
			utils.Contains(input.MasterAccountNumber, NEED_CREATE_PM_BANK_ACCOUNTS)
	}

	if order.TransferAmount != nil && *order.TransferAmount > 0 &&
		needCreatePayment {
		client.Services.Bill.CreatePayment(order.OrderID, int64(*order.TransferAmount), order.CustomerID, createSaleOrderRes.SaleOrderCode, input.SenderName, input.TransactionId)
	}
	return err
}

func orderItemToSaleOrderItem(item *model.OrderItem, skuMapInfo map[string]*client.ProductData) client.SaleOrderItem {
	sku := client.Sku{}
	product := client.Product{}
	if data, ok := skuMapInfo[item.Sku]; ok && data != nil {
		if data.SKU != nil {
			sku = *data.SKU
		}
		if data.Product != nil {
			product = *data.Product
		}
	}
	saleOrderItem := client.SaleOrderItem{
		ProductId:           int(item.ProductID),
		ProductCode:         item.ProductCode,
		SKU:                 item.Sku,
		MarketplaceItemType: item.Type,
		IsImportant:         item.IsImportant != nil && *item.IsImportant,
		ProductName:         product.Name,
		ProductType:         "product",
		SellerCode:          sku.SellerCode,
		Packaging:           product.Volume,
		Weight:              product.Weight,
		Quantity:            float64(item.Quantity),
		UnitPrice:           float64(item.Price),
		IsFragile:           product.IsFragile != nil && *product.IsFragile,
		IsFrozen:            product.IsFrozen != nil && *product.IsFrozen,
		IsNearExp:           item.IsNearExpired,
		Tags:                item.Tags,
	}
	if item.PriceAfterDiscount != nil {
		if *item.PriceAfterDiscount < 0 {
			item.PriceAfterDiscount = utils.ParseIntToPointer(0)
		}
		saleOrderItem.PriceAfterDiscount = float64(*item.PriceAfterDiscount)
	}

	if item.LotDates != nil && len(*item.LotDates) > 0 {
		var details []client.OrderDetailItem
		for _, lot := range *item.LotDates {
			if lot.ExpiredDate == nil {
				continue
			}
			details = append(details, client.OrderDetailItem{
				Lot:      lot.Lot,
				ExpDate:  *lot.ExpiredDate,
				Quantity: int64(lot.Quantity),
			})
		}
		saleOrderItem.Details = details
	}

	if item.VAT != nil {
		saleOrderItem.RateTax = int64(*item.VAT)
	}
	if product.ImageUrls != nil && len(*product.ImageUrls) > 0 {
		images := *product.ImageUrls
		saleOrderItem.ImageURL = images[0]
	}
	if item.VAT != nil {
		saleOrderItem.VatAmount = *item.VAT
	}
	if item.SubItems != nil && len(*item.SubItems) > 0 {
		saleOrderItem.ProductType = "combo"
		for _, sub := range *item.SubItems {
			subSaleOrderItem := orderItemToSaleOrderItem(sub, skuMapInfo)
			saleOrderItem.SubItems = append(saleOrderItem.SubItems, &subSaleOrderItem)
		}
	}
	return saleOrderItem
}

func getWardName(code string, wardMap map[string]*client.Ward) string {
	if ward, ok := wardMap[code]; ok && ward != nil {
		return ward.Name
	}
	return ""
}

func getDistrictName(code string, wardMap map[string]*client.Ward) string {
	if ward, ok := wardMap[code]; ok && ward != nil {
		return ward.DistrictName
	}
	return ""
}

func getProvinceName(code string, wardMap map[string]*client.Ward) string {
	if ward, ok := wardMap[code]; ok && ward != nil {
		return ward.ProvinceName
	}
	return ""
}

func getItemProductLink() client.SaleOrderItem {
	item := client.SaleOrderItem{}

	return item
}

func botNotifySyncFailTele(orderId int64, failReason string) error {
	bot, err := tgbotapi.NewBotAPI(conf.Config.BotTeleToken)
	if err != nil {
		return err
	}

	message := tgbotapi.NewMessage(conf.Config.BotTeleChannelID,
		fmt.Sprintf("CREATE SALE ORDER - %d - %s -> %s", orderId, failReason, conf.Config.InternalURL+fmt.Sprintf("/detail?orderId=%d", orderId)))
	_, err = bot.Send(message)
	return err
}

func WarmupMasterdataCache() {
	offset := 0
	limit := 100
	for {
		wards, err := client.Services.Location.GetWardList(nil, offset, limit)
		if err != nil && err.Status == common.APIStatus.NotFound {
			break
		}
		if len(wards) == 0 {
			continue
		}
		for _, ward := range wards {
			mapWard[ward.Code] = *ward
		}
		offset += limit
	}
}

type CheckCompletedOrder struct {
	OrderId int64
	Force   bool
}

func ReCheckCompletedOrderConsumer(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input CheckCompletedOrder
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	if input.OrderId == 0 {
		return nil
	}

	return RecheckCompletedOrder(input.OrderId, input.Force)
}

// RecheckCompletedOrder checks if an order should be completed and updates its status if conditions are met
func RecheckCompletedOrder(orderID int64, force bool) error {
	if orderID == 0 {
		return fmt.Errorf("orderID is required")
	}

	qOrder := model.OrderDB.QueryOne(model.Order{OrderID: orderID})
	if qOrder.Status != common.APIStatus.Ok {
		return fmt.Errorf("failed to query order: %s", qOrder.Message)
	}

	order := qOrder.Data.([]*model.Order)[0]
	if !force && order.Status == enum.OrderState.Completed {
		return fmt.Errorf("order is already completed")
	}

	isAcceptCompleteOrder := order.SaleOrderStatus == enum.SaleOrderStatus.Completed
	isCOInternal := func(coTag enum.TagValue, tags []enum.TagValue) bool {
		for _, tag := range tags {
			if tag == coTag {
				return true
			}
		}
		return false
	}

	if !isCOInternal(enum.Tag.CO_INTERNAL, order.Tags) {
		qOrderBill := model.OrderBillDB.QueryOne(model.OrderBill{OrderId: orderID})

		if qOrderBill.Status == common.APIStatus.NotFound {
			// Try to get bill from external service
			bills, err := client.Services.Bill.GetBill(order.OrderID)
			if err == nil && len(bills) > 0 && bills[0] != nil {
				bill := bills[0]

				if bill.Status == string(enum.BillStatus.Done) {
					isAcceptCompleteOrder = true
					// Create missing OrderBill
					CompleteOrderBill(&model.OrderBill{
						OrderId:  orderID,
						BillCode: bill.BillCode,
						Status:   enum.BillStatus.Done,
					})
				} else {
					isAcceptCompleteOrder = false
				}
			}
		} else if qOrderBill.Status != common.APIStatus.Ok {
			return fmt.Errorf("failed to query order bill: %s", qOrderBill.Message)
		} else if qOrderBill.Status == common.APIStatus.Ok {
			bill := qOrderBill.Data.([]*model.OrderBill)[0]
			isAcceptCompleteOrder = isAcceptCompleteOrder && bill.Status == enum.BillStatus.Done
		}
	}

	if isAcceptCompleteOrder {
		updateRes := model.OrderDB.UpdateOne(model.Order{OrderID: orderID}, model.Order{Status: enum.OrderState.Completed})
		if updateRes.Status != common.APIStatus.Ok {
			return fmt.Errorf("failed to update order status: %s", updateRes.Message)
		}

		completeOrderSeller(orderID)

		readyTime := time.Now().Add(25 * time.Minute)
		_ = model.CompleteOrderJobExecutor.Push(
			order,
			&job.JobItemMetadata{
				Keys: []string{
					TopicCompleteOrder,
					strconv.Itoa(int(order.OrderID)),
				},
				Topic:     "default",
				ReadyTime: &readyTime,
			},
		)
		return nil
	}

	return fmt.Errorf("order cannot be completed: sale_order_status=%s, order_status=%s", order.SaleOrderStatus, order.Status)
}

type processPaymentMethodCredit struct {
	*client.CheckoutOrderInput
	*client.RefundOrderInput
}

func ProcessPaymentMethodCreditConsumer(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input processPaymentMethodCredit
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	if input.CheckoutOrderInput != nil {
		if transactionResp := client.Services.AccountingDebt.CheckoutOrder(input.CheckoutOrderInput); transactionResp.Status == common.APIStatus.Ok && transactionResp.Data != nil && len(transactionResp.Data.([]*client.Transaction)) > 0 {
			transaction := transactionResp.Data.([]*client.Transaction)[0]
			orderResp := model.OrderDB.QueryOne(model.Order{OrderID: input.CheckoutOrderInput.OrderID, OrderCode: input.CheckoutOrderInput.OrderCode})
			if orderResp.Status == common.APIStatus.Ok {
				if errUpdate := model.OrderDB.UpdateOne(model.Order{
					OrderID: input.CheckoutOrderInput.OrderID},
					model.Order{TransactionID: transaction.TransactionID}); errUpdate.Status != common.APIStatus.Ok {
					if item.FailCount == 0 {
						botNotifyCallProcessCreditFail(input.CheckoutOrderInput.OrderID, "checkout", errUpdate.Message)
					}
					return fmt.Errorf(errUpdate.Message)
				}
			} else {
				if item.FailCount == 0 {
					botNotifyCallProcessCreditFail(input.CheckoutOrderInput.OrderID, "checkout", orderResp.Message)
				}
			}
			return nil
		} else {
			if item.FailCount == 0 {
				botNotifyCallProcessCreditFail(input.CheckoutOrderInput.OrderID, "checkout", transactionResp.Message)
			}
			return fmt.Errorf(transactionResp.Message)
		}
	} else if input.RefundOrderInput != nil {
		err := client.Services.AccountingDebt.RefundOrder(input.RefundOrderInput)
		if err != nil && item.FailCount == 0 {
			botNotifyCallProcessCreditFail(input.RefundOrderInput.OrderID, "refund", err.Error())
		}
		return err
	}
	return nil
}

func botNotifyCallProcessCreditFail(orderId int64, action, failReason string) error {
	bot, err := tgbotapi.NewBotAPI(conf.Config.BotTeleToken)
	if err != nil {
		return err
	}

	message := tgbotapi.NewMessage(conf.Config.BotTeleChannelID,
		fmt.Sprintf("Process credit %s transaction order %d failed: %s - %s", action, orderId, failReason, conf.Config.InternalURL+fmt.Sprintf("/detail?orderId=%d", orderId)))
	_, err = bot.Send(message)
	return err
}

func InitAutoCancelBankOrderConfig() {
	configResp := model.HoldOrderConfigDB.QueryOne(model.HoldOrderConfig{HoldOrderCode: enum.HoldOrder.AutoCancelBankOrder})
	if configResp.Status != common.APIStatus.Ok {
		_ = model.HoldOrderConfigDB.UpdateOne(model.HoldOrderConfig{
			HoldOrderCode: enum.HoldOrder.AutoCancelBankOrder,
		}, model.HoldOrderConfig{
			HoldOrderCode:           enum.HoldOrder.AutoCancelBankOrder,
			IsActive:                utils.ParseBoolToPointer(false),
			BankTransferWaitingTime: utils.ParseInt64ToPointer(2880), // minute
			CreatedTime:             utils.ParseTimeToPointer(time.Now()),
		}, &options.FindOneAndUpdateOptions{
			Upsert: utils.ParseBoolToPointer(true),
		})
	}
}
func saveCheckoutError(cart *model.Cart, customer *model.Customer, err *common.APIResponse) {
	if cart == nil {
		cart = &model.Cart{}
	}
	if customer == nil {
		customer = &model.Customer{}
	}
	if err == nil {
		return
	}
	now := time.Now()
	qCheckoutError := model.CheckoutErrorDB.QueryOne(model.CheckoutError{CartID: cart.CartID})
	if qCheckoutError.Status == common.APIStatus.Ok {
		checkoutError := qCheckoutError.Data.([]*model.CheckoutError)[0]

		checkoutError.ErrorMessage = err.Message
		checkoutError.ErrorCode = err.ErrorCode
		checkoutError.FailCount = checkoutError.FailCount + 1
		checkoutError.LastCheckoutTime = &now
		checkoutError.ErrorDetail = func() string {
			detail, _ := json.Marshal(err.Data)
			return string(detail)
		}()
		_ = model.CheckoutErrorDB.UpdateOne(model.CheckoutError{CartID: cart.CartID}, checkoutError)
	} else {
		_ = model.CheckoutErrorDB.Create(model.CheckoutError{
			FirstCheckoutTime: &now,
			Code:              model.GenCodeWithTime(),
			CustomerID:        customer.CustomerID,
			CustomerPhone:     customer.Phone,
			CustomerAddress:   customer.Address,
			AccountID:         customer.AccountID,
			CartID:            cart.CartID,
			FailCount:         1,
			ErrorDetail: func() string {
				detail, _ := json.Marshal(err.Data)
				return string(detail)
			}(),
			ErrorMessage: err.Message,
			ErrorCode:    err.ErrorCode,
		})
	}
}

type CheckPaymentMethodBankOrder struct {
	OrderId int64
	Unix    int64
}
type RemindPaymentMethodOnlineOrder struct {
	OrderId int64 `json:"-" bson:"order_id,omitempty"`
	Unix    int64 `json:"-" bson:"unix,omitempty"`
}

func AutoCancelPaymentMethodBankConsumer(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input CheckPaymentMethodBankOrder
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	if input.OrderId <= 0 {
		return nil
	}
	orderResp := model.OrderDB.QueryOne(model.Order{OrderID: input.OrderId})
	if orderResp.Status == common.APIStatus.NotFound {
		return nil
	}
	if orderResp.Status == common.APIStatus.Ok {
		order := orderResp.Data.([]*model.Order)[0]
		if order.AutoCancelTransferPaymentUnix == 0 && order.CreatedTime != nil && order.CreatedTime.Unix() != input.Unix {
			return nil
		}
		if order.AutoCancelTransferPaymentUnix > 0 && order.AutoCancelTransferPaymentUnix != input.Unix {
			return nil
		}
		if !IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, order.PaymentMethod) {
			return nil
		}
		if order.Status != enum.OrderState.WaitConfirm {
			return nil
		}
		if order.WaitForTransfer != nil && *order.WaitForTransfer {
			return nil
		}
		settingResp := model.HoldOrderConfigDB.QueryOne(model.HoldOrderConfig{HoldOrderCode: enum.HoldOrder.AutoCancelBankOrder})
		if settingResp.Status == common.APIStatus.Ok {
			setting := settingResp.Data.([]*model.HoldOrderConfig)[0]
			if setting.IsActive != nil && *setting.IsActive {
				if setting.BankTransferWaitingTime != nil && *setting.BankTransferWaitingTime > order.WaitForTransferTime {
					newNote := ""
					if order.Note != nil {
						newNote = *order.Note
					}
					if newNote != "" {
						newNote += "\n"
					}
					newNote += fmt.Sprintf("Thời gian chờ chuyển khoản thay đổi từ %d phút sang %d phút", &order.WaitForTransferTime, *setting.BankTransferWaitingTime-order.WaitForTransferTime)
					readyTime := time.Now().Add(time.Duration(*setting.BankTransferWaitingTime-order.WaitForTransferTime) * time.Minute)
					updateResp := model.OrderDB.UpdateOne(model.Order{OrderID: order.OrderID}, model.Order{
						WaitForTransferTime:           *setting.BankTransferWaitingTime,
						AutoCancelTransferPaymentUnix: readyTime.Unix(),
						Note:                          &newNote})
					if updateResp.Status == common.APIStatus.Ok {
						model.AutoCancelPaymentMethodBankJob.Push(CheckPaymentMethodBankOrder{
							OrderId: order.OrderID,
							Unix:    readyTime.Unix(),
						}, &job.JobItemMetadata{
							Topic:     "default",
							ReadyTime: &readyTime,
						})
					}
					return nil
				}
				updateStatus := &model.OrderUpdateStatus{
					OrderID:                   order.OrderID,
					Status:                    enum.OrderState.Canceled,
					CancelTime:                utils.ParseTimeToPointer(time.Now()),
					OrderCode:                 order.OrderCode,
					AutoCancelOverdueTransfer: utils.ParseBoolToPointer(true),
				}
				if item.FailCount == 0 {
					newNote := ""
					if order.Note != nil {
						newNote = *order.Note
					}
					if newNote != "" {
						newNote += "\n"
					}
					newNote += fmt.Sprintf("Đơn hàng bị hủy do quá hạn chuyển khoản trong vòng %d phút", order.WaitForTransferTime)
					updateStatus.Note = &newNote
				}
				res := model.OrderDB.UpdateOne(&model.Order{OrderID: order.OrderID, Status: enum.OrderState.WaitConfirm}, updateStatus)
				if res.Status == common.APIStatus.Ok {
					updateSkuLimitHistory(order)
					updateStatusOrderSeller(updateStatus.OrderID, updateStatus, updateStatus.SaleOrderCode)
					updatedOrder := res.Data.([]*model.Order)[0]
					processInvoice(updatedOrder)

					bot, err := tgbotapi.NewBotAPI(conf.Config.BotTeleToken)
					if err == nil {
						message := tgbotapi.NewMessage(conf.Config.BotTeleChannelID,
							fmt.Sprintf("Order was canceled due to the %d minutes waiting time for the transfer %s", order.WaitForTransferTime, conf.Config.InternalURL+fmt.Sprintf("/detail?orderId=%d", order.OrderID)))
						_, _ = bot.Send(message)
					}
					customer, err := client.Services.Customer.GetCustomerByCustomerID(order.CustomerID)
					if err == nil {
						_ = client.Services.Notification.CreateNotification(&client.Notification{
							Username:     customer.Username,
							UserID:       customer.AccountID,
							ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
							Topic:        "ANNOUNCEMENT",
							Title:        fmt.Sprintf("Đơn hàng của bạn đã bị hủy do quá hạn chuyển khoản trong vòng %d phút, mã đơn hàng #%d!", order.WaitForTransferTime, order.OrderID),
							Link:         fmt.Sprintf("/my-order/%d", order.OrderID),

							Tags: []enum.NotificationTagEnum{enum.NotificationTag.ORDER, enum.NotificationTag.IMPORTANT},
						})
						_, _ = client.Services.Promotion.CreateVoucherReuseOnOrderCancel(&client.CreateVoucherReuseOnOrderCancelReq{
							RedeemCodes: order.RedeemCode,
							AccountID:   order.AccountID,
							CustomerID:  order.CustomerID,
							OrderID:     order.OrderID,
						})
					}
				} else {
					return fmt.Errorf(res.ErrorCode + "-" + res.Message)
				}
			}
		}
		return nil
	}
	return fmt.Errorf(orderResp.Message)
}
func resolveCheckoutError(cart *model.Cart) {
	qCheckoutError := model.CheckoutErrorDB.QueryOne(model.CheckoutError{CartID: cart.CartID})
	if qCheckoutError.Status == common.APIStatus.Ok {
		_ = model.CheckoutErrorDB.Delete(model.CheckoutError{CartID: cart.CartID})
	}
}

func ListMonthByStartAndEndTime(startTime, endTime time.Time) []time.Time {
	// Create empty slice to hold list of months
	months := []time.Time{}

	startTime = startTime.In(model.VNTimeZone)
	endTime = endTime.In(model.VNTimeZone)

	// Set starting date to the beginning of the first month in the range
	start := time.Date(startTime.Year(), startTime.Month(), 1, 0, 0, 0, 0, model.VNTimeZone)

	// Set ending date to the beginning of the last month in the range
	end := time.Date(endTime.Year(), endTime.Month()+1, 1, 0, 0, 0, -1, model.VNTimeZone)

	// Loop through months and add them to the list
	for d := start; d.Before(end); d = d.AddDate(0, 1, 0) {
		months = append(months, d)
	}

	// Return list of months
	return months
}

func ListMonthByListOrderDetail(orders []*model.OrderDetail) ([]time.Time, []string) {
	// Create empty slice to hold list of months
	months := []time.Time{}
	orderCodes := []string{}
	mapMonths := make(map[string]struct{})

	for i := range orders {
		ord := orders[i]

		orderCodes = append(orderCodes, ord.OrderCode)

		if ord.CreatedTime != nil {
			dayStr := ord.CreatedTime.In(model.VNTimeZone).Format("2006-01")
			if _, ok := mapMonths[dayStr]; ok {
				continue
			}

			mapMonths[dayStr] = struct{}{}
			months = append(months, *ord.CreatedTime)
		}
	}

	// Return list of months
	return months, orderCodes
}

func ListMonthByListOrder(orders []*model.Order) ([]time.Time, []string) {
	// Create empty slice to hold list of months
	months := []time.Time{}
	orderCodes := []string{}
	mapMonths := make(map[string]struct{})

	for i := range orders {
		ord := orders[i]

		orderCodes = append(orderCodes, ord.OrderCode)

		if ord.CreatedTime != nil {
			dayStr := ord.CreatedTime.In(model.VNTimeZone).Format("2006-01")
			if _, ok := mapMonths[dayStr]; ok {
				continue
			}

			mapMonths[dayStr] = struct{}{}
			months = append(months, *ord.CreatedTime)
		}
	}

	// Return list of months
	return months, orderCodes
}

func CompleteSaleOrderConsumer(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input model.Order
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	if input.SaleOrderCode == "" {
		return nil
	}
	resp := client.Services.Invoice.CompleteOrder(&model.Order{OrderID: input.OrderID, SaleOrderCode: input.SaleOrderCode})
	if resp.Status != common.APIStatus.Ok {
		return fmt.Errorf(resp.ErrorCode + "-" + resp.Message)
	}
	return nil
}

func unsetfilterComplexQuery(complexQuery []*bson.M, keys []string) []*bson.M {
	if len(keys) == 0 {
		return complexQuery
	}

	filter := []*bson.M{}
	for i := range complexQuery {
		condition := complexQuery[i]
		if condition == nil {
			continue
		}

		newCond := make(bson.M)
		for oldKey, oldVal := range *condition {
			if !utils.IsContains(keys, oldKey) {
				newCond[oldKey] = oldVal
			}
		}

		if len(newCond) > 0 {
			filter = append(filter, &newCond)
		}
	}

	return filter
}

func WarmupDistrictSplitOrderCache() {
	offset := 0
	limit := 100
	for {
		regions, err := client.Services.Location.GetRegionList(offset, limit, nil)
		if err != nil && err.Status == common.APIStatus.NotFound {
			break
		}
		if len(regions) == 0 {
			continue
		}
		newMapSplitOrderDistrictCode := make(map[string]string)
		for _, item := range regions {
			if len(item.SplitOrderDistrictCodes) > 0 {
				for _, districtCode := range item.SplitOrderDistrictCodes {
					newMapSplitOrderDistrictCode[districtCode] = districtCode
				}
			}
		}
		mapSplitOrderDistrictCode = newMapSplitOrderDistrictCode
		offset += limit
	}
}

func CreateOrderRatingConsumer(item *job.JobItem) error {
	if item.FailCount > 50 {
		return nil
	}
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input model.Order
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	resp := client.Services.Customer.CreateOrderRating(&model.CustomerRating{
		OrderID:      input.OrderID,
		RegionCode:   input.RegionCode,
		ProvinceCode: input.ProvinceCode,
		Type:         enum.CustomerRating.ORDER,
		CustomerID:   input.CustomerID,
	})
	if resp.Status != common.APIStatus.Ok {
		if resp.ErrorCode == "ORDER_RATING_EXISTED" {
			return nil
		}
		return fmt.Errorf(resp.ErrorCode + "-" + resp.Message)
	}
	return nil
}

func WarmupConfigManagement() {
	params := map[string]string{
		"appCode": "XKU72AXH",
	}
	confResp := client.Services.ConfigManager.GetAppValueSingle(params)
	if confResp.Status == common.APIStatus.Ok {
		for _, con := range confResp.Data {
			appValue := con.Value
			if con.Value.Key == conf.Config.ForceSplitOrder {
				if appValue.Wards != "" {
					newMapConfigWardSplitWebMobile := make(map[string]struct{})
					for _, wardCode := range strings.Split(appValue.Wards, ",") {
						newMapConfigWardSplitWebMobile[strings.Trim(wardCode, " ")] = struct{}{}
					}
					mapConfigWardSplitWebMobile = newMapConfigWardSplitWebMobile
				}

				if appValue.Districts != "" {
					newMapConfigDistricsSplitWebMobile := make(map[string]struct{})
					for _, districtCode := range strings.Split(appValue.Districts, ",") {
						newMapConfigDistricsSplitWebMobile[strings.Trim(districtCode, " ")] = struct{}{}
					}
					mapConfigDistricsSplitWebMobile = newMapConfigDistricsSplitWebMobile
				}

				if appValue.Provinces != "" {
					newMapConfigProvinceSplitWebMobile := make(map[string]struct{})
					for _, provinceCode := range strings.Split(appValue.Provinces, ",") {
						newMapConfigProvinceSplitWebMobile[strings.Trim(provinceCode, " ")] = struct{}{}
					}
					mapConfigProvinceSplitWebMobile = newMapConfigProvinceSplitWebMobile
				}
			}
			if con.Value.Key == "CUSTOMER_COME_BACK" {
				customerComebackDays = int(con.Value.ValueInt)
			}

			if con.Value.Key == "PROVINCE_EXTRA_FEE" {
				tempMapProvinceExtraFee := make(map[string]struct {
					MinPrice                int `json:"minPrice,omitempty"`
					ExtraFee                int `json:"extraFee,omitempty"`
					MaxPriceAppliedExtraFee int `json:"maxPriceAppliedExtraFee,omitempty"` // dưới giá này đơn hàng sẽ bị thu phụ phí
				})
				if con.Value.ValString != "" {
					json.Unmarshal([]byte(appValue.ValString), &tempMapProvinceExtraFee)
				}
				mapProvinceExtraFee = tempMapProvinceExtraFee
			}

			if con.Value.Key == "LEVEL_EXTRA_FEE" {
				tempMapLevelExtraFee := make(map[string]model.ExtraFeeUse)
				if con.Value.ValString != "" {
					json.Unmarshal([]byte(appValue.ValString), &tempMapLevelExtraFee)
				}
				mapLevelExtraFee = tempMapLevelExtraFee
			}

			if con.Value.Key == "BRAND_PORTAL_CONFIG" {
				tempBrandPortalConfig := mapBrandPortalConfig
				if con.Value.ValString != "" {
					json.Unmarshal([]byte(appValue.ValString), &tempBrandPortalConfig)
				}
				mapBrandPortalConfig = tempBrandPortalConfig
			}

			if con.Value.Key == "CUSTOMER_ID_SKIP_VALIDATE_AMOUNT" {
				tempCustomerIDSkipValidateAmount := []int64{}
				if con.Value.CustomerIds != "" {
					idsStr := strings.Split(con.Value.CustomerIds, ",")
					for _, idStr := range idsStr {
						id, err := strconv.ParseInt(strings.Trim(idStr, " "), 10, 64)
						if err == nil {
							tempCustomerIDSkipValidateAmount = append(tempCustomerIDSkipValidateAmount, id)
						}
					}
				}
				conf.Config.CustomerIDSkipValidateAmount = tempCustomerIDSkipValidateAmount
			}

			if con.Value.Key == "CUSTOMER_ID_CROSS_PROVINCES" {
				var mapCustomerIDCrossProvincesTemp = map[int64]struct{}{}
				for _, customerID := range strings.Split(appValue.CustomerIds, ",") {
					id, err := strconv.ParseInt(strings.Trim(customerID, " "), 10, 64)
					if err == nil {
						mapCustomerIDCrossProvincesTemp[id] = struct{}{}
					}
				}
				mapCustomerIDCrossProvinces = mapCustomerIDCrossProvincesTemp
			}

			if con.Value.Key == "CONFIG_DEBT_CHECKOUT_OTHER_METHOD" {
				var mapCustomerIdTagCheckoutOtherMethodTemp = map[int64]struct{}{}
				if con.Value.CustomerIds != "" {
					for _, customerID := range strings.Split(appValue.CustomerIds, ",") {
						id, err := strconv.ParseInt(strings.Trim(customerID, " "), 10, 64)
						if err == nil {
							mapCustomerIdTagCheckoutOtherMethodTemp[id] = struct{}{}
						}
					}
				}
				mapCustomerIdTagCheckoutOtherMethod = mapCustomerIdTagCheckoutOtherMethodTemp

				var mapCustomerTagCheckoutOtherMethodTemp = map[string]struct{}{}
				if con.Value.ValString != "" {
					for _, customerTag := range strings.Split(con.Value.ValString, ",") {
						customerTag = strings.TrimSpace(customerTag)
						if customerTag != "" {
							mapCustomerTagCheckoutOtherMethodTemp[customerTag] = struct{}{}
						}
					}
				}
				mapCustomerTagCheckoutOtherMethod = mapCustomerTagCheckoutOtherMethodTemp
			}

			if con.Value.Key == "CANNOT_AUTO_CONFIRM_DEBT_ORDER" {
				var mapCustomerIdCannotAutoConfirmDebtOrderTemp = map[int64]struct{}{}
				if con.Value.CustomerIds != "" {
					for _, customerID := range strings.Split(appValue.CustomerIds, ",") {
						id, err := strconv.ParseInt(strings.Trim(customerID, " "), 10, 64)
						if err == nil {
							mapCustomerIdCannotAutoConfirmDebtOrderTemp[id] = struct{}{}
						}
					}
				}
				mapCustomerIdCannotAutoConfirmDebtOrder = mapCustomerIdCannotAutoConfirmDebtOrderTemp

				var mapCustomerTagCannotAutoConfirmDebtOrderTemp = map[string]struct{}{}
				if con.Value.ValString != "" {
					for _, customerTag := range strings.Split(con.Value.ValString, ",") {
						customerTag = strings.TrimSpace(customerTag)
						if customerTag != "" {
							mapCustomerTagCannotAutoConfirmDebtOrderTemp[customerTag] = struct{}{}
						}
					}
				}
				mapCustomerTagCannotAutoConfirmDebtOrder = mapCustomerTagCannotAutoConfirmDebtOrderTemp
			}

			if con.Value.Key == "ONLINE_PAYMENT_CONFIG" {
				var onelinePaymentConfig model.OnelinePaymentConfig

				tempPartnerPaymentMethod := PARTNER_PAYMENT_METHOD_HARDCODE
				tempPaymentMethodOrderTransfer := PAYMENT_METHOD_ORDER_TRANSFER_HARDCODE

				if con.Value.ValString != "" {
					json.Unmarshal([]byte(appValue.ValString), &onelinePaymentConfig)

					if len(onelinePaymentConfig.PartnerPaymentMethod) > 0 {
						tempPartnerPaymentMethod = onelinePaymentConfig.PartnerPaymentMethod
					}

					if len(onelinePaymentConfig.PaymentMethodOrderTransfer) > 0 {
						tempPaymentMethodOrderTransfer = onelinePaymentConfig.PaymentMethodOrderTransfer
					}
				}

				PARTNER_PAYMENT_METHOD = tempPartnerPaymentMethod
				PAYMENT_METHOD_ORDER_TRANSFER = tempPaymentMethodOrderTransfer
			}

			if con.Value.Key == "DISPLAY_PRODUCT_BY_SEGMENT" {
				customerSKUConfig := customerSKUConfigCache
				if con.Value.ValString != "" {
					json.Unmarshal([]byte(appValue.ValString), &customerSKUConfig)
				}
				customerSKUConfigCache = customerSKUConfig
				conf.Config.WarmupCustomerSKUs = int(customerSKUConfig.WarmupTime)
			}

			if con.Value.Key == "SYNC_CUSTOMER_TAG_FOR_WAREHOUSE" {
				if con.Value.ValString != "" {
					syncCustomerTagForWarehouseConfig = strings.Split(appValue.ValString, ",")
				}
			}

			// config for prevent send tag
			if con.Value.Key == "SOURCE_PREVENT_TAG_TO_WAREHOUSE" {
				var mutex = sync.Mutex{}
				var sourcePreventTagToWarehouseTemp = make(map[string][]string)
				if con.Value.ValString != "" {
					err := json.Unmarshal([]byte(con.Value.ValString), &sourcePreventTagToWarehouseTemp)
					if err == nil {
						mutex.Lock()
						sourcePreventSendTagToWarehouseConfig = sourcePreventTagToWarehouseTemp
						mutex.Unlock()
					}
				}
			}

			if appValue.Key == "PREVENT_SEND_TAG_DO_NOT_DELIVERY_BIN" {
				tempPreventSendTagDoNotDeliveryBin := preventSendTagDoNotDeliveryBinConfig
				if appValue.ValString != "" {
					err := json.Unmarshal([]byte(appValue.ValString), &tempPreventSendTagDoNotDeliveryBin)
					if err == nil {
						preventSendTagDoNotDeliveryBinConfig = tempPreventSendTagDoNotDeliveryBin
					}
				}

			}

			// config tag of products to append to orders
			if con.Value.Key == "CONFIG_PRODUCT_TAG_FOR_ORDER" {
				if con.Value.ValString != "" {
					productTagsConfig = strings.Split(appValue.ValString, ",")
				}
			}
		}
	}

	params2 := map[string]string{
		"appCode": "VJYJYJYJ",
	}
	confResp2 := client.Services.ConfigManager.GetAppValueSingle(params2)
	if confResp2.Status == common.APIStatus.Ok {
		for _, con := range confResp2.Data {
			appValue := con.Value
			if appValue.Key == "KIOTVIET_CONFIGS" {
				tempKiotvietConfigs := model.PartnerKiotvietConfigs{}
				if appValue.ValString != "" {
					err := json.Unmarshal([]byte(appValue.ValString), &tempKiotvietConfigs)
					if err == nil {
						kiotvietConfigs = tempKiotvietConfigs
					}
				}
			}
		}
	}
}

func AutoSendPaymentRemindConsumer(item *job.JobItem) error {
	if item.FailCount > 10 {
		return nil
	}
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input RemindPaymentMethodOnlineOrder
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}
	if input.OrderId <= 0 {
		return nil
	}
	orderResp := model.OrderDB.QueryOne(model.Order{OrderID: input.OrderId})
	if orderResp.Status == common.APIStatus.NotFound {
		return nil
	}
	if orderResp.Status == common.APIStatus.Ok {
		order := orderResp.Data.([]*model.Order)[0]
		if order.AutoSendPaymentNotificationUnix != input.Unix {
			return nil
		}
		if !IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, (order.PaymentMethod)) {
			return nil
		}
		if order.Status != enum.OrderState.WaitConfirm || (IsContainsT(PARTNER_PAYMENT_METHOD, order.PaymentMethod) && order.PartnerPaymentStatus != enum.PartnerPaymentStatus.WAIT_TO_PAY) {
			return nil
		}
		if order.TotalPrice == nil || *order.TotalPrice <= 0 {
			return nil
		}
		settingResp := model.HoldOrderConfigDB.QueryOne(model.HoldOrderConfig{HoldOrderCode: enum.HoldOrder.AutoSendPaymentRemind})
		if settingResp.Status == common.APIStatus.Ok {
			setting := settingResp.Data.([]*model.HoldOrderConfig)[0]
			if setting.IsActive != nil && *setting.IsActive {
				paymentMethodName := mapPaymentMethodName[order.PaymentMethod]
				if paymentMethodName == "" {
					setting.NotificationConfig.Description = strings.ReplaceAll(setting.NotificationConfig.Description, "{paymentMethod}", "")
					setting.NotificationConfig.Content = strings.ReplaceAll(setting.NotificationConfig.Content, "{paymentMethod}", "")
				}
				// send noti
				customer, err := client.Services.Customer.GetCustomerByCustomerID(order.CustomerID)
				if err == nil && customer != nil && setting.NotificationConfig != nil &&
					order.AutoSendPaymentNotificationUnix == input.Unix {
					desc := strings.ReplaceAll(setting.NotificationConfig.Description, "{orderId}", fmt.Sprintf("%d", order.OrderID))
					desc = strings.ReplaceAll(desc, "{paymentMethod}", paymentMethodName)
					_ = client.Services.Notification.CreateNotification(&client.Notification{
						Username:     customer.Username,
						UserID:       customer.AccountID,
						ReceiverType: utils.ParseStringToPointer(enum.AccountType.CUSTOMER),
						Topic:        "ANNOUNCEMENT",
						Title:        desc,
						Link:         fmt.Sprintf("%s/my-order/%d", conf.Config.FrontendURL, order.OrderID),
						Tags:         []enum.NotificationTagEnum{enum.NotificationTag.ORDER, enum.NotificationTag.IMPORTANT},
					})
				}
				// todo: app push
				customerNotificationPartner, err := client.Services.Customer.GetNotificationPartnerListByCustomerID(&client.NotificationPartner{CustomerID: customer.CustomerID, Status: "ACTIVE"}, 0, 0)

				if err == nil && customerNotificationPartner != nil && len(customerNotificationPartner) > 0 {
					var receiver []string
					for _, partner := range customerNotificationPartner {
						if partner.Token != "" {
							receiver = append(receiver, partner.Token)
						}
					}
					if len(receiver) > 0 {
						content := strings.ReplaceAll(setting.NotificationConfig.Content, "{orderId}", fmt.Sprintf("%d", order.OrderID))
						content = strings.ReplaceAll(content, "{paymentMethod}", paymentMethodName)
						client.Services.Notify.CreateNotify(&client.Notify{
							// Topic:    "ANNOUNCEMENT",
							Title:    strings.ReplaceAll(setting.NotificationConfig.Title, "{orderId}", fmt.Sprintf("%d", order.OrderID)),
							Content:  content,
							Receiver: receiver,
							DeepLink: fmt.Sprintf("%s/my-order/%d", conf.Config.FrontendURL, order.OrderID),
						})
					}
				}
			}
		}
	} else {
		return fmt.Errorf(orderResp.Message)
	}
	return nil
}

func WarmupPaymentMethodMap() {
	paymentMethods, _ := client.Services.Pricing.GetPaymentFeeConfig()
	newMapPaymentMethod := make(map[string]string)
	for _, item := range paymentMethods {
		newMapPaymentMethod[item.Code] = item.Name
	}
	mapPaymentMethodName = newMapPaymentMethod
}

var limitSalePrice = 0.02 // 2%
func checkSalePriceToWanring(customer *model.Customer, cartItem *model.CartItem) {
	// return if isCombo
	if cartItem.IsCombo != nil && *cartItem.IsCombo {
		return
	}
	bot, err := tgbotapi.NewBotAPI(conf.Config.BotMonitorPriceToken)
	if err != nil {
		return
	}
	// get bot description
	me, _ := bot.GetMe()
	gapStrs := strings.Split(me.FirstName, "SALE_GAP_")
	if len(gapStrs) > 1 {
		gapStr := gapStrs[1]
		limitSalePrice, _ = strconv.ParseFloat(gapStr, 64)
		limitSalePrice = limitSalePrice / 100
	}
	salePrice := cartItem.SalePrice
	price := cartItem.Price
	if !isNilOrDefaultValue(cartItem.DealCode) || !isNilOrDefaultValue(cartItem.CampaignCode) {
		gap := float64(salePrice-price) / float64(salePrice)
		if gap > limitSalePrice {
			msg := tgbotapi.NewMessage(conf.Config.BotMonitorPriceChannelID, "⚠️"+cartItem.ProductName+" giảm giá "+utils.FormatVNDCurrency(strconv.Itoa(salePrice-price))+" đồng từ "+utils.FormatVNDCurrency(strconv.Itoa(salePrice))+" xuống còn "+utils.FormatVNDCurrency(strconv.Itoa(price))+" đồng.⚠️"+"\n"+
				"🆔 "+cartItem.Sku+"\n"+
				"👨‍⚕️ "+customer.Name+" ☎️ "+customer.Phone+"\n"+
				"🛒 Cart ID "+strconv.Itoa(int(cartItem.CartID))+"\n"+
				"🏷 GAP "+fmt.Sprintf("%f", gap*100)+"%"+"\n"+
				"Is Near expired : "+fmt.Sprintf("%v", cartItem.IsNearExpired))
			_, _ = bot.Send(msg)
		}
	}
}

var MapVendorStoreByTag = make(map[string]string)
var muMapVendorStoreByTag = sync.RWMutex{}

func WarmupVendorStoreCache() {
	offset := 0
	limit := 100
	if MapVendorStoreByTag == nil {
		muMapVendorStoreByTag.Lock()
		MapVendorStoreByTag = make(map[string]string)
		muMapVendorStoreByTag.Unlock()
	}
	for {
		stores, err := client.Services.Seller.GetVendorStoreList(offset, limit)
		if err != nil {
			break
		}
		for _, store := range stores {
			muMapVendorStoreByTag.Lock()
			MapVendorStoreByTag[store.AllProductTag] = store.Code
			muMapVendorStoreByTag.Unlock()
		}
		offset += limit
	}
}

func WarmUpPaymentConfig() {
	paymentMethods, _ := client.Services.Pricing.GetPaymentFeeConfig()
	newMapPaymentMethod := make(map[string]*client.PaymentFeeConfig)
	for _, item := range paymentMethods {
		newMapPaymentMethod[item.Code] = item
	}
	mapPaymentMethodConfig = newMapPaymentMethod
}

func CheckAndUpdatePriceAfterDiscount(order *model.Order) {
	if order.Items == nil || len(order.Items) == 0 {
		qItems := model.OrderItemDB.Query(model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
		if qItems.Status == common.APIStatus.Ok {
			order.Items = qItems.Data.([]*model.OrderItem)
		}
	}
	//discountFromPayment := int64(0)
	//if order.PaymentMethodFee == nil {
	//	discountFromPayment = *order.PaymentMethodFee
	//}
	//for _, item := range order.Items {
	//	priceAfterDiscount := item.Price
	//	if item.DiscountDetail != nil {
	//		priceAfterDiscount = item.Price - item.DiscountDetail.TotalDiscount / int64(item.Quantity)
	//	}
	//}
}

func WarmupCustomerSKUs() {
	tempCustomerSKUs := make(map[string]*model.CustomerSKUItem)
	offset, limit := int64(0), int64(1000)
	count := int64(0)
	var lastID primitive.ObjectID
	for {
		querySku := bson.M{}
		if lastID != primitive.NilObjectID {
			querySku["_id"] = bson.M{"$gt": lastID}
		}
		res := model.CustomerSKUsCacheDB.Query(querySku, offset, limit, &primitive.M{"_id": 1})
		if res.Status != common.APIStatus.Ok {
			break
		}

		items := res.Data.([]*model.CustomerSKUs)
		if len(items) == 0 {
			break
		}

		for _, item := range items {
			lastID = item.ID
			tempCustomerSKUs[item.Sku] = &model.CustomerSKUItem{
				CustomerIds: item.CustomerIds,
				RuleType:    item.RuleType,
			}
		}
		count += int64(len(items))
	}
	customerSKUs = tempCustomerSKUs
}

func GetKiotvietConfigs() model.PartnerKiotvietConfigs {
	return kiotvietConfigs
}
