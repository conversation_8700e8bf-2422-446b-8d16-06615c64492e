package tool

import (
	"errors"
	"fmt"
	"math"
	"runtime/debug"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	MissingSku       = map[string]string{}
	ProductMap       = map[string]*client.Product{}
	SellerProductIDs = []int64{
		13661, 13664, 13665, 13666, 13982, 13983, 13988, 13989, 14010, 14088, 14115, 14120, 14123, 14126, 14128, 14130, 14133,
		14144, 14145, 14146, 14162, 14165, 14166, 14176, 14177, 14198, 14200, 14206, 14207, 14208, 14217, 14222, 14224, 14229,
		14230, 14231, 14232, 14233, 14234, 14235, 14236, 14237, 14238, 14239, 14240, 14249, 14250, 14306, 14348, 14349, 14350,
		14351, 14352, 14353, 14354, 14355, 14403, 14412, 14414, 14415, 14416, 14417, 14418, 14419, 14420, 14421, 14422, 14423,
		14424, 14426, 14427, 14428, 14429, 14430, 14431, 14432, 14434, 14435, 14436, 14437, 14438, 14439, 14874, 14879, 14915,
		14916, 14917, 14920, 14928, 14932, 14935, 14948, 14978, 14981, 15030, 15032, 15033, 15034, 15035, 15036, 15037, 15181,
		15184, 15187, 15266, 15312, 15390, 15392, 15417, 15418, 15468, 15469, 15470, 15471, 15472, 15482, 15581, 15615, 15616,
		15617, 15618, 15619, 15620, 15621, 15629, 15633, 15634, 15635, 15636, 15637, 15638, 15639, 15640, 15641, 15642, 15643,
		15644, 15645, 15646, 15647, 15648, 15659, 15704, 15706, 15712, 15713, 15715, 15717, 15718, 15719, 15720, 15721, 15726,
		15729, 15730, 15731, 15732, 15733, 15734, 15735, 15736, 15737, 15738, 15749, 15750, 15754, 15755, 15756, 15758, 15759,
		15760, 15761, 15762, 15803, 15864, 15865, 15866, 15869, 15870, 15876, 15880, 15908, 15947, 15948, 15959, 15961, 15962,
		15980, 15981, 15982, 15983, 15984, 15985, 15986, 15987, 15988, 16038, 16039, 16049, 16050, 16052, 16066, 16072, 16073,
		16074, 16088, 16089, 16092, 16097, 16099, 16101, 16102, 16121, 16126, 16130, 16132, 16133, 16141, 16142, 16143, 16144,
		16145, 16146, 16147, 16148, 16149, 16150, 16151, 16160, 16162, 16163, 16165, 16167, 16175, 16176, 16180, 16184, 16185,
		16192, 16193, 16194, 16195, 16206, 16207, 16228, 16241, 16242, 16243, 16244, 16248, 16249, 16250, 16251, 16252, 16253,
		16254, 16255, 16256, 16257, 16258, 16259, 16260, 16261, 16262, 16283, 16289, 16290, 16291, 16292, 16293, 16295, 16299,
		16307, 16309, 16310, 16311, 16312, 16313, 16316, 16317, 16318, 16319, 16320, 16322, 16323, 16335, 16337, 16339, 16340,
		16341, 16342, 16347, 16348, 16349, 16350, 16351, 16355, 16368, 16369, 16370, 16371, 16372, 16383, 16384, 16385, 16386,
		16410, 16411, 16412, 16413, 16414, 16415, 16416, 16417, 16418, 16419, 16420, 16442, 16444, 16445, 16446, 16447, 16485,
		16495, 16533, 16535, 16540, 16595, 16600, 16601, 16603, 16604, 16621, 16642, 16645, 16646, 16647, 16648, 16650, 16651,
		16652, 16654, 16655, 16658, 16675, 16686, 16687, 16688, 16689, 16703, 16742, 16762, 16764, 16781, 16782, 16783, 16784,
		16785, 16786, 16787, 16788, 16789, 16862, 16863, 16864, 16898,
	}
)

func FindInt64(ids []int64, id int64) int {
	for i, v := range ids {
		if v == id {
			return i
		}
	}
	return -1
}

func FindString(lStr []string, s string) int {
	for i, v := range lStr {
		if v == s {
			return i
		}
	}
	return -1
}

// var SellerSubCategories = []client.SellerSubCategory{}
func ProcessCalculateReconciliation(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *model.Reconciliation
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	filter := model.Reconciliation{
		SellerCode:                 input.SellerCode,
		ReconcileScheduleTimeIndex: input.ReconcileScheduleTimeIndex,
		ReconciliationStatus:       model.ReconciliationStatus.Waiting,
	}
	result := model.ReconciliationDB.QueryOne(filter)
	if result.Status != common.APIStatus.Ok {
		return res.Respond(result)
	}

	value, err := reconcile_action.CalculateReconciliation(input.SellerCode, input.ReconcileScheduleTimeIndex)
	if err != nil {
		if err.Error() == "Not found any matched reconciliation_item." {
			delRes := model.ReconciliationDB.Delete(filter)
			return res.Respond(delRes)
		}

		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		})
	}

	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}
	result = model.ReconciliationDB.UpdateOne(filter, updater)
	return res.Respond(result)
}

func RemoveEmptyReconciliation(req sdk.APIRequest, res sdk.APIResponder) error {
	zero := 0
	filter := model.Reconciliation{
		ReconciliationStatus: model.ReconciliationStatus.Waiting,
		TotalRevenue:         &zero,
		ListingFee:           &zero,
		FulfillmentFee:       &zero,
		PenaltyFee:           &zero,
		BonusAmount:          &zero,
		TotalPayment:         &zero,
	}
	response := model.ReconciliationDB.Delete(filter)
	return res.Respond(response)
}

func RemoveReconciliationSchedule(req sdk.APIRequest, res sdk.APIResponder) error {
	schedule := req.GetParam("schedule")
	if schedule == "" {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing schedule",
		})
	}

	riFilter := model.ReconciliationItem{
		ReconcileScheduleTimeIndex: schedule,
	}
	response := model.ReconciliationItemDB.Delete(riFilter)
	if response.Status != common.APIStatus.Ok {
		return res.Respond(response)
	}

	filter := model.Reconciliation{
		ReconcileScheduleTimeIndex: schedule,
	}
	response = model.ReconciliationDB.Delete(filter)
	return res.Respond(response)
}

func UpdateReconciliationItem(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *model.ReconciliationItem
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	reconciliationItemF := model.ReconciliationItem{ID: input.ID}
	result := model.ReconciliationItemDB.QueryOne(reconciliationItemF)
	if result.Status != common.APIStatus.Ok {
		return res.Respond(result)
	}
	rItem := result.Data.([]*model.ReconciliationItem)[0]

	reconciliationF := model.Reconciliation{
		SellerCode:                 rItem.SellerCode,
		ReconcileScheduleTimeIndex: rItem.ReconcileScheduleTimeIndex,
	}
	result = model.ReconciliationDB.QueryOne(reconciliationF)
	if result.Status != common.APIStatus.Ok {
		return res.Respond(result)
	}

	input.CreatedTime = nil
	input.LastUpdatedTime = nil
	input.OperationOr = nil
	input.OperationAnd = nil
	result = model.ReconciliationItemDB.UpdateOne(reconciliationItemF, input)
	if result.Status != common.APIStatus.Ok {
		return res.Respond(result)
	}

	return res.Respond(result)
}

type InputRemoveReconciliation struct {
	OrderID       int64  `json:"order_id"`
	SellerCode    string `json:"seller_code"`
	ScheduleIndex string `json:"schedule_index"`
}

func RemoveReconciliationItem(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *InputRemoveReconciliation
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	if input.OrderID == 0 || input.SellerCode == "" {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing order_id | seller_code",
		})
	}

	if input.ScheduleIndex == "" {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing schedule_index",
		})
	}

	filter := model.ReconciliationItem{
		OrderID:                    input.OrderID,
		ReconcileScheduleTimeIndex: input.ScheduleIndex,
		SellerCode:                 input.SellerCode,
		FeeType:                    enum.FeeType.REVENUE,
	}
	result := model.ReconciliationItemDB.Query(filter, 0, 0, nil)
	if result.Status == common.APIStatus.NotFound {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: result.Message,
		})
	} else if result.Status != common.APIStatus.Ok {
		return res.Respond(result)
	}

	sellerMap := map[string]*common.APIResponse{}
	reconciliationItems := result.Data.([]*model.ReconciliationItem)
	for i := range reconciliationItems {
		sellerMap[reconciliationItems[i].SellerCode] = nil
		model.ReconciliationItemDB.Delete(model.ReconciliationItem{ID: reconciliationItems[i].ID})
	}

	wg := &sync.WaitGroup{}
	for sellerCode := range sellerMap {
		value, err := reconcile_action.CalculateReconciliation(sellerCode, input.ScheduleIndex)
		if err != nil {
			if err.Error() == "Not found any matched reconciliation_item." {
				delFilter := model.Reconciliation{
					SellerCode:                 sellerCode,
					ReconcileScheduleTimeIndex: input.ScheduleIndex,
				}
				delRes := model.ReconciliationDB.Delete(delFilter)
				sellerMap[sellerCode] = delRes
			}
			continue
		}

		updater := model.Reconciliation{
			TotalBuyerFee:  &value.TotalBuyerFee,
			TotalRevenue:   &value.TotalRevenue,
			ListingFee:     &value.ListingFee,
			FulfillmentFee: &value.FulfillmentFee,
			PenaltyFee:     &value.PenaltyFee,
			BonusAmount:    &value.BonusAmount,
			TotalPayment:   &value.TotalPayment,
		}
		result = model.ReconciliationDB.UpdateOne(model.Reconciliation{
			SellerCode:                 sellerCode,
			ReconcileScheduleTimeIndex: input.ScheduleIndex,
		}, updater)
		sellerMap[sellerCode] = result
	}
	wg.Wait()

	data := []*common.APIResponse{}
	for sellerCode := range sellerMap {
		data = append(data, sellerMap[sellerCode])
	}

	return res.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   data,
	})
}

func UpdateAllowConfirm(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *model.Reconciliation
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	if input.ReconcileScheduleTimeIndex == "" || input.AllowConfirmTime.IsZero() {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing reconcileScheduleTimeIndex | allowConfirmTime",
		})
	}

	response := model.ReconciliationDB.UpdateMany(
		model.Reconciliation{
			ReconcileScheduleTimeIndex: input.ReconcileScheduleTimeIndex,
		}, model.Reconciliation{
			AllowConfirmTime: input.AllowConfirmTime,
		},
	)
	return res.Respond(response)
}

func AddReconciliationItem(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *InputProcessAddReconciliationItem
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	if input.CorrectCompletedTime != nil && input.CorrectCompletedTime.IsZero() {
		input.CorrectCompletedTime = nil
	}

	response := ProcessAddReconciliationItem(input)
	return res.Respond(response)
}

type InputProcessAddReconciliationItem struct {
	OrderID              int64                      `json:"order_id"`
	CorrectCompletedTime *time.Time                 `json:"correct_completed_time"`
	ExceptSellerCodes    []string                   `json:"except_seller_codes"`
	SellerCodes          []string                   `json:"seller_codes"`
	SellerSubCategory    []client.SellerSubCategory `json:"seller_sub_category"`
}

func ProcessAddReconciliationItem(input *InputProcessAddReconciliationItem) *common.APIResponse {
	if input.OrderID == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing order_id",
		}
	}

	orderResult := model.OrderDB.QueryOne(model.Order{OrderID: input.OrderID})
	if orderResult.Status != common.APIStatus.Ok {
		return orderResult
	}
	order := orderResult.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ProcessAddReconciliationItem")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	if input.CorrectCompletedTime == nil {
		input.CorrectCompletedTime = order.CompletedTime
	}
	if input.CorrectCompletedTime == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing completed_time in order",
		}
	}

	exceptSellerCodes := model.SELLER_INTERNALS
	if input.ExceptSellerCodes != nil && len(input.ExceptSellerCodes) > 0 {
		exceptSellerCodes = append(exceptSellerCodes, input.SellerCodes...)
	}

	sellerCond := bson.M{"$nin": exceptSellerCodes}
	if input.SellerCodes != nil && len(input.SellerCodes) > 0 {
		sellerCond["$in"] = input.SellerCodes
	}
	result := orderItemPartitionDB.Distinct(
		model.OrderItem{
			OrderID: input.OrderID,
			ComplexQuery: []*bson.M{
				{"seller_code": sellerCond},
			},
		},
		"seller_code",
	)
	if result.Status != common.APIStatus.Ok {
		return orderResult
	}

	scheduleTime, timeIndex, fromDate, toDate := ChooseReconciledTime(*input.CorrectCompletedTime)
	itemRes := model.ReconciliationItemDB.QueryOne(model.ReconciliationItem{
		OrderID: input.OrderID,
		FeeType: enum.FeeType.REVENUE,
		OperationAnd: []bson.M{
			{"reconcile_schedule_time_index": bson.M{"$ne": timeIndex}},
			{"seller_code": sellerCond},
		},
	})
	if itemRes.Status == common.APIStatus.Ok {
		fmt.Printf("%d has been reconciled in another schedule\n", input.OrderID)
		return itemRes
	}

	var specRevenue []model.ReconciliationSpecRevenue
	total := model.ReconciliationSpecRevenueDB.Count(model.ReconciliationSpecRevenue{Schedule: timeIndex}).Total
	specRevRes := model.ReconciliationSpecRevenueDB.Query(
		model.ReconciliationSpecRevenue{Schedule: timeIndex},
		0,
		total,
		nil,
	)
	if specRevRes.Status == common.APIStatus.Ok {
		specRevenue = specRevRes.Data.([]model.ReconciliationSpecRevenue)
	}

	sellerCodes := result.Data.([]interface{})
	listErr := make([]string, 0, len(sellerCodes))
	wg := &sync.WaitGroup{}
	for _, v := range sellerCodes {
		sellerCode := v.(string)
		switch sellerCode {
		case "MEDX", "MEDX-HN", "MARKETING", "MARKET", "MEDX_E", "DENX", "":
			println("Skip remake reconciliation for order-seller: [" + sellerCode + "]")
			continue
		}

		wg.Add(1)
		go doProcessForOrderItem(order, sellerCode, scheduleTime, timeIndex, fromDate, toDate, input.SellerSubCategory, specRevenue, wg, listErr)
	}
	wg.Wait()

	if len(listErr) == 0 {
		return &common.APIResponse{
			Status: common.APIStatus.Ok,
			Data:   listErr,
			Total:  int64(len(listErr)),
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Invalid,
		Data:   listErr,
		Total:  int64(len(listErr)),
	}
}

func doProcessForOrderItem(order *model.Order, sellerCode string, scheduleTime time.Time, timeIndex, fromDate, toDate string, categories []client.SellerSubCategory, specRevenue []model.ReconciliationSpecRevenue, wg *sync.WaitGroup, listErr []string) {
	if wg != nil {
		defer wg.Done()
	}

	defer func() {
		if r := recover(); r != nil {
			listErr = append(listErr, fmt.Sprintf("panic: %s - %s", r, string(debug.Stack())))
		}
	}()

	err := processForOrderItem(order, sellerCode, scheduleTime, timeIndex, fromDate, toDate, categories, specRevenue)
	if err != nil {
		listErr = append(listErr, err.Error())
	}
}

func processForOrderItem(order *model.Order, sellerCode string, scheduleTime time.Time, timeIndex, fromDate, toDate string, categories []client.SellerSubCategory, specRevenue []model.ReconciliationSpecRevenue) error {
	// Get reconciliation
	existReconciliation := false
	var reconciliation *model.Reconciliation

	reconciliationF := model.Reconciliation{
		SellerCode:                 sellerCode,
		ReconcileScheduleTimeIndex: timeIndex,
	}
	result := model.ReconciliationDB.QueryOne(reconciliationF)
	if result.Status == common.APIStatus.Ok {
		existReconciliation = true
		reconciliation = result.Data.([]*model.Reconciliation)[0]

		// Validate reconciliation_status
		if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
			return fmt.Errorf("reconciliation %s-%s is %s instead of %s", sellerCode, timeIndex, reconciliation.ReconciliationStatus, model.ReconciliationStatus.Waiting)
		}
	} else if result.Status == common.APIStatus.NotFound {
		reconciliation = &model.Reconciliation{
			RecCode:                    fmt.Sprintf("%s_%s", sellerCode, timeIndex),
			ReconciledTime:             &scheduleTime,
			AllowConfirmTime:           scheduleTime.AddDate(0, 0, conf.Config.AllowConfirmReconciliationAfter),
			FromTime:                   fromDate,
			ToTime:                     toDate,
			SellerCode:                 sellerCode,
			ReconcileScheduleTimeIndex: timeIndex,
			ReconciliationStatus:       model.ReconciliationStatus.Waiting,
		}
	} else {
		return fmt.Errorf("get reconciliation %s-%s failed", sellerCode, timeIndex)
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "processForOrderItem")
	if orderItemPartitionDB == nil {
		return errors.New(model.PARTITION_NOT_FOUND_RESPONSE.Message)
	}

	result = orderItemPartitionDB.Query(
		model.OrderItem{
			OrderID:    order.OrderID,
			SellerCode: sellerCode,
		},
		0,
		0,
		nil,
	)
	if result.Status != common.APIStatus.Ok {
		return errors.New(result.Message)
	}
	osItems := result.Data.([]*model.OrderItem)

	if categories == nil || len(categories) == 0 {
		categories = client.Services.Product.GetSellerSubCategories()
	}

	if specRevenue == nil || len(specRevenue) == 0 {
		total := model.ReconciliationSpecRevenueDB.Count(model.ReconciliationSpecRevenue{Schedule: timeIndex}).Total
		specRevRes := model.ReconciliationSpecRevenueDB.Query(model.ReconciliationSpecRevenue{Schedule: timeIndex}, 0, total, nil)
		if specRevRes.Status == common.APIStatus.Ok {
			specRevenue = specRevRes.Data.([]model.ReconciliationSpecRevenue)
		}
	}

	zero := 0
	// Create reconciliation items
	for i := range osItems {
		filter := model.ReconciliationItem{
			OrderID: order.OrderID,
			Sku:     osItems[i].Sku,
			FeeType: enum.FeeType.REVENUE,
			OperationAnd: []bson.M{
				{"reconcile_schedule_time_index": bson.M{"$ne": timeIndex}},
			},
		}
		itemRes := model.ReconciliationItemDB.QueryOne(filter)
		if itemRes.Status == common.APIStatus.Ok {
			fmt.Printf("%d - %d has been reconciled in another schedule\n", osItems[i].OrderID, osItems[i].ProductID)
			continue
		}

		if conf.Config.Env == "uat" || conf.Config.Env == "prd" && osItems[i].ProductID < 50000 {
			isSellerProduct := FindInt64(SellerProductIDs, osItems[i].ProductID)
			if isSellerProduct == -1 {
				continue
			}
		}

		if order.Status != enum.OrderState.Completed {
			riFilter := model.ReconciliationItem{
				SellerCode:                 osItems[i].SellerCode,
				ReconcileScheduleTimeIndex: timeIndex,
				FeeType:                    enum.FeeType.REVENUE,
				OrderID:                    osItems[i].OrderID,
				Sku:                        osItems[i].Sku,
			}
			riResult := model.ReconciliationItemDB.Delete(riFilter)
			if riResult.Status != common.APIStatus.Ok && riResult.Status != common.APIStatus.NotFound {
				return errors.New(result.Message)
			}

			// Update reconciliation
			value, err := reconcile_action.CalculateReconciliation(sellerCode, timeIndex)
			if err != nil {
				if err.Error() == "Not found any matched reconciliation_item." {
					return nil
				}

				return err
			}

			updater := model.Reconciliation{
				TotalBuyerFee:  &value.TotalBuyerFee,
				TotalRevenue:   &value.TotalRevenue,
				ListingFee:     &value.ListingFee,
				FulfillmentFee: &value.FulfillmentFee,
				PenaltyFee:     &value.PenaltyFee,
				BonusAmount:    &value.BonusAmount,
				TotalPayment:   &value.TotalPayment,
			}
			result = model.ReconciliationDB.UpdateOne(reconciliationF, updater)
			if result.Status != common.APIStatus.Ok {
				return errors.New("[ReconciliationDB.UpdateOne]: " + result.Message)
			}
			continue
		}

		if osItems[i].CompletedQuantity == nil {
			osItems[i].CompletedQuantity = &zero
		}

		if !existReconciliation {
			result = model.ReconciliationDB.Create(reconciliation)
			if result.Status != common.APIStatus.Ok {
				return errors.New(result.Message)
			}

			existReconciliation = true
			reconciliation = result.Data.([]*model.Reconciliation)[0]
		}

		priceType := enum.PriceType.FIXED_PRICE
		dealPricingType := "NO_DISCOUNT"
		dealChargeFee := "NONE"
		dealChargeFeeValue := 0
		revenue := osItems[i].SellerPrice
		totalBuyerFee := 0.0
		totalBuyerFeePerOrder := 0.0
		var priceRound *model.FeeValue

		if osItems[i].SkuPriceType != nil {
			priceType = *osItems[i].SkuPriceType
		}

		var feesApply []*model.FeeValue
		if osItems[i].Fee != nil && osItems[i].Fee.Result != nil {
			feesApply = osItems[i].Fee.Result
			for _, fee := range feesApply {
				if fee.FeeCode == "PRICE_ROUND" {
					priceRound = fee
				}
				if fee.FeeType == "" && fee.FeeCode == "3WQD9UBW961" {
					fee.FeeType = "CHARGE_BUYER"
				}
				if fee.FeeType == "" && fee.FeeCode == "1BRTE9AQ1PG" {
					fee.FeeType = "CHARGE_BUYER"
				}
				//if fee.FeeType == "CHARGE_BUYER" {
				//	totalBuyerFee += fee.FeeValue * float64(*osItems[i].CompletedQuantity)
				//	totalBuyerFeePerOrder += fee.FeeValue
				//}
			}
			//if totalBuyerFee == 0 {
			//	feesApply = nil
			//}
		}

		if osItems[i].Type == enum.ItemType.DEAL {
			dealChargeFee = osItems[i].ChargeDealFee
			dealChargeFeeValue = osItems[i].ChargeDealFeeValue
			if dealChargeFee == "" {
				dealChargeFee = "SELLER"
			}

			if osItems[i].DealPricingType != nil && *osItems[i].DealPricingType != "" {
				dealPricingType = *osItems[i].DealPricingType
			}

			switch dealChargeFee {
			case "SELLER":
				if dealChargeFeeValue == 0 {
					// Can't estimate difference revenue => use order-item revenue
					revenue = osItems[i].Price
				} else {
					if order.CreatedTime.Unix() < model.ApplyPricingNow.Unix() {
						if priceType == enum.PriceType.FIXED_REVENUE && dealPricingType == "PERCENTAGE" {
							revenue = osItems[i].SellerPrice - dealChargeFeeValue
						} else if priceType == enum.PriceType.FIXED_REVENUE && dealPricingType == "ABSOLUTE" {
							// Estimate original fee => extra fee applied (must be not included into charge deal on seller revenue)
							// Extra fee must be not included into charge deal on seller revenue
							originalPrice := osItems[i].Price + dealChargeFeeValue
							extraFee := originalPrice - osItems[i].SellerPrice
							revenue = osItems[i].SellerPrice - (dealChargeFeeValue - extraFee)
						} else {
							// revenue = revenue - dealChargeFeeValue
							revenue = osItems[i].Price
						}
					} else {
						revenue = osItems[i].SellerPrice - dealChargeFeeValue
					}
				}

			case "SELLER_MARKETPLACE":
				revenue = revenue - dealChargeFeeValue/2

			case "MARKETPLACE":
				revenue = osItems[i].SellerPrice
			}
		}

		// bookingRevenue := osItems[i].Quantity * revenue
		// totalRevenue := *osItems[i].CompletedQuantity * revenue

		err := handleCalculateReconcileForSku(
			sellerCode, timeIndex, revenue,
			order, osItems[i], categories, specRevenue,
			totalBuyerFee, totalBuyerFeePerOrder, priceType, feesApply, priceRound,
			dealPricingType, dealChargeFee, dealChargeFeeValue,
		)
		if err != nil {
			return err
		}
	}

	// Update reconciliation
	value, err := reconcile_action.CalculateReconciliation(sellerCode, timeIndex)
	if err != nil {
		if err.Error() == "Not found any matched reconciliation_item." {
			return nil
		}

		return err
	}

	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}
	result = model.ReconciliationDB.UpdateOne(reconciliationF, updater)
	if result.Status != common.APIStatus.Ok {
		return errors.New("[ReconciliationDB.UpdateOne]: " + result.Message)
	}
	return nil
}

/*
_type: SELLER_FEE | "" by default
*/
func findSpecRev(item *model.OrderItem, specRevenue []model.ReconciliationSpecRevenue, _type string) *model.ReconciliationSpecRevenue {
	for _, v := range specRevenue {
		switch _type {
		case "SELLER_FEE":
			if v.SellerCode == item.SellerCode && v.ProductID == item.ProductID && v.Sku == "" { // nếu spec (cũ) không có SKU thì xét theo ProductID
				return &v
			} else if v.SellerCode == item.SellerCode && v.Sku == item.Sku {
				if v.OrderID == 0 || v.OrderID == item.OrderID {
					return &v
				}
			}

		default:
			if v.OrderID == item.OrderID && v.ProductID == item.ProductID && v.Sku == "" { // nếu spec (cũ) không có SKU thì xét theo ProductID
				return &v
			} else if v.OrderID == item.OrderID && v.Sku == item.Sku {
				return &v
			}
		}
	}

	return nil
}

func getIngredientRatio(orderItem *model.OrderItem) (totalOriginalPrice int, priceRatio map[string]float64) {
	for _, subItem := range *orderItem.SubItems {
		totalOriginalPrice += subItem.SellerPrice * subItem.Quantity
	}

	priceRatio = map[string]float64{}
	for _, subItem := range *orderItem.SubItems {
		priceRatio[subItem.Sku] = float64(subItem.SellerPrice*subItem.Quantity) / float64(totalOriginalPrice)
	}

	return totalOriginalPrice, priceRatio
}

func getSellerCategoryFee(order *model.Order, orderItem *model.OrderItem, subCategories []client.SellerSubCategory, specRevenues []model.ReconciliationSpecRevenue) (listingRate float64, fulfillmentRate float64) {
	//specRev := findSpecRev(orderItem, specRevenues, "SELLER_FEE")
	//if specRev != nil && specRev.ListingRate != nil && specRev.FulfillmentRate != nil {
	//	return *specRev.ListingRate, *specRev.FulfillmentRate
	//}

	var productInfo *client.Product
	if orderItem.ProductID != 0 {
		productInfo = client.Services.Product.GetProductInfoByID(orderItem.ProductID, order.OrderID)
	} else {
		productInfo = client.Services.Product.GetProductInfo(orderItem.Sku, order.OrderID)
	}
	if productInfo != nil && productInfo.SellerSubCategoryCode != "" && subCategories != nil && len(subCategories) > 0 {
		for _, sellerSubCategory := range subCategories {
			if productInfo.SellerSubCategoryCode == sellerSubCategory.Code {
				listingRate = sellerSubCategory.SaleFeePercent / float64(100)
				fulfillmentRate = sellerSubCategory.FulfillmentFeePercent / float64(100)
				break
			}
		}
	}

	return listingRate, fulfillmentRate
}

func getSellerCategoryFeeWithLevel(order *model.Order, orderItem *model.OrderItem, categories []client.Category, subCategories []client.SellerSubCategory, specRevenues []model.ReconciliationSpecRevenue) (listingRate float64, fulfillmentRate float64) {
	specRev := findSpecRev(orderItem, specRevenues, "SELLER_FEE")
	if specRev != nil && specRev.ListingRate != nil && specRev.FulfillmentRate != nil {
		return *specRev.ListingRate, *specRev.FulfillmentRate
	}

	if order != nil && (order.CreatedTime == nil || order.CreatedTime.Before(conf.FirstDate2022)) {
		return getSellerCategoryFee(order, orderItem, subCategories, specRevenues)
	}

	if orderItem.SkuLevel == nil || string(*orderItem.SkuLevel) == "" {
		orderItem.SkuLevel = &enum.LevelSKU.LEVEL_2
	}

	var productInfo *client.Product
	if orderItem.ProductID != 0 {
		productInfo = client.Services.Product.GetProductInfoByID(orderItem.ProductID, orderItem.OrderID)
	} else {
		productInfo = client.Services.Product.GetProductInfo(orderItem.Sku, orderItem.OrderID)
	}
	if productInfo != nil && productInfo.SellerCategoryCode != "" && categories != nil && len(categories) > 0 {
		for _, category := range categories {
			if productInfo.SellerCategoryCode == category.CategoryCode &&
				string(*orderItem.SkuLevel) == category.Level {
				listingRate = category.SaleFeePercent / float64(100)
				fulfillmentRate = category.FulfillmentFeePercent / float64(100)
				break
			}
		}
	}

	return listingRate, fulfillmentRate
}

func handleCalculateReconcileForSku(
	sellerCode, scheduleTimeIndex string, revenue int,
	order *model.Order, orderItem *model.OrderItem, categories []client.SellerSubCategory, specRevenues []model.ReconciliationSpecRevenue,
	totalBuyerFee, totalBuyerFeePerOrder float64, priceType enum.PriceTypeValue, feesApply []*model.FeeValue, priceRound *model.FeeValue,
	dealPricingType string, dealChargeFee string, dealChargeFeeValue int,
) error {
	specRev := findSpecRev(orderItem, specRevenues, "")

	listingRate := 0.0
	fulfillmentRate := 0.0
	if orderItem.SubItems != nil {
		_, priceRatio := getIngredientRatio(orderItem)
		for i := range *orderItem.SubItems {
			subListingRate, subFulfillmentRate := getSellerCategoryFee(order, (*orderItem.SubItems)[i], categories, specRevenues)
			listingRate += subListingRate * priceRatio[(*orderItem.SubItems)[i].Sku]
			fulfillmentRate += subFulfillmentRate * priceRatio[(*orderItem.SubItems)[i].Sku]
		}
	} else {
		listingRate, fulfillmentRate = getSellerCategoryFee(order, orderItem, categories, specRevenues)
	}

	if specRev != nil {
		// if spec revenue => re-calculate value
		if specRev.Revenue != nil {
			revenue = *specRev.Revenue
		}

		if specRev.ReturnQty != nil {
			orderItem.ReturnedQuantity = specRev.ReturnQty
		}

		if specRev.DamageQty != nil {
			orderItem.DamageQuantity = specRev.DamageQty
		}

		if specRev.ListingRate != nil && specRev.FulfillmentRate != nil {
			listingRate = *specRev.ListingRate
			fulfillmentRate = *specRev.FulfillmentRate
		}

		if specRev.PriceType != nil {
			priceType = *specRev.PriceType
		}

		if specRev.DealPricing != nil {
			dealPricingType = *specRev.DealPricing
		}
		if specRev.DealCharge != nil {
			dealChargeFee = *specRev.DealCharge
		}
		if specRev.DealChargeValue != nil {
			dealChargeFeeValue = *specRev.DealChargeValue
		}
	}

	z := 0
	if orderItem.OutboundQuantity == nil {
		orderItem.OutboundQuantity = &z
	}
	if orderItem.DamageQuantity == nil {
		orderItem.DamageQuantity = &z
	}
	if orderItem.ReturnedQuantity == nil {
		orderItem.ReturnedQuantity = &z
	}

	bookingRevenue := orderItem.Quantity * revenue
	deliveryQty := *orderItem.OutboundQuantity - *orderItem.ReturnedQuantity + *orderItem.DamageQuantity
	totalRevenue := deliveryQty * revenue

	if feesApply != nil {
		for _, fee := range feesApply {
			if fee.FeeType == "CHARGE_BUYER" {
				totalBuyerFee += fee.FeeValue * float64(deliveryQty)
				totalBuyerFeePerOrder += fee.FeeValue
			}
		}

		if totalBuyerFee == 0 {
			feesApply = nil
		}
	}

	if priceType == enum.PriceType.FIXED_REVENUE {
		// Just round price on FIXED_REVENUE
		// FIXED_PRICE has been rounded when create sku
		if priceRound == nil {
			totalBeforeRounding := revenue + int(totalBuyerFeePerOrder)
			diff := float64(orderItem.Price - totalBeforeRounding)
			priceRound = &model.FeeValue{
				FeeCode:  "PRICE_ROUND",
				FeeType:  "CHARGE_BUYER",
				FeeValue: diff,
			}
			feesApply = append(feesApply, priceRound)

			totalBuyerFee += diff * float64(deliveryQty)
		}
	}

	// Lấy thời gian hoàn tất công nợ
	var completedDebtTime *time.Time
	bills, err := client.Services.Bill.GetBill(order.OrderID)
	if err == nil && len(bills) > 0 {
		completedDebtTime = bills[0].CompletedDebtTime
	}

	listingFee := int(math.Round(float64(totalRevenue) * listingRate))
	fulfillmentFee := int(math.Round(float64(totalRevenue) * fulfillmentRate))
	reconciliationItem := model.ReconciliationItem{
		BookingDate:                order.CreatedTime,
		SellerCode:                 sellerCode,
		ReconcileScheduleTimeIndex: scheduleTimeIndex,
		FeeType:                    enum.FeeType.REVENUE,
		OrderID:                    order.OrderID,
		ProductId:                  orderItem.ProductID,
		OrderCode:                  order.OrderCode,
		SaleOrderCode:              order.SaleOrderCode,
		Sku:                        orderItem.Sku,
		SkuPriceType:               priceType,
		DealCode:                   orderItem.DealCode,
		DealPricingType:            dealPricingType,
		DealChargeFee:              dealChargeFee,
		DealChargeFeeValue:         dealChargeFeeValue,
		Fees:                       feesApply,
		SellerPrice:                orderItem.SellerPrice,
		PriceInOrder:               &orderItem.Price,
		Price:                      &revenue,
		Quantity:                   orderItem.Quantity,
		DeliveryQuantity:           &deliveryQty,
		OrderedPrice:               &bookingRevenue,
		TotalRevenue:               &totalRevenue,
		CompletedQuantity:          orderItem.CompletedQuantity,
		OutboundQuantity:           orderItem.OutboundQuantity,
		ReturnQuantity:             orderItem.ReturnedQuantity,
		DamageQuantity:             orderItem.DamageQuantity,
		ListingFee:                 &listingFee,
		FulfillmentFee:             &fulfillmentFee,
		TotalBuyerFee:              &totalBuyerFee,
		ListingRate:                listingRate,
		FulfillmentRate:            fulfillmentRate,
		// thời gian hoàn tất đơn
		CompletedTime: order.CompletedTime,
		// thời gian hoàn tất công nợ
		CompletedDebtTime: completedDebtTime,
	}
	chargeBuyerFee := reconcile_action.HasToChargeBuyerFee(&reconciliationItem)
	if specRev != nil && specRev.ChargeBuyerFee != nil {
		chargeBuyerFee = *specRev.ChargeBuyerFee
	}
	reconciliationItem.HasChargedBuyerFee = &chargeBuyerFee

	payment := totalRevenue - (listingFee + fulfillmentFee)
	if chargeBuyerFee {
		payment -= int(math.Round(totalBuyerFee))
	}
	reconciliationItem.TotalPayment = &payment

	invoiceFilter := model.Invoice{
		OrderID:    order.OrderID,
		SellerCode: sellerCode,
	}
	invoiceRes := model.InvoiceDB.QueryOne(invoiceFilter)
	if invoiceRes.Status == common.APIStatus.Ok {
		invoice := invoiceRes.Data.([]*model.Invoice)[0]
		t := true
		if invoice.InvoiceStatus == model.InvoiceStatus.Completed {
			reconciliationItem.HasSentInvoice = &t
		} else if invoice.InvoiceDocumentURL != nil && *invoice.InvoiceDocumentURL != "" {
			reconciliationItem.HasSentInvoice = &t
		}
	}

	riFilter := model.ReconciliationItem{
		OrderID:   order.OrderID,
		OrderCode: order.OrderCode,
		Sku:       orderItem.Sku,
	}
	riResult := model.ReconciliationItemDB.Upsert(riFilter, reconciliationItem)
	if riResult.Status != common.APIStatus.Ok {
		return errors.New(riResult.Message)
	}

	return nil
}

func DistinctArrInt64(numbers []int64) []int64 {
	numMap := map[int64]int64{}
	for _, number := range numbers {
		numMap[number] = number
	}

	newNumbers := make([]int64, 0, len(numMap))
	for number := range numMap {
		newNumbers = append(newNumbers, number)
	}
	return newNumbers
}

/*
With Order: actionTime = completedTime
With Invoice: actionTime = deadline => if late deadline, seller will get a penalty

=> return: reconcile schedule time, time index
*/
func ChooseReconciledTime(actionTime time.Time) (scheduleTime time.Time, timeIndex string, fromDate string, toDate string) {
	actionTime.In(model.VNTimeZone)
	if actionTime.Day() <= 15 {
		fromDate = time.Date(actionTime.Year(), actionTime.Month(), 1, 0, 0, 0, 0, model.VNTimeZone).Format("2006-01-02")
		toDate = time.Date(actionTime.Year(), actionTime.Month(), 15, 0, 0, 0, 0, model.VNTimeZone).Format("2006-01-02")

		// if 01~15/Month => make reconciliation on 00:00-16/Month
		scheduleTime = time.Date(actionTime.Year(), actionTime.Month(), 16, 0, 0, 0, 0, model.VNTimeZone)

	} else {
		fromDate = time.Date(actionTime.Year(), actionTime.Month(), 16, 0, 0, 0, 0, model.VNTimeZone).Format("2006-01-02")
		toDate = time.Date(actionTime.Year(), actionTime.Month(), 1, 0, 0, 0, 0, model.VNTimeZone).AddDate(0, 1, 0).AddDate(0, 0, -1).Format("2006-01-02")

		// if 16~.../Month => make reconciliation on 00:00-01/Month+1
		// should be 01/Month + 1 month instead of 01/Month+1 on new Date()
		scheduleTime = time.Date(actionTime.Year(), actionTime.Month(), 1, 0, 0, 0, 0, model.VNTimeZone).AddDate(0, 1, 0)
	}

	timeIndex = scheduleTime.Format("20060102")

	return
}

func WarmupPriceInOrder(req sdk.APIRequest, res sdk.APIResponder) error {
	filter := model.ReconciliationItem{
		FeeType:                    enum.FeeType.REVENUE,
		ReconcileScheduleTimeIndex: "20210901",
		OperationAnd: []bson.M{
			{
				"price_in_order": bson.M{"$exists": false},
			},
			{
				"last_updated_time": bson.M{"$lt": time.Now().Add(-time.Minute)},
			},
		},
	}
	result := model.ReconciliationItemDB.UpdateOne(filter, model.ReconciliationItem{})
	if result.Status == common.APIStatus.Ok {
		item := result.Data.([]*model.ReconciliationItem)[0]

		orderItemF := model.OrderItem{
			OrderCode: item.OrderCode,
			OrderID:   item.OrderID,
			Sku:       item.Sku,
		}

		orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{OrderID: item.OrderID, OrderCode: item.OrderCode}, "WarmupPriceInOrder")
		if orderItemPartitionDB == nil {
			return errors.New(model.PARTITION_NOT_FOUND_RESPONSE.Message)
		}

		itemRes := orderItemPartitionDB.QueryOne(orderItemF)
		if result.Status == common.APIStatus.Ok {
			orderItem := itemRes.Data.([]*model.OrderItem)[0]

			updater := model.ReconciliationItem{
				PriceInOrder: &orderItem.Price,
			}
			result = model.ReconciliationItemDB.UpdateOne(model.ReconciliationItem{
				FeeType:                    item.FeeType,
				ReconcileScheduleTimeIndex: item.ReconcileScheduleTimeIndex,
				OrderCode:                  item.OrderCode,
				OrderID:                    item.OrderID,
				Sku:                        item.Sku,
			}, updater)
		} else {
			return res.Respond(itemRes)
		}
	}
	return res.Respond(result)
}

func WarmupManyPriceInOrder(req sdk.APIRequest, res sdk.APIResponder) error {
	filter := model.ReconciliationItem{
		FeeType:                    enum.FeeType.REVENUE,
		ReconcileScheduleTimeIndex: "20210901",
		OperationAnd: []bson.M{
			{
				"price_in_order": bson.M{"$exists": false},
			},
			{
				"last_updated_time": bson.M{"$lt": time.Now().Add(-time.Minute)},
			},
		},
	}
	go func() {
		for true {
			result := model.ReconciliationItemDB.UpdateOne(filter, model.ReconciliationItem{})
			if result.Status == common.APIStatus.Ok {
				item := result.Data.([]*model.ReconciliationItem)[0]

				orderItemF := model.OrderItem{
					OrderCode: item.OrderCode,
					OrderID:   item.OrderID,
					Sku:       item.Sku,
				}

				orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{OrderID: item.OrderID, OrderCode: item.OrderCode}, "WarmupManyPriceInOrder")
				if orderItemPartitionDB == nil {
					continue
				}

				itemRes := orderItemPartitionDB.QueryOne(orderItemF)
				if result.Status == common.APIStatus.Ok {
					orderItem := itemRes.Data.([]*model.OrderItem)[0]

					updater := model.ReconciliationItem{
						PriceInOrder: &orderItem.Price,
					}
					model.ReconciliationItemDB.UpdateOne(model.ReconciliationItem{
						FeeType:                    item.FeeType,
						ReconcileScheduleTimeIndex: item.ReconcileScheduleTimeIndex,
						OrderCode:                  item.OrderCode,
						OrderID:                    item.OrderID,
						Sku:                        item.Sku,
					}, updater)
				}
			} else {
				break
			}
		}
	}()
	return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok})
}

func WarmupDeal(req sdk.APIRequest, res sdk.APIResponder) error {
	filter := model.ReconciliationItem{
		FeeType: enum.FeeType.REVENUE,
		OperationAnd: []bson.M{
			{
				"reconcile_schedule_time_index": bson.M{"$ne": nil},
			},
			{
				"deal_code": bson.M{"$ne": nil},
			},
			{
				"last_updated_time": bson.M{"$lt": time.Now().Add(-time.Minute)},
			},
		},
	}
	go func() {
		for true {
			result := model.ReconciliationItemDB.UpdateOne(filter, model.ReconciliationItem{})
			if result.Status == common.APIStatus.Ok {
				item := result.Data.([]*model.ReconciliationItem)[0]
				item.Deal = &model.DealInfo{
					Code: func() string {
						if item.DealCode == nil {
							return ""
						}
						return *item.DealCode
					}(),
					PricingType:    item.DealPricingType,
					ChargeFee:      item.DealChargeFee,
					ChargeFeeValue: item.DealChargeFeeValue,
				}
				item.DealCode = nil
				item.DealPricingType = ""
				item.DealChargeFee = ""
				item.DealChargeFeeValue = 0

				model.ReconciliationItemDB.ReplaceOneWithOption(model.ReconciliationItem{
					ID: item.ID,
				}, item)
			} else {
				break
			}
		}
	}()
	return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok})
}
