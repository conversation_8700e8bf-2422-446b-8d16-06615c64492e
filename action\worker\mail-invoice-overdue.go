package worker

import (
	"bytes"
	"fmt"
	"html/template"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const DEFAULT_MAIL_OVERDUE_DAYS = 3

type MailData struct {
	SellerEmail string
	SellerName  string
	SellerCode  string
	Invoices    []*model.Invoice
}

func ProcessMailInvoiceOverdue(
	now *time.Time,
	config *schedule.Config,
) (err error, note string, nextRun *time.Time) {
	fmt.Println("Start process", config.Topic)
	defer fmt.Println("Finish process", config.Topic)

	if now == nil {
		_t := time.Now()
		now = &_t
	}
	vnNow := now.In(utils.VNTimeZone)

	MailInvoiceOverdue(vnNow)

	timeNextRun := time.Date(vnNow.Year(), vnNow.Month(), vnNow.Day(), 10, 0, 0, 0, utils.VNTimeZone).
		AddDate(0, 0, 1)
	nextRun = &timeNextRun

	return
}

func MailInvoiceOverdue(now time.Time) {
	mapSellerCodeInvoices := getSellerInvoicesMap(now)

	sellerCodes := make([]string, 0, len(mapSellerCodeInvoices))
	for sellerCode := range mapSellerCodeInvoices {
		sellerCodes = append(sellerCodes, sellerCode)
	}

	mapSeller := client.Services.Seller.GetSellerMapFromCodes(sellerCodes)
	for sellerCode, invoices := range mapSellerCodeInvoices {
		// hard-code skip seller
		// TODO: change to config later
		if sellerCode == "WNWOQEM8DN" ||
			sellerCode == "W4PWOQEM8D" {
			continue
		}

		SendInvoiceOverdueMail(mapSeller[sellerCode], invoices)
	}
}

func getInvoiceSetting() int {
	confResp := client.Services.Seller.GetSettingForSC("SELLER_INVOICE_CONFIG")
	if confResp.Status != common.APIStatus.Ok {
		return DEFAULT_MAIL_OVERDUE_DAYS
	}

	settings := confResp.Data.([]client.SettingForSC)[0]

	for _, item := range settings.Data {
		if item["Key"] == "overdueWarningDays" {
			if value, ok := item["Value"].(float64); ok {
				return int(value)
			}
		}
	}

	return DEFAULT_MAIL_OVERDUE_DAYS
}

func getSellerInvoicesMap(now time.Time) map[string][]*model.Invoice {
	var offset, limit int64 = 0, 100
	overdueWarningDays := getInvoiceSetting()
	start, end := getDaysAfter(now, overdueWarningDays)
	mapSellerCodeInvoices := map[string][]*model.Invoice{}

	for {
		invoicesRes := model.InvoiceDB.Query(model.Invoice{
			InvoiceStatus: model.InvoiceStatus.Waiting,
			OperationAnd: []bson.M{
				{
					"invoice_deadline": bson.M{
						"$gte": start,
						"$lte": end,
					},
				},
			},
		}, offset, limit, &primitive.M{"invoice_deadline": 1})
		if invoicesRes.Status != common.APIStatus.Ok {
			break
		}

		invoices := invoicesRes.Data.([]*model.Invoice)
		for _, invoice := range invoices {
			if invoice.InvoiceDocumentURL != nil &&
				*invoice.InvoiceDocumentURL != "" {
				continue
			}

			if invoice.Request == nil ||
				!*invoice.Request {
				continue
			}

			if invoice.DeliveredTime == nil &&
				invoice.InvoiceDeadline != nil {
				tempDeliveredTime := invoice.InvoiceDeadline.AddDate(0, 0, -10)
				invoice.DeliveredTime = &tempDeliveredTime
			}

			mapSellerCodeInvoices[invoice.SellerCode] = append(
				mapSellerCodeInvoices[invoice.SellerCode], invoice)
		}

		if len(invoices) < int(limit) {
			break
		}
		offset += limit
	}

	return mapSellerCodeInvoices
}

func getDaysAfter(now time.Time, daysAfter int) (time.Time, time.Time) {
	date := time.Date(now.Year(), now.Month(), now.Day()+daysAfter, 0, 0, 0, 0, utils.VNTimeZone)
	return date, date.AddDate(0, 0, 1)
}

func SendInvoiceOverdueMail(seller *client.Seller, invoices []*model.Invoice) *common.APIResponse {
	if seller == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Seller nil",
			ErrorCode: "SELLER_NIL",
		}
	}

	if len(invoices) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invoices empty",
			ErrorCode: "INVOICES_EMPTY",
		}
	}

	to := []model.Email{
		{
			Email: seller.Email,
			Name:  seller.Name,
		},
	}
	subject := "Nhắc nhở xuất hoá đơn cho khách hàng"

	data := MailData{
		SellerEmail: seller.Email,
		SellerName:  seller.Name,
		SellerCode:  seller.Code,
		Invoices:    invoices,
	}

	emailTmpl, err := template.ParseFiles("template/remind-invoice-issuance.html")
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "parse template error" + err.Error(),
			ErrorCode: "PARSE_TEMPLATE_ERROR",
		}
	}

	var contentBytes bytes.Buffer
	if err := emailTmpl.Execute(&contentBytes, data); err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "execute template error" + err.Error(),
			ErrorCode: "EXECUTE_TEMPLATE_ERROR",
		}
	}

	content := []model.Content{
		{
			Type:  "text/html",
			Value: contentBytes.String(),
		},
	}

	return client.Services.MailService.SendMailFromSellerCenter(to, subject, content, &[]string{
		seller.Code,
		utils.GetTimeNow().Format("2006-01-02"),
	})
}
