package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

// OrderCalcAmount ...
func OrderCalcAmount(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderCalcAmountRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.<PERSON><PERSON><PERSON>(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.OrderCalcAmount(&input))
}

func OrderCalcFormat(req sdk.APIRequest, resp sdk.APIResponder) error {
	orderID := sdk.ParseInt64(req.GetParam("orderID"), 0)
	return resp.Respond(action.OrderCalcFormat(orderID))
}

func SkuChangeAlert(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		itemCode    = req.GetParam("itemCode")
		skuName     = req.GetParam("skuName")
		status      = req.GetParam("status")
		priceStatus = req.GetParam("priceStatus")
	)

	if itemCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Skip notify",
		})
	}

	return resp.Respond(action.SkuChangeAlert(itemCode, skuName, status, priceStatus))
}
