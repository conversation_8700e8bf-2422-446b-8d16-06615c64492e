package model

type IncreaseSkuPriceRequest struct {
	TicketId   int64  `json:"ticketId,omitempty" bson:"ticketId,omitempty"`
	TicketCode string `json:"ticketCode,omitempty" bson:"ticketCode,omitempty"`
	Sku        string `json:"sku,omitempty" bson:"sku,omitempty"`
}

type PoorQualityProductRequest struct {
	OrderId       int64  `json:"orderId,omitempty" bson:"orderId,omitempty"`
	OrderCode     string `json:"orderCode,omitempty" bson:"orderCode,omitempty"`
	SaleOrderCode string `json:"saleOrderCode,omitempty" bson:"saleOrderCode,omitempty"`
	TicketId      int64  `json:"ticketId,omitempty" bson:"ticketId,omitempty"`
	TicketCode    string `json:"ticketCode,omitempty" bson:"ticketCode,omitempty"`

	SkuInfos []*PoorQualitySku `json:"skuInfos,omitempty" bson:"skuInfos,omitempty"`
	Skus     []string          `json:"skus,omitempty" bson:"skus,omitempty"` // deprecated
}

type PoorQualitySku struct {
	Sku   string `json:"sku,omitempty" bson:"sku,omitempty"`
	Price *int64 `json:"price,omitempty" bson:"price,omitempty"`
}

type BizHouseholdTaxItem struct {
	SellerCode                 string `json:"sellerCode,omitempty"`
	ReconcileScheduleTimeIndex string `json:"reconcileScheduleTimeIndex,omitempty"`
}
