package reconcile_action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func PushChargeSellerIncreaseProductPriceToQueue(input *model.IncreaseSkuPriceRequest) *common.APIResponse {
	if input.Sku == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "sku is required",
			ErrorCode: "INVALID_INPUT",
		}
	}

	err := model.ChargeSellerIncreasePriceJob.Push(input, &job.JobItemMetadata{
		Topic: "default",
		Keys:  []string{input.Sku},
	})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "PUSH_TO_QUEUE_ERROR",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "push to queue success",
	}
}
