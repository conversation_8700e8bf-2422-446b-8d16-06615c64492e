package reconcile_action

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

func GetReconcileTime(
	schedule string,
	now time.Time,
	scheduleSettings []*client.ReconciliationScheduleSetting,
) (from, scheduleTime time.Time, scheduleIndex string, appliedBefore time.Time, err error) {
	y, m, d := now.Date()

	// Process Reconciliation Schedule Settings in https://internal.thuocsi.vn/seller/settings/reconciliation
	for _, setting := range scheduleSettings {
		if schedule != string(setting.Schedule.Type) {
			continue
		}
		switch setting.Setting.Type {
		case enum.ScheduleSettingType.SKIP_TO_NEXT:
			if setting.Schedule.TimeFrom == nil || setting.Schedule.TimeTo == nil {
				fmt.Println("Setting for schedule [" + setting.Index + "] is missing TimeFrom and TimeTo")
				continue
			}
			// Nếu now nằm trong [TimeFrom, TimeTo] -> trả về data của nextSchedule
			if (*setting.Schedule.TimeFrom).Before(now) && now.Before(*setting.Schedule.TimeTo) {
				from = *setting.Schedule.TimeFrom
				scheduleTime = *setting.Schedule.TimeTo
				scheduleIndex = setting.NextSchedule.Index
				appliedBefore = getAppliedBefore(setting.NextSchedule.TimeFrom)
				return
			}

		case enum.ScheduleSettingType.CHANGE_TIME:
			if setting.Setting.NewTimeFrom == nil || setting.Setting.NewTimeTo == nil {
				fmt.Println("Setting for schedule [" + setting.Index + "] is missing NewTimeFrom and NewTimeTo")
				continue
			}
			// Nếu now nằm trong [NewTimeFrom, NewTimeTo] -> trả về data theo [NewTimeFrom, NewTimeTo]
			if (*setting.Setting.NewTimeFrom).Before(now) && now.Before(*setting.Setting.NewTimeTo) {
				from = *setting.Setting.NewTimeFrom
				scheduleTime = *setting.Setting.NewTimeTo
				scheduleIndex = setting.Schedule.Index
				appliedBefore = getAppliedBefore(setting.Setting.NewTimeFrom)
				return
			}

			// Nếu có setting cho lượt đối soát mà now nằm ngoài -> Skip, không xử lý
			continue
		}
	}

	switch schedule {
	case "SCHEDULE_01_TO_15":
		if d > 18 {
			break
		}

		from = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102")
		appliedBefore = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_16_TO_END":
		if d < 16 && d > 2 {
			break
		}

		if d <= 2 {
			from = time.Date(y, m-1, 16, 0, 0, 0, 0, model.VNTimeZone)
			scheduleTime = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)
		} else {
			from = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)
			scheduleTime = time.Date(y, m+1, 1, 0, 0, 0, 0, model.VNTimeZone)
		}
		scheduleIndex = scheduleTime.Format("20060102")
		appliedBefore = time.Date(from.Year(), from.Month(), 16, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_01_TO_07":
		if d > 9 {
			break
		}

		from = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 8, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_08_TO_15":
		if d < 8 || d > 17 {
			break
		}

		from = time.Date(y, m, 8, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_16_TO_22":
		if d < 16 || d > 24 {
			break
		}

		from = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 23, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_23_TO_END":
		if d < 23 && d > 2 {
			break
		}

		if d <= 2 {
			from = time.Date(y, m-1, 23, 0, 0, 0, 0, model.VNTimeZone)
			scheduleTime = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)
		} else {
			from = time.Date(y, m, 23, 0, 0, 0, 0, model.VNTimeZone)
			scheduleTime = time.Date(y, m+1, 1, 0, 0, 0, 0, model.VNTimeZone)
		}
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(from.Year(), from.Month(), 16, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_01_TO_03":
		if d > 5 {
			break
		}

		from = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 4, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_04_TO_06":
		if d < 4 || d > 7 {
			break
		}

		from = time.Date(y, m, 4, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 7, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_07_TO_09":
		if d < 7 || d > 10 {
			break
		}

		from = time.Date(y, m, 7, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 10, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_10_TO_12":
		if d < 10 || d > 13 {
			break
		}

		from = time.Date(y, m, 10, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 13, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_13_TO_15":
		if d < 13 || d > 16 {
			break
		}

		from = time.Date(y, m, 13, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_16_TO_18":
		if d < 16 || d > 19 {
			break
		}

		from = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 19, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_19_TO_21":
		if d < 19 || d > 22 {
			break
		}

		from = time.Date(y, m, 19, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 22, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_22_TO_24":
		if d < 22 || d > 25 {
			break
		}

		from = time.Date(y, m, 22, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 25, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_25_TO_27":
		if d < 25 || d > 28 {
			break
		}

		from = time.Date(y, m, 25, 0, 0, 0, 0, model.VNTimeZone)
		scheduleTime = time.Date(y, m, 28, 0, 0, 0, 0, model.VNTimeZone)
		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(y, m, 16, 0, 0, 0, 0, model.VNTimeZone)

	case "SCHEDULE_28_TO_END":
		if d < 28 && d > 1 {
			break
		}

		if d == 1 {
			from = time.Date(y, m-1, 28, 0, 0, 0, 0, model.VNTimeZone)
			scheduleTime = time.Date(y, m, 1, 0, 0, 0, 0, model.VNTimeZone)
		} else {
			from = time.Date(y, m, 28, 0, 0, 0, 0, model.VNTimeZone)
			scheduleTime = time.Date(y, m+1, 1, 0, 0, 0, 0, model.VNTimeZone)
		}

		scheduleIndex = scheduleTime.Format("20060102") + "." + from.Format("20060102")
		appliedBefore = time.Date(from.Year(), from.Month(), 16, 0, 0, 0, 0, model.VNTimeZone)

	default:
		err = errors.New("unknown schedule: " + schedule)
	}

	return
}

func getAppliedBefore(date *time.Time) time.Time {
	if 16 <= date.Day() {
		return time.Date(date.Year(), date.Month(), 16, date.Hour(), date.Minute(), date.Second(), date.Nanosecond(), date.Location())
	} else {
		return time.Date(date.Year(), date.Month(), 1, date.Hour(), date.Minute(), date.Second(), date.Nanosecond(), date.Location())
	}
}

func GetFromToDateFromIndex(scheduleIndex string) (fromDate, toDate time.Time) {
	layout := "20060102"

	if strings.Contains(scheduleIndex, ".") {
		parts := strings.Split(scheduleIndex, ".")
		fromDate, _ = time.Parse(layout, parts[1])
		toDate, _ = time.Parse(layout, parts[0])
	} else {
		fromDate, _ = time.Parse(layout, scheduleIndex)
		toDate, _ = time.Parse(layout, scheduleIndex)

		if toDate.Day() == 16 {
			fromDate = time.Date(fromDate.Year(), fromDate.Month(), 1, 0, 0, 0, 0, time.UTC)
		} else {
			fromDate = time.Date(fromDate.Year(), fromDate.Month()-1, 16, 0, 0, 0, 0, time.UTC)
		}
	}

	toDate = toDate.AddDate(0, 0, -1)

	return fromDate, toDate
}

// ConvertTimeIndexToSchedule converts ReconcileScheduleTimeIndex to schedule topic and start date
// e.g. "20250616" -> "SCHEDULE_01_TO_15", "2025-06-01T10:00:00.000Z"
// +------------------+--------------------+---------------------------+
// | Input            | Schedule Topic     | Start Date                |
// +------------------+--------------------+---------------------------+
// | 20250616         | SCHEDULE_01_TO_15  | 2025-06-01T10:00:00.000Z  |
// | 20250601         | SCHEDULE_16_TO_END | 2025-05-16T10:00:00.000Z  |
// | 20250608.20250601| SCHEDULE_01_TO_07  | 2025-06-01T10:00:00.000Z  |
// | 20250516.20250508| SCHEDULE_08_TO_15  | 2025-05-08T10:00:00.000Z  |
// | 20250523.20250516| SCHEDULE_16_TO_22  | 2025-05-16T10:00:00.000Z  |
// | 20250601.20250523| SCHEDULE_23_TO_END | 2025-05-23T10:00:00.000Z  |
// | 20250504.20250501| SCHEDULE_01_TO_03  | 2025-05-01T10:00:00.000Z  |
// | 20250507.20250504| SCHEDULE_04_TO_06  | 2025-05-04T10:00:00.000Z  |
// | 20250510.20250507| SCHEDULE_07_TO_09  | 2025-05-07T10:00:00.000Z  |
// | 20250513.20250510| SCHEDULE_10_TO_12  | 2025-05-10T10:00:00.000Z  |
// | 20250516.20250513| SCHEDULE_13_TO_15  | 2025-05-13T10:00:00.000Z  |
// | 20250519.20250516| SCHEDULE_16_TO_18  | 2025-05-16T10:00:00.000Z  |
// | 20250522.20250519| SCHEDULE_19_TO_21  | 2025-05-19T10:00:00.000Z  |
// | 20250525.20250522| SCHEDULE_22_TO_24  | 2025-05-22T10:00:00.000Z  |
// | 20250528.20250525| SCHEDULE_25_TO_27  | 2025-05-25T10:00:00.000Z  |
// | 20250601.20250528| SCHEDULE_28_TO_END | 2025-05-28T10:00:00.000Z  |
// +------------------+--------------------+---------------------------+
//
// Format explanation:
// - Simple format "YYYYMMDD": end date only, start date calculated based on schedule type
// - Detailed format "YYYYMMDD.YYYYMMDD": end date + start date, start date extracted directly
// - All returned dates have time set to 10:00:00.000Z UTC
// - Returns ("", nil) for invalid inputs or unknown schedule patterns
func ConvertTimeIndexToSchedule(scheduleIndex string) (string, *time.Time) {
	if scheduleIndex == "" {
		return "", nil
	}

	layout := "20060102"

	if strings.Contains(scheduleIndex, ".") {
		// Format: "endDate.startDate" for detailed schedules
		parts := strings.Split(scheduleIndex, ".")
		if len(parts) != 2 {
			return "", nil
		}

		fromDate, err1 := time.Parse(layout, parts[1])
		toDate, err2 := time.Parse(layout, parts[0])
		if err1 != nil || err2 != nil {
			return "", nil
		}

		fromDay := fromDate.Day()
		toDay := toDate.Day()

		// Create start date with 10:00:00 UTC time
		startDate := time.Date(fromDate.Year(), fromDate.Month(), fromDay, 10, 0, 0, 0, time.UTC)

		// Map date ranges to schedule topics
		switch {
		case fromDay == 1 && toDay == 8:
			return "SCHEDULE_01_TO_07", &startDate
		case fromDay == 8 && toDay == 16:
			return "SCHEDULE_08_TO_15", &startDate
		case fromDay == 16 && toDay == 23:
			return "SCHEDULE_16_TO_22", &startDate
		case fromDay == 23 && toDay == 1: // crosses month boundary
			return "SCHEDULE_23_TO_END", &startDate
		case fromDay == 1 && toDay == 4:
			return "SCHEDULE_01_TO_03", &startDate
		case fromDay == 4 && toDay == 7:
			return "SCHEDULE_04_TO_06", &startDate
		case fromDay == 7 && toDay == 10:
			return "SCHEDULE_07_TO_09", &startDate
		case fromDay == 10 && toDay == 13:
			return "SCHEDULE_10_TO_12", &startDate
		case fromDay == 13 && toDay == 16:
			return "SCHEDULE_13_TO_15", &startDate
		case fromDay == 16 && toDay == 19:
			return "SCHEDULE_16_TO_18", &startDate
		case fromDay == 19 && toDay == 22:
			return "SCHEDULE_19_TO_21", &startDate
		case fromDay == 22 && toDay == 25:
			return "SCHEDULE_22_TO_24", &startDate
		case fromDay == 25 && toDay == 28:
			return "SCHEDULE_25_TO_27", &startDate
		case fromDay == 28 && toDay == 1: // crosses month boundary
			return "SCHEDULE_28_TO_END", &startDate
		}
	} else {
		// Format: "endDate" for simple schedules
		toDate, err := time.Parse(layout, scheduleIndex)
		if err != nil {
			return "", nil
		}

		toDay := toDate.Day()
		year := toDate.Year()
		month := toDate.Month()

		// Map end dates to schedule topics and calculate start dates
		switch toDay {
		case 16:
			// SCHEDULE_01_TO_15: starts on 1st of the same month
			startDate := time.Date(year, month, 1, 10, 0, 0, 0, time.UTC)
			return "SCHEDULE_01_TO_15", &startDate
		case 1:
			// SCHEDULE_16_TO_END: starts on 16th of the previous month
			startDate := time.Date(year, month-1, 16, 10, 0, 0, 0, time.UTC)
			return "SCHEDULE_16_TO_END", &startDate
		}
	}

	return "", nil
}
