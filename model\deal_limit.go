package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DealLimit struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	DealType               string `json:"dealType,omitempty" bson:"deal_type,omitempty"` // DEAL || CAMPAIGN
	DealCode               string `json:"dealCode,omitempty" bson:"deal_code,omitempty"`
	Sku                    string `json:"sku,omitempty" bson:"sku,omitempty"`
	Quantity               int    `json:"quantity" bson:"quantity,omitempty"`
	MaxQuantityPerCustomer int    `json:"maxQuantityPerCustomer" bson:"max_quantity_per_customer,omitempty"`
	CustomerID             int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	Key                    string `json:"key,omitempty" bson:"key,omitempty"`
}

var DealLimitDB = &db.Instance{
	ColName:        "deal_limit",
	TemplateObject: &DealLimit{},
}

func InitDealLimitModel(s *mongo.Database) {
	DealLimitDB.ApplyDatabase(s)
}

var DealLimitCacheDB = &db.Instance{
	ColName:        "deal_limit",
	TemplateObject: &DealLimit{},
}

func InitDealLimitCacheModel(s *mongo.Database) {
	DealLimitCacheDB.ApplyDatabase(s)
	t := true
	_ = DealLimitCacheDB.CreateIndex(bson.D{
		bson.E{Key: "customer_id", Value: 1},
		bson.E{Key: "key", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
