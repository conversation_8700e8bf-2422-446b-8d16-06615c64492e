package action

import (
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func UpdateCartInfo(acc *model.Account, input *model.CartUpdateInfo) *common.APIResponse {
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}
	cartRes := model.CartDB.QueryOne(&model.Cart{
		CustomerID: customer.CustomerID,
	})
	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]

	t := time.Now()
	input.LastActionTime = &t
	model.CartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, input)

	if input.PaymentMethod != "" || input.DeliveryMethod != "" || input.CustomerProvinceCode != "" {
		cartCacheRes := model.CartCacheDB.QueryOne(bson.M{"account_id": acc.AccountID})
		if cartCacheRes.Status != common.APIStatus.Ok {
			return cartCacheRes
		}
		cartCache := cartCacheRes.Data.([]*model.CartCache)[0]
		cart.TotalPrice = cartCache.TotalPrice
		cart.Price = cartCache.Price
		cart.SubPrice = cartCache.SubPrice
		if input.PaymentMethod != "" {
			cart.PaymentMethod = input.PaymentMethod
		}
		if input.DeliveryMethod != "" {
			cart.DeliveryMethod = input.DeliveryMethod
		}
		if input.CustomerProvinceCode != "" {
			cart.CustomerProvinceCode = input.CustomerProvinceCode
		}
		handleExtraFee(customer, cart)
		pays, _ := handleProcessDeliveryAndPaymentMethod(cart, customer)
		handlePaymentMethodCredit(cart, customer, pays)
		handlePartnerPaymentMethodFee(cart)
		input.TotalPrice = cart.TotalPrice
		input.ExtraFee = utils.ParseInt64ToPointer(cart.ExtraFee)
		input.PaymentMethodFee = utils.ParseInt64ToPointer(cart.PaymentMethodFee)
		input.PaymentMethodPercentage = cart.PaymentMethodPercentage
		input.DeliveryMethodFee = utils.ParseInt64ToPointer(cart.DeliveryMethodFee)
		input.TotalPriceBeforePartnerPaymentFee = utils.ParseIntToPointer(cart.TotalPriceBeforePartnerPaymentFee)
		if cart.PartnerPaymentMethod == nil {
			input.PartnerPaymentMethod = &model.PartnerPaymentMethod{}
		} else {
			input.PartnerPaymentMethod = cart.PartnerPaymentMethod
		}
	}

	return model.CartCacheDB.UpdateOne(&model.CartCache{CartID: cart.CartID}, input)
}

func GetCartInfo(acc *model.Account, getVoucherAuto bool, redeemCodeRemovedArr []string, autoRemoveVoucherAutoInvalid bool, screen string) *common.APIResponse {
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cartRes := model.CartDB.QueryOne(&model.Cart{
		CustomerID: customer.CustomerID,
	})
	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]

	cartItemRes := model.CartItemDB.Query(&model.CartItem{CartID: cart.CartID}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		_ = model.CartDB.Delete(&model.Cart{CustomerID: customer.CustomerID})
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Not found any matched cart",
		}
	}
	cart.Items = cartItemRes.Data.([]*model.CartItem)
	cart.ProvinceCode = customer.ProvinceCode
	cart.DistrictCode = customer.DistrictCode
	cart.WardCode = customer.WardCode
	cart.CustomerScope = customer.Scope
	cart.SourceDetail = acc.SourceDetail

	// if customer.Scope == "DENTISTRY" {
	// 	cart.SystemDisplay = "BUYDENTAL"
	// } else {
	cart.SystemDisplay = "BUYMED"
	// }
	cart.GetVoucherAutoApply = getVoucherAuto
	cart.RedeemCodeRemovedArr = cart.RedeemCodeRemoved
	cart.AutoRemoveVoucherAutoInvalid = autoRemoveVoucherAutoInvalid
	cart.Screen = screen

	resetCartPrice(cart)
	_ = handleProcessContentItems(cart, customer, nil, true)
	_ = handleProcessPriceItems(customer, cart)
	pays, _ := handleProcessDeliveryAndPaymentMethod(cart, customer)
	_ = handleApplyVoucherCode(cart, customer, "GetCartInfo")
	//skipReturnAutoApplyVoucher(cart)
	_ = handleDeliveryDate(cart, common.False)
	_ = handleExtraFee(customer, cart)
	_ = checkCartLimit(cart)
	_ = validateCartItem(customer, cart)

	handlePaymentMethodCredit(cart, customer, pays)
	handlePartnerPaymentMethodFee(cart)

	go func(cart *model.Cart) {
		codes := make([]*string, 0)
		for _, code := range cart.RedeemApplyResult {
			codes = append(codes, &code.Code)
		}
		cartUpdater := &model.Cart{
			RegionCodes:        cart.RegionCodes,
			TotalPrice:         cart.TotalPrice,
			Price:              cart.Price,
			SubPrice:           cart.SubPrice,
			TotalFee:           cart.TotalFee,
			Discount:           cart.Discount,
			TotalItem:          cart.TotalItem,
			TotalQuantity:      cart.TotalQuantity,
			RedeemCode:         &codes,
			RedeemApplyResult:  cart.RedeemApplyResult,
			RedeemCodeMap:      cart.RedeemCodeMap,
			SystemDisplay:      cart.SystemDisplay,
			ProvinceCode:       customer.ProvinceCode,
			DistrictCode:       customer.DistrictCode,
			WardCode:           customer.WardCode,
			Status:             enum.CartState.DRAFT,
			IsRefuseSplitOrder: cart.IsRefuseSplitOrder,
			PaymentMethod:      cart.PaymentMethod,
		}
		cartUpdater.FlattenLocation = FlatLocationCart(*cartUpdater)

		model.CartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, cartUpdater)
	}(cart)
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    []*model.Cart{cart},
		Message: "Get cart success",
	}
}

func DeleteCart(acc *model.Account) *common.APIResponse {
	cart, errGetCart := getCart(acc, false)
	if errGetCart != nil {
		return errGetCart
	}

	go model.CartCacheDB.Delete(&model.CartCache{CartID: cart.CartID})
	delCartRes := model.CartDB.Delete(&model.Cart{CartID: cart.CartID})

	if delCartRes.Status == common.APIStatus.Ok {
		go model.CartItemCacheDB.Delete(&model.CartItemCache{CartID: cart.CartID})
		delCartItem := model.CartItemDB.Delete(&model.CartItem{CartID: cart.CartID})

		if delCartItem.Status != common.APIStatus.Ok {
			return delCartItem
		}
	}

	return delCartRes
}

func RemoveCartItem(acc *model.Account, input *model.CartRemoveItem, event *client.RemoveFromCartEvent) *common.APIResponse {
	if !input.IsDeleteAll && len(input.Skus) == 0 && input.Sku == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid body request",
			ErrorCode: "INVALID_DELETE_ITEM_REQUEST",
		}
	}
	cart, errGetCart := getCart(acc, true)
	if errGetCart != nil {
		return errGetCart
	}
	query := &model.CartItem{CartID: cart.CartID}

	itemBySkuCode := make(map[string]*model.CartItem, len(cart.Items))
	sellerCodes := make([]string, 0, len(cart.Items))
	for _, item := range cart.Items {
		itemBySkuCode[item.Sku] = item
		sellerCodes = append(sellerCodes, item.SellerCode)
	}

	// xoá 1 item
	if input.Sku != "" {
		input.Skus = append(input.Skus, input.Sku)
	}

	// xoá hết
	if input.IsDeleteAll {
		input.Skus = nil
		for _, item := range cart.Items {
			input.Skus = append(input.Skus, item.Sku)
		}
	}
	input.Skus = utils.UniqueStringSlice(input.Skus)
	if len(input.Skus) > 0 {
		query.ComplexQuery = []*bson.M{
			{
				"sku": bson.M{
					"$in": input.Skus,
				},
			},
		}
	}
	// TODO SKIP CHECK CART ITEM TYPE
	qResult := model.CartItemDB.Query(query, 0, 0, nil)
	if qResult.Status != common.APIStatus.Ok {
		return qResult
	}

	qItemResult := qResult.Data.([]*model.CartItem)

	res := model.CartItemDB.Delete(query)
	model.CartItemCacheDB.Delete(query)
	if res.Status == common.APIStatus.Ok {
		go sdk.Execute(func() {
			// send event
			sellerBySkuCode := client.Services.Seller.GetSellerMapFromCodes(sellerCodes)
			for _, sku := range input.Skus {
				if item, ok := itemBySkuCode[sku]; ok {
					for k, v := range input.Metadata {
						event.Metadata[k] = v
					}
					event.Metadata["sku"] = item.Sku
					event.Metadata["product_id"] = fmt.Sprint(item.ProductID)
					event.Metadata["seller_code"] = fmt.Sprint(item.SellerCode)
					if seller, ok := sellerBySkuCode[item.SellerCode]; ok {
						event.Metadata["seller_id"] = fmt.Sprint(seller.SellerID)
					}
					event.ResultStatus = res.Status
					event.ResultErrorCode = res.ErrorCode

					client.Services.Collector.SendEventRemoveFromCart(event)
				}
			}
		})

		if len(cart.Items) == 0 || len(cart.Items)-len(qItemResult) <= 0 {
			model.CartCacheDB.Delete(&model.Cart{CartID: cart.CartID})
			return model.CartDB.Delete(&model.Cart{CartID: cart.CartID})
		}
		go sdk.Execute(func() {
			SyncCartCache(acc, true, []string{}, true, "")
			t := time.Now()
			model.CartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, &model.Cart{LastActionTime: &t})
		})
	}
	return res
}

/*
AddCartItem func process:
- 1 - get customer & validate customer info
- 2 - get current cart: handle new cart if current cart is not exist
- 3 - item existed -> update current item, not existed -> new item
- 4 - create or update cart
*/
func AddCartItem(acc *model.Account, input *model.CartAddItem, eventTracking *client.AddToCartEvent, req sdk.APIRequest) *common.APIResponse {
	// Get customer
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}
	if customer.Scope != "DENTISTRY" && utils.IsContains(conf.DENTAL_SELLERS, input.SellerCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể mua sản phẩm thuộc Nha khoa",
			ErrorCode: "CAN_NOT_BUY_DENISTRY_PRODUCT",
		}
	}

	// Prepare cartItem model
	cartItem := &model.CartItem{
		Sku:         input.Sku,
		Quantity:    input.Quantity,
		Type:        input.Type,
		IsSelected:  input.IsSelected,
		Page:        input.Page,
		SearchKey:   input.SearchKey,
		EventSource: input.EventSource,
		EventScreen: input.EventScreen,
		BlockCode:   input.BlockCode,
		LastAdded:   utils.ParseTimeToPointer(time.Now()),
	}

	if input.Source != nil {
		cartItem.Source = *input.Source
	}

	// Check cart
	cartRes := model.CartDB.QueryOne(&model.Cart{CustomerID: customer.CustomerID})

	// if current cart not existed -> create new cart with first item
	if cartRes.Status != common.APIStatus.Ok {
		lastOrderResp := model.OrderDB.Query(
			model.Order{
				CustomerID: customer.CustomerID,
			}, 0, 1,
			&primitive.M{"_id": -1})
		t := time.Now()
		newCart := &model.Cart{
			AccountID:    acc.AccountID,
			CustomerID:   customer.CustomerID,
			CustomerCode: customer.CustomerCode,
			Status:       enum.CartState.DRAFT,
			CreatedTime:  &t,
			RefCartItem:  t.UnixNano(),
			Source:       input.Source,
		}
		newCart.CartID, newCart.CartNo = model.GetOrderID()
		newCart.LastActionTime = &t
		cartItem.CartID = newCart.CartID
		cartItem.CartNo = newCart.CartNo
		cartItem.RefCart = newCart.RefCartItem
		cartItem.IsFirstAdd = true
		newCart.Items = []*model.CartItem{cartItem}
		newCart.RegionCode = customer.RegionCode
		newCart.ProvinceCode = customer.ProvinceCode
		newCart.DistrictCode = customer.DistrictCode
		newCart.WardCode = customer.WardCode
		newCart.CustomerScope = customer.Scope
		if lastOrderResp.Status == common.APIStatus.Ok && len(lastOrderResp.Data.([]*model.Order)) > 0 {
			lastOrder := lastOrderResp.Data.([]*model.Order)[0]
			if lastOrder != nil && lastOrder.PaymentMethod != "" {
				newCart.PaymentMethod = lastOrder.PaymentMethod
				pays, errCheckPaymentMethod := handleProcessDeliveryAndPaymentMethod(newCart, customer)
				if newCart.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
					errCheckPaymentMethod = handlePaymentMethodCredit(newCart, customer, pays)
					if errCheckPaymentMethod != nil {
						newCart.PaymentMethod = ""
					}
				} else {
					if errCheckPaymentMethod != nil && errCheckPaymentMethod == PaymentMethodError {
						newCart.PaymentMethod = ""
					}
				}
				newCart.PaymentMethod = lastOrder.PaymentMethod
			}
		}
		// for PIC
		newCart.FlattenLocation = FlatLocationCart(*newCart)
		err := handleProcessContentItems(newCart, customer, nil, false)
		if err != nil {
			return err
		}

		// Check for special control items and validate mobile app version
		if err := canAddSpecialControlItems(newCart.Items, req); err != nil {
			return err
		}
		_ = handleProcessPriceItems(customer, newCart)
		err = validateCartItem(customer, newCart)
		if err != nil {
			return err
		}

		newCart.SystemDisplay = input.SystemDisplay
		if newCart.SystemDisplay == "" {
			newCart.SystemDisplay = "BUYMED"
		}

		createCartRes := model.CartDB.Create(&newCart)
		model.CartCacheDB.Create(&newCart)
		if createCartRes.Status == common.APIStatus.Ok {
			if newCart.Items[0].IsCombo == nil || (newCart.Items[0].IsCombo != nil && !*newCart.Items[0].IsCombo) {
				newCart.Items[0].CurPrice = newCart.Items[0].Price
				newCart.Items[0].OldType = newCart.Items[0].Type
				newCart.Items[0].OldLevel = customer.Level
			}
			createCartItemRes := model.CartItemDB.CreateMany(newCart.Items)

			// cart items to cart items cache
			cacheItems := make([]*model.CartItemCache, 0, len(newCart.Items))
			cartItemsByte, _ := json.Marshal(newCart.Items)
			_ = json.Unmarshal(cartItemsByte, &cacheItems)
			model.CartItemCacheDB.CreateMany(cacheItems)

			go sdk.Execute(func() {
				SyncCartCache(acc, true, []string{}, true, "")
				for k, v := range input.Metadata {
					eventTracking.Metadata[k] = v
				}
				eventTracking.ResultErrorCode = createCartItemRes.ErrorCode
				eventTracking.ResultStatus = createCartItemRes.Status
				eventTracking.Metadata["product_id"] = fmt.Sprint(cartItem.ProductID)
				eventTracking.Metadata["seller_code"] = fmt.Sprint(cartItem.SellerCode)
				eventTracking.Metadata["cart_id"] = fmt.Sprint(cartItem.CartID)
				sellerResp := client.Services.Seller.GetSellerListWithParams(client.ReqSellerList{SellerCode: cartItem.SellerCode})
				if sellerResp.Status == common.APIStatus.Ok {
					eventTracking.Metadata["seller_id"] = fmt.Sprint(sellerResp.Data[0].SellerID)
				}
				item := newCart.Items[0]
				if item.IsCombo != nil && *item.IsCombo {
					// get sku price of combo
					skuData, _ := client.Services.Product.GetSingleSku(item.Sku, newCart.ProvinceCode, newCart.CustomerID)
					if skuData != nil && skuData.SKU != nil {
						item.Price = int(skuData.SKU.RetailPriceValue)
					}
					if skuData != nil && skuData.Deal != nil {
						item.Price = skuData.Deal.Price
					}
					if skuData != nil && skuData.Campaign != nil {
						item.Price = int(skuData.Campaign.SalePrice)
					}
				}
				eventTracking.Metadata["price"] = fmt.Sprint(cartItem.Price)
				client.Services.Collector.SendEventAddToCart(eventTracking)
			})

			if createCartItemRes.Status != common.APIStatus.Ok {
				return createCartItemRes
			}
		}
		return createCartRes
	}

	// else, current cart existed -> update current item or create new item
	curCart := cartRes.Data.([]*model.Cart)[0]
	cartItemRes := model.CartItemDB.Query(&model.CartItem{CartID: curCart.CartID}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		_ = model.CartDB.Delete(&model.Cart{
			CustomerID: customer.CustomerID,
		})
		return cartItemRes
	}

	curItems := cartItemRes.Data.([]*model.CartItem)

	// check cart item limit
	if len(curItems) >= 200 {
		return CartItemLimitError
	}

	isExisted := false // cart item existed in cart
	cartItem.CartID = curCart.CartID
	cartItem.CartNo = curCart.CartNo
	cartItem.RefCart = curCart.RefCartItem
	cartItem.CreatedTime = utils.ParseTimeToPointer(time.Now())
	for _, item := range curItems {
		if item.Sku == input.Sku {
			isExisted = true
			cartItem.IsSelected = item.IsSelected
			cartItem.CurPrice = item.CurPrice
			if item.CreatedTime != nil {
				cartItem.CreatedTime = item.CreatedTime
			}
		}
	}

	curCart.Items = []*model.CartItem{cartItem}
	curCart.CustomerScope = customer.Scope

	resetCartPrice(curCart)
	err := handleProcessContentItems(curCart, customer, nil, false)
	if err != nil {
		return err
	}

	// Check for special control items and validate mobile app version
	if err := canAddSpecialControlItems(curCart.Items, req); err != nil {
		return err
	}

	_ = handleProcessPriceItems(customer, curCart)
	if !isExisted {
		cartItem.IsFirstAdd = true
		if cartItem.IsCombo == nil || (cartItem.IsCombo != nil && !*cartItem.IsCombo) {
			cartItem.CurPrice = cartItem.Price
			cartItem.OldType = cartItem.Type
			cartItem.OldLevel = customer.Level
		}
	}
	err = validateCartItem(customer, curCart)
	if err != nil {
		return err
	}

	cartItem = curCart.Items[0]
	returnOpt := options.After
	res := model.CartItemDB.UpdateOne(
		&model.CartItem{
			CartID: curCart.CartID,
			Sku:    cartItem.Sku,
		},
		cartItem,
		&options.FindOneAndUpdateOptions{
			Upsert:         utils.ParseBoolToPointer(true),
			ReturnDocument: &returnOpt,
		},
	)
	if isExisted {
		model.CartItemCacheDB.UpdateOne(
			&model.CartItemCache{
				CartID: curCart.CartID,
				Sku:    cartItem.Sku,
			},
			cartItem,
		)
	}

	if !isExisted {
		// cart items to cart items cache
		cacheItems := make([]*model.CartItemCache, 0, len(curCart.Items))
		cartItemsByte, _ := json.Marshal(curCart.Items)
		_ = json.Unmarshal(cartItemsByte, &cacheItems)
		model.CartItemCacheDB.CreateMany(cacheItems)
	}

	go func() {
		for k, v := range input.Metadata {
			eventTracking.Metadata[k] = v
		}
		eventTracking.ResultErrorCode = res.ErrorCode
		eventTracking.ResultStatus = res.Status
		eventTracking.Metadata["product_id"] = fmt.Sprint(cartItem.ProductID)
		eventTracking.Metadata["seller_code"] = fmt.Sprint(cartItem.SellerCode)
		eventTracking.Metadata["cart_id"] = fmt.Sprint(cartItem.CartID)
		sellerResp := client.Services.Seller.GetSellerListWithParams(client.ReqSellerList{SellerCode: cartItem.SellerCode})
		if sellerResp.Status == common.APIStatus.Ok {
			eventTracking.Metadata["seller_id"] = fmt.Sprint(sellerResp.Data[0].SellerID)
		}

		item := curCart.Items[0]
		if item.IsCombo != nil && *item.IsCombo {
			// get sku price of combo
			skuData, _ := client.Services.Product.GetSingleSku(item.Sku, curCart.ProvinceCode, curCart.CustomerID)
			if skuData != nil && skuData.SKU != nil {
				item.Price = int(skuData.SKU.RetailPriceValue)
			}
			if skuData != nil && skuData.Deal != nil {
				item.Price = skuData.Deal.Price
			}
			if skuData != nil && skuData.Campaign != nil {
				item.Price = int(skuData.Campaign.SalePrice)
			}
		}
		eventTracking.Metadata["price"] = fmt.Sprint(cartItem.Price)
		if !isExisted {
			client.Services.Collector.SendEventAddToCart(eventTracking)
		} else {
			eventTracking.Metadata["quantity"] = fmt.Sprint(cartItem.Quantity)
			client.Services.Collector.SendEventUpdateCartQuantity(eventTracking)
		}
	}()

	if res.Status == common.APIStatus.Ok {
		go sdk.Execute(func() {
			SyncCartCache(acc, true, []string{}, true, "")
			t := time.Now()
			model.CartDB.UpdateOne(&model.Cart{CartID: cartItem.CartID}, &model.Cart{LastActionTime: &t})
			if cartItem.SalePrice != 0 && cartItem.SalePrice != cartItem.Price {
				checkSalePriceToWanring(customer, cartItem)
			}
		})
	}
	return res
}

/*
CheckoutCart func process:
- 1 - get customer & validate customer info : customer is active
- 2 - get cart, price & validate cart, items : subTotalPrice -> min = 1tr for first order and then min = 2tr
- 3 - handle order data from cart
- 4 - delete current cart
- 5 - create order
- 6 - complete process : move cart -> cart deleted, push notification, increase order count for customer, call apply voucher if order has redeem code
*/
func CheckoutCart(acc *model.Account, session *model.Session, in *model.CartCheckoutData, isBrandSales bool, headers map[string]string) *common.APIResponse {
	var customer *model.Customer
	var errCustomerRes *common.APIResponse

	if in.CustomerID != 0 && isBrandSales {
		customer, errCustomerRes = getCustomerInfoBrandSales(acc, in.CustomerID)
	} else {
		customer, errCustomerRes = getCustomerProfile(acc)
	}
	if errCustomerRes != nil {
		return errCustomerRes
	}

	// Pharmacist only can view the cart, not allow checkout. This is our rule
	if customer.Scope == "PHARMACIST" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Dược sĩ không thể đặt hàng.",
			ErrorCode: "NOT_ALLOW_PHARMACIST_CHECKOUT",
		}
	}

	if !isBrandSales {
		if _, ok := customer.TagMap["CIRCA"]; ok && !in.AcceptAdvancePolicies {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Vui lòng xác nhận sự đồng ý đối với các điều khoản và điều kiện về tạm ứng",
				ErrorCode: "NOT_ACCEPT_ADVANCE_POLICIES",
			}
		}
	}

	in.CustomerID = customer.CustomerID

	// check is customer valid license https://buymed.atlassian.net/browse/CUS-4117
	if customer.IsInvalidLicense && customer.IsBlockCheckout {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng cung cấp đầy đủ giấy phép để có thể tiếp tục đặt hàng.",
			ErrorCode: "INVALID_CUSTOMER_LICENSE",
		}
	}

	if acc.Type == enum.AccountType.CUSTOMER && customer.Status != "ACTIVE" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Tài khoản của bạn chưa được kích hoạt",
			ErrorCode: "CUSTOMER_NOT_ACTIVE",
		}
	}

	if customer.Level == "LEVEL_BLACKLIST" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Tài khoản của bạn đã bị khóa",
			ErrorCode: "LOCKED_CUSTOMER",
		}
	}

	if customer.Status == "DELETED" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Tài khoản của bạn đã bị xóa",
			ErrorCode: "CUSTOMER_DELETED",
		}
	}

	if customer.TagMap != nil && customer.TagMap[string(enum.CustomerTag.Ban)] {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Tài khoản của bạn đã bị khóa",
			ErrorCode: "LOCKED_CUSTOMER",
		}
	}

	cartRes := &common.APIResponse{}
	// CustomerID is of the feature sales brand
	if isBrandSales {
		cartRes = model.BrandCartDB.QueryOne(&model.Cart{
			CreatedByAccountID: acc.AccountID,
			CustomerID:         in.CustomerID,
		})
	} else {
		cartRes = model.CartDB.QueryOne(&model.Cart{
			CustomerID: customer.CustomerID,
		})
	}

	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]
	qUpdateCartStatus := &common.APIResponse{}
	if isBrandSales {
		qUpdateCartStatus = model.BrandCartDB.UpdateOne(
			&model.Cart{CustomerID: in.CustomerID}, &model.Cart{Status: enum.CartState.PENDING},
		)
	} else {
		qUpdateCartStatus = model.CartDB.UpdateOne(
			&model.Cart{CustomerID: customer.CustomerID}, &model.Cart{Status: enum.CartState.PENDING},
		)
	}
	if qUpdateCartStatus.Status != common.APIStatus.Ok {
		return qUpdateCartStatus
	}
	if cart.Status == enum.CartState.PENDING {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Giỏ hàng của bạn đang được xử lý",
			ErrorCode: "CART_PENDING",
		}
	}
	var errCheckoutRes *common.APIResponse
	defer func() {
		go func() {
			if isBrandSales {
				model.BrandCartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, &model.Cart{Status: enum.CartState.DRAFT})
				if errCheckoutRes != nil && errCheckoutRes.Status != common.APIStatus.Ok {
					saveCheckoutError(cart, customer, errCheckoutRes)
				}
			} else {
				model.CartDB.UpdateOne(&model.Cart{CustomerID: customer.CustomerID}, &model.Cart{Status: enum.CartState.DRAFT})
				if errCheckoutRes != nil && errCheckoutRes.Status != common.APIStatus.Ok {
					saveCheckoutError(cart, customer, errCheckoutRes)
				}
			}
		}()
	}()

	cartItemRes := &common.APIResponse{}
	if isBrandSales {
		cartItemRes = model.BrandCartItemDB.Query(&model.CartItem{CartID: cart.CartID}, 0, 0, nil)
		if cartItemRes.Status != common.APIStatus.Ok {
			return cartItemRes
		}
	} else {
		cartItemRes = model.CartItemDB.Query(&model.CartItem{CartID: cart.CartID}, 0, 0, nil)
		if cartItemRes.Status != common.APIStatus.Ok {
			return cartItemRes
		}
	}

	cart.Items = cartItemRes.Data.([]*model.CartItem)
	cart.AccountID = customer.AccountID
	cart.CustomerCode = customer.CustomerCode
	cart.CustomerEmail = utils.ParseStringToPointer(customer.Email)
	cart.CustomerScope = customer.Scope
	cart.ProvinceCode = customer.ProvinceCode
	cart.DistrictCode = customer.DistrictCode
	cart.WardCode = customer.WardCode
	cart.SourceDetail = acc.SourceDetail
	cart.CustomerScope = customer.Scope
	cart.Screen = "Payment"

	var salesTypeCode string
	if isBrandSales {
		if acc.Type == enum.AccountType.BRAND_SALES {
			salesTypeCode = session.EntityCode
		} else if acc.Type == enum.AccountType.CUSTOMER {
			salesTypeCode = in.SalesTypeCode
		}
	}
	salesType, errSales := client.Services.BrandSales.GetSalesType(salesTypeCode)
	if errSales == nil && salesType != nil {
		cart.SalesType = salesType.Type
		cart.SalesTypeCode = salesType.Code
	}

	// cart.Items
	if cart.CustomerName == "" || cart.CustomerPhone == "" || cart.CustomerShippingAddress == "" {
		errCheckoutRes = &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Yêu cầu thông tin giao hàng",
			ErrorCode: "SHIPPING_INFO_MISSING",
		}
		return errCheckoutRes
	}
	if _, ok := mapCustomerIDCrossProvinces[customer.CustomerID]; !ok {
		if customer.ProvinceCode != cart.CustomerProvinceCode {
			errCheckoutRes = &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Thuocsi chỉ nhận giao đến tỉnh có cùng địa chỉ đăng ký doanh nghiệp của khách hàng. Vui lòng kiểm tra lại thông tin.",
				ErrorCode: "PROVINCE_NOT_MATCH",
			}
			return errCheckoutRes
		}
	}

	if cart.Invoice != nil && cart.Invoice.IsIncidentTaxCode == nil {
		taxCodeInfo := client.Services.Invoice.GetTaxInfo(cart.Invoice.TaxCode)
		if taxCodeInfo != nil {
			// We use HILO to verify tax code, but if there
			// is an incident, we consider INVALID tax as VALID
			// to allow customer to checkout, but we won't
			// create seller invoice for this order
			cart.Invoice.IsIncidentTaxCode = taxCodeInfo.IsIncidentTaxCode
		}
	}

	order := &model.Order{
		OrderID:                 cart.CartID,
		OrderCode:               cart.CartNo,
		AccountID:               customer.AccountID,
		CustomerID:              cart.CustomerID,
		CustomerCode:            customer.CustomerCode,
		CustomerName:            cart.CustomerName,
		CustomerPhone:           cart.CustomerPhone,
		CustomerEmail:           cart.CustomerEmail,
		CustomerShippingAddress: cart.CustomerShippingAddress,
		CustomerDistrictCode:    cart.CustomerDistrictCode,
		CustomerWardCode:        cart.CustomerWardCode,
		CustomerProvinceCode:    cart.CustomerProvinceCode,
		CustomerRegionCode:      cart.CustomerRegionCode,
		PaymentMethod:           cart.PaymentMethod,
		DeliveryMethod:          cart.DeliveryMethod,
		Status:                  enum.OrderState.WaitConfirm,
		Source:                  in.Source,
		SourceDetail:            &in.SourceDetail,
		Note:                    cart.Note,
		RedeemCode:              cart.RedeemCode,
		Invoice:                 cart.Invoice,
		ProvinceCode:            customer.ProvinceCode,
		DistrictCode:            customer.DistrictCode,
		WardCode:                customer.WardCode,
		CanExportInvoice:        cart.CanExportInvoice,
		SystemDisplay:           cart.SystemDisplay,
		CustomerTags:            customer.Tags,
		CustomerScope:           customer.Scope,
		CustomerLevel:           customer.Level,

		// brand
		CreatedByAccountID: acc.AccountID,
		BrandCode:          cart.BrandCode,
		SalesTypeCode:      cart.SalesTypeCode,
	}

	// thêm tag SELLER vào đơn hàng của brand portal.
	if isBrandPortal(cart.Source) && isBrandSales && salesType != nil && salesType.Type == "SELLER" {
		for _, sellerCode := range salesType.SellerCodes {
			sellerCode = strings.Trim(sellerCode, " ")
			if sellerCode != "" {
				sellerTag := enum.TagValue("SELLER_" + sellerCode)
				if !utils.IsOrderTagContains(order.Tags, sellerTag) {
					order.Tags = append(order.Tags, sellerTag)
				}
			}
		}
	}

	if utils.IsContains(customer.Tags, "ONBOARDING") && (customer.OrdersCount == nil || (customer.OrdersCount != nil && *customer.OrdersCount == 0)) {
		order.Tags = append(order.Tags, "ONBOARDING")
	}

	if isBrandPortal(cart.Source) {
		for _, tag := range conf.Config.BrandOrderTags {
			if utils.IsContains(customer.Tags, tag) {
				order.Tags = append(order.Tags, enum.Tag.BRAND_ORDER)
				break
			}
		}
	}

	// if source order is brand portal or clinic portal, add tag
	if order.Source != nil {
		var newTag enum.TagValue = ""
		switch *order.Source {
		case enum.Source.BRAND_PORTAL:
			newTag = enum.Tag.BRAND_PORTAL
		case enum.Source.CLINIC_PORTAL:
			newTag = enum.Tag.CLINIC_PORTAL
		}

		if newTag != "" && !utils.IsOrderTagContains(order.Tags, newTag) {
			order.Tags = append(order.Tags, newTag)
		}
	}

	if len(cart.Items) >= 0 &&
		(isClinicPortal(cart.Source) ||
			(isBrandPortal(cart.Source) && salesType != nil && salesType.Type == "BRAND_SALES")) {

		manufacturerCodesList := []string{}

		for _, item := range cart.Items {
			if item.ManufacturerCode == "" || utils.IsContains(manufacturerCodesList, item.ManufacturerCode) || item.IsSelected == nil || !*item.IsSelected {
				continue
			}
			manufacturerCodesList = append(manufacturerCodesList, item.ManufacturerCode)
		}

		// if len manufacturerCodes > 0
		if len(manufacturerCodesList) > 0 {
			manufacturers := client.Services.Product.GetManufacturerByCodes(manufacturerCodesList)
			if len(manufacturers) > 0 {
				for _, manufacturer := range manufacturers {
					order.Tags = append(order.Tags, enum.TagValue("BRAND_NAME_"+manufacturer.ShortName))
				}
			}
		}
	}

	// for PIC
	order.FlattenLocation = FlatLocationOrder(*order)

	if order.Source == nil {
		order.Source = cart.Source
	}

	if customer.OrdersCount == nil {
		order.CustomerOrderIndex = 1
		cart.CustomerOrderIndex = 1
	} else {
		order.CustomerOrderIndex = *customer.OrdersCount + 1
		cart.CustomerOrderIndex = *customer.OrdersCount + 1
	}
	resetCartPrice(cart)
	err := validateCustomerRegion(customer, cart)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	tagSettingResp := model.OrderTagConfigDB.QueryAll()
	tagSettingMap := make(map[string]*model.OrderTagConfig)
	if tagSettingResp.Status == common.APIStatus.Ok {
		tagSettings := tagSettingResp.Data.([]*model.OrderTagConfig)
		for _, setting := range tagSettings {
			if setting.IsActive != nil && *setting.IsActive {
				tagSettingMap[setting.TagCode] = setting
				if setting.ProvinceAppliedCodes != nil {
					setting.ProvinceAppliedMap = map[string]string{}
					for _, code := range *setting.ProvinceAppliedCodes {
						setting.ProvinceAppliedMap[code] = code
					}
				}
			}
		}
	}
	err = handleProcessContentItems(cart, customer, tagSettingMap, true)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	err = handleProcessCheckQtyItemImportants(cart)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	err = handleProcessPriceItems(customer, cart)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	//if isCartItemPriceChange(cart) {
	//	return CartItemChangePrice
	//}
	paymentMethods, err := handleProcessDeliveryAndPaymentMethod(cart, customer)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	err = handleApplyVoucherCode(cart, customer, "CheckoutCart")
	if err != nil {
		errCheckoutRes = err
		return err
	}
	err = handleProcessContentGiftItems(cart, customer, tagSettingMap)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	err = handleDeliveryDate(cart, common.True)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	err = handleExtraFee(customer, cart)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	err = checkCartLimit(cart)
	if err != nil {
		errCheckoutRes = err
		return err
	}

	if isBrandSales {
		err = checkBrandSkuLimit(cart)
		if err != nil {
			errCheckoutRes = err
			return err
		}
	} else {
		err = checkSkuLimit(cart)
		if err != nil {
			errCheckoutRes = err
			return err
		}
	}

	err = validateCartItem(customer, cart)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	// check if can process this payment method when checkout
	err = handlePaymentMethodCredit(cart, customer, paymentMethods)
	if err != nil {
		errCheckoutRes = err
		return err
	}

	handlePartnerPaymentMethodFee(cart)

	if cart.PartnerPaymentMethod != nil {
		order.TotalPriceBeforePartnerPaymentFee = utils.ParseIntToPointer(cart.TotalPriceBeforePartnerPaymentFee)
		order.PartnerPaymentMethod = cart.PartnerPaymentMethod
	}
	order.TotalFee = utils.ParseIntToPointer(cart.TotalFee)
	order.Price = utils.ParseIntToPointer(cart.Price)
	order.TotalDiscount = utils.ParseIntToPointer(cart.Discount)
	order.TotalPrice = utils.ParseIntToPointer(cart.TotalPrice)

	// FORCE zero
	if order.Price != nil && *order.Price < 0 {
		order.Price = utils.ParseIntToPointer(0)
	}
	if order.TotalPrice != nil && *order.TotalPrice < 0 {
		order.TotalPrice = utils.ParseIntToPointer(0)
	}
	// HANDLE CART ITEM
	t := time.Now()
	order.CreatedTime = &t
	order.Items, order.HasDeal, order.HasCampaign, order.TotalQuantity, order.TotalItem = handleCartItemToOrderItem(cart.Items, cart, order)

	_ = handleOrderTags(cart, order, customer)

	order.PaymentMethodFee = utils.ParseInt64ToPointer(cart.PaymentMethodFee)
	order.DeliveryMethodFee = utils.ParseInt64ToPointer(cart.DeliveryMethodFee)
	order.ExtraFee = utils.ParseInt64ToPointer(cart.ExtraFee)
	order.DeliveryDate = cart.DeliveryDate
	order.RegionCode = cart.RegionCode
	order.PaymentMethodPercentage = cart.PaymentMethodPercentage
	order.RedeemApplyResult = cart.RedeemApplyResult

	isLimit, msg := checkIsDeliveryLimitation(
		order.CustomerProvinceCode,
		order.CustomerDistrictCode,
		order.CustomerWardCode,
	)
	if isLimit {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   msg,
			ErrorCode: "DELIVERY_LIMIT",
		}
	}

	// IMPORTANT -- Vui lòng không handle return error sau 2 step này
	err = handleProcessBuySku(cart)
	if err != nil {
		errCheckoutRes = err
		return err
	}
	err = handleProcessBuyDeal(cart)
	if err != nil {
		returnQuantitySku(order)
		return err
	}

	// setup forced value
	if order.PaymentMethod != string(enum.PaymentMethod.COD) && order.PaymentMethod != string(enum.PaymentMethod.CREDIT) &&
		!IsContainsT(PARTNER_PAYMENT_METHOD, (order.PaymentMethod)) {
		order.PaymentMethod = string(enum.PaymentMethod.BANK)
	}

	if IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, (order.PaymentMethod)) {
		settingResp := model.HoldOrderConfigDB.Query(model.HoldOrderConfig{
			ComplexQuery: []*bson.M{
				{"hold_order_code": bson.M{"$in": []enum.HoldOrderValue{enum.HoldOrder.AutoCancelBankOrder, enum.HoldOrder.AutoSendPaymentRemind}}},
			},
		}, 0, 2, nil)
		if settingResp.Status == common.APIStatus.Ok {
			settings := settingResp.Data.([]*model.HoldOrderConfig)
			for _, setting := range settings {
				if setting.IsActive != nil && *setting.IsActive {
					if setting.HoldOrderCode == enum.HoldOrder.AutoCancelBankOrder {
						if setting.BankTransferWaitingTime != nil {
							readyTime := time.Now().Add(time.Duration(*setting.BankTransferWaitingTime) * time.Minute)
							model.AutoCancelPaymentMethodBankJob.Push(CheckPaymentMethodBankOrder{
								OrderId: order.OrderID,
								Unix:    readyTime.Unix(),
							}, &job.JobItemMetadata{
								Topic:     "default",
								ReadyTime: &readyTime,
							})
							order.WaitForTransferTime = *setting.BankTransferWaitingTime
							order.AutoCancelTransferPaymentUnix = readyTime.Unix()
						}
					} else if setting.HoldOrderCode == enum.HoldOrder.AutoSendPaymentRemind {
						if setting.DisplayTime != nil {
							readyTime := utils.ParseTimeToPointer(time.Now().Add(time.Duration(*setting.DisplayTime) * time.Minute))
							model.AutoSendPaymentRemindJob.Push(RemindPaymentMethodOnlineOrder{
								OrderId: order.OrderID,
								Unix:    readyTime.Unix(),
							}, &job.JobItemMetadata{
								Topic:     "default",
								ReadyTime: readyTime,
							})
							order.AutoSendPaymentNotificationUnix = readyTime.Unix()
						}
					}
				}
			}
		}
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "CheckoutCart")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	// Add a sign to identify if it is an employee placing an order on behalf of a customer
	if session != nil && session.RootAccountID != 0 {
		order.EmployeeID = session.RootAccountID
		order.Tags = append(order.Tags, enum.Tag.ORDER_ASSIST)
	}

	if !utils.IsOrderTagContains(order.Tags, enum.Tag.REFUSE_SPLIT_ORDER) && (cart.IsRefuseSplitOrder != nil && *cart.IsRefuseSplitOrder) {
		order.Tags = append(order.Tags, enum.Tag.REFUSE_SPLIT_ORDER)
	}

	lastOrderResp := model.OrderDB.Query(
		model.Order{
			CustomerID: order.CustomerID,
		}, 0, 1,
		&primitive.M{"_id": -1})
	if lastOrderResp.Status == common.APIStatus.Ok {
		now := utils.GetTimeNow()
		lastOrder := lastOrderResp.Data.([]*model.Order)[0]
		dayDiff := int(now.Sub(lastOrder.CreatedTime.In(utils.TimeZoneVN)).Hours() / 24)
		if customerComebackDays > 0 && dayDiff >= customerComebackDays {
			order.Tags = append(order.Tags, enum.Tag.CUSTOMER_COME_BACK)
		}
	}

	// for online payment
	if IsContainsT(PARTNER_PAYMENT_METHOD, (order.PaymentMethod)) {
		order.PartnerPaymentStatus = enum.PartnerPaymentStatus.WAIT_TO_PAY
	}

	order.UseFreeDelivery = cart.UseFreeDelivery

	// add CIRCA_RFID tags to order
	customerAddress, errCustomerAddress := client.Services.Customer.GetAccountAddress(order.CustomerID, cart.CustomerAddressCode)
	if errCustomerAddress == nil && len(customerAddress) > 0 {
		dataAddress := customerAddress[0]
		if len(dataAddress.Tags) > 0 {
			for _, addressTag := range dataAddress.Tags {
				order.Tags = append(order.Tags, enum.TagValue(addressTag))
			}
		}
	}

	// create order
	res := model.OrderDB.Create(&order)
	if res.Status == common.APIStatus.Ok {
		sellerCodes, skus, itemCodes, productIds, dealCodes, subSkus, subProductIds := GetOrderDistinctInfo(order.Items)
		model.OrderDetailDB.Create(&model.OrderDetail{
			CreatedTime:   order.CreatedTime,
			OrderID:       order.OrderID,
			OrderCode:     order.OrderCode,
			AccountID:     order.AccountID,
			CustomerID:    order.CustomerID,
			CustomerCode:  order.CustomerCode,
			SellerCodes:   sellerCodes,
			Skus:          skus,
			ItemCodes:     itemCodes,
			ProductIds:    productIds,
			DealCodes:     dealCodes,
			SubSkus:       &subSkus,
			SubProductIds: &subProductIds,
		})
		GenerateSellerClassForOrderItems(order.Items)

		createItemRes := orderItemPartitionDB.CreateMany(order.Items)
		if createItemRes.Status == common.APIStatus.Ok {
			// sellerStores := []model.SellerStores{}
			// for _, item := range order.Items {
			// 	sellerStores = append(sellerStores, model.SellerStores{SellerCode: item.SellerCode, Revenue: &item.TotalPrice})
			// }
			// _ = client.Services.Seller.UpdateRevenueStoreSeller(model.SellerStore{SellerStores: &sellerStores}, false)

			confirmOrderJob(order)
			if cart.RedeemCode != nil && len(*cart.RedeemCode) > 0 {
				redeemCodes := *cart.RedeemCode
				_ = client.Services.Promotion.UseVoucher(&client.UseVoucherRequest{
					VoucherCodes: redeemCodes,
					AccountID:    customer.AccountID,
					OrderID:      order.OrderID,
					ApplyVoucherCount: func() map[string]int {
						m := make(map[string]int)
						for _, code := range order.RedeemApplyResult {
							m[code.Code] = code.NumberOfAutoApply
						}
						return m
					}(),
				})
			}

			// push queue to fill product fee
			model.FillOrderProductFeeJob.Push(
				&model.Order{
					OrderID: order.OrderID,
				},
				&job.JobItemMetadata{
					Keys: []string{
						strconv.Itoa(int(order.OrderID)),
					},
					Topic: "default",
				})

			// safe process, can run later
			go func(isBrandSales bool) {
				if isBrandSales {
					logBrandSkuLimit(order)
				} else {
					logSkuLimit(order)
				}

				// Delete current cart
				var delCartRes *common.APIResponse
				if isBrandSales && session != nil {
					delCartRes = DeleteBrandCart(acc, in.CustomerID, session)
				} else {
					delCartRes = DeleteCart(acc)
				}
				if delCartRes.Status != common.APIStatus.Ok {
					fmt.Println("Checkout error - delCartRes:", utils.PrintValue(delCartRes), ". isBrandSales", isBrandSales)
					return
				}
				completeCheckoutCartProcess(customer, cart, order, isBrandSales)
			}(isBrandSales)
		} else {
			fmt.Println("Checkout error - order:", utils.PrintValue(order), ". createItemRes:", utils.PrintValue(createItemRes))
			return createItemRes
		}

		if IsContainsT(PARTNER_PAYMENT_METHOD, (order.PaymentMethod)) {
			params := map[string]interface{}{
				"orderId":    order.OrderID,
				"customerId": order.CustomerID,

				"returnUrl": fmt.Sprintf("%s/thankyou/%d", conf.Config.FrontendURL, order.OrderID),

				"paymentMethod": order.PaymentMethod,

				"totalPrice": order.TotalPrice,
			}
			paymentLinkResp := client.Services.OnepayClient.GetPaymentLink(params, headers)
			if paymentLinkResp.Status == common.APIStatus.Ok {
				paymentLink := paymentLinkResp.Data.([]*model.OnepayGetList)[0]

				orderResult := res.Data.([]*model.Order)[0]
				orderResult.PaymentLink = paymentLink.Url
			}
		}
	}
	return res
}

/*
RevertCart2 func process:
- 1 - get customer & validate customer info
- 2 - get order & check order : type != normal, status != wait to confirm, created time > 30p -> can't edit order
- 3 - get cart backup from cart deleted (get latest cart deleted)
- 4 - delete order
- 5 - create cart
- 6 - complete process : move order -> order deleted, push notification, decrease order count for customer, check to return voucher for customer
*/
func RevertCart2(acc *model.Account, orderId int64, isInternal ...bool) *common.APIResponse {
	if orderId <= 0 {
		return OrderIdNotFoundError
	}
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	// get order and check validate
	orderRes := model.OrderDB.QueryOne(&model.Order{
		CustomerID: customer.CustomerID,
		OrderID:    orderId,
	})
	if orderRes.Status != common.APIStatus.Ok {
		return OrderNotFoundError
	}
	order := orderRes.Data.([]*model.Order)[0]
	if acc.AccountID != order.CreatedByAccountID {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể sửa đơn hàng",
			ErrorCode: "CAN_NOT_REVERT_CART_ANOTHER_ACCOUNT_ID",
		}
	}
	if isBrandOrClinic(order.Source) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể sửa đơn hàng này",
			ErrorCode: "CAN_NOT_REVERT_CART_BY_SOURCE_ANOTHER_PORTAL",
		}
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "RevertCart2")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	orderItemRes := orderItemPartitionDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
	if orderItemRes.Status != common.APIStatus.Ok {
		return OrderItemNotFoundError
	}
	order.Items = orderItemRes.Data.([]*model.OrderItem)
	if len(isInternal) == 0 {
		revertErrRes := checkOrderToRevertCart(order)
		if revertErrRes != nil {
			return revertErrRes
		}
	}

	orderUpdateStatus := model.OrderDB.UpdateOne(&model.Order{
		CustomerID: customer.CustomerID,
		OrderID:    orderId,
		Status:     enum.OrderState.WaitConfirm,
	}, model.Order{Status: enum.OrderState.Reverting})
	if orderUpdateStatus.Status != common.APIStatus.Ok {
		return orderUpdateStatus
	}

	// get cart backup
	cartRes := model.CartDeletedDB.Query(&model.Cart{CartID: order.OrderID}, 0, 1, &primitive.M{"ref_cart_item": -1})
	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]

	cartItemRes := model.CartItemDeletedDB.Query(&model.CartItem{CartID: cart.CartID, RefCart: cart.RefCartItem}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		return cartItemRes
	}
	cart.Items = cartItemRes.Data.([]*model.CartItem)

	// get current cart to delete
	var curCart *model.Cart
	curCartRes := model.CartDB.QueryOne(&model.Cart{CustomerID: customer.CustomerID})
	if curCartRes.Status == common.APIStatus.Ok {
		curCart = curCartRes.Data.([]*model.Cart)[0]
		qCartItem := model.CartItemDB.Query(&model.CartItem{CartID: curCart.CartID, RefCart: curCart.RefCartItem}, 0, 0, nil)
		if qCartItem.Status == common.APIStatus.Ok {
			curCart.Items = qCartItem.Data.([]*model.CartItem)
		}
		delCurCartRes := DeleteCart(acc)
		if delCurCartRes.Status != common.APIStatus.Ok {
			return delCurCartRes
		}
	}
	newCart := mixCart(curCart, cart, true, customer)

	newCart.SystemDisplay = order.SystemDisplay
	if newCart.SystemDisplay == "" {
		newCart.SystemDisplay = "BUYMED"
	}

	if utils.IsOrderTagContains(order.Tags, enum.Tag.REFUSE_SPLIT_ORDER) {
		TRUE := true
		newCart.IsRefuseSplitOrder = &TRUE
	}

	// create cart & cart item
	t := time.Now()
	newCart.RefCartItem = t.UnixNano()
	newCart.LastActionTime = &t
	newCart.FlattenLocation = FlatLocationCart(*newCart)
	//cart.ID = primitive.NilObjectID
	res := model.CartDB.Create(newCart)
	if res.Status == common.APIStatus.Ok {
		for _, item := range newCart.Items {
			item.RefCart = newCart.RefCartItem
		}
		createCartItem := model.CartItemDB.CreateMany(newCart.Items)
		if createCartItem.Status == common.APIStatus.Ok {
			go func() {
				completeRevertCartProcess(acc, customer, order)
			}()
		} else {
			return createCartItem
		}
	}
	return res
}

func ReOrder(acc *model.Account, orderId int64) *common.APIResponse {
	if orderId <= 0 {
		return OrderIdNotFoundError
	}
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	// get order and check validate
	orderRes := model.OrderDB.QueryOne(&model.Order{
		CustomerID: customer.CustomerID,
		OrderID:    orderId,
	})
	if orderRes.Status != common.APIStatus.Ok {
		return OrderNotFoundError
	}
	order := orderRes.Data.([]*model.Order)[0]

	if order.SystemDisplay == "BUYDENTAL" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể đặt lại đơn hàng này",
			ErrorCode: "CAN_NOT_REORDER_BUYDENTAL_ORDER",
		}
	}
	if acc.AccountID != order.CreatedByAccountID {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể đặt lại đơn hàng này",
			ErrorCode: "CAN_NOT_REORDER_ANOTHER_ACCOUNT_ID",
		}
	}
	if isBrandOrClinic(order.Source) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể đặt lại đơn hàng này",
			ErrorCode: "CAN_NOT_REORDER_BY_SOURCE_ANOTHER_PORTAL",
		}
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ReOrder")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	orderItemRes := orderItemPartitionDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
	if orderItemRes.Status != common.APIStatus.Ok {
		return OrderItemNotFoundError
	}
	order.Items = orderItemRes.Data.([]*model.OrderItem)

	// get cart backup
	cartRes := model.CartDeletedDB.Query(&model.Cart{CartID: order.OrderID}, 0, 1, &primitive.M{"ref_cart_item": -1})
	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]
	cartItemRes := model.CartItemDeletedDB.Query(&model.CartItem{CartID: cart.CartID, RefCart: cart.RefCartItem}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		return cartItemRes
	}
	cart.Items = cartItemRes.Data.([]*model.CartItem)

	// Filter out items with isSpecialControl = true
	if len(cart.Items) > 0 {
		var filteredItems []*model.CartItem

		for _, item := range cart.Items {
			sku := client.Services.Product.GetSkuItemInfo(item.ItemCode)
			if sku != nil && sku.IsSpecialControl {
				continue
			}

			filteredItems = append(filteredItems, item)
		}

		cart.Items = filteredItems
	}

	// get current cart to delete
	var curCart *model.Cart
	curCartRes := model.CartDB.QueryOne(&model.Cart{CustomerID: customer.CustomerID})
	if curCartRes.Status == common.APIStatus.Ok {
		curCart = curCartRes.Data.([]*model.Cart)[0]
		qCartItem := model.CartItemDB.Query(&model.CartItem{CartID: curCart.CartID, RefCart: curCart.RefCartItem}, 0, 0, nil)
		if qCartItem.Status == common.APIStatus.Ok {
			curCart.Items = qCartItem.Data.([]*model.CartItem)
		}
		delCurCartRes := DeleteCart(acc)
		if delCurCartRes.Status != common.APIStatus.Ok {
			return delCurCartRes
		}
	}

	newCart := mixCart(curCart, cart, false, customer)

	newCart.SystemDisplay = order.SystemDisplay
	if newCart.SystemDisplay == "" {
		newCart.SystemDisplay = "BUYMED"
	}

	// create cart & cart item
	t := time.Now()
	newCart.RefCartItem = t.UnixNano()
	newCart.LastActionTime = &t
	emptyArr := make([]*string, 0)
	newCart.RedeemCode = &emptyArr
	newCart.FlattenLocation = FlatLocationCart(*newCart)
	//cart.ID = primitive.NilObjectID
	res := model.CartDB.Create(newCart)
	if res.Status == common.APIStatus.Ok {
		for _, item := range newCart.Items {
			item.RefCart = newCart.RefCartItem
		}
		createCartItem := model.CartItemDB.CreateMany(newCart.Items)
		if createCartItem.Status != common.APIStatus.Ok {
			return createCartItem
		}
	}
	return res
}

// UpdateCartItem ...
func UpdateCartItem(acc *model.Account, input *model.CartItemUpdate) *common.APIResponse {
	cart, errGetCart := getCart(acc, true)
	if errGetCart != nil {
		return errGetCart
	}

	query := model.CartItem{CartID: cart.CartID, Sku: input.Sku, Type: input.Type}
	updater := model.CartItem{}
	if input.IsImportant != nil {
		updater.IsImportant = input.IsImportant
	}
	qResult := model.CartItemDB.QueryOne(query)
	if qResult.Status != common.APIStatus.Ok {
		return qResult
	}
	curItem := qResult.Data.([]*model.CartItem)[0]
	if curItem.IsImportant == nil || (curItem.IsImportant != nil && !*curItem.IsImportant) {
		if input.IsImportant != nil && *input.IsImportant {
			totalItemImportant := 0
			totalItemSelected := 0
			for _, item := range cart.Items {
				if item.IsImportant != nil && *item.IsImportant {
					totalItemImportant = totalItemImportant + 1
				}
				if isCartItemSelected(item) {
					totalItemSelected = totalItemSelected + 1
				}
			}
			if ((totalItemImportant + 1) * 5) > totalItemSelected {
				return ImportantItemError
			}
		}
	}
	model.CartItemCacheDB.UpdateOne(query, updater)
	res := model.CartItemDB.UpdateOne(query, updater)
	if res.Status == common.APIStatus.Ok {
		go sdk.Execute(func() {
			SyncCartCache(acc, true, []string{}, true, "")
			t := time.Now()
			model.CartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, &model.Cart{LastActionTime: &t})
		})
	}
	return res
}

// ConfirmCart ...
func ConfirmCart(acc *model.Account, input *model.CartConfirm) *common.APIResponse {
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cart, errGetCart := getCartFromCustomerID(customer.CustomerID, true)
	if errGetCart != nil {
		return errGetCart
	}

	resetCartPrice(cart)
	_ = handleProcessContentItems(cart, customer, nil, false)
	_ = handleProcessPriceItems(customer, cart)

	isConfirmAll := len(input.Skus) == 0
	if isConfirmAll {
		for _, item := range cart.Items {
			if errRes := confirmSingleCartItem(customer, item); errRes != nil {
				return errRes
			}
		}
	} else {
		mapCartItem := make(map[string]*model.CartItem)
		for _, item := range cart.Items {
			mapCartItem[item.Sku] = item
		}
		for _, sku := range input.Skus {
			if data, ok := mapCartItem[sku]; ok && data != nil {
				if errRes := confirmSingleCartItem(customer, mapCartItem[sku]); errRes != nil {
					return errRes
				}
			}
		}
	}
	go func() {
		t := time.Now()
		model.CartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, &model.Cart{LastActionTime: &t})
	}()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Xác nhận thành công",
	}
}

// VerifyCart ...
func VerifyCart(acc *model.Account, input *model.VerifyCartRequest) *common.APIResponse {
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Xác nhận thành công",
	}
}

func confirmSingleCartItem(customer *model.Customer, item *model.CartItem) *common.APIResponse {
	switch item.WarningCode {
	case ChangePriceError.ErrorCode:
		item.CurPrice = item.Price
		item.OldType = item.Type
		item.OldLevel = customer.Level
		qUpdate := model.CartItemDB.UpdateOne(&model.CartItem{CartID: item.CartID, Sku: item.Sku}, &model.CartItem{CurPrice: item.CurPrice, OldType: item.OldType, OldLevel: item.OldLevel})
		if qUpdate.Status != common.APIStatus.Ok {
			return qUpdate
		}
	case DealToNormalError.ErrorCode, NormalToDealError.ErrorCode:
		qUpdate := model.CartItemDB.UpdateOne(&model.CartItem{CartID: item.CartID, Sku: item.Sku}, &model.CartItem{CurPrice: item.CurPrice})
		if qUpdate.Status != common.APIStatus.Ok {
			return qUpdate
		}
	}
	return nil
}

func getCart(acc *model.Account, getItems bool) (*model.Cart, *common.APIResponse) {
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return nil, errCustomerRes
	}

	return getCartFromCustomerID(customer.CustomerID, getItems)
}

func getCartFromCustomerID(customerID int64, getItems bool) (*model.Cart, *common.APIResponse) {
	cartRes := model.CartDB.QueryOne(&model.Cart{CustomerID: customerID})
	if cartRes.Status != common.APIStatus.Ok {
		return nil, cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]

	if getItems {
		cartItemRes := model.CartItemDB.Query(&model.CartItem{CartID: cart.CartID}, 0, 0, nil)
		if cartItemRes.Status != common.APIStatus.Ok {
			_ = model.CartDB.Delete(&model.Cart{
				CustomerID: customerID,
			})
			return nil, cartItemRes
		}
		cart.Items = cartItemRes.Data.([]*model.CartItem)
	}

	return cartRes.Data.([]*model.Cart)[0], nil
}

func handleExtraFee(customer *model.Customer, cart *model.Cart) *common.APIResponse {
	cart.UseFreeDelivery = false
	cart.ExtraFee = 0

	// // mở chặn giới hạn cho khách hàng là nhà thuốc của thuocsi
	// if customer.Level == "LEVEL_CIRCA" || (customer.TagMap != nil && customer.TagMap[string(enum.CustomerTag.Circa)]) {
	// 	return nil
	// }

	// if customer.TagMap != nil && customer.TagMap["OPV"] {
	// 	return nil
	// }

	if utils.IsInt64Contains(conf.Config.CustomerIDSkipValidateAmount, customer.CustomerID) { // bỏ chặn với tài khoản cs tạo đơn ảo
		return nil
	}

	// ================================ Min per order ================================
	if cart.SystemDisplay == "BUYDENTAL" {
		if cart.Price < 500000 {
			return OrderMinPrice500Error
		}
	} else if isClinicPortal(cart.Source) {
		if cart.Price < 500000 {
			return ClinicOrderMinPrice500Error
		}
	} else if isBrandPortal(cart.Source) {

		if mapBrandPortalConfig.MinPrice > 0 && cart.Price < mapBrandPortalConfig.MinPrice {
			return OrderMinPriceError(mapBrandPortalConfig.MinPrice)
		}
		// if cart.Price < 50000 {
		// 	return BrandOrderMinPrice500Error
		// }

	} else {
		if config, ok := mapProvinceExtraFee[cart.CustomerProvinceCode]; ok && config.MinPrice > 0 && cart.Price < config.MinPrice {
			return OrderMinPriceError(config.MinPrice)
		}

		//1.000.000vnd is default price if not have config
		if _, ok := mapProvinceExtraFee[cart.CustomerProvinceCode]; !ok && cart.Price < 1000000 {
			return OrderMinPrice1MError
		}
	}

	// ================================ extra fee ================================
	// Ignore with BRAND_SALES, CLINIC_PORTAL
	if isBrandOrClinic(cart.Source) {
		return nil
	}

	mapSellerItemCount := make(map[string]int)
	for _, item := range cart.Items {
		mapSellerItemCount[item.SellerCode]++
	}

	if customer.OrdersCount != nil && *customer.OrdersCount >= 1 {
		if cart.SystemDisplay == "BUYDENTAL" {
			if cart.Price < 2000000 && mapSellerItemCount[conf.DENX_SELLER] == 0 {
				cart.ExtraFee = 50000
				cart.ExtraFeeNote = "Phụ thu đơn hàng dưới 2 triệu"
			}

		} else if config, ok := mapProvinceExtraFee[cart.CustomerProvinceCode]; ok {
			if cart.Price < config.MaxPriceAppliedExtraFee {
				cart.ExtraFee = int64(config.ExtraFee)
				cart.ExtraFeeNote = fmt.Sprintf("Phụ thu đơn hàng %s", utils.FormatInt(config.ExtraFee))
			}
		} else if cart.CustomerProvinceCode == "01" || cart.CustomerProvinceCode == "79" {
			// feature: nếu là Ha Noi và HCM thì điều kiện tối thiểu 1tr5, nếu dưới thì phí dịch vụ 50k.
			// Các tỉnh thành còn lại vẫn 2tr
			if cart.Price < 1500000 {
				cart.ExtraFee = 50000
				cart.ExtraFeeNote = "Phụ thu đơn hàng dưới 1,5 triệu"
			}
		} else {
			if cart.Price < 2000000 {
				cart.ExtraFee = 50000
				cart.ExtraFeeNote = "Phụ thu đơn hàng dưới 2 triệu"
			}
		}
	}

	extraFeeConfig, extraConfigOk := mapLevelExtraFee[customer.Level]
	// 1. check level which isn't applied free-ship or not exist config
	if (extraConfigOk && extraFeeConfig.Max == 0) || !extraConfigOk || cart.ExtraFee == 0 {
		cart.TotalPrice = cart.TotalPrice + int(cart.ExtraFee)
		return nil
	}

	var extraFeeHistory *model.UserExtraFeeHistory
	qExtraFeeHistory := model.UserExtraFeeHistoryDB.QueryOne(&model.UserExtraFeeHistory{CustomerID: customer.CustomerID})

	// at start every month will reset data => extraFeeHistory will nil
	// History not found mean customer used not to use extraFee => auto apply free-ship
	if qExtraFeeHistory.Status != common.APIStatus.Ok {
		cart.ExtraFee = 0
		cart.UseFreeDelivery = true

		return nil
	}

	extraFeeHistory = qExtraFeeHistory.Data.([]*model.UserExtraFeeHistory)[0]

	var maxExtraFeeUseByLevel int64
	var extraFeeUseByLevelUsed int64

	// check current customer level if it existed
	if extraFeeHistory.ExtraFeeUse != nil {
		maxExtraFeeUseByLevel = mapLevelExtraFee[customer.Level].Max
		extraFeeUseByLevelUsed = extraFeeHistory.ExtraFeeUse[customer.Level].Used
	}

	if extraFeeUseByLevelUsed < maxExtraFeeUseByLevel {
		cart.ExtraFee = 0
		cart.UseFreeDelivery = true
	}

	cart.TotalPrice = cart.TotalPrice + int(cart.ExtraFee)
	return nil
}

func handleProcessDeliveryAndPaymentMethod(cart *model.Cart, customer *model.Customer) (paymentMethods []*client.PaymentFeeConfig, errRes *common.APIResponse) {
	cart.DeliveryDate = nil

	var (
		wg  = sync.WaitGroup{}
		err *common.APIResponse

		feeDeliveryMap = make(map[string]*client.DeliveryFeeConfig)
		feePaymentMap  = make(map[string]*client.PaymentFeeConfig)
	)

	// get region of cart
	if cart.RegionCode == "" {
		wg.Add(1)
		go sdk.Execute(func() {
			defer wg.Done()

			region, err := client.Services.Location.GetRegion(cart.ProvinceCode)
			if err == nil {
				cart.RegionCode = region.Code
			}
		})
	}

	// get fee data
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		feeDeliveryConfigs, priceErr := client.Services.Pricing.GetDeliveryFeeConfig()
		for _, fee := range feeDeliveryConfigs {
			feeDeliveryMap[fee.Code] = fee
		}
		err = priceErr
	})

	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		feePaymentConfigs, priceErr := client.Services.Pricing.GetPaymentFeeConfig()
		for _, fee := range feePaymentConfigs {
			paymentMethods = append(paymentMethods, fee)
			feePaymentMap[fee.Code] = fee
		}
		err = priceErr
	})

	wg.Wait()

	if err != nil {
		return paymentMethods, DeliveryAndPaymentError
	}

	if cart.PaymentMethod == "" && !isBrandOrClinic(cart.Source) {
		// get last order. If cart don't have payment method, then payment will be last order payment method
		lastOrderResp := model.OrderDB.Query(model.Order{CustomerID: customer.CustomerID}, 0, 1, &primitive.M{"_id": -1})
		if lastOrderResp.Status == common.APIStatus.Ok && len(lastOrderResp.Data.([]*model.Order)) > 0 && lastOrderResp.Data.([]*model.Order)[0].PaymentMethod != "" {
			lastOrder := lastOrderResp.Data.([]*model.Order)[0]
			cart.PaymentMethod = lastOrder.PaymentMethod
			pays, errCheckPaymentMethod := handleProcessDeliveryAndPaymentMethod(cart, customer)
			if cart.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
				errCheckPaymentMethod = handlePaymentMethodCredit(cart, customer, pays)
				if errCheckPaymentMethod != nil {
					cart.PaymentMethod = ""
				}
			} else {
				if errCheckPaymentMethod != nil && errCheckPaymentMethod == PaymentMethodError {
					cart.PaymentMethod = ""
				}
			}
		}
	}

	// DELIVERY
	if fee, ok := feeDeliveryMap[cart.DeliveryMethod]; ok {
		errValidRes := checkConditionDeliveryFeeConfig(cart, fee)
		if errValidRes != nil {
			errRes = errValidRes
			errRes.ErrorCode = "DELIVERY_METHOD_INVALID"
			makeCartError(errRes, cart)
		}
		// DeliveryDate
		t := time.Now()
		if cart.DeliveryMethod == "DELIVERY_PLATFORM_SUPER_QUICK" { // giao siêu tốc, giao trong 24h
			cart.DeliveryDate = utils.ParseTimeToPointer(t.Add(12 * time.Hour))
		} else if fee.Condition.TimeToDeliver != nil && *fee.Condition.TimeToDeliver > 0 {
			number := time.Duration(*fee.Condition.TimeToDeliver)
			cart.DeliveryDate = utils.ParseTimeToPointer(t.Add(number * time.Hour))
		}
		// DeliveryMethodFee
		var defaultFeeValue, regionFeeValue, provinceFeeValue, districtFeeValue, wardFeeValue *int64
		for i := range fee.DeliveryLocations {
			location := fee.DeliveryLocations[i]
			if location.FeeValue == nil {
				continue
			}
			for _, locCode := range location.LocationCodes {
				if locCode == nil {
					continue
				}
				if *locCode == "00" {
					defaultFeeValue = location.FeeValue
				}
				if cart.CustomerRegionCode == *locCode {
					regionFeeValue = location.FeeValue
				}
				if cart.CustomerProvinceCode == *locCode {
					provinceFeeValue = location.FeeValue
				}
				if cart.CustomerDistrictCode == *locCode {
					districtFeeValue = location.FeeValue
				}
				if cart.CustomerWardCode == *locCode {
					wardFeeValue = location.FeeValue
				}
			}
		}
		if wardFeeValue != nil {
			cart.DeliveryMethodFee = *wardFeeValue
		} else if districtFeeValue != nil {
			cart.DeliveryMethodFee = *districtFeeValue
		} else if provinceFeeValue != nil {
			cart.DeliveryMethodFee = *provinceFeeValue
		} else if regionFeeValue != nil {
			cart.DeliveryMethodFee = *regionFeeValue
		} else if defaultFeeValue != nil {
			cart.DeliveryMethodFee = *defaultFeeValue
		} else {
			errRes = DeliveryMethodError
			makeCartError(errRes, cart)
		}
		//t12 := time.Date(t.Year(), t.Month(), t.Day(), 12, 0, 0, 0, time.UTC)
	} else {
		errRes = DeliveryMethodError
		makeCartError(errRes, cart)
	}

	// PAYMENT
	if fee, ok := feePaymentMap[cart.PaymentMethod]; ok {

		if cart.PaymentMethod == string(enum.PaymentMethod.BANK) && customer.TagMap != nil && customer.TagMap["CIRCA"] {
			// CIRCA customer, do nothing https://buymed.atlassian.net/browse/CUS-30
		} else {

			// PaymentMethodPercentage
			// PaymentMethodFee
			var defaultDiscountPercent, regionDiscountPercent, provinceDiscountPercent, districtDiscountPercent, wardDiscountPercent *float64
			// wardFeeValue, districtFeeValue, provinceFeeValue, regionFeeValue
			for _, location := range fee.PaymentLocations {
				if location.FeeDiscountPercentage == nil {
					continue
				}
				for _, locCode := range location.LocationCodes {
					if locCode == nil {
						continue
					}

					if *locCode == "00" {
						defaultDiscountPercent = location.FeeDiscountPercentage
					}
					if cart.RegionCode == *locCode {
						regionDiscountPercent = location.FeeDiscountPercentage
					}
					if cart.ProvinceCode == *locCode {
						provinceDiscountPercent = location.FeeDiscountPercentage
					}
					if cart.DistrictCode == *locCode {
						districtDiscountPercent = location.FeeDiscountPercentage
					}
					if cart.WardCode == *locCode {
						wardDiscountPercent = location.FeeDiscountPercentage
					}
				}
			}
			if wardDiscountPercent != nil {
				cart.PaymentMethodPercentage = wardDiscountPercent
				feeValue := float64(cart.PaymentMethodFee) - (*wardDiscountPercent*float64(cart.Price))/100
				cart.PaymentMethodFee = int64(feeValue)
			} else if districtDiscountPercent != nil {
				cart.PaymentMethodPercentage = districtDiscountPercent
				feeValue := float64(cart.PaymentMethodFee) - (*districtDiscountPercent*float64(cart.Price))/100
				cart.PaymentMethodFee = int64(feeValue)
			} else if provinceDiscountPercent != nil {
				cart.PaymentMethodPercentage = provinceDiscountPercent
				feeValue := float64(cart.PaymentMethodFee) - (*provinceDiscountPercent*float64(cart.Price))/100
				cart.PaymentMethodFee = int64(feeValue)
			} else if regionDiscountPercent != nil {
				cart.PaymentMethodPercentage = regionDiscountPercent
				feeValue := float64(cart.PaymentMethodFee) - (*regionDiscountPercent*float64(cart.Price))/100
				cart.PaymentMethodFee = int64(feeValue)
			} else if defaultDiscountPercent != nil {
				cart.PaymentMethodPercentage = defaultDiscountPercent
				feeValue := float64(cart.PaymentMethodFee) - (*defaultDiscountPercent*float64(cart.Price))/100
				cart.PaymentMethodFee = int64(feeValue)
			} else {
				// không match dc location
				errRes = PaymentMethodError
				makeCartError(errRes, cart)
			}

		}
	} else {
		errRes = PaymentMethodError
		makeCartError(errRes, cart)
	}
	if customer.TagMap != nil && customer.TagMap[string(enum.CustomerTag.BlockCod)] && cart.PaymentMethod == "PAYMENT_METHOD_NORMAL" {
		errRes = NotSupportPaymentMethodError
		makeCartError(errRes, cart)
	}
	if customer.TagMap != nil && customer.TagMap[string(enum.CustomerTag.BlockBank)] && cart.PaymentMethod == "PAYMENT_METHOD_BANK" {
		errRes = NotSupportPaymentMethodBankError
		makeCartError(errRes, cart)
	}
	cart.PaymentMethodFee = utils.Ceil(cart.PaymentMethodFee)
	cart.DeliveryMethodFee = utils.Ceil(cart.DeliveryMethodFee)

	// if source cart is brand portal or clinic portal , then set payment method is COD
	if cart.Source != nil && (*cart.Source == enum.Source.BRAND_PORTAL || *cart.Source == enum.Source.CLINIC_PORTAL) {
		// BANK -> COD
		var zero int64 = 0
		cart.PaymentMethodFee = zero
		var zeroF float64 = 0
		cart.PaymentMethodPercentage = &zeroF
		if isClinicPortal(cart.Source) {
			cart.PaymentMethod = string(enum.PaymentMethod.COD)
		}
	}

	cart.TotalPrice = cart.TotalPrice + int(cart.PaymentMethodFee) + int(cart.DeliveryMethodFee)

	if cart.IsRefuseSplitOrder == nil {
		if _, ok := mapSplitOrderDistrictCode[cart.CustomerDistrictCode]; ok {
			if _, ok := mapConfigWardSplitWebMobile[cart.CustomerWardCode]; ok {
				// do nothing
			} else if _, ok := mapConfigDistricsSplitWebMobile[cart.CustomerDistrictCode]; ok {
				// do nothing
			} else if _, ok := mapConfigProvinceSplitWebMobile[cart.CustomerProvinceCode]; ok {
				// do nothing
			} else {
				cart.IsRefuseSplitOrder = utils.WithBool(true)
			}
		}
	}

	return paymentMethods, errRes
}

func checkOrderToRevertCart(order *model.Order) *common.APIResponse {
	if order.HasDeal {
		return OrderHasDealError
	}
	if order.HasCampaign {
		return OrderHasCampaignError
	}
	if order.Status != enum.OrderState.WaitConfirm {
		return OrderStatusError
	}
	editOrderTime := order.CreatedTime.Add(time.Minute * 30)
	if time.Now().UTC().After(editOrderTime) {
		return OrderCreatedTimeError
	}
	return nil
}

func checkConditionDeliveryFeeConfig(cart *model.Cart, feeConfig *client.DeliveryFeeConfig) *common.APIResponse {
	condition := feeConfig.Condition
	if condition != nil {
		if condition.MinPrice != nil && cart.Price < int(*condition.MinPrice) {
			priceString := fmt.Sprint(*feeConfig.Condition.MinPrice)
			priceString = utils.FormatVNDCurrency(priceString) + "đ"
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("%s chỉ áp dụng cho đơn hàng từ %s", feeConfig.Name, priceString),
				ErrorCode: "FEE_INVALID",
			}
		}

		if condition.MaxPrice != nil && cart.Price > int(*condition.MaxPrice) {
			priceString := fmt.Sprint(*feeConfig.Condition.MaxPrice)
			priceString = utils.FormatVNDCurrency(priceString) + "đ"
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("%s chỉ áp dụng cho đơn hàng dưới %s", feeConfig.Name, priceString),
				ErrorCode: "FEE_INVALID",
			}
		}

		if len(condition.NotSupportLocationCodes) > 0 {
			for _, locCode := range condition.NotSupportLocationCodes {
				if locCode != nil && (*locCode == cart.CustomerRegionCode || *locCode == cart.CustomerProvinceCode ||
					*locCode == cart.CustomerDistrictCode || *locCode == cart.CustomerWardCode) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("%s không áp dụng tại khu vực này", feeConfig.Name),
						ErrorCode: "FEE_INVALID",
					}
				}
			}
		}

		isValidLocation := false
		for _, location := range feeConfig.DeliveryLocations {
			for _, locCode := range location.LocationCodes {
				if locCode != nil && (*locCode == cart.CustomerRegionCode || *locCode == cart.CustomerProvinceCode ||
					*locCode == cart.CustomerDistrictCode || *locCode == cart.CustomerWardCode) ||
					*locCode == "00" {
					isValidLocation = true
					break
				}
			}
		}
		if !isValidLocation {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("%s không áp dụng tại khu vực này", feeConfig.Name),
				ErrorCode: "FEE_INVALID",
			}
		}

		if len(condition.Tags) > 0 {
			for _, item := range cart.Items {
				if !isCartItemSelected(item) {
					continue
				}
				tagItemStr := strings.Join(item.Tags, "+")
				for _, tagCondition := range condition.Tags {
					if tagCondition != nil && !strings.Contains(tagItemStr, *tagCondition) {
						return &common.APIResponse{
							Status:    common.APIStatus.Invalid,
							Message:   fmt.Sprintf("%s không thể áp dụng cho đơn hàng này", feeConfig.Name),
							ErrorCode: "FEE_INVALID",
						}
					}
				}
			}
		}

		// kiểm tra ngày không áp dụng
		if len(condition.NotSupportYMDs) > 0 {
			tz := time.FixedZone("UTC+7", +7*60*60)
			nowDMY := time.Now().In(tz).Format("20060102")
			for _, dmy := range condition.NotSupportYMDs {
				if nowDMY == *dmy {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   fmt.Sprintf("%s không áp dụng cho ngày hôm nay", feeConfig.Name),
						ErrorCode: "FEE_INVALID",
					}
				}
			}
		}
	}
	return nil
}

func handleApplyVoucherCode(cart *model.Cart, customer *model.Customer, source string) (errRes *common.APIResponse) {
	cart.SubPrice = cart.Price

	tempCart := *cart
	tempCart.TotalPrice = cart.TotalPrice
	tempCart.TotalQuantity = cart.TotalQuantitySelected
	tempCart.TotalItem = cart.TotalItemSelected

	tempCartItems := make([]*model.CartItem, 0)
	for _, item := range cart.Items {
		if item.IsSelected != nil && *item.IsSelected {
			tempCartItems = append(tempCartItems, item)
		}
	}
	tempCart.Items = tempCartItems

	prevRedeemMap := make(map[string]*model.RedeemApply)

	if len(cart.RedeemCodeMap) > 0 {
		for _, redeem := range cart.RedeemCodeMap {
			prevRedeemMap[redeem.Code] = &model.RedeemApply{
				Code:      redeem.Code,
				AutoApply: redeem.AutoApply,
			}
		}
	}

	emptyArr := make([]*string, 0)
	if cart.RedeemCode == nil {
		cart.RedeemCode = &emptyArr
	}
	cart.RedeemCodeRemovedArr = cart.RedeemCodeRemoved
	if cart.Screen == "Payment" {
		cart.RedeemCodeRemovedArr = []string{}
	}
	promoApply, errCheck := client.Services.Promotion.CheckVoucherCode(&client.CheckVoucherRequest{
		Customer:                customer,
		Cart:                    &tempCart,
		VoucherCode:             *cart.RedeemCode,
		AccountID:               cart.AccountID,
		GetVoucherAutoApply:     cart.GetVoucherAutoApply,
		GetVoucherAutoByPayment: cart.Screen == "Payment" || (cart.PaymentMethod != "" && cart.GetVoucherAutoApply),
		SystemDisplay:           cart.SystemDisplay,
		RedeemCodeRemovedArr:    cart.RedeemCodeRemovedArr,

		Source:                     source,
		SourceDetail:               cart.SourceDetail,
		SkipVoucherByPaymentMethod: cart.Screen != "Payment",
	})
	if errCheck != nil {
		errRes = &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errCheck.Error(),
			ErrorCode: "VOUCHER_CODE_INVALID",
		}
		makeCartError(errRes, cart)
	}

	redeemApplyResult := make([]*model.PromoApplyResult, 0)
	redeemCodeMap := make([]*model.RedeemApply, 0)
	curCodes := make([]*string, 0)
	mapItemDiscount := make(map[string]*model.DiscountDetail)
	for _, redeem := range promoApply {
		message := ""
		redeem.DiscountValue = int(utils.Ceil(int64(redeem.DiscountValue)))
		if cart.AutoRemoveVoucherAutoInvalid && redeem.AutoApply && !redeem.CanUse {
			continue
		}
		if redeem.CanUse {
			if cart.Screen == "Payment" && redeem.PaymentMethod != "" && cart.PaymentMethod == "" {
				cart.PaymentMethod = redeem.PaymentMethod
			}
			cart.Discount = cart.Discount + int(utils.Ceil(int64(redeem.DiscountValue)))
			if redeem.DiscountInfos != nil {
				for _, discountInfo := range redeem.DiscountInfos {
					if data, ok := mapItemDiscount[discountInfo.Sku]; ok {
						msg := data.Message
						if discountInfo.Message != "" {
							msg = discountInfo.Message
						}
						if data.Message != "" && discountInfo.Message != "" {
							msg = "Mã giảm giá không áp dụng cho sản phẩm này"
						}
						mapItemDiscount[discountInfo.Sku] = &model.DiscountDetail{
							TotalDiscount: data.TotalDiscount + discountInfo.Discount,
							Message:       msg,
							VoucherDetails: append(data.VoucherDetails, &model.VoucherDetail{
								VoucherCode:   redeem.VoucherCode,
								DiscountValue: discountInfo.Discount,
								IsApply:       discountInfo.IsApply,
								SellerCodes:   discountInfo.SellerCodes,
								StoreCode:     redeem.StoreCode,
							}),
						}
					} else {
						mapItemDiscount[discountInfo.Sku] = &model.DiscountDetail{
							TotalDiscount: discountInfo.Discount,
							Message:       discountInfo.Message,
							VoucherDetails: []*model.VoucherDetail{
								{
									VoucherCode:   redeem.VoucherCode,
									DiscountValue: discountInfo.Discount,
									IsApply:       discountInfo.IsApply,
									SellerCodes:   discountInfo.SellerCodes,
									StoreCode:     redeem.StoreCode,
								},
							},
						}
					}

				}
			}
		} else {
			errRes = &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   redeem.ErrorMessage,
				ErrorCode: "VOUCHER_CODE_INVALID",
			}
			makeCartError(errRes, cart)
			message = redeem.ErrorMessage
		}
		redeemApplyResult = append(redeemApplyResult, &model.PromoApplyResult{
			Code:              redeem.VoucherCode,
			CanUse:            redeem.CanUse,
			Message:           message,
			Gift:              redeem.Gifts,
			AutoApply:         redeem.AutoApply,
			MatchSeller:       redeem.MatchSeller,
			MatchProducts:     redeem.MatchProducts,
			DiscountValue:     redeem.DiscountValue,
			SellerCode:        redeem.SellerCode,
			SellerCodes:       redeem.SellerCodes,
			ApplySkus:         redeem.ApplySkus,
			NotApplySkus:      redeem.NotApplySkus,
			StoreCode:         redeem.StoreCode,
			PaymentMethod:     redeem.PaymentMethod,
			Name:              redeem.VoucherName,
			NumberOfAutoApply: redeem.NumberOfAutoApply,
			ChargeFee:         redeem.ChargeFee,
		})
		redeemCodeMap = append(redeemCodeMap, &model.RedeemApply{
			Code:      redeem.VoucherCode,
			AutoApply: redeem.AutoApply,
		})
		curCodes = append(curCodes, &redeem.VoucherCode)

		//}
	}
	if len(mapItemDiscount) > 0 {
		for _, item := range cart.Items {
			if data, ok := mapItemDiscount[item.Sku]; ok {
				item.DiscountDetail = data
			}
		}
	}

	cart.RedeemCode = &curCodes
	if cart.Discount > cart.TotalPrice {
		cart.Discount = cart.TotalPrice
	}
	cart.TotalPrice = cart.TotalPrice - cart.Discount
	cart.SubPrice = cart.Price - cart.Discount
	cart.RedeemApplyResult = redeemApplyResult
	cart.RedeemCodeMap = redeemCodeMap

	return errRes
}

func handleProcessCheckQtyItemImportants(cart *model.Cart) (errRes *common.APIResponse) {
	iptItemQty := 0
	for _, item := range cart.Items {
		if isCartItemSelected(item) && item.IsImportant != nil && *item.IsImportant {
			iptItemQty += 1
		}
	}
	if cart.TotalItemSelected != nil && *cart.TotalItemSelected >= iptItemQty*5 {
		return nil
	}
	return ImportantItemError
}

// handleProcessContentItems processes the items in the cart, updating various cart properties and fetching necessary data.
// It performs the following tasks:
// - Updates the total item count, total quantity, total selected quantity, and total selected item count in the cart.
// - Fetches SKU data and updates the cart's SKU map and invoice export capability.
// - Fetches SKU limit configurations and prepares SKU limit logs.
// - Optionally fetches region codes based on the cart's province code.
// - Processes each cart item to save content, validate SKU limits, and update item properties.
//
// Parameters:
// - cart: The cart object containing items to be processed.
// - customer: The customer object associated with the cart.
// - tagSettingMap: A map of order tag configurations.
// - getRegionCodes: true = also get cart.RegionCodes and cart.SaleRegionCodes. [HACK] Must set to true to be called later in handleApplyVoucherCode(). It is called here to utilize the go routines in this function to fetch data concurrently.
//
// Returns:
// - errRes: An API response object containing any errors encountered during processing.
func handleProcessContentItems(cart *model.Cart, customer *model.Customer, tagSettingMap map[string]*model.OrderTagConfig, getRegionCodes bool) (errRes *common.APIResponse) {

	cart.TotalItem = utils.ParseIntToPointer(len(cart.Items))
	cart.TotalQuantity = utils.ParseIntToPointer(0)
	cart.TotalQuantitySelected = utils.ParseIntToPointer(0)
	cart.TotalItemSelected = utils.ParseIntToPointer(0)

	var (
		skuMap       = make(map[string]*client.ProductData)
		skuItemCodes = make([]string, 0)
	)

	// Init skuMap
	skus := make([]string, 0)

	for _, item := range cart.Items {
		cart.TotalQuantity = utils.ParseIntToPointer(*cart.TotalQuantity + item.Quantity)
		if isCartItemSelected(item) {
			cart.TotalQuantitySelected = utils.ParseIntToPointer(*cart.TotalQuantitySelected + item.Quantity)
			cart.TotalItemSelected = utils.ParseIntToPointer(*cart.TotalItemSelected + 1)
		}
		if item.Skus != nil {
			for _, sku := range *item.Skus {
				if sku != nil {
					skus = append(skus, sku.SKU)
				}
			}
		}
		skus = append(skus, item.Sku)
	}

	isBrandSales := isBrandOrClinic(cart.Source)

	var salesTypeCodeParam string
	if cart.Source != nil {
		if *cart.Source == enum.Source.CLINIC_PORTAL {
			salesTypeCodeParam = fmt.Sprintf("%s_%s", "clinic-portal", cart.SalesTypeCode)
		} else {
			salesTypeCodeParam = fmt.Sprintf("%s_%s", "brand-portal", cart.SalesTypeCode)
		}
	}

	skuData, err := client.Services.Product.GetListSku(skus, customer.ProvinceCode, customer.CustomerID, isBrandSales, salesTypeCodeParam)
	if err != nil {
		return err
	}

	canExportInvoice := false
	for _, skuData := range skuData {
		if skuData.SKU != nil {
			skuMap[skuData.SKU.Code] = skuData

			if skuData.SKU.VAT != nil && *skuData.SKU.VAT > 0 {
				canExportInvoice = true
			}

			skuItemCodes = append(skuItemCodes, skuData.SKU.ItemCode)
		}
	}
	cart.CanExportInvoice = utils.ParseBoolToPointer(canExportInvoice)

	var (
		wg sync.WaitGroup

		skuLimitMap = make(map[string]*model.SkuLimit)
		logMap      = make(map[string]*model.SkuLimitHistory)
		now         = time.Now()
	)

	// Init skuLimitMap to check for sku limit
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		skuLimitMap = getMapSkuLimitsBySkuItemCodeLive(skuItemCodes, cart.CustomerID)
	})

	// Init logMap: check for quantity purchased today of skus
	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		queryLog := &model.SkuLimitHistory{
			CustomerID: cart.CustomerID,
			ComplexQuery: []*bson.M{
				{"item_code": bson.M{"$in": skuItemCodes}},
				{"start_time": bson.M{"$lte": now}},
				{"end_time": bson.M{"$gte": now}},
			}}
		qLog := model.SkuLimitHistoryDB.Query(queryLog, 0, 0, nil)
		if qLog.Status == common.APIStatus.Ok {
			logs := qLog.Data.([]*model.SkuLimitHistory)
			for _, log := range logs {
				logMap[log.Sku] = log
			}
		}
	})

	// init region data in cart
	if getRegionCodes {
		wg.Add(1)
		go sdk.Execute(func() {
			defer wg.Done()

			regions, err := client.Services.Location.GetRegionList(0, 100, []string{cart.ProvinceCode})
			cart.RegionCodes = []string{}
			cart.SaleRegionCodes = []string{}
			if err == nil {
				for _, region := range regions {
					cart.RegionCodes = append(cart.RegionCodes, region.Code)
					if region.Scope == "SALE_REGION" {
						cart.SaleRegionCodes = append(cart.SaleRegionCodes, region.Code)
					}
				}
			}
		})
	}

	wg.Wait()

	if errRes != nil {
		return errRes
	}

	// Update the max quantity based on sku and sku_limit
	for _, sku := range skuMap {
		if sku != nil && sku.SKU != nil {
			sku.LimitPerOrder = int(sku.SKU.MaxQuantityPerOrder)

			sl := skuLimitMap[sku.SKU.ItemCode]
			if sl != nil && sl.LimitPerOrder > 0 {
				sku.LimitPerOrder = int(sl.LimitPerOrder)
			}
		}
	}
	cart.SkuMap = skuMap

	// Check for special control items and mark cart items accordingly
	for _, item := range cart.Items {
		if skuData, ok := skuMap[item.Sku]; ok && skuData != nil && skuData.SKU != nil {
			if skuData.SKU.IsSpecialControl {
				// Mark cart item as special control
				item.IsSpecialControl = utils.ParseBoolToPointer(true)
			}
		}
	}

	// end prepare data check sku limit
	for _, item := range cart.Items {
		if skuMap[item.Sku] != nil && len(skuMap[item.Sku].SKU.ItemCode) > 0 {
			item.ItemCode = skuMap[item.Sku].SKU.ItemCode
		}
		errSaveContentRes := saveContentToItem(cart, customer, item, skuMap, nil, tagSettingMap, customer.CertificateMap)
		if limit, ok := skuLimitMap[item.ItemCode]; ok && limit != nil {
			item.IsSkuLimitExisted = true
			if limit.CanApplyLimitPerDay() {
				item.SkuLimitQuantity = limit.Quantity
			}

			if limit.IsActive != nil && *limit.IsActive { // skip validate if config is not active
				if log, ok := logMap[item.Sku]; ok && log != nil { // if existed quantity ordered
					if log.Quantity == nil {
						log.Quantity = utils.ParseIntToPointer(0)
					}
					if item.ProductData == nil {
						item.ProductData = &client.ProductData{QuantityPurchasedToday: int64(*log.Quantity)}
					} else if val, ok := item.ProductData.(*client.ProductData); ok {
						val.QuantityPurchasedToday = int64(*log.Quantity)
					}
				} else {
					quantityRecount, _ := recountQuantityOrdered(syncSkuLimitData{
						CustomerID:   cart.CustomerID,
						AccountID:    cart.AccountID,
						Sku:          item.Sku,
						ItemCode:     item.ItemCode,
						Date:         &now,
						NumberOfDays: limit.NumberOfDays,
					})
					if item.ProductData == nil {
						item.ProductData = &client.ProductData{QuantityPurchasedToday: int64(quantityRecount)}
					} else if val, ok := item.ProductData.(*client.ProductData); ok {
						val.QuantityPurchasedToday = int64(quantityRecount)
					}
				}
			}
		}
		if errSaveContentRes != nil && isCartItemSelected(item) {
			errRes = errSaveContentRes
		}
		if item.SubItems != nil {
			for _, subItem := range *item.SubItems {
				if subItem == nil {
					continue
				}

				errSubItem := saveContentToItem(cart, customer, subItem, skuMap, item, tagSettingMap, customer.CertificateMap)
				if errSubItem != nil && errSubItem.ErrorCode == RequiredCertificateError.ErrorCode {
					item.ErrorCode = errSubItem.ErrorCode
					item.ErrorMessage = errSubItem.Message
					if isCartItemSelected(item) {
						errRes = errSubItem
					}
				}
			}
		}
	}

	return errRes
}

func saveContentToItem(cart *model.Cart, customer *model.Customer, item *model.CartItem, skuMap map[string]*client.ProductData, parentItem *model.CartItem, tagSettingMap map[string]*model.OrderTagConfig, customerCertificateMap map[enum.CustomerCertificateValue]bool) (errRes *common.APIResponse) {
	if sku, ok := skuMap[item.Sku]; ok {
		isCheckAvailable := customerSKUConfigCache.IsActive
		item.ProductData = sku
		item.DealCode = &sku.SKU.DealCode
		item.SellerCode = sku.SKU.SellerCode
		item.SellerPrice = int(sku.SKU.RetailPriceValue)
		item.ProductID = sku.SKU.ProductID
		item.ProductCode = sku.SKU.ProductCode
		item.VAT = sku.SKU.VAT
		item.NoneVAT = sku.SKU.NoneVat
		if sku.Product != nil {
			item.ProductName = sku.Product.Name
		}
		item.MaxQuantity = int(sku.LimitPerOrder)
		item.Price = int(sku.SKU.RetailPriceValue)
		item.IsDynamicPricingLevel = sku.SKU.IsDynamicPricingLevel
		item.DynamicPricingLevel = sku.SKU.DynamicPricingLevel
		item.PricingStrategy = sku.SKU.PricingStrategy
		item.FeeCodes = sku.SKU.FeeCodes
		item.IsAvailable = sku.IsAvailable
		item.SkuStatus = sku.SKU.Status
		item.LotDates = sku.SKU.LotDates
		item.SkuPriceType = sku.SKU.RetailPriceType
		item.SkuStatusData = sku.SKU.StatusData
		item.SkuVersion = sku.SKU.Version
		item.Tags = sku.SKU.Tags
		item.Point = sku.SKU.Point
		item.PointMultiplier = sku.SKU.PointMultiplier
		item.ItemCode = sku.SKU.ItemCode
		item.SellerClass = sku.SKU.SellerClass

		if sku.SKU.CampaignCode != nil {
			item.CampaignCode = *sku.SKU.CampaignCode
		}

		if sku.SKU.RetailPriceType != nil {
			item.RetailPriceType = string(*sku.SKU.RetailPriceType)
		}

		if MapVendorStoreByTag != nil {
			if item.Tags != nil {
				muMapVendorStoreByTag.RLock()
				for _, tag := range item.Tags {
					if vendorStore, ok := MapVendorStoreByTag[tag]; ok {
						item.StoreCode = vendorStore
						break
					}
				}
				muMapVendorStoreByTag.RUnlock()
			}
		}

		if len(sku.SKU.RequiredCertificates) > 0 {
			isValid := false
			for _, cert := range sku.SKU.RequiredCertificates {
				if _, ok := customerCertificateMap[enum.CustomerCertificateValue(cert)]; ok {
					isValid = true
					break
				}
			}
			if !isValid {
				errRes = buildError(RequiredCertificateError.ErrorCode, RequiredCertificateError.Status, strings.Join(sku.SKU.RequiredCertificates, ", "), sku.SKU.RequiredCertificates)
				makeCartItemError(errRes, item)
			}
		}

		// TODO: Only allow customer trading can buy sku trading
		if customer.BusinessType == enum.BusinessType.TRADING {
			if sku.SKU != nil && (sku.SKU.IsTrading == nil || !*sku.SKU.IsTrading) {
				errRes = buildError(CartItemError.ErrorCode, CartItemError.Status, CartItemError.Message)
				makeCartItemError(errRes, item)
			}
		}

		if cart.SystemDisplay == "BUYMED" && utils.IsContains(conf.SELLERS_NOT_SHOW_BUYMED, sku.SKU.SellerCode) {
			errRes = buildError(SuspendedSkuError.ErrorCode, SuspendedSkuError.Status, SuspendedSkuError.Message)
			makeCartItemError(errRes, item)
		}

		if isCartItemSelected(item) && tagSettingMap != nil {
			newTags := item.Tags[:0]
			for _, tag := range item.Tags {
				if setting, ok := tagSettingMap[tag]; ok {
					if setting.IsActive != nil && *setting.IsActive {
						if _, ok := setting.ProvinceAppliedMap[cart.CustomerProvinceCode]; ok {
							if cart.TagMap == nil {
								cart.TagMap = map[string]*model.OrderTagConfig{}
							}
							cart.TagMap[tag] = setting
							newTags = append(newTags, tag)
						}
					}
					continue
				}
				newTags = append(newTags, tag)
			}
			item.Tags = newTags
		}

		if sku.Product != nil && isCartItemSelected(item) {
			item.Weight = sku.Product.Weight / 1000
			item.TotalWeight = item.Weight * float64(item.Quantity)
			item.Volume = calVolume(sku.Product.Width, sku.Product.Height, sku.Product.Length) / 1000
			item.TotalVolume = item.Volume * float64(item.Quantity)

			item.Unit = sku.Product.Unit
			item.SkuUnit = sku.Product.Unit
		}
		newYear := time.Date(2022, 1, 1, 0, 0, 0, 0, model.VNTimeZone)
		if newYear.Before(time.Now()) {
			item.SkuLevel = &sku.SKU.Level
			if sku.SKU.LevelSpecial != nil && sku.SKU.LevelSpecial.TimeEndApplyLevel != nil {
				t := time.Now()
				if sku.SKU.LevelSpecial.TimeEndApplyLevel.After(t) {
					item.SkuLevel = sku.SKU.LevelSpecial.Level
				}
			}
		}

		if isBrandOrClinic(cart.Source) {
			if cart.SalesType != "SELLER" {
				item.ContractPrice = int(sku.SKU.RetailPriceValue)
			}
		} else {
			item.SkuContractCode = &sku.SKU.SkuContractCode
			item.SkuContractDetailCode = &sku.SKU.SkuContractDetailCode
			if len(sku.SKU.SkuContractCode) > 0 && len(sku.SKU.SkuContractDetailCode) > 0 {
				item.ContractPrice = int(sku.SKU.RetailPriceValue)
				item.ContractInfo.Price = int(sku.SKU.RetailPriceValue)
			}
		}

		// item.Owner = sku.Owner
		if sku.SKU.SKUs != nil && len(*sku.SKU.SKUs) > 0 {
			subItems := make([]*model.CartItem, 0)
			for _, sku := range *sku.SKU.SKUs {
				subItems = append(subItems, &model.CartItem{
					Sku:      sku.SKU,
					Quantity: sku.Quantity,
					Type:     enum.ItemType.IN_COMBO,
				})
			}
			item.SubItems = &subItems
			item.Weight = 0
			item.TotalWeight = 0
			item.Volume = 0
			item.TotalVolume = 0
		}

		// combo
		item.IsCombo = sku.SKU.IsCombo
		item.Skus = sku.SKU.SKUs
		// combo price
		item.UseSKUsPrice = sku.SKU.UseSKUsPrice
		item.ComboDiscountType = sku.SKU.ComboDiscountType
		item.ComboDiscountValue = sku.SKU.ComboDiscountValue
		item.ComboMaxDiscountValue = sku.SKU.ComboMaxDiscountValue

		item.IsNearExpired = false
		if sku.SKU.LotDates != nil {
			for _, lotDate := range *sku.SKU.LotDates {
				if lotDate.IsNearExpired != nil && *lotDate.IsNearExpired {
					item.IsNearExpired = true
					break
				}
			}
		}

		switch item.Type {
		case enum.ItemType.NORMAL:
			if sku.SKU.Status != nil && *sku.SKU.Status == enum.SkuStatus.LIMIT {
				if sku.SKU.StatusData != nil && sku.SKU.StatusData.Quantity != nil {
					if item.Quantity > *sku.SKU.StatusData.Quantity {
						errRes = buildMaxQuantityError(&maxQuantityErrorData{
							MaxQty: *sku.SKU.StatusData.Quantity,
							SKU:    item.Sku,
							Note:   "for type=NORMAL",
						})
						makeCartItemError(errRes, item)
					}
				}
			}
			item.MaxQuantity = int(sku.LimitPerOrder)
			item.Price = int(sku.SKU.RetailPriceValue)
			item.SellerRevenue = int(sku.SKU.RetailPriceValue)

			// switch normal to deal
			if sku.Deal != nil {
				errCheckDeal := checkDealValid(sku.Deal)
				if errCheckDeal == nil {
					res := switchCartItemType(item, item.Type, enum.ItemType.DEAL)
					if res.Status != common.APIStatus.Ok {
						errRes = res
						makeCartItemError(errRes, item)
					}
					item.MaxQuantity = sku.Deal.MaxQuantity
					item.Price = int(sku.SKU.RetailPriceValue)
					item.DealPrice = sku.Deal.Price
					item.DealDiscountPercent = sku.Deal.DiscountPercent
					item.DealMaxDiscountValue = sku.Deal.MaxDiscountValue
					item.ChargeDealFee = sku.Deal.ChargeDealFee
					item.DealPricingType = sku.Deal.PricingType
					item.Type = enum.ItemType.DEAL
					item.DealCode = &sku.Deal.Code
					item.DealMaxQuantityPerCustomer = sku.Deal.MaxQuantityPerCustomer
					item.TotalDealQuantity = sku.Deal.TotalDealQuantity
					if sku.Deal.VendorPromoInfo != nil {
						item.DealVendorPrice = sku.Deal.VendorPromoInfo.Price
					}
					item.DealInfo = model.DiscountPromoInfo{
						StartTime:        sku.Deal.StartTime,
						EndTime:          sku.Deal.EndTime,
						Type:             sku.Deal.PricingType,
						MaxDiscount:      sku.Deal.MaxDiscountValue,
						DiscountPercent:  sku.Deal.DiscountPercent,
						Discounted:       sku.Deal.Price,
						ChargeFee:        sku.Deal.ChargeDealFee,
						Tags:             sku.Deal.DealTags,
						SegmentationCode: sku.Deal.SegmentationCode,
					}
				}
			}

			// switch normal to campaign
			if sku.Campaign != nil {
				item.MaxQuantity = int(sku.Campaign.Quantity)
				item.Price = int(sku.SKU.RetailPriceValue)
				item.CampaignPrice = int(sku.Campaign.CampaignPrice)
				item.CampaignDiscountPercent = sku.Campaign.PercentageDiscount
				item.CampaignMaxDiscountValue = sku.Campaign.MaxDiscount
				item.ChargeCampaignFee = sku.Campaign.ChargeFee
				item.CampaignPricingType = sku.Campaign.SaleType
				item.CampaignDiscountValue = sku.Campaign.AbsoluteDiscount
				item.Type = enum.ItemType.CAMPAIGN
				item.CampaignCode = sku.Campaign.CampaignCode
				item.CampaignProductCode = sku.Campaign.CampaignProductCode
				item.DealMaxQuantityPerCustomer = sku.Campaign.MaxQuantityPerCustomer
				item.TotalDealQuantity = sku.Campaign.TotalQuantity
				if sku.Campaign.Campaign != nil && sku.Campaign.Campaign.CampaignType == "MKT_SUPPORT_PRICE" {
					item.SubsidyType = sku.Campaign.Campaign.SubsidyType
					item.SubsidyValue = sku.Campaign.Campaign.SubsidyValue
				}
				campaign := sku.Campaign.Campaign
				if campaign != nil {
					item.CampaignInfo = model.DiscountPromoInfo{
						StartTime:  &campaign.StartTime,
						EndTime:    &campaign.EndTime,
						Type:       &sku.Campaign.SaleType,
						Discounted: int(sku.Campaign.CampaignPrice),
						ChargeFee:  sku.Campaign.ChargeFee,
					}
					if sku.Campaign.MaxDiscount != nil {
						item.CampaignInfo.MaxDiscount = utils.ParseIntToPointer(int(*sku.Campaign.MaxDiscount))
					}
					if sku.Campaign.PercentageDiscount != nil {
						item.CampaignInfo.DiscountPercent = utils.ParseFloat64ToPointer(float64(*sku.Campaign.PercentageDiscount))
					}
					if sku.Campaign.AbsoluteDiscount != nil {
						item.CampaignInfo.Discount = int(*sku.Campaign.AbsoluteDiscount)
					}
				}
			}
		case enum.ItemType.DEAL:
			if sku.Deal == nil {
				res := switchCartItemType(item, item.Type, enum.ItemType.NORMAL)
				if res.Status != common.APIStatus.Ok {
					errRes = res
					makeCartItemError(res, item)
				}
				item.MaxQuantity = int(sku.LimitPerOrder)
				item.Price = int(sku.SKU.RetailPriceValue)
				item.Type = enum.ItemType.NORMAL
				item.DealCode = nil
			} else {
				if checkDealValid(sku.Deal) != nil {
					res := switchCartItemType(item, item.Type, enum.ItemType.NORMAL)
					if res.Status != common.APIStatus.Ok {
						errRes = res
						makeCartItemError(res, item)
					}
					item.MaxQuantity = int(sku.LimitPerOrder)
					item.Price = int(sku.SKU.RetailPriceValue)
				} else {
					/*
						nếu ko có deal => lấy seller price -> default
						nếu có deal:
						+ nếu là deal MARKETPLACE => lấy seller price
						+ nếu là deal SELLER => lấy deal price
					*/
					item.Owner = sku.Deal.Owner
					item.ChargeDealFee = sku.Deal.ChargeDealFee
					if item.ChargeDealFee == "SELLER" {
						item.SellerRevenue = int(sku.Deal.Price)
					} else {
						item.SellerRevenue = int(sku.SKU.RetailPriceValue)
					}
					item.MaxQuantity = sku.Deal.MaxQuantity
					item.Price = int(sku.SKU.RetailPriceValue)
					item.DealPrice = sku.Deal.Price
					item.DealDiscountPercent = sku.Deal.DiscountPercent
					item.DealMaxDiscountValue = sku.Deal.MaxDiscountValue

					item.DealPricingType = sku.Deal.PricingType
					item.DealMaxQuantityPerCustomer = sku.Deal.MaxQuantityPerCustomer
					item.TotalDealQuantity = sku.Deal.TotalDealQuantity
					if sku.Deal.VendorPromoInfo != nil {
						item.DealVendorPrice = sku.Deal.VendorPromoInfo.Price
					}
					item.DealInfo = model.DiscountPromoInfo{
						StartTime:        sku.Deal.StartTime,
						EndTime:          sku.Deal.EndTime,
						Type:             sku.Deal.PricingType,
						MaxDiscount:      sku.Deal.MaxDiscountValue,
						DiscountPercent:  sku.Deal.DiscountPercent,
						Discounted:       sku.Deal.Price,
						ChargeFee:        sku.Deal.ChargeDealFee,
						Tags:             sku.Deal.DealTags,
						SegmentationCode: sku.Deal.SegmentationCode,
					}
				}
			}
		case enum.ItemType.CAMPAIGN:
			if sku.Campaign != nil {
				item.MaxQuantity = int(sku.Campaign.Quantity)
				item.Price = int(sku.SKU.RetailPriceValue)
				item.CampaignPrice = int(sku.Campaign.CampaignPrice)
				item.CampaignDiscountPercent = sku.Campaign.PercentageDiscount
				item.CampaignMaxDiscountValue = sku.Campaign.MaxDiscount
				item.CampaignPricingType = sku.Campaign.SaleType
				item.CampaignDiscountValue = sku.Campaign.AbsoluteDiscount
				item.ChargeCampaignFee = sku.Campaign.ChargeFee
				item.DealMaxQuantityPerCustomer = sku.Campaign.MaxQuantityPerCustomer
				item.CampaignCode = sku.Campaign.CampaignCode
				item.CampaignProductCode = sku.Campaign.CampaignProductCode
				item.TotalDealQuantity = sku.Campaign.TotalQuantity
				if sku.Campaign.Campaign != nil && sku.Campaign.Campaign.CampaignType == "MKT_SUPPORT_PRICE" {
					item.SubsidyType = sku.Campaign.Campaign.SubsidyType
					item.SubsidyValue = sku.Campaign.Campaign.SubsidyValue
				}
				campaign := sku.Campaign.Campaign
				if campaign != nil {
					item.CampaignInfo = model.DiscountPromoInfo{
						StartTime:  &campaign.StartTime,
						EndTime:    &campaign.EndTime,
						Type:       &sku.Campaign.SaleType,
						Discounted: int(sku.Campaign.CampaignPrice),
						ChargeFee:  sku.Campaign.ChargeFee,
					}
					if sku.Campaign.MaxDiscount != nil {
						item.CampaignInfo.MaxDiscount = utils.ParseIntToPointer(int(*sku.Campaign.MaxDiscount))
					}
					if sku.Campaign.PercentageDiscount != nil {
						item.CampaignInfo.DiscountPercent = utils.ParseFloat64ToPointer(float64(*sku.Campaign.PercentageDiscount))
					}
					if sku.Campaign.AbsoluteDiscount != nil {
						item.CampaignInfo.Discount = int(*sku.Campaign.AbsoluteDiscount)
					}
				}
			} else {
				switchCartItemType(item, item.Type, enum.ItemType.NORMAL)
				item.Type = enum.ItemType.NORMAL
				item.CampaignCode = ""
			}

		case enum.ItemType.IN_COMBO:
			if sku.SKU.Status != nil && *sku.SKU.Status == enum.SkuStatus.LIMIT {
				if sku.SKU.StatusData != nil && !isNilOrDefaultValue(sku.SKU.StatusData) && !isNilOrDefaultValue(sku.SKU.StatusData.Quantity) {
					if item.Quantity > *sku.SKU.StatusData.Quantity {
						errRes = buildMaxQuantityError(&maxQuantityErrorData{
							MaxQty: *sku.SKU.StatusData.Quantity,
							SKU:    item.Sku,
							Note:   "for type=IN_COMBO",
						})
						makeCartItemError(errRes, item)
					}
				}
			}
			item.MaxQuantity = int(sku.LimitPerOrder)
			item.Price = int(sku.SKU.RetailPriceValue)
			item.SellerRevenue = int(sku.SKU.RetailPriceValue)
			parentItem.Weight = parentItem.Weight + item.TotalWeight
			parentItem.TotalWeight = parentItem.Weight * float64(parentItem.Quantity)
			parentItem.Volume = parentItem.Volume + item.TotalVolume
			parentItem.TotalVolume = parentItem.Volume * float64(parentItem.Quantity)
		case enum.ItemType.GIFT:
			item.Price = 0
		default:
			errRes = CartItemError
			makeCartItemError(errRes, item)
		}

		if item.DealCode != nil && *item.DealCode != "" && item.DealMaxQuantityPerCustomer > 0 {
			cart.DealLimitPerCustomerCodes = append(cart.DealLimitPerCustomerCodes, *item.DealCode)
		}
		if item.CampaignCode != "" && item.DealMaxQuantityPerCustomer > 0 {
			cart.DealLimitPerCustomerCodes = append(cart.DealLimitPerCustomerCodes, item.CampaignProductCode)
		}
		if sku.SKU.IsActive == nil || !*sku.SKU.IsActive {
			errRes = NotActiveSkuError
			makeCartItemError(errRes, item)
		}
		if sku.SKU.Status != nil {
			if *sku.SKU.Status == enum.SkuStatus.SUSPENDED {
				errRes = SuspendedSkuError
				makeCartItemError(errRes, item)
			} else if *sku.SKU.Status == enum.SkuStatus.STOP_PRODUCING {
				errRes = StopProducingSkuError
				makeCartItemError(errRes, item)
			} else if *sku.SKU.Status == enum.SkuStatus.OUT_OF_STOCK {
				errRes = OutOfStockSkuError
				makeCartItemError(errRes, item)
			}
		}

		if item.Type == enum.ItemType.CAMPAIGN && sku.Campaign != nil {
			if sku.Campaign.MaxQuantityPerOrder < int64(item.Quantity) {
				errRes = buildMaxQuantityError(&maxQuantityErrorData{
					MaxQty: int(sku.LimitPerOrder),
					SKU:    item.Sku,
					Note:   "for type=CAMPAIGN over MaxQuantityPerOrder",
				})
				makeCartItemError(errRes, item)

			} else if sku.Campaign.Quantity-sku.Campaign.SoldQuantity < int64(item.Quantity) {
				errRes = buildMaxQuantityError(&maxQuantityErrorData{
					MaxQty: int(sku.Campaign.Quantity - sku.Campaign.SoldQuantity),
					SKU:    item.Sku,
					Note:   "for type=CAMPAIGN over SoldQuantity",
				})
				makeCartItemError(errRes, item)
			}
		}
		item.TotalPrice = item.Quantity * item.Price
		if item.Type == enum.ItemType.IN_COMBO {
			cart.TotalWeight = cart.TotalWeight + item.TotalWeight*float64(parentItem.Quantity)
			cart.TotalVolume = cart.TotalVolume + item.TotalVolume*float64(parentItem.Quantity)
		} else {
			cart.TotalWeight = cart.TotalWeight + item.TotalWeight
			cart.TotalVolume = cart.TotalVolume + item.TotalVolume
		}

		if !item.IsAvailable {
			errRes = NotAvailableSkuError
			makeCartItemError(errRes, item)
		}

		isAvailableDisplay := checkAvailableBySku(item.Sku, customer.CustomerID)
		if isCheckAvailable && !isAvailableDisplay {
			errRes = OutOfStockSkuError
			makeCartItemError(errRes, item)
		}

	} else {
		errRes = NotFoundSkuError
		makeCartItemError(errRes, item)
	}
	return errRes
}

func handleProcessContentGiftItems(cart *model.Cart, customer *model.Customer, tagSettingMap map[string]*model.OrderTagConfig) (errRes *common.APIResponse) {
	skus := make([]string, 0)

	// GIFT cart item
	mapItemGift := make(map[string]*model.CartItem, 0)
	for _, redeem := range cart.RedeemApplyResult {
		if redeem.CanUse && len(redeem.Gift) > 0 {
			for _, gift := range redeem.Gift {
				item := &model.CartItem{
					RefCart:     cart.RefCartItem,
					CartID:      cart.CartID,
					CartNo:      cart.CartNo,
					Sku:         gift.Sku,
					Quantity:    gift.Quantity,
					MaxQuantity: gift.Quantity,
					Type:        enum.ItemType.GIFT,
					IsAvailable: true,
					VoucherCode: redeem.Code,
				}
				if existedItem, ok := mapItemGift[gift.Sku]; ok && existedItem != nil {
					item.Quantity = item.Quantity + existedItem.Quantity
					item.MaxQuantity = item.MaxQuantity + existedItem.MaxQuantity
					item.VoucherCode = fmt.Sprintf("%s,%s", item.VoucherCode, existedItem.VoucherCode)
				}
				skus = append(skus, gift.Sku)
				mapItemGift[gift.Sku] = item
			}
		}
	}
	for _, item := range mapItemGift {
		cart.Items = append(cart.Items, item)
	}

	var salesTypeCode string
	if cart.Source != nil {
		if *cart.Source == enum.Source.CLINIC_PORTAL {
			salesTypeCode = fmt.Sprintf("%s_%s", "clinic-portal", cart.SalesTypeCode)
		} else {
			salesTypeCode = fmt.Sprintf("%s_%s", "brand-portal", cart.SalesTypeCode)
		}
	}

	// Brand GIFT cart item
	mapBrandItemGift := map[string]*model.CartItem{}

	newBrandGifts := []model.BrandGift{}
	if cart.BrandGifts != nil {
		for i := range *cart.BrandGifts {
			brandGift := (*cart.BrandGifts)[i]

			skuData, err := client.Services.Product.GetListSku([]string{brandGift.Sku}, customer.ProvinceCode, customer.CustomerID, true, salesTypeCode, true)
			if err != nil || len(skuData) == 0 || skuData[0].SKU == nil || !utils.IsContains(skuData[0].SKU.Tags, string(enum.Tag.BRANDGIFT)) {
				continue
			}
			newBrandGifts = append(newBrandGifts, brandGift)

			item := &model.CartItem{
				RefCart:     cart.RefCartItem,
				CartID:      cart.CartID,
				CartNo:      cart.CartNo,
				Sku:         brandGift.Sku,
				ItemCode:    brandGift.ItemCode,
				Quantity:    brandGift.Quantity,
				MaxQuantity: brandGift.Quantity,
				IsAvailable: true,
				Type:        enum.ItemType.GIFT,
				Source:      *cart.Source,
			}

			key := fmt.Sprintf("%s_%s", item.Sku, item.ItemCode)
			if _, ok := mapBrandItemGift[key]; ok {
				continue
			}
			mapBrandItemGift[key] = item
			skus = append(skus, brandGift.Sku)
			cart.Items = append(cart.Items, item)
		}

		if len(newBrandGifts) != len(*cart.BrandGifts) {
			model.BrandCartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, &model.CartUpdateInfo{
				BrandGifts: &newBrandGifts,
			})
			cart.BrandGifts = &newBrandGifts
		}
	}

	if len(skus) == 0 {
		return errRes
	}

	isBrandSales := isBrandOrClinic(cart.Source)
	skuData, err := client.Services.Product.GetListSku(skus, customer.ProvinceCode, customer.CustomerID, isBrandSales, salesTypeCode, isBrandSales)
	if err != nil {
		return err
	}

	skuMap := make(map[string]*client.ProductData)
	for _, skuData := range skuData {
		if skuData.SKU != nil {
			skuMap[skuData.SKU.Code] = skuData
		}
	}

	for _, item := range cart.Items {
		if _, ok := skuMap[item.Sku]; ok && item.Type == enum.ItemType.GIFT {
			saveContentToItem(cart, customer, item, skuMap, nil, tagSettingMap, customer.CertificateMap)
			//item.SellerCode = sku.SKU.SellerCode
			//item.SellerPrice = int(sku.SKU.RetailPriceValue)
			//item.ProductID = sku.SKU.ProductID
			//item.ProductCode = sku.SKU.ProductCode
			//item.MaxQuantity = int( sku.LimitPerOrder)
			//item.Price = 0
			//item.SkuStatus = sku.SKU.Status
			//item.LotDates = sku.SKU.LotDates
			//
			//item.IsNearExpired = false
			//if sku.SKU.LotDates != nil {
			//	for _, lotDate := range *sku.SKU.LotDates {
			//		if lotDate.IsNearExpired != nil && *lotDate.IsNearExpired {
			//			item.IsNearExpired = true
			//			break
			//		}
			//	}
			//}
		}
	}
	return errRes
}

// handleProcessPriceItems processes the pricing of items in the cart for a given customer.
// It performs the following steps:
// 1. Prepares a list of SKU codes from the cart items.
// 2. Fetches product data and pricing information for the SKUs.
// 3. Retrieves SKU brand contract prices if the cart source is specified.
// 4. Waits for all concurrent operations to complete.
// 5. Updates the prices and total prices of the cart items based on the fetched pricing information.
// 6. Handles different item types such as NORMAL, DEAL, CAMPAIGN, and GIFT, applying appropriate pricing logic.
// 7. Calculates and updates the total price and fees for the cart.
//
// Parameters:
// - customer: A pointer to the Customer model containing customer information.
// - cart: A pointer to the Cart model containing cart items.
//
// Returns:
// - errRes: A pointer to an APIResponse containing any error that occurred during processing.
func handleProcessPriceItems(customer *model.Customer, cart *model.Cart) (errRes *common.APIResponse) {
	// Prepare to query
	skusCodes := []string{}
	for _, item := range cart.Items {
		skusCodes = append(skusCodes, item.Sku)
	}

	// Fetch product data
	var (
		skus     = make([]client.SKUInformation, 0)
		priceMap = make(map[string]*client.SKUPrice)
		err      *common.APIResponse

		skuBrandConfigMap = make(map[string]*int64)

		wg sync.WaitGroup
	)

	wg.Add(1)
	go sdk.Execute(func() {
		defer wg.Done()

		for _, item := range cart.Items {
			func(item *model.CartItem) {
				sdk.Execute(func() {
					if len(item.RetailPriceType) > 0 {
						skuInfo := client.SKUInformation{
							Type:                  string(item.Type),
							RetailPriceType:       item.RetailPriceType,
							Code:                  item.Sku,
							Price:                 item.Price,
							FeeCodes:              item.FeeCodes,
							IsDynamicPricingLevel: item.IsDynamicPricingLevel,
							DynamicPricingLevel:   item.DynamicPricingLevel,
							PricingStrategy:       item.PricingStrategy,
							Weight:                item.Weight * 1000,

							// combo
							UseSKUsPrice:          item.UseSKUsPrice,
							ComboDiscountType:     item.ComboDiscountType,
							ComboDiscountValue:    item.ComboDiscountValue,
							ComboMaxDiscountValue: item.ComboMaxDiscountValue,
							ChargeDealFee:         item.ChargeDealFee,
						}

						// get sub sku info
						if item.IsCombo != nil && *item.IsCombo {
							// combine
							for i := range *item.SubItems {
								subItem := (*item.SubItems)[i]
								if subItem == nil {
									continue
								}

								if len(subItem.RetailPriceType) > 0 {
									skuInfo.ComboSKUs = append(skuInfo.ComboSKUs, &client.ComboSKU{
										Type:                  string(subItem.Type),
										RetailPriceType:       subItem.RetailPriceType,
										Status:                subItem.SkuStatus,
										Code:                  subItem.Sku,
										Price:                 subItem.Price,
										IsDynamicPricingLevel: subItem.IsDynamicPricingLevel,
										DynamicPricingLevel:   subItem.DynamicPricingLevel,
										PricingStrategy:       subItem.PricingStrategy,
										FeeCodes:              subItem.FeeCodes,
										Quantity:              subItem.Quantity,
										Weight:                subItem.Weight * 1000,
									})
								}
							}
						}

						skus = append(skus, skuInfo)
						if item.SubItems != nil {
							for _, subItem := range *item.SubItems {
								if subItem == nil {
									continue
								}

								if len(subItem.RetailPriceType) > 0 {
									skuInfo := client.SKUInformation{
										Type:                  string(subItem.Type),
										RetailPriceType:       subItem.RetailPriceType,
										Code:                  subItem.Sku,
										Price:                 subItem.Price,
										FeeCodes:              subItem.FeeCodes,
										IsDynamicPricingLevel: subItem.IsDynamicPricingLevel,
										DynamicPricingLevel:   subItem.DynamicPricingLevel,
										PricingStrategy:       subItem.PricingStrategy,
										Weight:                subItem.Weight * 1000,
									}
									skus = append(skus, skuInfo)
								}
							}
						}
					}
				})
			}(item)
		}

		priceMap, err = client.Services.Pricing.GetPrice(&client.LookupSKUPriceRequest{
			CustomerLevel: customer.Level,
			ProvinceCode:  customer.ProvinceCode,
			DistrictCode:  customer.DistrictCode,
			WardCode:      customer.WardCode,
			Skus:          skus,
		})
	})

	// get sku brand contract
	if cart.Source != nil {
		wg.Add(1)
		go sdk.Execute(func() {
			defer wg.Done()

			skuBrandConfigRes, _ := client.Services.Product.GetListSkuBrandContract(skusCodes, string(*cart.Source), &cart.SalesTypeCode)
			for _, skuBrandConfig := range skuBrandConfigRes {
				skuBrandConfigMap[skuBrandConfig.SKU] = skuBrandConfig.ContractPrice
			}
		})
	}

	wg.Wait()

	if err != nil {
		return err
	}

	for _, item := range cart.Items {
		if item.SubItems != nil {
			for _, subItem := range *item.SubItems {
				if subItem == nil {
					continue
				}

				if price, ok := priceMap[fmt.Sprintf("%s_%s", subItem.Sku, subItem.Type)]; ok {
					if price.SalePrice < 0 {
						price.SalePrice = 0
					}
					subItem.Price = price.SalePrice
					if subItem.SkuContractCode != nil && len(*subItem.SkuContractCode) > 0 {
						subItem.Price = subItem.ContractPrice
					}
					subItem.TotalPrice = subItem.Price * subItem.Quantity
					subItem.Fee = price.FeesApply
				} else if subItem.Price > 0 {
					makeCartItemError(NotFoundPriceSkuInComboError, item)
					if isCartItemSelected(item) {
						errRes = NotFoundPriceSkuInComboError
					}
				}
			}
		}
		if len(item.ErrorCode) == 0 || item.ErrorCode == Err_MAX_QUANTITY {
			if price, ok := priceMap[fmt.Sprintf("%s_%s", item.Sku, item.Type)]; ok {
				if price.SalePrice < 0 { // if sale price < 0 -> reset  = 0
					price.SalePrice = 0
				}
				if item.IsCombo != nil && *item.IsCombo {
					item.Price = price.Price
				}
				if item.Type == enum.ItemType.NORMAL {
					// BRAND_PORTAL & CLINIC_PORTAL
					if cart.Source != nil && isBrandOrClinic(cart.Source) {
						if *cart.Source == enum.Source.BRAND_PORTAL {
							switch cart.SalesType {
							case "SELLER":
								item.Price = price.SalePrice
							case "BRAND_SALES":
								if configPrice, exist := skuBrandConfigMap[item.Sku]; exist {
									item.Price = int(*configPrice)
								} else {
									item.Price = price.Price
								}
							}
						}
						if *cart.Source == enum.Source.CLINIC_PORTAL {
							if configPrice, exist := skuBrandConfigMap[item.Sku]; exist {
								item.Price = int(*configPrice)
							} else {
								item.Price = price.Price
							}
						}
					} else if item.SkuContractCode != nil && len(*item.SkuContractCode) > 0 {
						item.Price = item.ContractPrice
					} else {
						item.Price = price.SalePrice
					}
					item.TotalPrice = item.Price * item.Quantity
					item.Fee = price.FeesApply
				} else if price.SalePrice <= item.DealPrice && item.Type == enum.ItemType.DEAL && (item.DealPricingType != nil && *item.DealPricingType == "ABSOLUTE") {
					// giá deal cao hơn hoặc bằng giá bán -> switch type = NORMAL
					switchCartItemType(item, enum.ItemType.DEAL, enum.ItemType.NORMAL)
					item.Type = enum.ItemType.NORMAL
					item.SalePrice = price.SalePrice
					item.Price = price.SalePrice
					if item.SkuContractCode != nil && len(*item.SkuContractCode) > 0 {
						item.Price = item.ContractPrice
					} else {
						item.Price = price.SalePrice
					}
					item.TotalPrice = item.SalePrice * item.Quantity
					item.Fee = price.FeesApply
					item.DealCode = nil
					item.DealMaxQuantityPerCustomer = 0
				} else if item.Type != enum.ItemType.GIFT && item.DealPricingType != nil && item.Type == enum.ItemType.DEAL {
					// deal
					item.SalePrice = price.SalePrice
					item.Fee = price.FeesApply
					if price.RetailPriceType == "FIXED_REVENUE" && item.ChargeDealFee == "SELLER" {
						// giá bán theo doanh thu, charge fee = seller -> bên chịu phí nhà bán hàng
						// tính giá trị giảm trên giá gốc sau đó cộng fee -> giá bán deal cuối cùng
						if *item.DealPricingType == "PERCENTAGE" {
							dealDiscountValue := 0
							if !isNilOrDefaultValue(item.DealDiscountPercent) {
								dealDiscountValue = int((float64(item.Price) * *item.DealDiscountPercent) / 100)
								if !isNilOrDefaultValue(item.DealMaxDiscountValue) && dealDiscountValue > *item.DealMaxDiscountValue {
									dealDiscountValue = *item.DealMaxDiscountValue
								}
								item.Price = item.Price - dealDiscountValue
								if price.FeesApply != nil {
									item.Price = item.Price + price.FeesApply.Total
								}
							}
						} else if *item.DealPricingType == "ABSOLUTE" {
							item.Price = item.DealPrice
							if price.FeesApply != nil {
								item.Price = item.Price + price.FeesApply.Total
							}
						}
						item.Price = int(utils.Ceil(int64(item.Price)))
						item.TotalPrice = item.Price * item.Quantity
						item.ChargeDealFeeValue = item.SalePrice - item.Price
					} else {
						// giá bán cố định
						// tính giá trị giảm trên giá bán -> giá bán deal cuối cùng
						item.Price = item.DealPrice
						if *item.DealPricingType == "PERCENTAGE" {
							if !isNilOrDefaultValue(item.DealDiscountPercent) {
								dealDiscountValue := int((float64(item.SalePrice) * *item.DealDiscountPercent) / 100)
								if !isNilOrDefaultValue(item.DealMaxDiscountValue) && dealDiscountValue > *item.DealMaxDiscountValue {
									dealDiscountValue = *item.DealMaxDiscountValue
								}
								item.Price = item.SalePrice - dealDiscountValue
							}
						}
						item.Price = int(utils.Ceil(int64(item.Price)))
						item.TotalPrice = item.Price * item.Quantity
						item.ChargeDealFeeValue = item.SalePrice - item.Price
					}
				} else if item.Type == enum.ItemType.CAMPAIGN {
					// giá bán theo doanh thu, charge fee = seller -> bên chịu phí nhà bán hàng
					// tính giá trị giảm trên giá gốc sau đó cộng fee -> giá bán campagin cuối cùng
					item.SalePrice = price.SalePrice
					item.Fee = price.FeesApply
					if price.RetailPriceType == "FIXED_REVENUE" && (item.ChargeCampaignFee == "SELLER" || item.ChargeCampaignFee == "") {
						if item.CampaignPricingType == "PERCENTAGE" {
							discountValue := 0
							if !isNilOrDefaultValue(item.CampaignDiscountPercent) {
								discountValue = int((float64(item.Price) * float64(*item.CampaignDiscountPercent)) / 100)
								if !isNilOrDefaultValue(item.CampaignMaxDiscountValue) && discountValue > int(*item.CampaignMaxDiscountValue) {
									discountValue = int(*item.CampaignMaxDiscountValue)
								}
								item.Price = item.Price - discountValue
								if price.FeesApply != nil {
									item.Price = item.Price + price.FeesApply.Total
								}
							}
						} else if item.CampaignPricingType == "PRICE" {
							item.Price = item.CampaignPrice
							if price.FeesApply != nil {
								item.Price = item.Price + price.FeesApply.Total
							}
						} else if item.CampaignPricingType == "ABSOLUTE" {
							if item.CampaignDiscountValue != nil {
								item.Price = item.Price - int(*item.CampaignDiscountValue)
							}
							if price.FeesApply != nil {
								item.Price = item.Price + price.FeesApply.Total
							}
							if item.Price < 0 {
								item.Price = 0
							}
						}
					} else {
						/*giá bán cố định
						**tính giá trị giảm trên giá bán -> giá bán campaign cuối cùng
						 */
						if item.CampaignPricingType == "PRICE" {
							item.Price = item.CampaignPrice
						} else if item.CampaignPricingType == "ABSOLUTE" {
							if item.CampaignDiscountValue != nil {
								item.Price = item.SalePrice - int(*item.CampaignDiscountValue)
							}
						} else if item.CampaignPricingType == "PERCENTAGE" {
							if !isNilOrDefaultValue(item.CampaignDiscountPercent) {
								discountValue := int((float64(item.SalePrice) * float64(*item.CampaignDiscountPercent)) / 100)
								if !isNilOrDefaultValue(item.CampaignMaxDiscountValue) && discountValue > int(*item.CampaignMaxDiscountValue) {
									discountValue = int(*item.CampaignMaxDiscountValue)
								}
								item.Price = item.SalePrice - discountValue
							}
						}
					}
					subsidyPercentage := int64(0)
					subsidyPrice := int64(0)
					isInteralSeller := item.SellerClass == "INTERNAL"
					if item.SubsidyType == "PERCENTAGE" && item.SubsidyValue > 0 {
						subsidyPercentage = item.SubsidyValue
						if isInteralSeller {
							subsidyPrice = subsidyPercentage * (int64(price.Price) + int64(price.FeesApply.Total)) / 100
						} else {
							subsidyPrice = subsidyPercentage * int64(price.Price) / 100
						}
						item.SubsidyPrice = subsidyPrice
					}
					item.Price = int(utils.Ceil(int64(item.Price - int(subsidyPrice))))
					item.TotalPrice = item.Price * item.Quantity
					item.ChargeCampaignFeeValue = item.SalePrice - item.Price - int(subsidyPrice)
				} else if item.Type == enum.ItemType.GIFT {
					item.Price = 0
					item.SalePrice = 0
					item.TotalPrice = 0
				} else {
					makeCartItemError(NotFoundPriceSkuError, item)
					if !isCartItemSelected(item) {
						errRes = NotFoundPriceSkuError
					}
				}
			}
			if item.CurPrice != 0 && item.CurPrice != item.Price {
				notiCartItemChangedPrice(cart, customer, item)
				//makeCartItemError(ChangePriceError, item)
				makeCartItemWarning(ChangePriceError, customer, item)
			}
			//item.CurPrice = item.Price
		}
		if isCartItemSelected(item) {
			if item.Price <= 0 && item.Type != enum.ItemType.GIFT {
				makeCartItemError(PriceZeroError, item)
				errRes = PriceZeroError
			}
			cart.Price = cart.Price + item.TotalPrice
			cart.TotalPrice = cart.Price
			if item.Fee != nil {
				cart.TotalFee = cart.TotalFee + item.Fee.Total
			}
		}
		cart.TotalPriceAllItem = cart.TotalPriceAllItem + item.TotalPrice
	}

	return errRes
}

func handleProcessBuySku(cart *model.Cart) *common.APIResponse {
	items := make([]*model.CartItem, 0)
	mapSku := make(map[string]int)
	for _, item := range cart.Items {
		if !isCartItemSelected(item) {
			continue
		}
		if item.SkuStatus != nil && *item.SkuStatus == enum.SkuStatus.LIMIT &&
			item.SkuStatusData != nil && item.SkuStatusData.Quantity != nil {
			maxQuantity := *item.SkuStatusData.Quantity
			res := model.SkuApplyResultDB.UpdateOneWithOption(
				bson.M{
					"sku":       item.Sku,
					"item_code": item.ItemCode,
					"$expr": bson.M{
						"$lte": []interface{}{
							"$quantity", maxQuantity - item.Quantity,
						},
					},
				},
				bson.M{
					"$inc": bson.M{"quantity": item.Quantity},
				},
			)

			if res.Status != common.APIStatus.Ok {
				for k, v := range mapSku {
					rollBack := model.SkuApplyResultDB.UpdateOneWithOption(
						bson.M{
							"item_code": k,
						},
						bson.M{
							"$inc": bson.M{"quantity": -v},
						},
					)
					if rollBack.Status != common.APIStatus.Ok {
						return rollBack
					}
				}
				errRes := SkuMaxQuantityError
				errRes.Data = []*model.CartItem{item}
				return errRes
			}
			mapSku[item.ItemCode] = item.Quantity
			items = append(items, item)
		}
	}
	go func() {
		updateSkuQuantity(items)
	}()
	return nil
}

func updateSkuQuantity(items []*model.CartItem) {
	for _, item := range items {
		qResult := model.SkuApplyResultDB.QueryOne(&model.SkuApplyResult{SKU: item.Sku, ItemCode: item.ItemCode})
		if qResult.Status == common.APIStatus.Ok {
			skuApplyResult := qResult.Data.([]*model.SkuApplyResult)[0]
			if skuApplyResult.Quantity != nil && item.SkuStatusData != nil && item.SkuStatusData.Quantity != nil {
				statusData := item.SkuStatusData
				statusData.Quantity = utils.ParseIntToPointer(*item.SkuStatusData.Quantity - *skuApplyResult.Quantity)
				_ = client.Services.Product.UpdateSkuQuantity(&client.UpdateSkuQuantityRequest{
					ItemCode:   item.ItemCode,
					StatusData: statusData,
				})
			}
		}
	}
}

func handleProcessBuyDeal(cart *model.Cart) *common.APIResponse {
	items := make([]*model.CartItem, 0)
	mapSku := make(map[string]*model.CartItem)
	for _, item := range cart.Items {
		if !isCartItemSelected(item) {
			continue
		}
		if (item.Type == enum.ItemType.DEAL && item.DealCode != nil) || (item.Type == enum.ItemType.CAMPAIGN && item.CampaignCode != "") {
			keyInc := item.CampaignCode
			if item.Type == enum.ItemType.DEAL && item.DealCode != nil {
				keyInc = *item.DealCode
			}
			res := model.DealApplyResultDB.UpdateOneWithOption(
				bson.M{
					"deal_code": keyInc,
					"sku":       item.Sku,
					"$expr": bson.M{
						"$lte": []interface{}{
							"$quantity", item.TotalDealQuantity - item.Quantity,
						},
					},
				},
				bson.M{
					"$inc": bson.M{"quantity": item.Quantity},
				},
			)
			if res.Status != common.APIStatus.Ok {
				for k, v := range mapSku {
					rollBack := model.DealApplyResultDB.UpdateOneWithOption(
						bson.M{
							"deal_code": k,
							"sku":       v.Sku,
						},
						bson.M{
							"$inc": bson.M{"quantity": -v.Quantity},
						},
					)
					if rollBack.Status != common.APIStatus.Ok {
						return rollBack
					}
				}
				errRes := DealMaxQuantityError
				errRes.Data = []*model.CartItem{item}
				return errRes
			}
			mapSku[keyInc] = item
			items = append(items, item)
		}
	}
	go func() {
		updateDealQuantity(items, cart)
	}()
	return nil
}

func updateDealQuantity(items []*model.CartItem, cart *model.Cart) {
	for _, item := range items {
		query := &model.DealApplyResult{DealCode: item.DealCode, SKU: item.Sku}
		if item.Type == enum.ItemType.CAMPAIGN {
			query.DealCode = &item.CampaignCode
			query.SKU = item.Sku
		}
		qResult := model.DealApplyResultDB.QueryOne(query)
		if qResult.Status == common.APIStatus.Ok {
			dealApplyResult := qResult.Data.([]*model.DealApplyResult)[0]
			if item.DealCode != nil && dealApplyResult.Quantity != nil {
				if item.Type == enum.ItemType.DEAL {
					_ = client.Services.Product.UpdateDealQuantity(&client.UpdateDealQuantityRequest{
						Code:     *item.DealCode,
						Quantity: *dealApplyResult.Quantity,
						Action:   "CHECKOUT_CART",
						OrderID:  cart.CartID,
					})
				}
				if item.Type == enum.ItemType.CAMPAIGN {
					_ = client.Services.Promotion.UpdateSoldQuantityCampaign(&client.UpdateSoldQuantityCampaign{
						OrderId:          cart.CartID,
						CustomerId:       cart.CustomerID,
						CampaignCode:     item.CampaignCode,
						Sku:              item.Sku,
						Quantity:         int64(*dealApplyResult.Quantity),
						CheckoutQuantity: int64(item.Quantity),
					})
				}
			}
		}
		if item.DealMaxQuantityPerCustomer > 0 {
			dealCode := item.CampaignCode
			key := item.CampaignProductCode
			if item.DealCode != nil && *item.DealCode != "" {
				dealCode = *item.DealCode
				key = dealCode
			}
			quantityUpdate := item.Quantity
			if cart.DealLimitMap != nil && cart.DealLimitMap[key] != 0 {
				quantityUpdate = quantityUpdate + cart.DealLimitMap[key]
			}
			res := model.DealLimitDB.Upsert(model.DealLimit{
				Key:        key,
				CustomerID: cart.CustomerID,
			}, model.DealLimit{
				DealCode:               dealCode,
				DealType:               string(item.Type),
				Sku:                    item.Sku,
				MaxQuantityPerCustomer: item.DealMaxQuantityPerCustomer,
				Quantity:               quantityUpdate,
			})
			if res.Status == common.APIStatus.Ok {
				warmupDealLimitCache(res.Data.([]*model.DealLimit)[0], cart.CustomerID, key)
			}
		}
	}
}

func handleCartItemToOrderItem(cartItems []*model.CartItem, cart *model.Cart, order *model.Order) ([]*model.OrderItem, bool, bool, *int, *int) {
	now := time.Now()
	if order.CreatedTime != nil {
		now = *order.CreatedTime
	}
	orderItems := make([]*model.OrderItem, 0)
	hasDeal := false
	hasCampaign := false
	totalQuantity := utils.ParseIntToPointer(0)
	totalItem := utils.ParseIntToPointer(0)
	//totalDiscountFromPayment := -cart.PaymentMethodFee
	//lastItemIndex := 0
	//numberOrderItemGift := 0
	for _, item := range cartItems {
		if !isCartItemSelected(item) {
			continue
		}
		if item.Type == enum.ItemType.DEAL || item.Type == enum.ItemType.COMBO {
			hasDeal = true
		}
		if item.Type == enum.ItemType.CAMPAIGN {
			hasCampaign = true
		}

		orderItem := &model.OrderItem{
			OrderID:                item.CartID,
			OrderCode:              item.CartNo,
			Sku:                    item.Sku,
			ItemCode:               item.ItemCode,
			Quantity:               item.Quantity,
			MaxQuantity:            item.MaxQuantity,
			IsImportant:            item.IsImportant,
			Type:                   item.Type,
			DealCode:               item.DealCode,
			SellerCode:             item.SellerCode,
			Price:                  item.Price,
			TotalPrice:             item.TotalPrice,
			Fee:                    item.Fee,
			SellerPrice:            item.SellerPrice,
			TotalSellerPrice:       item.Quantity * item.SellerPrice,
			SellerRevenue:          item.SellerRevenue,
			TotalSellerRevenue:     item.Quantity * item.SellerRevenue,
			ProductCode:            item.ProductCode,
			ProductID:              item.ProductID,
			VAT:                    item.VAT,
			NoneVat:                item.NoneVAT,
			Skus:                   item.Skus,
			SkuStatus:              item.SkuStatus,
			SkuStatusData:          item.SkuStatusData,
			SkuPriceType:           item.SkuPriceType,
			ChargeDealFeeValue:     item.ChargeDealFeeValue,
			ChargeDealFee:          item.ChargeDealFee,
			DealPricingType:        item.DealPricingType,
			DealVendorPrice:        item.DealVendorPrice,
			Tags:                   item.Tags,
			SkuVersion:             item.SkuVersion,
			Source:                 item.Source,
			SkuLevel:               item.SkuLevel,
			CampaignCode:           item.CampaignCode,
			CampaignPricingType:    item.CampaignPricingType,
			ChargeCampaignFeeValue: item.ChargeCampaignFeeValue,
			CreatedTime:            &now,
			LastUpdatedTime:        &now,
			Point:                  item.Point,
			PointMultiplier:        item.PointMultiplier,
			SkuContractCode:        item.SkuContractCode,
			SkuContractDetailCode:  item.SkuContractDetailCode,
			IsSkuLimitExisted:      item.IsSkuLimitExisted,
			SkuLimitQuantity:       item.SkuLimitQuantity,
			CustomerID:             cart.CustomerID,
			CartItemCreatedTime:    item.CreatedTime,
			Page:                   item.Page,
			SearchKey:              item.SearchKey,
			VoucherCode:            item.VoucherCode,
			LotDates:               item.LotDates,
			IsNearExpired:          item.IsNearExpired,
			EventSource:            item.EventSource,
			EventScreen:            item.EventScreen,
			BlockCode:              item.BlockCode,
			DiscountDetail:         item.DiscountDetail,
			DealInfo:               &item.DealInfo,
			CampaignInfo:           &item.CampaignInfo,
			ContractInfo:           &item.ContractInfo,

			SubsidyPrice: item.SubsidyPrice,

			Unit:      item.Unit,    // field new for OMS system
			SkuUnit:   item.SkuUnit, // field new for OMS system
			StoreCode: item.StoreCode,
			PriceAfterDiscount: func() *int {
				price := 0
				if item.Price == 0 {
					return &price
				}
				if item.DiscountDetail != nil {
					price = item.Price - item.DiscountDetail.TotalDiscount/item.Quantity
				}
				if price < 0 {
					price = 0
				}
				return &price
			}(),
		}

		if item.DealCode != nil {
			orderItem.DealOwner = item.Owner
		}

		if item.LotDates != nil {
			for _, lotDate := range *item.LotDates {
				if lotDate.IsNearExpired != nil && *lotDate.IsNearExpired {
					if lotDate.ExpiredDate != nil && *lotDate.ExpiredDate != "" {
						t, err := time.Parse("2006-01-02", *lotDate.ExpiredDate)
						if err != nil {
							continue
						}
						orderItem.SkuExpiredDate = &t
					}
					break
				}
			}
		}
		if item.SubItems != nil {
			subItems, _, _, _, _ := handleCartItemToOrderItem(*item.SubItems, cart, order)
			orderItem.SubItems = &subItems
		}
		if orderItem.DiscountDetail != nil {
			voucherAmountBySKUUnit, actualInvoiceVoucherBySKU := CalculateVoucherAmountBySKUUnit(orderItem.DiscountDetail, orderItem.Quantity, orderItem.SellerCode, 1)
			orderItem.VoucherAmountBySKUUnit = voucherAmountBySKUUnit
			orderItem.ActualInvoiceVoucherAmountBySKU = int64(actualInvoiceVoucherBySKU)
		}

		totalQuantity = utils.ParseIntToPointer(*totalQuantity + item.Quantity)
		totalItem = utils.ParseIntToPointer(*totalItem + 1)
		//lastItemIndex = len(orderItems) - numberOrderItemGift
		orderItems = append(orderItems, orderItem)
	}
	//if totalDiscountFromPayment != 0 && lastItemIndex >= 0 && lastItemIndex < len(orderItems) {
	//	if orderItems[lastItemIndex].PriceAfterDiscount == nil {
	//		orderItems[lastItemIndex].PriceAfterDiscount = utils.ParseIntToPointer(0)
	//	}
	//	orderItems[lastItemIndex].PriceAfterDiscount = utils.ParseIntToPointer(*orderItems[lastItemIndex].PriceAfterDiscount + int(totalDiscountFromPayment))
	//}
	return orderItems, hasDeal, hasCampaign, totalQuantity, totalItem
}

func CalculateVoucherAmountBySKUUnit(discountDetail *model.DiscountDetail, quantity int, sellerCode string, invoiceQty int) (int, int) {
	var (
		voucherAmountBySKUUnit    int
		actualInvoiceVoucherBySKU int
	)
	if discountDetail == nil || len(discountDetail.VoucherDetails) == 0 {
		return voucherAmountBySKUUnit, actualInvoiceVoucherBySKU
	}

	for _, voucherDetail := range discountDetail.VoucherDetails {
		if voucherDetail == nil || !voucherDetail.IsApply {
			continue
		}

		if utils.IsContains(voucherDetail.SellerCodes, sellerCode) {
			voucherAmountBySKUUnit += voucherDetail.DiscountValue
		}
	}

	voucherAmountBySKUUnit = voucherAmountBySKUUnit / quantity
	actualInvoiceVoucherBySKU = voucherAmountBySKUUnit * invoiceQty
	return voucherAmountBySKUUnit, actualInvoiceVoucherBySKU
}

func handleDeliveryDate(cart *model.Cart, isCheckout bool) *common.APIResponse {
	// set default delivery time
	//addTime:
	t := time.Now()
	if len(cart.CustomerProvinceCode) == 0 {
		return ProvinceCodeError
	}
	region, err := client.Services.Location.GetRegionGetDeliveryTime(cart.CustomerProvinceCode, cart.CustomerDistrictCode)
	if err != nil {
		// fallback call older api
		regionTemp, err := client.Services.Location.GetRegion(cart.CustomerProvinceCode)
		if err != nil {
			// province don't belong any region
			region = &client.RegionDeliveryTime{
				EstThuocSi: 7,
			}
		} else {
			region = &client.RegionDeliveryTime{
				EstLogistic: regionTemp.EstLogistic,
				EstThuocSi:  regionTemp.EstThuocSi,
				RegionCode:  regionTemp.Code,
			}
		}
	}
	cart.RegionCode = region.RegionCode

	if cart.DeliveryDate != nil {

		//dayOfWeek := cart.DeliveryDate.Weekday()
		//switch dayOfWeek {
		//case time.Saturday:
		//	cart.DeliveryDate = utils.ParseTimeToPointer(cart.DeliveryDate.AddDate(0,0,2))
		//case time.Sunday:
		//	cart.DeliveryDate = utils.ParseTimeToPointer(cart.DeliveryDate.AddDate(0,0,1))
		//}
		return nil
	}
	if cart.DeliveryMethod == "DELIVERY_PLATFORM_NORMAL" {
		if deliveryDate := getDeliveryTimeFromWarehouse(cart.CustomerProvinceCode, cart.CustomerDistrictCode, cart.CustomerWardCode, cart.CustomerID); deliveryDate != nil {
			cart.DeliveryDate = deliveryDate
			return nil
		}
	}

	cart.DeliveryDate = utils.ParseTimeToPointer(t.Add(7 * 24 * time.Hour))

	deliveryTime := int((region.EstLogistic + region.EstThuocSi) * 24 * 60) // minute
	if deliveryTime > 0 {
		cart.DeliveryDate = utils.ParseTimeToPointer(t.Add(time.Duration(deliveryTime) * time.Minute))
	}

	if cart.TagMap != nil {
		for _, tagSetting := range cart.TagMap {
			if tagSetting.ProvinceAppliedValue != nil {
				cart.DeliveryDate = utils.ParseTimeToPointer(cart.DeliveryDate.Add(time.Duration(*tagSetting.ProvinceAppliedValue) * time.Hour))
			}
		}
	}
	//goto addTime
	return nil
}

func switchCartItemType(cartItem *model.CartItem, curType, newType enum.ItemTypeValue) *common.APIResponse {
	return model.CartItemDB.UpdateOne(
		model.CartItem{
			ID:   cartItem.ID,
			Type: curType,
		},
		model.CartItem{Type: newType},
	)
}

func completeCheckoutCartProcess(customer *model.Customer, cart *model.Cart, order *model.Order, isBrandSales bool) {

	handleCountExtraFeeHistory(customer, cart)

	if cart.SystemDisplay == "" {
		cart.SystemDisplay = "BUYMED"
	}

	if isBrandSales {
		cart.ID = primitive.NilObjectID
		_ = model.BrandCartDeletedDB.Create(cart)
	} else {
		cart.ID = primitive.NilObjectID
		_ = model.CartDeletedDB.Create(cart)
	}
	newDelCartItem := make([]*model.CartItem, 0)
	for _, item := range cart.Items {
		if item.Type != enum.ItemType.GIFT {
			newDelCartItem = append(newDelCartItem, item)
		}
	}
	cart.Items = newDelCartItem
	if isBrandSales {
		model.BrandCartItemDeletedDB.CreateMany(cart.Items)
	} else {
		// loop thru cart.Items, set ID = nil
		for _, item := range cart.Items {
			item.ID = primitive.NilObjectID
		}
		model.CartItemDeletedDB.CreateMany(cart.Items)
	}

	_ = client.Services.Customer.UpdatePointAndOrderCount(&client.UpdatePointAndOrderCountRequest{
		OrderCount: utils.ParseIntToPointer(1),
		CustomerID: int(customer.CustomerID),
		OrderID:    cart.CartID,
	})

	_ = client.Services.Notification.CreateNotification(&client.Notification{
		Username:     customer.Username,
		UserID:       customer.AccountID,
		ReceiverType: utils.ParseStringToPointer(enum.AccountType.CUSTOMER),
		Topic:        "ANNOUNCEMENT",
		Title:        fmt.Sprintf("Chúc mừng! Bạn vừa đặt hàng thành công, mã đơn hàng #%d!", cart.CartID),
		Link:         fmt.Sprintf("/my-order/%d", cart.CartID),

		Tags: []enum.NotificationTagEnum{enum.NotificationTag.ORDER, enum.NotificationTag.IMPORTANT},
	})

	// send order-created ZNS to customer
	//tr := true
	//msg := client.Message{
	//	Topic:       "CHECKOUT",
	//	Source:      "marketplace/order",
	//	Receiver:    customer.Phone,
	//	UseTemplate: &tr,
	//	Dictionary: map[string]string{
	//		"customer_name": customer.Name,
	//		"order_id":      strconv.FormatInt(order.OrderID, 10),
	//	},
	//}
	//_, _ = client.Services.Message.SendMessage(&msg)

	// convert customerID to string type
	sendNotifyOrderCreated(order.OrderID, customer.CustomerID)

	// createOrderSeller(order.OrderID, customer)
	triggerCreateOrderSeller(order.OrderID)
	filterSellerFirstOrderSku(order.Items)

	if cart.TotalItem != nil && cart.TotalItemSelected != nil && *cart.TotalItem != *cart.TotalItemSelected {
		newItems := make([]*model.CartItem, 0)
		cart.CartID, cart.CartNo = model.GetOrderID()
		emtStrs := make([]*string, 0)
		cart.RedeemCode = &emtStrs
		cart.RedeemApplyResult = make([]*model.PromoApplyResult, 0)
		cart.FlattenLocation = FlatLocationCart(*cart)
		for _, item := range cart.Items {
			if !isCartItemSelected(item) {
				item.CartNo, item.CartID = cart.CartNo, cart.CartID
				newItems = append(newItems, item)
			}
		}
		resetCartPrice(cart)
		newCartRes := &common.APIResponse{}
		if isBrandSales {
			newCartRes = model.BrandCartDB.Create(cart)
		} else {
			// cart to cart cache
			_ = model.CartCacheDB.Create(cart)
			newCartRes = model.CartDB.Create(cart)
		}

		if newCartRes.Status == common.APIStatus.Ok {
			if isBrandSales {
				model.BrandCartItemDB.CreateMany(newItems)
			} else {
				cacheItems := make([]*model.CartItemCache, 0, len(newItems))
				cartItemsByte, _ := json.Marshal(newItems)
				_ = json.Unmarshal(cartItemsByte, &cacheItems)
				model.CartItemCacheDB.CreateMany(cacheItems)
				model.CartItemDB.CreateMany(newItems)
			}
		}
	}
	// push job to compute dynamic level of ordered SKU
	if order.Items != nil && len(order.Items) > 0 {
		for _, item := range order.Items {
			model.OrderedSKUJob.Push(client.Sku{
				Code: item.Sku,
			}, &job.JobItemMetadata{
				Topic:     "default",
				UniqueKey: item.Sku,
			})
		}
	}

	// call if payment method is credit - complete checkout
	if order.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
		model.ProcessPaymentMethodCreditJob.Push(processPaymentMethodCredit{
			CheckoutOrderInput: &client.CheckoutOrderInput{
				CustomerID:  order.CustomerID,
				OrderAmount: int64(*order.TotalPrice),
				IsVerify:    false,
				OrderCode:   order.OrderCode,
				OrderID:     order.OrderID,
				Action:      "checkout",
				RequestID:   time.Now().UnixNano(),
			},
		}, &job.JobItemMetadata{
			Topic: "checkout",
			Keys:  []string{fmt.Sprintf("%d", order.OrderID)},
		})
	}
	client.Services.Promotion.ScoreMission("Checkout cart", order)
	updateWarehouseInfoOfPlacedOrder(order)
	resolveCheckoutError(cart)

}

func completeRevertCartProcess(acc *model.Account, customer *model.Customer, order *model.Order) {

	// rollback extra fee history used if it was used
	if order.UseFreeDelivery {
		qExtraFeeHistory := model.UserExtraFeeHistoryDB.QueryOne(&model.UserExtraFeeHistory{CustomerID: customer.CustomerID})

		if qExtraFeeHistory.Status == common.APIStatus.Ok {
			extraFeeHistory := qExtraFeeHistory.Data.([]*model.UserExtraFeeHistory)[0]

			extraHistoryMap := make(map[string]model.ExtraFeeUse)

			//clone extraFeeHistory map
			for k, v := range extraFeeHistory.ExtraFeeUse {
				extraHistoryMap[k] = v
			}

			extraFeeHistoryByLevel, extraFeeHistoryByLevelOk := extraFeeHistory.ExtraFeeUse[customer.Level]
			if extraFeeHistoryByLevelOk {
				used := extraFeeHistoryByLevel.Used
				if used > 0 {
					extraHistoryMap[customer.Level] = model.ExtraFeeUse{
						Used: used - 1,
						Max:  extraFeeHistoryByLevel.Max,
					}
					//prepare data update
					historyUpdated := model.UserExtraFeeHistory{
						CustomerID:  customer.CustomerID,
						ExtraFeeUse: extraHistoryMap,
					}
					model.UserExtraFeeHistoryDB.UpdateOne(&model.UserExtraFeeHistory{CustomerID: customer.CustomerID}, historyUpdated)
				}
			}
		}
	}

	// delete order & order item
	delOrderRes := model.OrderDB.Delete(model.Order{OrderID: order.OrderID})
	if delOrderRes.Status == common.APIStatus.Ok {

		orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "completeRevertCartProcess")
		if orderItemPartitionDB != nil {
			_ = orderItemPartitionDB.Delete(model.OrderItem{OrderID: order.OrderID})
		}

		orderDetailRes := model.OrderDetailDB.QueryOne(model.OrderDetail{
			OrderID: order.OrderID,
		})
		if orderDetailRes.Status == common.APIStatus.Ok {
			orderDetail := orderDetailRes.Data.([]*model.OrderDetail)[0]
			_ = model.OrderDetailDB.Delete(model.OrderDetail{OrderID: order.OrderID})
			_ = model.OrderDetailDeletedDB.Create(orderDetail)
		}
	}
	_ = model.OrderDeletedDB.Create(order)
	_ = model.OrderItemDeletedDB.CreateMany(order.Items)

	query := &model.Order{CustomerID: customer.CustomerID}
	mSort := primitive.M{"created_time": -1}
	var lastOrderAt *time.Time

	query.ComplexQuery = []*bson.M{
		{
			"order_id": &bson.M{
				"$ne": order.OrderID,
			},
		},
	}

	orderData := model.OrderDB.Query(query, 0, 1, &mSort)
	if orderData.Status == common.APIStatus.Ok {
		lastOrderAt = orderData.Data.([]*model.Order)[0].CreatedTime
	}

	_ = client.Services.Customer.UpdatePointAndOrderCount(&client.UpdatePointAndOrderCountRequest{
		OrderCount:  utils.ParseIntToPointer(-1),
		CustomerID:  int(customer.CustomerID),
		OrderID:     order.OrderID,
		LastOrderAt: lastOrderAt,
	})
	if order.RedeemCode != nil && len(*order.RedeemCode) > 0 {
		redeemCodes := *order.RedeemCode
		_ = client.Services.Promotion.RefundVoucher(&client.UseVoucherRequest{
			VoucherCodes: redeemCodes,
			AccountID:    acc.AccountID,
			OrderID:      order.OrderID,
			ApplyVoucherCount: func() map[string]int {
				m := make(map[string]int)
				for _, code := range order.RedeemApplyResult {
					m[code.Code] = code.NumberOfAutoApply
				}
				return m
			}(),
		})
	}
	_ = client.Services.Notification.CreateNotification(&client.Notification{
		Username:     customer.Username,
		UserID:       customer.AccountID,
		ReceiverType: utils.ParseStringToPointer(acc.Type),
		Topic:        "ANNOUNCEMENT",
		Title:        fmt.Sprintf("Bạn vừa hủy thành công đơn hàng, mã đơn hàng #%d!", order.OrderID),
		Link:         "/cart",

		Tags: []enum.NotificationTagEnum{enum.NotificationTag.ORDER, enum.NotificationTag.IMPORTANT},
	})
	deleteOrderSeller(order.OrderID)
	returnQuantitySku(order)

	if order.Source != nil && *order.Source == enum.Source.CLINIC_PORTAL {
		updateBrandSkuLimitHistory(order)
	} else {
		updateSkuLimitHistory(order)
	}

	// call if payment method is credit
	if order.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
		model.ProcessPaymentMethodCreditJob.Push(processPaymentMethodCredit{
			RefundOrderInput: &client.RefundOrderInput{
				CustomerID: order.CustomerID,
				OrderID:    order.OrderID,
				Type:       enum.RefundOrderInputType.EDIT_ORDER,
				RequestID:  time.Now().UnixNano(),
			},
		}, &job.JobItemMetadata{
			Topic: "refund",
			Keys:  []string{fmt.Sprintf("%d", order.OrderID)},
		})
	}
}

func returnQuantitySku(order *model.Order) {
	for _, item := range order.Items {
		if item.Type == enum.ItemType.NORMAL && *item.SkuStatus == enum.SkuStatus.LIMIT {
			_ = client.Services.Product.UpdateSkuQuantity(&client.UpdateSkuQuantityRequest{
				ItemCode:      item.ItemCode,
				IncreQuantity: &item.Quantity,
				Status:        item.SkuStatus,
			})
		}
	}
}

func updateSkuLimitHistory(order *model.Order) {
	now := time.Now()
	for _, item := range order.Items {
		if item.IsSkuLimitExisted {
			_ = model.SyncSkuLimitHistoryJob.Push(syncSkuLimitData{
				CustomerID: item.CustomerID,
				Sku:        item.Sku,
				ItemCode:   item.ItemCode,
				Date:       &now,
			}, &job.JobItemMetadata{
				Topic: "default",
			})
		}
	}
}

func updateBrandSkuLimitHistory(order *model.Order) {
	now := time.Now().In(model.VNTimeZone)

	for _, item := range order.Items {
		if item.Type == enum.ItemType.GIFT {
			continue
		}

		// query brand sku limit history
		qLog := model.BrandSkuLimitHistoryDB.Query(
			model.BrandSkuLimitHistory{
				CustomerID: order.CustomerID,
				Sku:        item.Sku,
				Version:    now.Format("2006-01"),
			},
			0, 0, nil)
		if qLog.Status != common.APIStatus.Ok {
			continue
		}

		log := qLog.Data.([]*model.BrandSkuLimitHistory)[0]
		if log.Quantity == nil || *log.Quantity == 0 {
			continue
		}

		qty := *log.Quantity - item.Quantity
		if qty < 0 {
			qty = 0
		}
		model.BrandSkuLimitHistoryDB.UpdateOne(
			&model.BrandSkuLimitHistory{
				Sku:        item.Sku,
				CustomerID: item.CustomerID,
				Version:    now.Format("2006-01"),
			}, model.BrandSkuLimitHistory{
				Quantity: &qty,
			},
		)
	}
}

func checkDealValid(deal *client.Deal) *common.APIResponse {
	t := time.Now()
	if deal.StartTime.After(t) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Sản phẩm khuyến mãi %s này chưa được áp dụng", strings.ToLower(deal.Name)),
			ErrorCode: "INVALID_START_TIME",
		}
	}

	if deal.EndTime.Before(t) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Sản phẩm khuyến mãi %s đã hết hạn", strings.ToLower(deal.Name)),
			ErrorCode: "INVALID_END_TIME",
		}
	}

	if *deal.Status != enum.DealStatus.ACTIVE {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Sản phẩm khuyến mãi %s hiện đang tắt", strings.ToLower(deal.Name)),
			ErrorCode: "DEAL_INACTIVE",
		}
	}
	return nil
}

func makeCartItemError(err *common.APIResponse, item *model.CartItem) {
	item.ErrorCode = err.ErrorCode
	item.ErrorMessage = err.Message
}

func makeCartItemWarning(err *common.APIResponse, customer *model.Customer, item *model.CartItem) {
	item.WarningCode = err.ErrorCode
	item.WarningMessage = err.Message
	if item.WarningCode == ChangePriceError.ErrorCode {
		if item.SkuContractCode != nil && *item.SkuContractCode != "" {
			item.WarningMessage = ""
			item.WarningCode = ""
			return
		}
	}
	if item.Price > item.CurPrice && item.WarningCode == ChangePriceError.ErrorCode {
		if item.OldType != "" && (item.OldType == enum.ItemType.DEAL || item.OldType == enum.ItemType.CAMPAIGN) && item.Type == enum.ItemType.NORMAL {
			item.WarningMessage = "Sản phẩm đã hết khuyến mãi"
		}
		if item.OldLevel != "" && item.OldLevel != customer.Level {
			item.WarningMessage = "Cấp bậc của bạn đã thay đổi"
		}
	}
}

func makeCartError(err *common.APIResponse, cart *model.Cart) {
	cart.ErrorCode = err.ErrorCode
	cart.ErrorMessage = err.Message
}

func isCartItemPriceChange(cart *model.Cart) bool {
	for _, item := range cart.Items {
		if item.CurPrice != item.Price {
			return true
		}
	}
	return false
}

func GetCartList(query *model.Cart, skuCode string, offset, limit int64, getTotal, hasVoucher bool) *common.APIResponse {
	if len(skuCode) > 0 {
		qResult := model.CartItemDB.Query(&model.CartItem{Sku: skuCode}, 0, 0, nil)
		if qResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "NOT_FOUND",
				Message:   "Không tìm thấy giỏ hàng phù hợp",
			}
		}
		cartIds := make([]int64, 0)
		for _, item := range qResult.Data.([]*model.CartItem) {
			cartIds = append(cartIds, item.CartID)
		}
		if len(cartIds) == 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "NOT_FOUND",
				Message:   "Không tìm thấy giỏ hàng phù hợp",
			}
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"cart_id": bson.M{
				"$in": cartIds,
			},
		})
	}
	sortFields := &bson.M{
		"last_action_time": -1,
	}

	if query.PriceFrom != nil || query.PriceTo != nil {
		if query.PriceFrom == nil {
			t := 0
			query.PriceFrom = &t
		} else if query.PriceTo == nil {
			t := 10000000000 // 10 billions is too much
			query.PriceTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"total_price": bson.M{
				"$gte": query.PriceFrom,
				"$lte": query.PriceTo,
			},
		})
	}

	if query.TotalQuantityFrom != nil || query.TotalQuantityTo != nil {
		if query.TotalQuantityFrom == nil {
			t := 0
			query.TotalQuantityFrom = &t
		} else if query.TotalQuantityTo == nil {
			t := 100000
			query.TotalQuantityTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"total_quantity": bson.M{
				"$gte": query.TotalQuantityFrom,
				"$lte": query.TotalQuantityTo,
			},
		})
	}

	if query.TotalItemFrom != nil || query.TotalItemTo != nil {
		if query.TotalItemFrom == nil {
			t := 0
			query.TotalItemFrom = &t
		} else if query.TotalItemTo == nil {
			t := 1000
			query.TotalItemTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"total_item": bson.M{
				"$gte": query.TotalItemFrom,
				"$lte": query.TotalItemTo,
			},
		})
	}

	if query.RedeemCode != nil && len(*query.RedeemCode) > 0 {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"redeem_code": bson.M{
					"$all": *query.RedeemCode,
				},
			})
		query.RedeemCode = nil
	}

	if hasVoucher {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"redeem_code.0": bson.M{
				"$exists": true,
			},
		})
	}

	result := model.CartDB.Query(query, offset, limit, sortFields)
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "NOT_FOUND"
		return result
	}
	if getTotal {
		result.Total = model.CartDB.Count(query).Total
	}
	return result
}

// GetCartItemList is func get list cart item have pagination
func GetCartItemList(account *model.Account, query *model.CartItem, offset, limit int64, getTotal, onlyQuantity bool) *common.APIResponse {
	if account.Type == enum.AccountType.CUSTOMER {
		cart, errCart := getCart(account, false)
		if errCart != nil {
			return errCart
		}
		query.ComplexQuery = nil
		query.CartID = cart.CartID
	}
	result := model.CartItemDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		return result
	}
	if onlyQuantity {
		items := result.Data.([]*model.CartItem)
		newItems := make([]*model.CartItem, len(items))
		for i, item := range items {
			newItems[i] = &model.CartItem{
				Quantity: item.Quantity,
				Sku:      item.Sku,
				Type:     item.Type,
			}
		}
		result.Data = newItems
	}
	if getTotal {
		result.Total = model.CartItemDB.Count(query).Total
	}
	return result
}

func resetCartPrice(cart *model.Cart) {
	cart.TotalPrice = 0
	cart.TotalWeight = 0
	cart.TotalVolume = 0
	cart.Price = 0
	cart.SubPrice = 0
	cart.TotalFee = 0
	cart.Discount = 0
}

func mixCart(currentCart, revertCart *model.Cart, isEditOrder bool, customer *model.Customer) *model.Cart {
	mapCartItem := map[string]*model.CartItem{}
	cartID := int64(0)
	cartNo := ""
	if currentCart != nil {
		for _, item := range currentCart.Items {
			if item.Type == enum.ItemType.GIFT {
				continue
			}
			mapCartItem[item.Sku] = item
		}
		cartID = currentCart.CartID
		cartNo = currentCart.CartNo
	} else {
		cartID, cartNo = model.GetOrderID()
	}
	if isEditOrder {
		cartID = revertCart.CartID
		cartNo = revertCart.CartNo
	}
	for _, item := range revertCart.Items {
		if !isCartItemSelected(item) || item.Type == enum.ItemType.GIFT {
			continue
		}
		if curItem, ok := mapCartItem[item.Sku]; ok {
			item.Quantity = item.Quantity + curItem.Quantity
			item.TotalPrice = item.Price * item.Quantity
			mapCartItem[item.Sku] = item
		} else {
			mapCartItem[item.Sku] = item
		}
	}
	var newItems []*model.CartItem
	for _, item := range mapCartItem {
		item.CartID, item.CartNo = cartID, cartNo
		newItems = append(newItems, item)
	}
	if currentCart != nil {
		currentCart.CartID, currentCart.CartNo = cartID, cartNo
		currentCart.Items = newItems
		currentCart.Status = enum.CartState.DRAFT
		addBrandGiftsToRevertCart(currentCart, revertCart)
		return currentCart
	} else {
		newCart := &model.Cart{
			CartNo:        cartNo,
			CartID:        cartID,
			AccountID:     revertCart.AccountID,
			CustomerID:    revertCart.CustomerID,
			CustomerCode:  revertCart.CustomerCode,
			Status:        enum.CartState.DRAFT,
			Items:         newItems,
			ProvinceCode:  customer.ProvinceCode,
			DistrictCode:  customer.DistrictCode,
			WardCode:      customer.WardCode,
			CustomerScope: customer.Scope,
		}

		// for PIC
		newCart.FlattenLocation = FlatLocationCart(*newCart)
		addBrandGiftsToRevertCart(newCart, revertCart)
		return newCart
	}
}

func addBrandGiftsToRevertCart(currentCart, revertCart *model.Cart) {
	if isBrandOrClinic(currentCart.Source) {
		currentCart.BrandGifts = revertCart.BrandGifts
	}
}

func checkSkuLimit(cart *model.Cart) (errRes *common.APIResponse) {
	logMap := make(map[string]*model.SkuLimitHistory)
	errData := make([]*model.CartItem, 0)

	skuItemCodes := make([]string, 0)
	for _, item := range cart.Items {
		skuItemCodes = append(skuItemCodes, item.ItemCode)
	}

	skuLimitMap := getMapSkuLimitsBySkuItemCodeLive(skuItemCodes, cart.CustomerID)

	now := time.Now()
	queryLog := &model.SkuLimitHistory{
		CustomerID: cart.CustomerID,
		ComplexQuery: []*bson.M{
			{"item_code": bson.M{"$in": skuItemCodes}},
			{"start_time": bson.M{"$lte": now}},
			{"end_time": bson.M{"$gte": now}},
		},
	}
	qLog := model.SkuLimitHistoryDB.Query(queryLog, 0, 0, nil)
	if qLog.Status == common.APIStatus.Ok {
		logs := qLog.Data.([]*model.SkuLimitHistory)
		for _, log := range logs {
			logMap[log.Sku] = log
		}
	}

	for _, item := range cart.Items {
		if limit, ok := skuLimitMap[item.ItemCode]; ok && limit != nil && isCartItemSelected(item) {
			item.IsSkuLimitExisted = true
			if limit.CanApplyLimitPerDay() {
				item.SkuLimitQuantity = limit.Quantity
			}
			if limit.IsActive != nil && !*limit.IsActive { // skip validate if config is not active
				continue
			}
			if log, ok := logMap[item.Sku]; ok && log != nil { // if existed quantity ordered
				if log.Quantity == nil {
					log.Quantity = utils.ParseIntToPointer(0)
				}
				if limit.CanApplyLimitPerDay() && *log.Quantity+item.Quantity > limit.Quantity {
					errRes = buildMaxQuantityError(&maxQuantityErrorData{
						MaxQty: limit.Quantity,
						SKU:    item.Sku,
						Note:   "Checkout with cached SkuLimitHistory",
					})
					errData = append(errData, item)
				}
			} else {
				quantityRecount, _ := recountQuantityOrdered(syncSkuLimitData{
					CustomerID:   cart.CustomerID,
					AccountID:    cart.AccountID,
					Sku:          item.Sku,
					ItemCode:     item.ItemCode,
					Date:         &now,
					NumberOfDays: limit.NumberOfDays,
				})
				if limit.CanApplyLimitPerDay() && item.Quantity+quantityRecount > limit.Quantity {
					errRes = buildMaxQuantityError(&maxQuantityErrorData{
						MaxQty: limit.Quantity,
						SKU:    item.Sku,
						Note:   "Checkout",
					})
					errData = append(errData, item)
				}
			}
		}
	}
	if errRes != nil {
		errRes.Data = errData
	}
	return errRes
}

func checkCartLimit(cart *model.Cart) (errRes *common.APIResponse) {
	qLimit := model.CartLimitDB.QueryOne(model.CartLimit{IsActive: true})
	if qLimit.Status == common.APIStatus.Ok {
		now := time.Now()
		limit := qLimit.Data.([]*model.CartLimit)[0]
		if limit.StartTime != nil {
			if now.Before(*limit.StartTime) {
				return errRes
			}
		}
		if limit.EndTime != nil {
			if now.After(*limit.EndTime) {
				return errRes
			}
		}
		cart.TotalWeight = float64(int(cart.TotalWeight))
		cart.TotalVolume = float64(int(cart.TotalVolume))
		cart.MaxWeight = limit.MaxWeight
		cart.MaxLength = limit.Length
		cart.MaxWidth = limit.Width
		cart.MaxHeight = limit.Height
		cart.MaxVolume = calVolume(limit.Width, limit.Height, limit.Length)
		if cart.MaxWeight != 0 && cart.TotalWeight > cart.MaxWeight {
			errRes = &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Đơn hàng của bạn vượt quá %d kg", int(limit.MaxWeight)),
				ErrorCode: "CART_LIMIT_WEIGHT",
			}
			makeCartError(errRes, cart)
			return errRes
		}
		if cart.MaxVolume != 0 && cart.TotalVolume > cart.MaxVolume {
			errRes = &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Đơn hàng của bạn vượt quá quy cách thùng %dx%dx%dcm", int(limit.Width), int(limit.Length), int(limit.Height)),
				ErrorCode: "CART_LIMIT_VOLUME",
			}
			makeCartError(errRes, cart)
			return errRes
		}
	}
	return errRes
}

func logSkuLimit(order *model.Order) {
	now := time.Now()
	queryLog := &model.SkuLimitHistory{
		CustomerID: order.CustomerID,
		ComplexQuery: []*bson.M{
			{"start_time": bson.M{"$lte": now}},
			{"end_time": bson.M{"$gte": now}},
		}}
	qLogs := model.SkuLimitHistoryDB.Query(queryLog, 0, 0, nil)

	logMap := make(map[string]*model.SkuLimitHistory)
	if qLogs.Status == common.APIStatus.Ok {
		for _, log := range qLogs.Data.([]*model.SkuLimitHistory) {
			logMap[log.ItemCode] = log
		}
	}

	for _, item := range order.Items {
		// Only log skuLimitHistory if item has SKULimit
		if !item.IsSkuLimitExisted {
			continue
		}

		if log, ok := logMap[item.ItemCode]; log != nil && ok { // existed --> upsert
			model.SkuLimitHistoryDB.IncreOne(&model.SkuLimitHistory{Code: log.Code}, "quantity", item.Quantity)
		} else {
			newLog := &model.SkuLimitHistory{
				Code:       model.GenCodeWithTime(),
				CustomerID: order.CustomerID,
				AccountID:  order.AccountID,
				Quantity:   utils.ParseIntToPointer(item.Quantity),
				Sku:        item.Sku,
				ItemCode:   item.ItemCode,

				NumberOfDays: 1, // TODO: skuLimit is hardcode to 1 day. Need update
			}
			newLog.SetKey()

			now := time.Now().Add(time.Hour * 7)

			start := time.Date(now.Year(), now.Month(), now.Day(), 17, 0, 0, 0, now.Location())
			start = start.AddDate(0, 0, -1)
			newLog.StartTime = &start

			end := start.AddDate(0, 0, newLog.NumberOfDays)
			newLog.EndTime = &end

			model.SkuLimitHistoryDB.Create(newLog)
		}
	}
}

func RecountSkuLimit(customerID int64, sku, itemCode string) *common.APIResponse {
	now := time.Now()
	err := model.SyncSkuLimitHistoryJob.Push(syncSkuLimitData{
		CustomerID: customerID,
		Sku:        sku,
		ItemCode:   itemCode,
		Date:       &now,
	}, &job.JobItemMetadata{
		Topic: "default",
	})

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PUSH_RECOUNT_FAIL",
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
	}
}

func isCartItemSelected(item *model.CartItem) bool {
	if item.IsSelected == nil || (item.IsSelected != nil && *item.IsSelected == true) {
		item.IsSelected = utils.ParseBoolToPointer(true)
		return true
	}
	return false
}

// SelectCartItem handles the selection of cart items based on the provided input.
// It supports selecting a single SKU, multiple SKUs, or applying the selection to all items in the cart.
func SelectCartItem(acc *model.Account, input *model.SelectCartItemRequest) *common.APIResponse {
	// Validate input
	if !input.IsAppliedAll && len(input.Skus) == 0 && len(input.Sku) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing sku/skus param",
			ErrorCode: "MISSING_PARAM",
		}
	}

	defer func() {
		go sdk.Execute(func() {
			SyncCartCache(acc, true, []string{}, true, "")
		})
	}()

	// Get cartID
	cart, errGetCart := getCart(acc, false)
	if errGetCart != nil {
		return errGetCart
	}
	cartID := cart.CartID

	// Case: select All
	if input.IsAppliedAll {
		go model.CartItemCacheDB.UpdateMany(
			&model.CartItem{CartID: cartID},
			&model.CartItem{IsSelected: &input.IsSelected},
		)

		return model.CartItemDB.UpdateMany(
			&model.CartItem{CartID: cartID},
			&model.CartItem{IsSelected: &input.IsSelected},
		)
	}

	// Case: select a single SKU, using input.Sku
	if len(input.Sku) != 0 {
		go model.CartItemCacheDB.UpdateOne(
			&model.CartItem{
				CartID: cartID,
				Sku:    input.Sku,
			},
			&model.CartItem{IsSelected: &input.IsSelected},
		)

		return model.CartItemDB.UpdateOne(
			&model.CartItem{
				CartID: cartID,
				Sku:    input.Sku,
			},
			&model.CartItem{IsSelected: &input.IsSelected},
		)
	}

	// Case: select an array of SKUs, using input.Skus
	go model.CartItemCacheDB.UpdateMany(
		&model.CartItem{
			CartID: cartID,
			ComplexQuery: []*primitive.M{
				{"sku": primitive.M{"$in": input.Skus}},
			},
			IsSelected: nil,
		},
		&model.CartItem{IsSelected: &input.IsSelected},
	)

	return model.CartItemDB.UpdateMany(
		&model.CartItem{
			CartID: cartID,
			ComplexQuery: []*primitive.M{
				{"sku": primitive.M{"$in": input.Skus}},
			}},
		&model.CartItem{IsSelected: &input.IsSelected},
	)
}

func validateCustomerRegion(customer *model.Customer, cart *model.Cart) *common.APIResponse {
	if ward, ok := mapWard[customer.WardCode]; ok && ward.Code != "" {
		if ward.DistrictCode != customer.DistrictCode || ward.ProvinceCode != customer.ProvinceCode {
			return CustomerBusinessAddressError
		}
	} else if !isValidDistrictWithWardNotExist(customer.DistrictCode, customer.ProvinceCode) {
		return CustomerBusinessAddressError
	}
	if ward, ok := mapWard[cart.CustomerWardCode]; ok && ward.Code != "" {
		if ward.DistrictCode != cart.CustomerDistrictCode {
			return CartDistrictInvalid
		}
		if ward.ProvinceCode != cart.CustomerProvinceCode {
			return CartProvinceInvalid
		}
	} else if !isValidDistrictWithWardNotExist(cart.CustomerDistrictCode, cart.CustomerProvinceCode) {
		return CartWardNotFound
	}
	return nil
}

func isValidDistrictWithWardNotExist(districtCode string, provinceCode string) bool {
	return (districtCode == "755" && provinceCode == "77") ||
		(districtCode == "536" && provinceCode == "51") ||
		(districtCode == "498" && provinceCode == "48") ||
		(districtCode == "318" && provinceCode == "31") ||
		(districtCode == "471" && provinceCode == "45")
}

func handleOrderTags(cart *model.Cart, order *model.Order, customer *model.Customer) *common.APIResponse {
	if customer.TagMap != nil && (customer.TagMap[string(enum.CustomerTag.Vip)] || customer.TagMap[string(enum.CustomerTag.VipDebt)]) {
		order.Tags = append(order.Tags, enum.Tag.VIP)
	}
	if cart.TagMap != nil {
		for tag, config := range cart.TagMap {
			order.Tags = append(order.Tags, enum.TagValue(tag))
			if config.ProvinceAppliedValue != nil {
				order.AdditionalTime += *config.ProvinceAppliedValue * 60 * 60 // to second
			}
		}
	}
	if cart.SystemDisplay == "BUYDENTAL" {
		order.Tags = append(order.Tags, enum.Tag.BUYDENTAL)
	}
	if isBrandOrClinic(cart.Source) {
		orderTagsMap := make(map[string]bool)
		for _, tag := range order.Tags {
			orderTagsMap[string(tag)] = true
		}

		// Check if orderTags contains tag of preventSendTagDoNotDeliveryBinConfig => do not add tag NO_DELIVERY_BY_BIN to order and send tag to warehouse
		shouldAddTagNoDeliveryByBin := true

		for _, tag := range preventSendTagDoNotDeliveryBinConfig {
			if _, ok := orderTagsMap[tag]; ok {
				shouldAddTagNoDeliveryByBin = false
			}
		}

		if shouldAddTagNoDeliveryByBin {
			order.Tags = append(order.Tags, enum.Tag.NO_DELIVERY_BY_BIN)
		}
	}

	if (order.Invoice == nil || order.Invoice.RequestInvoice == nil || !*order.Invoice.RequestInvoice) && utils.IsContains(customer.Tags, "NON_INVOICE_CUSTOMER") {
		order.Tags = append(order.Tags, enum.Tag.NON_INVOICE_CUSTOMER)
	}

	// Prevent send tag by source which is config by config_code: SOURCE_PREVENT_TAG_TO_WAREHOUSE
	tagInfo := make(map[string]bool)
	for _, tag := range customer.Tags {
		tagInfo[tag] = true
	}

	if order.Source != nil {
		// check config source
		if tagsToRemove, ok := sourcePreventSendTagToWarehouseConfig[string(*order.Source)]; ok {
			for _, valueTag := range tagsToRemove {
				if _, tagExists := tagInfo[valueTag]; tagExists {
					filteredTags := []enum.TagValue{}

					// check tag then remove tag = valueTag (valueTag is config in sourcePreventSendTagToWarehouseConfig)
					for _, tag := range order.Tags {
						if tag != enum.TagValue(valueTag) {
							filteredTags = append(filteredTags, tag)
						}
					}

					order.Tags = filteredTags
				}
			}
		}
	}

	// logic add tags of product by config to order
	if order.Items != nil {
		tagSet := make(map[string]struct{})
		for _, tag := range order.Tags {
			tagSet[string(tag)] = struct{}{}
		}

		for _, item := range order.Items {
			for _, tag := range item.Tags {
				if _, exists := tagSet[tag]; exists {
					continue // Nếu đã có trong order.Tags thì bỏ qua
				}
				if utils.IsContains(productTagsConfig, tag) { // Check if the tag is mapped to a value in the config
					tagSet[tag] = struct{}{}
					order.Tags = append(order.Tags, enum.TagValue(tag))
				}
			}
		}
	}

	return nil
}

func handlePaymentMethodCredit(cart *model.Cart, customer *model.Customer, paymentMethods []*client.PaymentFeeConfig) *common.APIResponse {
	if cart.PaymentMethod == "" {
		return PaymentMethodError
	}

	debtResp, err := client.Services.AccountingDebt.CheckDebtContractByCustomerID(cart.CustomerID)
	if err != nil && cart.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
		// if cart.PaymentMethod == string(enum.PaymentMethod.CREDIT) && err != nil {}
		return PaymentMethodCredit_DebtContractNotFound
	}

	if err == nil {
		debtContract := debtResp[0]
		if cart.PaymentMethod != string(enum.PaymentMethod.CREDIT) {

			// If order source is "brand-portal" or "clinic-portal" then allow to checkout using other payment methods
			if isBrandOrClinic(cart.Source) {
				return nil
			}

			// Only allow debt customers (based on configuration) to checkout using other payment methods
			// if their balance is lower than the total price of the cart
			if debtContract.IsValid && int(debtContract.Balance) >= cart.TotalPrice {
				//auto apply credit payment method if it is not selected
				cart.PaymentMethod = string(enum.PaymentMethod.CREDIT)
				return PaymentMethodCredit_NotAllowAnotherMethod
			}

			if !debtContract.HasOutstandingBalance {
				return nil
			}

			if _, ok := mapCustomerIdTagCheckoutOtherMethod[cart.CustomerID]; ok {
				return nil
			}
			for _, tag := range customer.Tags {
				if _, ok := mapCustomerTagCheckoutOtherMethod[tag]; ok {
					return nil
				}
			}

		}

		if debtContract.IsValid {
			if cart.PaymentMethod != string(enum.PaymentMethod.CREDIT) {
				return PaymentMethodCredit_NotAllowAnotherMethod
			}
		}

		if debtContract.IsValid {
			if cart.PaymentMethod != string(enum.PaymentMethod.CREDIT) {
				cart.PaymentMethod = string(enum.PaymentMethod.CREDIT)
				return nil
			}
		}
		if !debtContract.IsValid {
			if cart.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
				defaultPayment := getDefaultPaymentMethod(paymentMethods)
				if defaultPayment != "" {
					cart.PaymentMethod = defaultPayment
				} else {
					return PaymentMethodCredit_InvalidContract
				}
			}
		}

		isAllow := client.Services.AccountingDebt.CheckoutOrder(&client.CheckoutOrderInput{
			IsVerify:    true,
			OrderCode:   cart.CartNo,
			CustomerID:  cart.CustomerID,
			OrderAmount: int64(cart.TotalPrice),
			RequestID:   time.Now().UnixNano(),
		})
		// set default first payment method to cart from list payment method
		if isAllow.Status != common.APIStatus.Ok {
			return isAllow
		}
	}
	return nil
}

func getDefaultPaymentMethod(paymentMethods []*client.PaymentFeeConfig) string {
	//sort payment method by priority
	sort.Slice(paymentMethods, func(i, j int) bool {
		// if priority == nil, set Priority = 0
		if paymentMethods[i].Priority == nil {
			paymentMethods[i].Priority = utils.ParseInt64ToPointer(0)
		}
		if paymentMethods[j].Priority == nil {
			paymentMethods[j].Priority = utils.ParseInt64ToPointer(0)
		}
		return *paymentMethods[i].Priority < *paymentMethods[j].Priority
	})

	if len(paymentMethods) > 0 {
		return paymentMethods[0].Code
	}
	return ""
}

func skipReturnAutoApplyVoucher(cart *model.Cart) {
	cart.AllRedeemCode = cart.RedeemCode
	if len(cart.RedeemApplyResult) > 0 && cart.RedeemCode != nil {
		newRedeemCodes := make([]*string, 0)
		mapCodeAuto := make(map[string]bool, 0)
		for _, res := range cart.RedeemApplyResult {
			if res.AutoApply {
				mapCodeAuto[res.Code] = true
			}
		}
		for _, code := range *cart.RedeemCode {
			if !mapCodeAuto[*code] {
				newRedeemCodes = append(newRedeemCodes, code)
			}
		}
		cart.RedeemCode = &newRedeemCodes
	}
}

func notiCartItemChangedPrice(cart *model.Cart, customer *model.Customer, item *model.CartItem) {
	if item.Notification != nil && item.Notification[string(enum.NotificationTag.PRICE)] == 1 {
		return // skip
	}
	change := "tăng"
	if item.Price < item.CurPrice {
		change = "giảm"
	}
	if item.Notification == nil {
		item.Notification = make(map[string]int, 0)
	}
	item.Notification[string(enum.NotificationTag.PRICE)] = 1
	go func() {
		title := fmt.Sprintf("Sản phẩm %s đã có sự %s giá bán so với lúc bạn để vào giỏ hàng, kiểm tra ngay để đặt được giá tốt !", item.ProductName, change)
		if change == "tăng" {
			title = fmt.Sprintf("Sản phẩm %s đã có sự %s giá bán so với lúc bạn để vào giỏ hàng, vui lòng kiểm tra lại trước khi thanh toán!", item.ProductName, change)
		}
		err := client.Services.Notification.CreateNotification(&client.Notification{
			Username:     customer.Username,
			UserID:       customer.AccountID,
			ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
			Topic:        "ANNOUNCEMENT",
			Title:        title,
			Link:         "/cart",

			Tags: []enum.NotificationTagEnum{enum.NotificationTag.PRICE, enum.NotificationTag.IMPORTANT},
		})
		if err.Status == common.APIStatus.Ok {
			model.CartItemDB.UpdateOne(model.CartItem{CartID: cart.CartID, Sku: item.Sku}, model.CartItem{Notification: item.Notification})
		}
	}()
}

func GetCartInfoLite(acc *model.Account) *common.APIResponse {
	cartRes := model.CartDB.QueryOne(&model.Cart{
		AccountID: acc.AccountID,
	})
	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]
	cartItemRes := model.CartItemDB.Query(&model.CartItem{CartID: cart.CartID}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		_ = model.CartDB.Delete(&model.Cart{AccountID: acc.AccountID})
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Not found any matched cart",
		}
	}
	items := cartItemRes.Data.([]*model.CartItem)
	cart.Items = items
	cart.TotalItem = utils.ParseIntToPointer(len(items))
	cart.TotalQuantity = utils.ParseIntToPointer(0)
	cart.TotalItemSelected = utils.ParseIntToPointer(0)
	cart.TotalQuantitySelected = utils.ParseIntToPointer(0)
	for _, item := range cart.Items {
		cart.TotalQuantity = utils.ParseIntToPointer(*cart.TotalQuantity + item.Quantity)
		if isCartItemSelected(item) {
			cart.TotalQuantitySelected = utils.ParseIntToPointer(*cart.TotalQuantitySelected + item.Quantity)
			cart.TotalItemSelected = utils.ParseIntToPointer(*cart.TotalItemSelected + 1)
		}
	}
	return cartRes
}

func MigrateFlattenLocationCart(_ sdk.APIRequest, resp sdk.APIResponder) error {
	go func() {
		log.Println("START MigrateFlattenLocationOrder")
		defer log.Println("END MigrateFlattenLocationOrder")
		errList := []string{}
		total := 0
		for offset, limit := int64(0), int64(1000); ; offset += limit {
			resp := model.CartDB.Query(bson.M{},
				offset, limit, &primitive.M{"cart_id": 1})
			if resp.Status != common.APIStatus.Ok {
				break
			}

			carts := resp.Data.([]*model.Cart)
			for _, cart := range carts {

				if cart.ProvinceCode == "" && cart.AccountID != 0 {
					customer, errCustomerRes := getCustomerProfile(&model.Account{AccountID: cart.AccountID, Type: enum.AccountType.CUSTOMER})
					if errCustomerRes != nil {
						continue
					}
					cart.ProvinceCode = customer.ProvinceCode
					cart.DistrictCode = customer.DistrictCode
					cart.WardCode = customer.WardCode
					cart.CustomerScope = customer.Scope
				}

				flatten := FlatLocationCart(*cart)
				if len(flatten) == 0 {
					continue
				}
				query := model.Cart{CartID: cart.CartID}
				updater := bson.M{
					"$set": bson.M{
						"province_code":    cart.ProvinceCode,
						"district_code":    cart.DistrictCode,
						"ward_code":        cart.WardCode,
						"flatten_location": flatten,
						"customer_scope":   cart.CustomerScope,
					},
				}
				updateResp := model.CartDB.UpdateOneWithOption(
					query,
					updater,
				)
				if updateResp.Status != common.APIStatus.Ok {
					errList = append(errList, fmt.Sprint(cart.CartID))
				}
				total++
			}

			if len(carts) < int(limit) {
				break
			}
		}

		fmt.Println(errList)
		fmt.Println("total updated:", total)
	}()

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "run in background",
	})
}

func GetCartItemLite(acc *model.Account, input *model.CartItemRequest) *common.APIResponse {
	// Get customerID
	customerID := input.CustomerID
	if customerID == 0 {
		customer, errCustomerRes := getCustomerProfile(acc)
		if errCustomerRes != nil {
			return errCustomerRes
		}
		customerID = customer.CustomerID
	}

	// Get cart
	cartRes := model.CartDB.QueryOne(&model.Cart{
		CustomerID: customerID,
	})
	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]

	// Get cart items
	query := &model.CartItem{CartID: cart.CartID}
	if len(input.SKUs) > 0 {
		query.ComplexQuery = []*bson.M{
			{
				"sku": bson.M{
					"$in": input.SKUs,
				},
			},
		}
	}

	return model.CartItemDB.Query(query, 0, 0, nil)
}

func MigrateFlattenLocationOrder(_ sdk.APIRequest, resp sdk.APIResponder) error {
	go func() {
		log.Println("START MigrateFlattenLocationOrder")
		defer log.Println("END MigrateFlattenLocationOrder")
		errList := []string{}
		_id_offset := primitive.NilObjectID
		total := 0
		progress := 0
		for {
			resp := model.OrderDB.Query(model.Order{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id_offset}},
				},
			},
				0, 1000, &bson.M{"_id": 1})
			if resp.Status != common.APIStatus.Ok {
				break
			}

			orders := resp.Data.([]*model.Order)
			for _, order := range orders {
				_id_offset = *order.ID

				if order.CustomerScope != "" {
					continue
				}

				if order.AccountID != 0 {
					customer, errCustomerRes := getCustomerProfile(&model.Account{AccountID: order.AccountID, Type: enum.AccountType.CUSTOMER})
					if errCustomerRes != nil {
						continue
					}
					order.CustomerScope = customer.Scope
				}

				// log
				progress++
				if progress%1000 == 0 {
					fmt.Println("progress:", progress)
				}

				// flatten := action.FlatLocationOrder(*order)
				// if len(flatten) == 0 {
				// 	continue
				// }
				updater := bson.M{
					"$set": bson.M{
						// "flatten_location": flatten,
						"customer_scope": order.CustomerScope,
					},
				}
				query := model.Order{ID: order.ID}
				updateResp := model.OrderDB.UpdateOneWithOption(
					query,
					updater,
				)
				if updateResp.Status != common.APIStatus.Ok {
					errList = append(errList, fmt.Sprint(order.OrderID))
				}
				total++
			}
		}

		fmt.Println(errList)
		fmt.Println("total updated:", total)
	}()

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "run in background",
	})
}

func calculatePaymentMethod(cartLocation string, fee *client.PaymentFeeConfig) *float64 {
	var discountPercent *float64
	for _, locationFee := range fee.PaymentLocations {
		if locationFee.FeeDiscountPercentage == nil {
			continue
		}

		for _, locationCode := range locationFee.LocationCodes {
			if cartLocation == *locationCode {
				discountPercent = locationFee.FeeDiscountPercentage
				break
			}
		}
	}
	return discountPercent
}

// calculateOnepayPaymentMethod
func calculateOnepayPaymentMethod(curLocation string, totalPrice int, fee *client.PaymentFeeConfig) *model.PartnerPaymentMethod {

	if totalPrice == 0 {
		return nil
	}

	var partnerPaymentMethod *model.PartnerPaymentMethod
	for _, locationFee := range fee.PartnerPaymentServiceLocationFees {
		if locationFee.FixedFeeConfig == nil && locationFee.DynamicFeeConfig == nil {
			continue
		}

		for _, locationCode := range locationFee.LocationCodes {
			if locationCode == "" {
				continue
			}

			if curLocation == locationCode {

				if locationFee.FixedFeeConfig != nil && locationFee.FixedFeeConfig.IsActive {
					if partnerPaymentMethod == nil {
						partnerPaymentMethod = &model.PartnerPaymentMethod{}
					}
					partnerPaymentMethod.FixedBuymedFee = int64(locationFee.FixedFeeConfig.BuymedFeeValue)
					partnerPaymentMethod.FixedCustomerFee = int64(locationFee.FixedFeeConfig.CustomerFeeValue)
				}
				if locationFee.DynamicFeeConfig != nil && locationFee.DynamicFeeConfig.IsActive {
					if partnerPaymentMethod == nil {
						partnerPaymentMethod = &model.PartnerPaymentMethod{}
					}
					partnerPaymentMethod.DynamicBuymedPercentage = locationFee.DynamicFeeConfig.BuymedFeeValue
					partnerPaymentMethod.DynamicCustomerPercentage = locationFee.DynamicFeeConfig.CustomerFeeValue
				}

				// temp := partnerPaymentMethod.FixedBuymedFee + partnerPaymentMethod.FixedCustomerFee
				// partnerPaymentMethod.FixedFee = &temp

				// tempF := partnerPaymentMethod.DynamicBuymedPercentage + partnerPaymentMethod.DynamicCustomerPercentage
				// partnerPaymentMethod.DynamicPercentage = &tempF
				break
			}
		}
	}

	if partnerPaymentMethod != nil {

		// calculate trans value
		// docs: https://buymed.atlassian.net/wiki/spaces/~106813061/pages/870154259/Online+Payment

		// Trans_value: (order_value + fixed_fee_customer))/(1-%trans_customer))
		transValue := utils.Ceil(int64((float64(totalPrice) + float64(partnerPaymentMethod.FixedCustomerFee)) / (1 - (partnerPaymentMethod.DynamicCustomerPercentage / 100))))

		totalCustomerFee := transValue - int64(totalPrice)

		// Total service fee = sum(fixed_fee) + sum(%trans)*Trans_value
		totalServiceFee := utils.Ceil(int64(float64(partnerPaymentMethod.FixedCustomerFee+partnerPaymentMethod.FixedBuymedFee) + ((partnerPaymentMethod.DynamicCustomerPercentage + partnerPaymentMethod.DynamicBuymedPercentage) * float64(transValue) / 100)))

		// ts sponsor fee = fixed_fee_ts + %trans_ts*trans_value
		totalBuymedSponsorFee := utils.Ceil(partnerPaymentMethod.FixedBuymedFee + int64(partnerPaymentMethod.DynamicBuymedPercentage*float64(transValue)/100))

		partnerPaymentMethod.TotalCustomerFee = totalCustomerFee
		partnerPaymentMethod.TotalBuymedSponsorFee = totalBuymedSponsorFee
		partnerPaymentMethod.TotalFee = totalServiceFee
	}

	return partnerPaymentMethod
}

func handlePartnerPaymentMethodFee(cart *model.Cart) (errRes *common.APIResponse) {

	// chỉ tính với 1 số phương thức được cài đặt
	if !IsContainsT(PARTNER_PAYMENT_METHOD, (cart.PaymentMethod)) {
		return nil
	}

	cart.TotalPriceBeforePartnerPaymentFee = cart.TotalPrice

	feePaymentMap := make(map[string]*client.PaymentFeeConfig)
	feePaymentConfigs, err := client.Services.Pricing.GetPaymentFeeConfig()
	if err != nil {
		return PaymentMethodError
	}
	for _, fee := range feePaymentConfigs {
		feePaymentMap[fee.Code] = fee
	}
	// partner payment fee
	if fee, ok := feePaymentMap[cart.PaymentMethod]; ok {
		var partnerPaymentMethod *model.PartnerPaymentMethod
		locationOffset := []string{cart.CustomerWardCode, cart.CustomerDistrictCode, cart.CustomerProvinceCode, cart.CustomerRegionCode, "00"}
		for _, locationCode := range locationOffset {
			if locationCode == "" {
				continue
			}
			partnerPaymentMethod = calculateOnepayPaymentMethod(locationCode, cart.TotalPrice, fee)
			if partnerPaymentMethod != nil {
				break
			}
		}
		if partnerPaymentMethod != nil {
			cart.PartnerPaymentMethod = partnerPaymentMethod
		}
	}

	if cart.PartnerPaymentMethod != nil {
		if cart.PartnerPaymentMethod.TotalCustomerFee > 0 {
			cart.TotalPrice += int(cart.PartnerPaymentMethod.TotalCustomerFee)
		}
		// // không tính phí này cho KH
		// if cart.PartnerPaymentMethod.TotalBuymedSponsorFee > 0 {
		// 	cart.TotalPrice -= int(cart.PartnerPaymentMethod.TotalBuymedSponsorFee)
		// }
	}

	return nil
}

func RemoveCartItemImportant(acc *model.Account, input *model.CartRemoveItemImportant) *common.APIResponse {
	if len(input.Skus) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid body request",
			ErrorCode: "INVALID_DELETE_ITEM_REQUEST",
		}
	}
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cartRes := model.CartDB.QueryOne(&model.Cart{
		CartNo:     input.CartNo,
		CustomerID: customer.CustomerID,
	})
	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]
	query := &model.CartItem{CartID: cart.CartID}

	input.Skus = utils.UniqueStringSlice(input.Skus)
	query.ComplexQuery = []*bson.M{
		{
			"sku": bson.M{
				"$in": input.Skus,
			},
		},
	}
	model.CartItemCacheDB.UpdateMany(query, model.CartItem{IsImportant: utils.ParseBoolToPointer(false)})
	return model.CartItemDB.UpdateMany(query, model.CartItem{IsImportant: utils.ParseBoolToPointer(false)})
}

func CartUncheckRefuseSplitOrder() *common.APIResponse {

	timeSkipAfter := time.Date(2024, 3, 25, 0, 0, 0, 0, utils.TimeZoneVN)
	go func() {
		currentObjID := primitive.NilObjectID
		for {
			cartResp := model.CartDB.Query(model.Cart{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": currentObjID}},
				},
			}, 0, 1000, &bson.M{"_id": 1})
			if cartResp.Status != common.APIStatus.Ok {
				break
			}
			carts := cartResp.Data.([]*model.Cart)
			for _, cart := range carts {
				currentObjID = cart.ID

				if cart.CreatedTime.After(timeSkipAfter) {
					continue
				}

				if cart.IsRefuseSplitOrder != nil && *cart.IsRefuseSplitOrder {
					model.CartDB.UpdateOne(model.Cart{CartID: cart.CartID}, model.Cart{IsRefuseSplitOrder: utils.ParseBoolToPointer(false)})
				}
			}
			if len(carts) < 1000 {
				break
			}

			time.Sleep(50 * time.Millisecond)
		}

	}()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "ok",
	}
}

func handleCountExtraFeeHistory(customer *model.Customer, cart *model.Cart) {
	if !cart.UseFreeDelivery {
		return
	}

	extraFeeConfig := mapLevelExtraFee[customer.Level]

	qExtraFeeHistory := model.UserExtraFeeHistoryDB.QueryOne(&model.UserExtraFeeHistory{CustomerID: customer.CustomerID})

	// get extraFee config
	var maxExtraFeeCanUse = int64(0)
	extraFeeConfig, extraFeeConfigOk := mapLevelExtraFee[customer.Level]
	if extraFeeConfigOk {
		maxExtraFeeCanUse = extraFeeConfig.Max
	}

	// initialize data
	extraFeeUseInitialized := model.ExtraFeeUse{
		Used: 1,
		Max:  maxExtraFeeCanUse,
	}

	// handle initialize if history not found
	if qExtraFeeHistory.Status != common.APIStatus.Ok {
		currentTimestamp := time.Now().UTC()
		currentYear, currentMonth, _ := currentTimestamp.Date()
		currentLocation := currentTimestamp.Location()

		lastOfMonth := time.Date(currentYear, currentMonth+1, 0, 23, 59, 59, 999999999, currentLocation)

		// prepare data to create new record
		newExtraFeeHistory := model.UserExtraFeeHistory{
			CustomerID:  customer.CustomerID,
			CreatedTime: utils.ParseTimeToPointer(time.Now()),
			ExpireTime:  utils.ParseTimeToPointer(lastOfMonth),
			ExtraFeeUse: map[string]model.ExtraFeeUse{
				customer.Level: extraFeeUseInitialized,
			},
		}

		model.UserExtraFeeHistoryDB.Create(newExtraFeeHistory)
	} else {
		extraFeeHistory := qExtraFeeHistory.Data.([]*model.UserExtraFeeHistory)[0]

		extraHistoryMap := make(map[string]model.ExtraFeeUse)

		//clone extraFeeHistory map
		for k, v := range extraFeeHistory.ExtraFeeUse {
			extraHistoryMap[k] = v
		}

		// +1 used of user fee history if it exist in config by level
		extraFeeHistoryByLevel, extraFeeHistoryByLevelOk := extraFeeHistory.ExtraFeeUse[customer.Level]
		if extraFeeHistoryByLevelOk {
			used := extraFeeHistoryByLevel.Used
			extraHistoryMap[customer.Level] = model.ExtraFeeUse{
				Used: used + 1,
				Max:  extraFeeHistoryByLevel.Max,
			}
		} else {
			// not found user history mean they get new level
			// initialize new level to user history
			extraHistoryMap[customer.Level] = extraFeeUseInitialized
		}

		//prepare data update
		historyUpdated := model.UserExtraFeeHistory{
			CustomerID:  customer.CustomerID,
			ExtraFeeUse: extraHistoryMap,
		}
		model.UserExtraFeeHistoryDB.UpdateOne(&model.UserExtraFeeHistory{CustomerID: customer.CustomerID}, historyUpdated)
	}
}

// canAddSpecialControlItems checks if special control items can be added based on mobile app version
func canAddSpecialControlItems(cartItems []*model.CartItem, req sdk.APIRequest) *common.APIResponse {
	for _, item := range cartItems {
		if item.IsSpecialControl != nil && *item.IsSpecialControl {
			if !utils.IsMobileAppVersionSupported(req, 56, 56) {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Vui lòng cập nhật app phiên bản mới nhất, để thêm sản phẩm này vài giỏ hàng.",
					ErrorCode: "SPECIAL_CONTROL_SKU_MOBILE_APP",
				}
			}
		}
	}
	return nil
}
