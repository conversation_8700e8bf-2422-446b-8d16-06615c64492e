package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

func filterSellerFirstOrderSku(items []*model.OrderItem) {
	for _, item := range items {
		if item.SellerClass == nil || *item.SellerClass == enum.SellerClass.INTERNAL {
			continue
		}

		if !isSkuFirstOrder(item) {
			continue
		}

		model.PushNotificationJob.Push(model.PushNotificationItem{
			SellerCode:       item.SellerCode,
			SkuCode:          item.Sku,
			OrderId:          item.OrderID,
			OrderCode:        item.OrderCode,
			NotificationType: enum.NotificationType.SKU_FIRST_ORDER,
		}, &job.JobItemMetadata{
			Keys: []string{
				"SKU_FIRST_ORDER",
			},
			Topic: "default",
		})
	}
}

func isSkuFirstOrder(item *model.OrderItem) bool {
	queryRes := model.SkuFirstOrderDB.QueryOne(model.SkuFirstOrder{
		SkuCode: item.Sku,
	})
	if queryRes.Status == common.APIStatus.Ok {
		return false
	}

	model.SkuFirstOrderDB.Create(model.SkuFirstOrder{
		SkuCode:    item.Sku,
		SellerCode: item.SellerCode,
		OrderId:    item.OrderID,
		OrderCode:  item.OrderCode,
	})

	return true
}
