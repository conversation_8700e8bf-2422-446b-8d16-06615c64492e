package enum

type FeeTypeValue string

type feeType struct {
	REVENUE                 FeeTypeValue // doanh thu
	REVENUE_RETURN_OUT_TURN FeeTypeValue // trả hàng

	BONUS                   FeeTypeValue // thuocsi tài trợ
	OTHER_BONUS             FeeTypeValue // cộng khác
	ACCUMULATE_POINTS_BONUS FeeTypeValue // hoàn phí hàng điểm

	PENALTY                  FeeTypeValue // phí phạt
	FULFILLMENT_PENALTY      FeeTypeValue // phí phạt fulfillment
	INBOUND_OVERDUE_PENALTY  FeeTypeValue // phí lưu kho quá hạn
	INVOICE_OVERDUE_FEE      FeeTypeValue // phí xuất hóa đơn trễ
	ACCUMULATE_POINTS_FEE    FeeTypeValue // phí hàng điểm
	INCREASE_PRICE_FEE       FeeTypeValue // phí tăng giá sản phẩm
	POOR_QUALITY_PRODUCT_FEE FeeTypeValue // phí sản phẩm chất lượng kém
	ORDER_RETURN             FeeTypeValue // tiền đơn trả hàng
	OTHER_FEE                FeeTypeValue // phí khác

	NO_INVOICE_FEE   FeeTypeValue // phí khác không xuất hóa đơn
	NO_INVOICE_BONUS FeeTypeValue // cộng khác không xuất hóa đơn

	// BIZ_HOUSEHOLD_TAX
	BIZ_HOUSEHOLD_TAX        FeeTypeValue // thu thuế hộ kinh doanh
	REFUND_BIZ_HOUSEHOLD_TAX FeeTypeValue // hoàn thuế hộ kinh doanh

	// Voucher
	VOUCHER_DEDUCTION FeeTypeValue // giảm giá voucher
	VOUCHER_REFUND    FeeTypeValue // hoàn voucher
}

var FeeType = &feeType{
	REVENUE:                 "REVENUE",
	REVENUE_RETURN_OUT_TURN: "REVENUE_RETURN_OUT_TURN",

	BONUS:                   "BONUS",
	OTHER_BONUS:             "OTHER_BONUS",
	ACCUMULATE_POINTS_BONUS: "ACCUMULATE_POINTS_BONUS",

	PENALTY:                  "PENALTY",
	FULFILLMENT_PENALTY:      "FULFILLMENT_PENALTY",
	INBOUND_OVERDUE_PENALTY:  "INBOUND_OVERDUE_PENALTY",
	INVOICE_OVERDUE_FEE:      "INVOICE_OVERDUE_FEE",
	ACCUMULATE_POINTS_FEE:    "ACCUMULATE_POINTS_FEE",
	INCREASE_PRICE_FEE:       "INCREASE_PRICE_FEE",
	POOR_QUALITY_PRODUCT_FEE: "POOR_QUALITY_PRODUCT_FEE",
	ORDER_RETURN:             "ORDER_RETURN",
	OTHER_FEE:                "OTHER_FEE",

	BIZ_HOUSEHOLD_TAX:        "BIZ_HOUSEHOLD_TAX",
	REFUND_BIZ_HOUSEHOLD_TAX: "REFUND_BIZ_HOUSEHOLD_TAX",

	VOUCHER_DEDUCTION: "VOUCHER_DEDUCTION",
	VOUCHER_REFUND:    "VOUCHER_REFUND",

	NO_INVOICE_FEE:   "NO_INVOICE_FEE",
	NO_INVOICE_BONUS: "NO_INVOICE_BONUS",
}

type TaxReportType string

var TaxReport = struct {
	SaleOrder           TaxReportType
	DeliveryOrder       TaxReportType
	CreditNote          TaxReportType
	CancelledCreditNote TaxReportType
}{
	SaleOrder:           "SALE_ORDER",
	DeliveryOrder:       "DELIVERY_ORDER",
	CreditNote:          "CREDIT_NOTE",
	CancelledCreditNote: "CANCELLED_CREDIT_NOTE",
}
