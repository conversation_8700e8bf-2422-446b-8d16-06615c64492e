package utils

import "sync"

type Semaphore struct {
	pool chan struct{}
	wg   sync.WaitGroup
}

func (sema *Semaphore) Acquire() {
	sema.pool <- struct{}{}
	sema.wg.Add(1)
}

func (sema *Semaphore) Release() {
	sema.wg.Done()
	<-sema.pool
}

func (sema *Semaphore) Wait() {
	sema.wg.Wait()
}

func NewSemaphore(numWorker int) *Semaphore {
	sema := &Semaphore{
		pool: make(chan struct{}, numWorker),
	}

	return sema
}
