package action

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"go.mongodb.org/mongo-driver/bson"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

var (
	TopicConfirmOrder    = "topic_confirm_order"
	TopicDeliveringOrder = "topic_delivering_order"
	TopicDeliveredOrder  = "topic_delivered_order"
	TopicReturnedOrder   = "topic_returned_order"
	TopicCompleteOrder   = "topic_complete_order"
)

func confirmOrderJob(order *model.Order) {

	autoConfirmed, errMessage := isAutoConfirm(order)
	if autoConfirmed {
		model.OrderDB.UpdateOne(&model.Order{
			OrderID: order.OrderID,
		}, &model.Order{
			AutoConfirmNote: &errMessage,
		})

		readyTime := order.CreatedTime.Add(time.Minute * time.Duration(conf.Config.AutoConfirmOrderTime))
		if order.HasCampaign || order.HasDeal {
			// if order.HasCampaign || order.HasDeal: push to confirm as soon as possible
			readyTime = time.Now()
		}
		model.PushTopic(context.TODO(), &model.ConfirmOrderData{
			OrderID:     order.OrderID,
			CreatedTime: order.CreatedTime,
		}, &job.JobItemMetadata{
			Topic:     TopicConfirmOrder,
			ReadyTime: &readyTime,
		})
	} else {
		model.OrderDB.UpdateOne(&model.Order{
			OrderID: order.OrderID,
		}, &model.Order{
			AutoConfirmNote: &errMessage,
		})
	}
}

func isAutoConfirm(order *model.Order) (bool, string) {
	if order.Status != enum.OrderState.WaitConfirm {
		return false, "Đã được xác nhận"
	}

	if len(order.CustomerTags) > 0 {
		for _, tag := range order.CustomerTags {
			if tag == string(enum.CustomerTag.Circa) {

				// only auto confirm for circa customer(hardcode)
				if utils.IsInt64Contains(conf.Config.CustomerIDAutoConfirm, order.CustomerID) {
					return true, ""
				}
			}
		}
	}

	if order.PaymentMethod != "PAYMENT_METHOD_NORMAL" && order.PaymentMethod != string(enum.PaymentMethod.CREDIT) {
		return false, "Thanh toán chuyển khoản"
	} else if order.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
		// follow check debt order
		// https://buymed.atlassian.net/browse/CUS-3446 do not allow auto confirm if had config tag
		if _, ok := mapCustomerIdCannotAutoConfirmDebtOrder[order.CustomerID]; ok {
			return false, "Khách hàng chặn auto xác nhận đơn công nợ"
		}
		for _, tag := range order.CustomerTags {
			if _, ok := mapCustomerTagCannotAutoConfirmDebtOrder[tag]; ok {
				return false, "Khách hàng chặn auto xác nhận đơn công nợ"
			}
		}
	}

	//if order.CustomerOrderIndex == 1 {
	//	return false, "Đơn đầu tiên"
	//}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "isAutoConfirm")
	if orderItemPartitionDB == nil {
		return false, model.PARTITION_NOT_FOUND_RESPONSE.Message
	}

	/////////////////////////////
	// old logic
	// find item which has quantity more than 50

	// findMoreThan50 := orderItemPartitionDB.QueryOne(&model.OrderItem{
	// 	OrderID: order.OrderID,
	// 	ComplexQuery: []*bson.M{
	// 		{
	// 			"quantity": &bson.M{
	// 				"$gte": 50,
	// 			}
	// 		},
	// 	},
	// })

	// if findMoreThan50.Status == common.APIStatus.Ok {
	// 	return false, "Có SP với SL trên 50"
	// }
	/////////////////////////////

	/////////////////////////////
	// New logic :
	// find item which has
	// quantity more than 50
	// seller_code = MEDX
	findMoreThan50 := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID: order.OrderID,
		ComplexQuery: []*bson.M{
			{
				"quantity": &bson.M{
					"$gte": 50,
				},
			},
		},
	}, 0, 1000, nil)

	if findMoreThan50.Status == common.APIStatus.Ok {
		orderItem := findMoreThan50.Data.([]*model.OrderItem)
		for _, item := range orderItem {
			// "seller_code": bson.M{"$in": []string{"MEDX", "MEDX_E", "MED_NT"}},
			if item.SellerCode == "MEDX" || item.SellerCode == "MEDX_E" || item.SellerCode == "MED_NT" {
				return false, "Có SP thuộc seller MEDX với SL trên 50"
			}
		}

	}

	blackListSeller := orderItemPartitionDB.QueryOne(&model.OrderItem{
		OrderID: order.OrderID,
		ComplexQuery: []*bson.M{
			{
				"seller_code": "2XACBVI4OQ", // list seller
			},
		},
	})

	if blackListSeller.Status == common.APIStatus.Ok {
		return false, "Cập nhật GPP"
	}

	return true, ""
}

// confirmOrder ...
func confirmOrder(order *model.Order) *common.APIResponse {
	if autoConfirm, reason := isAutoConfirm(order); !autoConfirm {
		model.OrderDB.UpdateOne(&model.Order{
			OrderID: order.OrderID,
		}, &model.Order{
			AutoConfirmNote: &reason,
		})
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: reason,
		}
	}
	t := time.Now()
	order.Status = enum.OrderState.Confirmed
	order.ConfirmationDate = &t
	order.PrivateNote = fmt.Sprintf("%s \n %v - %s", order.PrivateNote, t, "Xác nhận đơn hàng thành công")
	order.LastUpdatedTime = &t
	order.ConfirmType = &enum.OrderConfirmType.Auto
	res := model.OrderDB.UpdateOne(&model.Order{
		OrderID: order.OrderID,
	}, order, nil)
	if res.Status == common.APIStatus.Ok {
		_ = model.CreateSaleOrderJob.Push(createSaleOrderData{OrderID: order.OrderID}, &job.JobItemMetadata{
			UniqueKey: fmt.Sprint(order.OrderID),
			Keys: []string{
				strconv.Itoa(int(order.OrderID)),
			},
			SortedKey: fmt.Sprint(order.OrderID),
			Topic:     "default",
		})
		go func() {
			//sendSMSOrderConfirmed(order.OrderID, order.CustomerID)
			if order.Source == nil || *order.Source != enum.Source.THUOCSI_MOBILE {
				sendZNSOrderConfirmed(order.OrderID, order.CustomerID, order.CreatedTime)
			}

			sendNotifyOrderConfirmed(order.OrderID, order.CustomerID)

			updateStatusOrderSeller(
				order.OrderID,
				&model.OrderUpdateStatus{
					Status:           enum.OrderState.Confirmed,
					ConfirmationDate: &t,
				}, order.SaleOrderCode,
			)
			if order.Items == nil || len(order.Items) == 0 {
				orderItemDB := model.GetOrderItemPartitionDB(order, "confirmOrder")
				qOrderItemUpdated := orderItemDB.Query(model.OrderItem{OrderID: order.OrderID}, 0, 0, &primitive.M{"_id": -1})
				if qOrderItemUpdated.Status == common.APIStatus.Ok {
					order.Items = qOrderItemUpdated.Data.([]*model.OrderItem)
				}
			}
			client.Services.Promotion.ScoreMission("Update order status "+string(order.Status), order)
		}()
	}
	return res
}

func updateStatusOrderSeller(
	orderId int64,
	orderUpdate *model.OrderUpdateStatus,
	saleOrderCode string,
) {
	client.Services.SellerMis.UpdateOrderStatus(&client.OrderSellerUpdate{
		ParentOrderID:    orderId,
		SaleOrderCode:    &saleOrderCode,
		Status:           orderUpdate.Status,
		ConfirmationDate: orderUpdate.ConfirmationDate,
		CompletedTime:    orderUpdate.CompletedTime,
	})

	// qResult := model.OrderSellerDB.Query(&model.OrderSeller{
	// 	ParentOrderID: orderId,
	// }, 0, 0, nil)
	// if qResult.Status != common.APIStatus.Ok {
	// 	return
	// }
	// model.OrderSellerDB.UpdateMany(&model.OrderSeller{ParentOrderID: orderId}, model.OrderSeller{
	// 	SaleOrderCode:    &saleOrderCode,
	// 	Status:           orderUpdate.Status,
	// 	ConfirmationDate: orderUpdate.ConfirmationDate,
	// 	CompletedTime:    orderUpdate.CompletedTime,
	// })
}

func triggerCreateOrderSeller(orderId int64) {
	res := client.Services.SellerMis.CreateOrderSeller(orderId)
	if res.Status != common.APIStatus.Ok {
		fmt.Println("triggerCreateOrderSeller failed:", res.Message, orderId)
	}
}

func deleteOrderSeller(orderId int64) {
	res := client.Services.SellerMis.DeleteOrderSeller(orderId)
	if res.Status != common.APIStatus.Ok {
		fmt.Println("triggerDeleteOrderSeller failed:", res.Message, orderId)
	}
}

func sendSMSOrderConfirmed(orderId int64, customerId int64) {
	if conf.Config.IsSendSMSOrderConfirmed {
		customer, err := client.Services.Customer.GetCustomerByCustomerID(customerId)
		if err != nil {
			return
		}
		if customer != nil && len(customer.Phone) > 0 {
			_, _ = client.Services.SMS.CreateSMS(&client.SMS{
				Topic:    "ANNOUNCEMENT",
				Content:  fmt.Sprintf("Don hang %d cua %s da duoc xac nhan.", orderId, customer.Name),
				Receiver: []string{normalizePhoneNumber(customer.Phone)},
			})
		}
	}
}

func sendNotifyOrderConfirmed(orderId int64, customerId int64) {

	_, err := client.Services.Customer.GetCustomerByCustomerID(customerId)
	if err != nil {
		return
	}

	customerNotificationPartner, err := client.Services.Customer.GetNotificationPartnerListByCustomerID(&client.NotificationPartner{CustomerID: customerId, Status: "ACTIVE"}, 0, 0)

	if err != nil || customerNotificationPartner == nil || len(customerNotificationPartner) == 0 {
		return
	}
	var receiver []string
	for _, partner := range customerNotificationPartner {
		if partner.Token != "" {
			receiver = append(receiver, partner.Token)
		}
	}
	if len(receiver) > 0 {
		client.Services.Notify.CreateNotify(&client.Notify{
			// Topic:    "ANNOUNCEMENT",
			Title:    fmt.Sprintf("Đơn hàng đã được xác nhận"),
			Content:  fmt.Sprintf("Đơn hàng %d của Quý khách đã được xác nhận tại thuocsi.vn. Hy vọng rằng Quý khách sẽ hài lòng và sẽ quay trở lại mua sắm tại thuocsi.vn", orderId),
			Receiver: receiver,
			DeepLink: fmt.Sprintf("%s/my-order", conf.Config.FrontendURL),
		})
	}
}

func sendNotifyOrderCreated(orderId int64, customerId int64) {

	_, err := client.Services.Customer.GetCustomerByCustomerID(customerId)
	if err != nil {
		return
	}

	customerNotificationPartner, err := client.Services.Customer.GetNotificationPartnerListByCustomerID(&client.NotificationPartner{CustomerID: customerId, Status: "ACTIVE"}, 0, 0)

	if err != nil || customerNotificationPartner == nil || len(customerNotificationPartner) == 0 {
		return
	}
	var receiver []string
	for _, partner := range customerNotificationPartner {
		if partner.Token != "" {
			receiver = append(receiver, partner.Token)
		}
	}
	if len(receiver) > 0 {
		client.Services.Notify.CreateNotify(&client.Notify{
			Title:    fmt.Sprintf("Đặt hàng thành công"),
			Content:  fmt.Sprintf("Đơn hàng %d của Quý khách đã được đặt hàng thành công tại thuocsi.vn. Cảm ơn Quý khách đã lựa chọn thuocsi.vn để mua sắm.", orderId),
			Receiver: receiver,
			DeepLink: fmt.Sprintf("%s/my-order", conf.Config.FrontendURL),
		})
	}
}
func sendZNSOrderConfirmed(orderId int64, customerId int64, createdTime *time.Time) {
	// send order-created ZNS to customer
	customer, err := client.Services.Customer.GetCustomerByCustomerID(customerId)
	if err != nil {
		return
	}

	if !customer.CanReceiveZns() {
		return
	}

	znsMessage := client.ZnsMessage{
		DevMode:    true,
		Topic:      client.ORDER_CONFIRMATION_TOPIC,
		BusinessID: "thuocsi.vn",
		Receiver:   customer.Phone,
		Data: map[string]string{
			"customer_name": customer.Name,
			"order_id":      strconv.FormatInt(orderId, 10),
			"date":          createdTime.Format("02/01/2006"),
		},
	}
	_, _ = client.Services.Zns.SendZnsMessage(&znsMessage)
}

func sendZNSOrderMissingItem(orderId int64, customerId int64) {
	// send order-created ZNS to customer
	customer, err := client.Services.Customer.GetCustomerByCustomerID(customerId)
	if err != nil {
		return
	}

	if !customer.CanReceiveZns() {
		return
	}

	msg := client.ZnsMessage{
		Topic:      client.MISSING_ITEM_TOPIC,
		BusinessID: "thuocsi.vn",
		Receiver:   customer.Phone,
		Data: map[string]string{
			"customer_name": customer.Name,
			"order_id":      strconv.FormatInt(orderId, 10),
		},
	}
	_, _ = client.Services.Zns.SendZnsMessage(&msg)
}

func processInvoice(order *model.Order) {
	t := time.Now()
	switch order.Status {
	case enum.OrderState.Delivering:
		if order.OutboundDate == nil {
			order.OutboundDate = &t
		}
		_ = model.CreateInvoiceDraftJobExecutor.Push(&client.InvoiceRequest{
			OrderID:      order.OrderID,
			OutboundDate: order.OutboundDate,
			DoCode:       order.DeliveryOrderCode,
		}, &job.JobItemMetadata{
			Topic: "default",
			Keys:  []string{fmt.Sprint(order.OrderID), order.DeliveryOrderCode},
		})
		_ = model.CalcTransferingDifferenceJob.Push(
			&model.CalcTransferingDifferenceRequest{
				OrderID:                           order.OrderID,
				TransferringDifferenceArisingTime: time.Now(),
				DoCode:                            order.DeliveryOrderCode,
			},
			&job.JobItemMetadata{
				Topic: "default",
				Keys:  []string{order.OrderCode},
			})
		//_ = client.Services.Invoice.CreateInvoiceDraft(&client.InvoiceRequest{OrderID: order.OrderID, OutboundDate: updateData.OutboundDate})
	case enum.OrderState.Delivered:
		_ = model.ExportInvoiceJobExecutor.Push(&client.InvoiceRequest{
			OrderID:       order.OrderID,
			ReconcileDate: &t,
			DoCode:        order.DeliveryOrderCode,
		}, &job.JobItemMetadata{
			Topic: "default",
		})
		//_ = client.Services.Invoice.ExportInvoice(&client.InvoiceRequest{OrderID: order.OrderID, ReconcileDate: &t})
	case enum.OrderState.Returned:
		_ = model.ReplaceInvoiceJobExecutor.Push(&client.InvoiceRequest{
			OrderID: order.OrderID,
			DoCode:  order.DeliveryOrderCode,
		}, &job.JobItemMetadata{
			Topic: "default",
		})
	case enum.OrderState.Canceled:
		_ = model.CalcTransferingDifferenceJob.Push(
			&model.CalcTransferingDifferenceRequest{
				OrderID:                           order.OrderID,
				TransferringDifferenceArisingTime: time.Now(),
				DoCode:                            order.DeliveryOrderCode,
			},
			&job.JobItemMetadata{
				Topic: "default",
				Keys:  []string{order.OrderCode},
			})
	}
}

func processBill(order *model.Order, autoSent bool) {
	if autoSent { // no use
		_ = client.Services.Bill.CreateBill(order.OrderID, order.DeliveryOrderCode)
		return
	}
	switch order.Status {
	case enum.OrderState.Delivering:
		_ = client.Services.Bill.CreateBill(order.OrderID, order.DeliveryOrderCode)
		//case enum.OrderState.Delivered:
		//_ = client.Services.Bill.CompletedBill(order.OrderID)
		//case enum.OrderState.Canceled:
		//	_ = client.Services.Bill.CancelBill(order.OrderID)
	}
}

func processCancel(order *model.Order, cancelRemainDO bool) *common.APIResponse {
	switch order.Status {
	case enum.OrderState.WaitConfirm, enum.OrderState.Reverting:
		return nil
	case enum.OrderState.Confirmed, enum.OrderState.Processing, enum.OrderState.WaitToDeliver:
		if order.Status == enum.OrderState.Confirmed && len(order.SaleOrderCode) == 0 {
			return nil
		}
		res := client.Services.Warehouse.CancelOrder(&client.CancelOrderRequest{
			OrderID:        order.OrderID,
			CancelRemainDO: cancelRemainDO,
		})
		if res.Status != common.APIStatus.Ok {
			if res.ErrorCode == "SALE_ORDER_NOT_FOUND" {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Cập nhật không thành công, vui lòng thử lại sau vài phút",
					ErrorCode: "ERP_NOT_FOUND",
				}
			}
			return res
		}
	case enum.OrderState.Delivering:
		res := client.Services.Warehouse.CancelOrder(&client.CancelOrderRequest{
			OrderID:        order.OrderID,
			CancelRemainDO: cancelRemainDO,
		})
		if res.Status != common.APIStatus.Ok {
			if res.ErrorCode == "SALE_ORDER_NOT_FOUND" {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Cập nhật không thành công, vui lòng thử lại sau vài phút",
					ErrorCode: "ERP_NOT_FOUND",
				}
			}
			return res
		}
	default:
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_ORDER_STATUS",
			Message:   "Không thể hủy đơn hàng",
		}
	}
	return nil
}

func notifyCancelOrder(orderID, customerID int64) {
	customer, err := client.Services.Customer.GetCustomerByCustomerID(customerID)
	if err != nil {
		return
	}
	_ = client.Services.Notification.CreateNotification(&client.Notification{
		Username:     customer.Username,
		UserID:       customer.AccountID,
		ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
		Topic:        "ANNOUNCEMENT",
		Title:        fmt.Sprintf("Đơn hàng của bạn đã bị hủy, mã đơn hàng #%d!", orderID),
		Link:         fmt.Sprintf("/my-order/%d", orderID),

		Tags: []enum.NotificationTagEnum{enum.NotificationTag.ORDER, enum.NotificationTag.IMPORTANT},
	})
}

// return true if missing
func checkIfMissingItemAtWaitToDeliver(order *model.Order) (bool, *model.OrderNotDeliveryRequest) {
	// TODO defer
	orderRes := model.OrderDB.QueryOne(&model.Order{
		OrderID: order.OrderID,
	})
	if orderRes.Status != common.APIStatus.Ok {
		return false, nil
	}

	latestOrder := orderRes.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(latestOrder, "notifyCancelOrder")
	if orderItemPartitionDB == nil {
		return false, nil
	}

	itemRes := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID: order.OrderID,
	}, 0, 1000, nil)
	if itemRes.Status != common.APIStatus.Ok {
		return false, nil
	}

	var fee int64 = 0
	if latestOrder.DeliveryMethodFee != nil {
		fee = fee + *latestOrder.DeliveryMethodFee
	}
	if latestOrder.PaymentMethodFee != nil {
		fee = fee + *latestOrder.PaymentMethodFee
	}
	if latestOrder.ExtraFee != nil {
		fee = fee + *latestOrder.ExtraFee
	}
	if latestOrder.ExtraFee != nil {
		fee = fee + *latestOrder.ExtraFee
	}
	if latestOrder.TotalDiscount != nil {
		fee = fee - int64(*latestOrder.TotalDiscount)
	}
	items := itemRes.Data.([]*model.OrderItem)
	payload := &model.OrderNotDeliveryRequest{
		OrderID:                 latestOrder.OrderID,
		TotalPrice:              *latestOrder.TotalPrice,
		CreatedAt:               latestOrder.CreatedTime,
		CustomerPhone:           latestOrder.CustomerPhone,
		CustomerName:            latestOrder.CustomerName,
		CustomerCode:            latestOrder.CustomerCode,
		CustomerID:              latestOrder.CustomerID,
		CustomerShippingAddress: latestOrder.CustomerShippingAddress,
		CustomerDistrictCode:    latestOrder.CustomerDistrictCode,
		CustomerWardCode:        latestOrder.CustomerWardCode,
		CustomerProvinceCode:    latestOrder.CustomerProvinceCode,
		TotalFee:                fee,
		PaymentMethodFee: func() int64 {
			if latestOrder.PaymentMethodFee != nil {
				return *latestOrder.PaymentMethodFee
			}
			return 0
		}(),
		PaymentMethodPercent: func() float64 {
			if latestOrder.PaymentMethodPercentage != nil {
				return *latestOrder.PaymentMethodPercentage
			}
			return 0
		}(),
		DeliveryMethodFee: func() int64 {
			if latestOrder.DeliveryMethodFee != nil {
				return *latestOrder.DeliveryMethodFee
			}
			return 0
		}(),
		TotalDiscount: func() int64 {
			if latestOrder.TotalDiscount != nil {
				return int64(*latestOrder.TotalDiscount)
			}
			return 0
		}(),
	}

	lstItems := make([]*model.OrderItemRequest, 0)
	isMissQuantity := false
	for _, item := range items {
		_orderItem := &model.OrderItemRequest{
			Sku:       item.Sku,
			ProductID: item.ProductID,
			Price:     item.Price,
			Quantity:  item.Quantity,
			OutboundQuantity: func() int {
				if item.NotionalQuantity != nil {
					return *item.NotionalQuantity
				}
				return 0
			}(),
			TotalPrice: item.TotalPrice,
		}
		if _orderItem.Quantity != _orderItem.OutboundQuantity {
			isMissQuantity = true
		}
		_orderItem.NotOutboundQuantity = _orderItem.Quantity - _orderItem.OutboundQuantity
		lstItems = append(lstItems, _orderItem)
	}

	payload.Items = lstItems
	return isMissQuantity, payload
}

func sendMissingItemAlert(order *model.Order, payload *model.OrderNotDeliveryRequest) *common.APIResponse {
	// Notify
	sendZNSOrderMissingItem(order.OrderID, order.CustomerID)

	_ = client.Services.Notification.CreateNotification(&client.Notification{
		UserID:       order.AccountID,
		ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
		Topic:        "ANNOUNCEMENT",
		Title:        fmt.Sprintf("Đơn hàng #%d của bạn có một số sản phẩm không giao được!", order.OrderID),
		Link:         fmt.Sprintf("/my-order/%d", order.OrderID),

		Tags: []enum.NotificationTagEnum{enum.NotificationTag.ORDER, enum.NotificationTag.IMPORTANT},
	})

	err := client.Services.EmailFile.SendEmailWithPdf(payload)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "SEND_EMAIL_ERROR",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
	}
}

func updateWarehouseInfoOfPlacedOrder(order *model.Order) {
	warehousesList, err := client.Services.Warehouse.GetWarehousesList()
	if err != nil || len(warehousesList) == 0 {
		return
	}

	provinceWarehouseMapping := createProvinceWarehouseMapping(warehousesList)

	updateWarehouseInfoOfOrder(order, provinceWarehouseMapping)
}

func updateWarehouseInfoOfOrder(
	order *model.Order,
	provinceWarehouseMapping map[string](*model.Warehouse)) {
	if order.CustomerProvinceCode == "" {
		return
	}

	if warehouse, ok := provinceWarehouseMapping[order.CustomerProvinceCode]; ok {
		query := &model.Order{
			OrderID: order.OrderID,
		}
		updater := &model.Order{
			WarehouseCode: warehouse.Code,
			WarehouseName: warehouse.Name,
		}

		model.OrderDB.UpdateOne(query, updater)
	}
}

func createProvinceWarehouseMapping(warehousesList []*model.Warehouse) map[string](*model.Warehouse) {

	provinceWarehouseMapping := make(map[string]*model.Warehouse)

	for _, warehouse := range warehousesList {
		if warehouse == nil || warehouse.Areas == nil {
			continue
		}
		for _, area := range *warehouse.Areas {
			provinceWarehouseMapping[area] = warehouse
		}
	}

	return provinceWarehouseMapping
}
