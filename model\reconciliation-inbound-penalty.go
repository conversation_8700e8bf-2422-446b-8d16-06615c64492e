package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var ReconciliationInboundPenaltyDB = &db.Instance{
	ColName:        "reconciliation_inbound_penalty",
	TemplateObject: &ReconciliationInboundPenalty{},
}

type ReconciliationInboundPenalty struct {
	ID              *primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// for reference
	Sku             string     `json:"sku,omitempty" bson:"sku,omitempty"`
	WarehouseCode   string     `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	InboundCode     string     `json:"inboundCode,omitempty" bson:"inbound_code,omitempty"`
	InboundTime     *time.Time `json:"inboundTime,omitempty" bson:"inbound_time,omitempty"`
	PenaltyTimeline int        `json:"penaltyTimeline,omitempty" bson:"penalty_timeline,omitempty"`

	ReconcileTimeIndex string `json:"reconcileTimeIndex,omitempty" bson:"reconcile_time_index,omitempty"`
	AvailableQuantity  int64  `json:"availableQuantity,omitempty" bson:"available_quantity,omitempty"`
	PenaltyFee         int    `json:"penaltyFee,omitempty" bson:"penalty_fee,omitempty"`
}

func InitReconciliationInboundPenaltyModel(s *mongo.Database) {
	ReconciliationInboundPenaltyDB.ApplyDatabase(s)
}
