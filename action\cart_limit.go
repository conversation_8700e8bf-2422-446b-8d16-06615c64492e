package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetCartLimit ...
func GetCartLimit(query *model.CartLimit) *common.APIResponse {
	return model.CartLimitDB.QueryOne(query)
}

// GetCartLimitList is func get list category
func GetCartLimitList(query *model.CartLimit, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.CartLimitDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.CartLimitDB.Count(query)
		result.Total = countResult.Total
	}

	return result
}

// CreateCartLimit is func create new category
func CreateCartLimit(input *model.CartLimit) *common.APIResponse {
	input.Code = model.GenCodeWithTime()
	return model.CartLimitDB.Create(input)
}

// UpdateCartLimit ...
func UpdateCartLimit(code string, updateData *model.CartLimitUpdate) *common.APIResponse {
	if len(code) <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy mã cài đặt",
			ErrorCode: "CODE_MISSING",
		}
	}
	qResult := model.CartLimitDB.QueryOne(&model.CartLimit{
		Code: code,
	})

	if qResult.Status != common.APIStatus.Ok {
		return qResult
	}

	return model.CartLimitDB.UpdateOne(&model.CartLimit{
		Code: code,
	}, updateData)
}
