package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// OrderBillAudit ...
type OrderBillAudit struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	OrderedAt       *time.Time         `json:"orderedAt,omitempty" bson:"ordered_at,omitempty"`

	BillId         int64  `json:"billId,omitempty" bson:"bill_id,omitempty"`
	BillCode       string `json:"billCode,omitempty" bson:"bill_code,omitempty"`
	CustomerID     int64  `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	CustomerCode   string `json:"customerCode,omitempty" bson:"customer_code,omitempty"`
	SaleOrderCode  string `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	OrderID        int64  `json:"orderID,omitempty" bson:"order_id,omitempty"`
	OrderCode      string `json:"orderCode,omitempty" bson:"order_code,omitempty"`
	ActionAt       int64  `json:"actionAt,omitempty" bson:"action_at,omitempty"`
	Retry          int64  `json:"retry,omitempty" bson:"retry,omitempty"`
	Status         string `json:"status,omitempty" bson:"status,omitempty"`
	OrderStatus    string `json:"orderStatus,omitempty" bson:"order_status,omitempty"`
	SoStatus       string `json:"soStatus,omitempty" bson:"so_status,omitempty"`
	BillStatus     string `json:"billStatus,omitempty" bson:"bill_status,omitempty"`
	Note           string `json:"note,omitempty" bson:"note,omitempty"`
	TotalOrderBill int    `json:"totalOrderBill,omitempty" bson:"total_order_bill,omitempty"` // accounting sum
	TotalOrder     int    `json:"totalOrder,omitempty" bson:"total_order,omitempty"`
	TotalOrderWms  int    `json:"totalOrderWms,omitempty" bson:"total_order_wms,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// OrderBillAuditDB ...
var OrderBillAuditDB = &db.Instance{
	ColName:        "order_bill_audit",
	TemplateObject: &OrderBillAudit{},
}

// InitOrderBillAuditModel ...
func InitOrderBillAuditModel(s *mongo.Database) {
	OrderBillAuditDB.ApplyDatabase(s)

	t := true
	_ = OrderBillAuditDB.CreateIndex(bson.D{
		bson.E{Key: "order_id", Value: 1},
		bson.E{Key: "order_status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	_ = OrderBillAuditDB.CreateIndex(bson.D{
		bson.E{Key: "order_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = OrderBillAuditDB.CreateIndex(bson.D{
		bson.E{Key: "action_at", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = OrderBillAuditDB.CreateIndex(bson.D{
		bson.E{Key: "ordered_at", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = OrderBillAuditDB.CreateIndex(bson.D{
		primitive.E{Key: "order_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = OrderBillAuditDB.CreateIndex(bson.D{
		primitive.E{Key: "status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
