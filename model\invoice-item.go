package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

type InvoiceItem struct {
	OrderItem `json:",inline" bson:",inline"`

	InvoiceID   int64  `json:"invoiceID" bson:"invoice_id,omitempty"`
	InvoiceCode string `json:"invoiceCode" bson:"invoice_code,omitempty"`
}

// InvoiceItemDB represent DB repo of this model
var InvoiceItemDB = &db.Instance{
	ColName:        "invoice_item",
	TemplateObject: &InvoiceItem{},
}

func InitInvoiceItemDBModel(s *mongo.Database) {
	InvoiceItemDB.ApplyDatabase(s)
}
