package migrate

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateOrderDetails() *common.APIResponse {
	fmt.Println("MigrateOrderDetails START")
	defer fmt.Println("MigrateOrderDetails FINISH")

	var limit int64 = 100
	updatedCount := 0
	cursor := primitive.NewObjectIDFromTimestamp(time.Date(2000, 01, 01, 0, 0, 0, 0, utils.VNTimeZone))

	for {
		ordersRes := model.OrderDB.Query(bson.M{
			"_id": bson.M{
				"$gt": cursor,
			},
		}, 0, limit, &primitive.M{"_id": 1})
		if ordersRes.Status != common.APIStatus.Ok {
			break
		}

		orders := ordersRes.Data.([]*model.Order)
		for _, order := range orders {
			action.SyncOrderDetail(*order)
			updatedCount++
		}

		cursor = *orders[len(orders)-1].ID
		// fmt.Println("MigrateOrderDetails", orders[len(orders)-1].CreatedTime, orders[len(orders)-1].ID)
		if updatedCount != 0 && updatedCount%200000 <= int(limit) {
			fmt.Println("MigrateOrderDetails updated", updatedCount)
		}

		time.Sleep(time.Millisecond * 50)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateOrderDetails finish",
	}
}

func MigrateChangeNonVATOrderItem(orderIDs []int64) *common.APIResponse {
	if len(orderIDs) == 0 || len(orderIDs) > 1000 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "invalid orderIDs length",
			ErrorCode: "INVALID_PAYLOAD",
		}
	}
	orderResp := model.OrderDB.Query(bson.M{
		"order_id": bson.M{
			"$in": orderIDs,
		},
	}, 0, 1000, nil)
	orders := orderResp.Data.([]*model.Order)
	for _, order := range orders {
		if order.Status == enum.OrderState.Canceled || order.Status == enum.OrderState.Completed {
			continue
		}

		orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ToolChangeNonVATOrderItem")
		if orderItemPartitionDB == nil {
			continue
		}

		orderItemResp := orderItemPartitionDB.Query(&model.OrderItem{
			OrderID: order.OrderID,
		}, 0, 0, nil)
		if orderItemResp.Status == common.APIStatus.Ok {
			items := orderItemResp.Data.([]*model.OrderItem)

			isChanged := false
			for _, item := range items {

				oldData := model.OrderItem{
					SellerCode: item.SellerCode,
					Sku:        item.Sku,
					ItemCode:   item.ItemCode,
				}

				if item.OldData != nil {
					oldData = *item.OldData
				}

				if oldData.SellerCode != "MEDX" {
					continue
				}

				if item.Skus == nil || len(*item.Skus) == 0 {
					if utils.IsContains(item.Tags, "HOADONNHANH") {
						continue
					}

					// only check sku don't have tag HOADONNHANH

					sku := client.Services.Product.GetSkuByLocation(client.SKU{
						SellerCode:    "MED_NT",
						ProductID:     item.ProductID,
						LocationCodes: []string{order.ProvinceCode},
					})

					if sku == nil {
						sku = client.Services.Product.GetSkuByLocation(client.SKU{
							SellerCode: "MED_NT",
							ProductID:  item.ProductID,
							//LocationCodes: []string{order.ProvinceCode},
						})
					}

					if sku != nil {
						update := model.OrderItem{
							OldData: &model.OrderItem{
								SellerCode:      oldData.SellerCode,
								Sku:             oldData.Sku,
								ItemCode:        oldData.ItemCode,
								LastUpdatedTime: utils.ParseTimeToPointer(time.Now()),
							},
							SellerCode: sku.SellerCode,
							Sku:        sku.Code,
							ItemCode:   sku.ItemCode,
						}
						isChanged = true
						updateResp := orderItemPartitionDB.UpdateOne(bson.M{"_id": item.ID}, update)
						if updateResp.Status != common.APIStatus.Ok {
							continue
						}
					}
				} else {
					// TODO: logic for combo
					mapSubItems := make(map[string]*model.OrderItem)
					if item.SubItems != nil {
						for _, ele := range *item.SubItems {
							mapSubItems[ele.Sku] = ele
							if ele.OldData != nil {
								mapSubItems[ele.OldData.Sku] = ele
							}
						}
					}
					for _, ele := range *item.Skus {

						if utils.IsContains(item.Tags, "HOADONNHANH") {
							continue
						}

						sku := client.Services.Product.GetSkuByLocation(client.SKU{
							ProductID:     ele.ProductID,
							SellerCode:    "MED_NT",
							LocationCodes: []string{order.ProvinceCode},
						})

						if sku == nil {
							sku = client.Services.Product.GetSkuByLocation(client.SKU{
								ProductID:  ele.ProductID,
								SellerCode: "MED_NT",
								//LocationCodes: []string{order.ProvinceCode},
							})
						}

						if sku != nil {
							if ele.OldSKU == "" {
								ele.OldSKU = ele.SKU
							}
							ele.SKU = sku.Code
							if subItem, ok := mapSubItems[ele.OldSKU]; ok {
								oldData := model.OrderItem{
									SellerCode: subItem.SellerCode,
									Sku:        subItem.Sku,
									ItemCode:   subItem.ItemCode,
								}

								if subItem.OldData != nil {
									oldData = *subItem.OldData
								}

								if oldData.SellerCode != "MEDX" {
									continue
								}

								oldData.LastUpdatedTime = utils.ParseTimeToPointer(time.Now())
								subItem.OldData = &oldData
								subItem.SellerCode = sku.SellerCode
								subItem.Sku = sku.Code
								subItem.ItemCode = sku.ItemCode

								isChanged = true
							}
						}
					}
					if isChanged {
						update := model.OrderItem{
							Skus:     item.Skus,
							SubItems: item.SubItems,
						}
						updateResp := orderItemPartitionDB.UpdateOne(bson.M{"_id": item.ID}, update)
						if updateResp.Status != common.APIStatus.Ok {
							continue
						}
					}
				}
			}

			if isChanged {
				action.SyncOrderDetail(*order)
			}

			fmt.Println("orderID: ", order.OrderID)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
	}
}

func ToolRevertNonVATOrderItem(orderIDs []int64) *common.APIResponse {
	if len(orderIDs) == 0 || len(orderIDs) > 1000 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "invalid orderIDs length",
			ErrorCode: "INVALID_PAYLOAD",
		}
	}
	orderResp := model.OrderDB.Query(bson.M{
		"order_id": bson.M{
			"$in": orderIDs,
		},
	}, 0, 1000, nil)
	orders := orderResp.Data.([]*model.Order)
	for _, order := range orders {

		orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ToolRevertNonVATOrderItem")
		if orderItemPartitionDB == nil {
			continue
		}

		orderItemResp := orderItemPartitionDB.Query(&model.OrderItem{
			OrderID: order.OrderID,
		}, 0, 0, nil)
		if orderItemResp.Status == common.APIStatus.Ok {
			items := orderItemResp.Data.([]*model.OrderItem)

			isChanged := false
			for _, item := range items {

				if item.Skus == nil || len(*item.Skus) == 0 {

					if item.OldData == nil {
						continue
					}
					oldData := item.OldData

					update := model.OrderItem{
						SellerCode: oldData.SellerCode,
						Sku:        oldData.Sku,
						ItemCode:   oldData.ItemCode,
					}

					isChanged = true

					updateResp := orderItemPartitionDB.UpdateOne(bson.M{"_id": item.ID}, update)
					if updateResp.Status != common.APIStatus.Ok {
						continue
					}

				} else {
					// TODO: logic for combo
					mapSubItems := make(map[string]*model.OrderItem)
					if item.SubItems != nil {
						for _, ele := range *item.SubItems {
							mapSubItems[ele.Sku] = ele
							if ele.OldData != nil {
								mapSubItems[ele.OldData.Sku] = ele
							}
						}
					}
					for _, ele := range *item.Skus {
						if ele.OldSKU == "" {
							continue
						}

						ele.SKU = ele.OldSKU
						if subItem, ok := mapSubItems[ele.OldSKU]; ok {

							if subItem.OldData == nil {
								continue
							}
							oldData := subItem.OldData

							subItem.SellerCode = oldData.SellerCode
							subItem.Sku = oldData.Sku
							subItem.ItemCode = oldData.ItemCode

							isChanged = true
						}
					}
					if isChanged {
						update := model.OrderItem{
							Skus:     item.Skus,
							SubItems: item.SubItems,
						}
						updateResp := orderItemPartitionDB.UpdateOne(bson.M{"_id": item.ID}, update)
						if updateResp.Status != common.APIStatus.Ok {
							continue
						}
					}
				}
			}

			if isChanged {
				action.SyncOrderDetail(*order)
			}

			fmt.Println("orderID: ", order.OrderID)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
	}
}

// func MigrateOrderSkusSellerCodes() *common.APIResponse {
// 	fmt.Println("MigrateOrderSkusSellerCodes START")
// 	defer fmt.Println("MigrateOrderSkusSellerCodes FINISH")

// 	var limit int64 = 100
// 	updatedCount := 0
// 	cursor := primitive.NewObjectIDFromTimestamp(time.Date(2000, 01, 01, 0, 0, 0, 0, utils.VNTimeZone))

// 	for {
// 		ordersRes := model.OrderDB.Query(bson.M{
// 			"_id": bson.M{
// 				"$gt": cursor,
// 			},
// 		}, 0, limit, &primitive.M{"_id": 1})
// 		if ordersRes.Status != common.APIStatus.Ok {
// 			break
// 		}

// 		orders := ordersRes.Data.([]*model.Order)
// 		for _, order := range orders {
// 			if len(order.Skus) != 0 && len(order.SellerCodes) != 0 && len(order.ItemCodes) != 0 {
// 				continue
// 			}

// 			orderItems := getOrderItems(order.OrderID)
// 			if len(orderItems) == 0 {
// 				continue
// 			}

// 			updater := model.Order{}
// 			skus, itemCodes, sellerCodes := action.GetOrderDistinctInfo(orderItems)

// 			if len(order.Skus) == 0 {
// 				updater.Skus = skus
// 			}

// 			if len(order.ItemCodes) == 0 {
// 				updater.ItemCodes = itemCodes
// 			}

// 			if len(order.SellerCodes) == 0 {
// 				updater.SellerCodes = sellerCodes
// 			}

// 			if order.SystemDisplay == "" {
// 				updater.SystemDisplay = "BUYMED"
// 			}

// 			model.OrderDB.UpdateOne(&model.Order{
// 				ID: order.ID,
// 			}, &updater)
// 			updatedCount++
// 		}

// 		cursor = *orders[len(orders)-1].ID
// 		// fmt.Println("MigrateOrderSkusSellerCodes", orders[len(orders)-1].CreatedTime, orders[len(orders)-1].ID)
// 		if updatedCount != 0 && updatedCount%200000 <= int(limit) {
// 			fmt.Println("MigrateOrderSkusSellerCodes updated", updatedCount)
// 		}
// 	}

// 	return &common.APIResponse{
// 		Status:  common.APIStatus.Ok,
// 		Message: "MigrateOrderSkusSellerCodes finish",
// 	}
// }
