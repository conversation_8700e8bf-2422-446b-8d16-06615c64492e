package action

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/panjf2000/ants/v2"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

func OrderCalcFormat(orderID int64) *common.APIResponse {
	queryOrder := model.OrderDB.QueryOne(&model.Order{
		OrderID: orderID,
	})
	if queryOrder.Status != common.APIStatus.Ok {
		return queryOrder
	}
	order := queryOrder.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "OrderCalcFormat")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	queryOrderItem := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID: orderID,
	}, 0, 0, nil)
	if queryOrderItem.Status != common.APIStatus.Ok {
		return queryOrderItem
	}
	orderItems := queryOrderItem.Data.([]*model.OrderItem)
	items := make([]*model.OrderCalcItemRequest, 0)
	for _, item := range orderItems {
		items = append(items, &model.OrderCalcItemRequest{
			Sku:      item.Sku,
			Quantity: item.Quantity,
		})
	}
	return &common.APIResponse{
		ErrorCode: common.APIStatus.Ok,
		Message:   "",
		Data: []*model.OrderCalcAmountRequest{
			&model.OrderCalcAmountRequest{
				OrderID:    orderID,
				OrderItems: items,
			},
		},
	}

}

func OrderCalcAmount(input *model.OrderCalcAmountRequest) *common.APIResponse {
	t := time.Now()
	result := orderCalcAmount(input)
	if result.Status != common.APIStatus.Ok {
		go func(res *common.APIResponse) {
			_ = model.OrderCalcPriceLog.Create(map[string]interface{}{
				"action": func() string {
					if res.Message == "Ok" {
						return "VIEW"
					}
					return "CALC"
				}(),
				"order_id":    input.OrderID,
				"result":      result,
				"input":       input,
				"time":        time.Since(t).Milliseconds(),
				"number_line": len(input.OrderItems),
			})
		}(result)
	}
	return result
}

func getPercentSellerValue(order *model.Order, sellerCodes []string, storeCode string, applySku, notApplySku []string) float64 {
	total := 0
	deliveryPrice := 0
	mapApplySku := make(map[string]bool)
	mapNotApplySku := make(map[string]bool)
	for _, sku := range applySku {
		mapApplySku[sku] = true
	}
	for _, sku := range notApplySku {
		mapNotApplySku[sku] = true
	}
	isMatchSeller := func(sellerCode string) bool {
		if len(sellerCodes) == 0 {
			return true
		}
		for _, code := range sellerCodes {
			if sellerCode == code {
				return true
			}
		}
		return false
	}
	if len(applySku) != 0 || len(notApplySku) != 0 {
		if len(applySku) != 0 {
			for _, item := range order.Items {
				if mapApplySku[item.Sku] {
					total += item.Price * item.Quantity
					if item.NotionalQuantity != nil {
						deliveryPrice += item.Price * *item.NotionalQuantity
					}
				}
			}
		} else if len(notApplySku) != 0 {
			for _, item := range order.Items {
				if !mapNotApplySku[item.Sku] && isMatchSeller(item.SellerCode) {
					if !mapNotApplySku[item.Sku] {
						if storeCode != "" {
							if item.StoreCode == storeCode {
								total += item.Price * item.Quantity
								if item.NotionalQuantity != nil {
									deliveryPrice += item.Price * *item.NotionalQuantity
								}
							}
						} else {
							if isMatchSeller(item.SellerCode) {
								total += item.Price * item.Quantity
								if item.NotionalQuantity != nil {
									deliveryPrice += item.Price * *item.NotionalQuantity
								}
							}
						}
					}
				}
			}
		}
	} else {
		for _, item := range order.Items {
			if storeCode != "" {
				if item.StoreCode == storeCode {
					total += item.Price * item.Quantity
					if item.NotionalQuantity != nil {
						deliveryPrice += item.Price * *item.NotionalQuantity
					}
				}
			} else {
				if isMatchSeller(item.SellerCode) {
					total += item.Price * item.Quantity
					if item.NotionalQuantity != nil {
						deliveryPrice += item.Price * *item.NotionalQuantity
					}
				}
			}
		}
	}

	return float64(deliveryPrice) / float64(total)
}

func getPercentSellerValueReCalc(order *model.Order, orderLines []*model.OrderCalcItemResponse, sellerCodes []string, storeCode string, applySku, notApplySku []string) float64 {
	total := 0
	deliveryPrice := 0
	mapApplySku := make(map[string]bool)
	mapNotApplySku := make(map[string]bool)
	for _, sku := range applySku {
		mapApplySku[sku] = true
	}
	for _, sku := range notApplySku {
		mapNotApplySku[sku] = true
	}
	isMatchSeller := func(sellerCode string) bool {
		if len(sellerCodes) == 0 {
			return true
		}
		for _, code := range sellerCodes {
			if sellerCode == code {
				return true
			}
		}
		return false
	}
	if len(applySku) != 0 || len(notApplySku) != 0 {
		if len(applySku) != 0 {
			for _, item := range orderLines {
				if mapApplySku[item.Sku] {
					total += item.Price * item.Quantity
					deliveryPrice += item.Price * item.OutboundQuantity
				}
			}
		} else if len(notApplySku) != 0 {
			for _, item := range orderLines {
				if !mapNotApplySku[item.Sku] {
					if storeCode != "" {
						if item.StoreCode == storeCode {
							total += item.Price * item.Quantity
							deliveryPrice += item.Price * item.OutboundQuantity
						}
					} else {
						if isMatchSeller(item.SellerCode) {
							total += item.Price * item.Quantity
							deliveryPrice += item.Price * item.OutboundQuantity
						}
					}
				}
			}
		}
	} else {
		for _, item := range orderLines {
			if storeCode != "" {
				if item.StoreCode == storeCode {
					total += item.Price * item.Quantity
					deliveryPrice += item.Price * item.OutboundQuantity
				}
			} else {
				if isMatchSeller(item.SellerCode) {
					total += item.Price * item.Quantity
					deliveryPrice += item.Price * item.OutboundQuantity
				}
			}
		}
	}

	return float64(deliveryPrice) / float64(total)
}

// OrderCalcAmount ...
func orderCalcAmount(input *model.OrderCalcAmountRequest) *common.APIResponse {
	// check duplicate sku
	mapCheckDup := make(map[string]int)
	for _, item := range input.OrderItems {
		key := fmt.Sprintf(keyMatchItem, item.Sku, item.Type)
		if check, ok := mapCheckDup[key]; ok && check >= 1 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "DUPLICATE_ITEM",
				Message:   fmt.Sprintf("SKU %s duplicate", item.Sku),
			}
		}
		mapCheckDup[key] = 1
	}

	// query order -> check status
	orderQuery := model.OrderDB.QueryOne(&model.Order{
		OrderID: input.OrderID,
	})
	if orderQuery.Status != common.APIStatus.Ok {
		return orderQuery
	}

	order := orderQuery.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "orderCalcAmount")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	if len(order.SaleOrderCode) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "ORDER_MISSING_SO",
			Message:   "Order don't has sale order code (so)",
		}
	}

	t1 := time.Date(2021, time.November, 1, 0, 0, 0, 0, time.UTC)
	t := t1.Add(-7 * time.Hour)
	isSkip := false

	if !order.CreatedTime.Before(t) && order.Status != enum.OrderState.Processing && order.Status != enum.OrderState.WaitToDeliver && order.Status != enum.OrderState.Delivering {
		if order.TotalNotionalPrice == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "ORDER_NOT_CALC",
				Message:   "Order don't calc price before",
			}
		}
		isSkip = true
	}

	orderItemQuery := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID: order.OrderID,
	}, 0, 0, nil)

	if orderItemQuery.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "ORDER_EMPTY",
			Message:   "Order empty",
		}
	}

	orderItems := orderItemQuery.Data.([]*model.OrderItem)
	order.Items = orderItems

	if isSkip {
		return &common.APIResponse{
			Status:    common.APIStatus.Ok,
			ErrorCode: common.APIStatus.Ok,
			Message:   "Ok",
			Data: []*model.Order{
				order,
			},
		}
	}

	isItemWithNoType := false
	if len(input.OrderItems) > 0 {
		if len(input.OrderItems[0].Type) == 0 {
			isItemWithNoType = true
		}
	}
	mapOrderItems := make(map[string]*model.OrderItem)
	for _, item := range orderItems {
		key := fmt.Sprintf(keyMatchItem, item.Sku, item.Type)
		if isItemWithNoType {
			key = fmt.Sprintf(keyMatchItem, item.Sku, "")
		}
		mapOrderItems[key] = item
	}

	match := true
	mapOrderLine := make([]*model.OrderCalcItemResponse, 0)
	for _, line := range input.OrderItems {
		key := fmt.Sprintf(keyMatchItem, line.Sku, line.Type)
		orderLine := &model.OrderCalcItemResponse{
			Sku:  line.Sku,
			Type: line.Type,
		}
		item, ok := mapOrderItems[key]
		if !ok {
			match = false
			orderLine.IsError = true
			orderLine.Note = "Order line not match"
		} else {
			orderLine.Quantity = item.Quantity
			orderLine.OutboundQuantity = line.Quantity
			orderLine.Price = item.Price
			orderLine.SellerCode = item.SellerCode
			orderLine.StoreCode = item.StoreCode

			if line.Quantity > item.Quantity {
				match = false
				orderLine.IsError = true
				orderLine.Note = fmt.Sprintf("Order outbound quantity %d greater current quantity %d", line.Quantity, item.Quantity)
			}
		}

		mapOrderLine = append(mapOrderLine, orderLine)
	}

	if !match {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "ORDER_ITEM_NOT_MATCH",
			Message:   "Order item not match",
			Data:      mapOrderLine,
		}
	}

	return handleCalcAmount(order, mapOrderLine, input.DoCode, input.IsSplitDeliveryOrder, input.ReCalcDiscountAmount)
}

func handleCalcAmount(order *model.Order, orderLines []*model.OrderCalcItemResponse, doCode string, isSplitDO, reCalcDiscount bool) *common.APIResponse {
	t := time.Now()
	fee, discount, totalPrice := int64(0), 0, 0
	if order.PaymentMethodFee != nil {
		fee = fee + *order.PaymentMethodFee
	}
	if order.DeliveryMethodFee != nil {
		fee = fee + *order.DeliveryMethodFee
	}

	if order.ExtraFee != nil {
		fee = fee + *order.ExtraFee
	}

	if order.TotalDiscount != nil {
		discount = *order.TotalDiscount
	}

	totalFee := int(fee) - discount

	for idx, item := range orderLines {
		itemPrice := item.Price * item.OutboundQuantity
		orderLines[idx].TotalPrice = itemPrice
		orderLines[idx].CreatedAt = &t
		totalPrice = totalPrice + itemPrice
	}

	finalPrice := totalPrice + totalFee
	ZERO := 0

	updater := &model.Order{
		NotionalPrice:      &totalPrice,
		TotalNotionalPrice: &finalPrice,
		NotionalPriceTime:  &t,
		Cod:                &ZERO,
	}

	if !IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, order.PaymentMethod) && order.PaymentMethod != "PAYMENT_METHOD_CREDIT" {
		updater.Cod = &finalPrice
	}

	updateOrderRs := model.OrderDB.UpdateOne(&model.Order{
		OrderID: order.OrderID,
		//Status:  enum.OrderState.Processing,
	}, updater)

	if updateOrderRs.Status != common.APIStatus.Ok {
		return updateOrderRs
	}

	latestOrder := updateOrderRs.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(latestOrder, "handleCalcAmount")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	isError := false
	if len(orderLines) > 100 {
		var wg sync.WaitGroup
		errs := make([]*common.APIResponse, 0)
		p, _ := ants.NewPoolWithFunc((len(orderLines)/4 + 50), func(updater interface{}) {
			es := updateOrderItemFunc(updater.(*model.OrderItem))
			if es.Status != common.APIStatus.Ok {
				errs = append(errs, es)
			}
			wg.Done()
		})
		defer p.Release()
		for _, item := range orderLines {
			updater := &model.OrderItem{
				NotionalQuantity: &item.OutboundQuantity,
				NotionalPrice:    &item.TotalPrice,
				OrderID:          order.OrderID,
				Sku:              item.Sku,
				Type:             enum.ItemTypeValue(item.Type),
			}
			wg.Add(1)
			_ = p.Invoke(updater)
		}
		wg.Wait()
		if len(errs) > 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Update order item failed",
				Data:    errs,
			}
		}
	} else {
		for _, item := range orderLines {
			updater := &model.OrderItem{
				NotionalQuantity: &item.OutboundQuantity,
				NotionalPrice:    &item.TotalPrice,
			}
			updateItemRs := orderItemPartitionDB.UpdateOne(&model.OrderItem{
				OrderID: order.OrderID,
				Sku:     item.Sku,
				Type:    enum.ItemTypeValue(item.Type),
			}, updater)
			if updateItemRs.Status != common.APIStatus.Ok {
				isError = true
			}
		}
	}

	queryItemRs := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID: order.OrderID,
	}, 0, 0, nil)

	if queryItemRs.Status != common.APIStatus.Ok {
		return queryItemRs
	}

	if isError {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Update order item failed",
		}
	}
	latestOrder.Items = queryItemRs.Data.([]*model.OrderItem)
	// handle split discount
	var history *model.CalOrderDiscountHistory
	totalVoucherAmount := int64(0)
	totalPaymentAmount := int64(0)
	if !reCalcDiscount {
		qHistory := model.CalOrderDiscountHistoryDB.QueryOne(&bson.M{
			"order_id":            order.OrderID,
			"delivery_order_code": bson.M{"$ne": doCode},
		})
		if qHistory.Status == common.APIStatus.Ok {
			history = qHistory.Data.([]*model.CalOrderDiscountHistory)[0]
		}
	}
	p := float64(1)
	if order.Price != nil && *order.Price != 0 {
		p = float64(totalPrice) / float64(*order.Price) // default percent
	}
	if !isSplitDO {
		p = 1
	}

	newHistory := &model.CalOrderDiscountHistory{
		OrderID:           order.OrderID,
		DeliveryOrderCode: doCode,
	}
	newHistory.VoucherAmountDetailMap = make(map[string]int64)
	newHistory.VoucherAmountDetails = make([]*model.VoucherAmountDetail, 0)
	for _, item := range order.RedeemApplyResult {
		vDiscountAmount := int64(0)
		if item.DiscountValue != 0 {
			if history != nil && history.VoucherAmountDetailMap != nil {
				vDiscountAmount = int64(item.DiscountValue) - history.VoucherAmountDetailMap[item.Code]
			} else {
				if len(item.SellerCodes) > 0 {
					pSeller := float64(0)
					if !isSplitDO {
						pSeller = 1
					} else {
						if reCalcDiscount {
							pSeller = getPercentSellerValueReCalc(latestOrder, orderLines, item.SellerCodes, item.StoreCode, item.ApplySkus, item.NotApplySkus)
						} else {
							pSeller = getPercentSellerValue(latestOrder, item.SellerCodes, item.StoreCode, item.ApplySkus, item.NotApplySkus)
						}
					}
					vDiscountAmount = utils.Ceil(int64(pSeller * float64(item.DiscountValue)))
				} else {
					vDiscountAmount = utils.Ceil(int64(p * float64(item.DiscountValue)))
				}
			}
		}
		totalVoucherAmount += vDiscountAmount
		newHistory.VoucherAmountDetailMap[item.Code] = vDiscountAmount
		if vDiscountAmount != 0 {
			newHistory.VoucherAmountDetails = append(newHistory.VoucherAmountDetails, &model.VoucherAmountDetail{
				Code:           item.Code,
				DiscountAmount: vDiscountAmount,
				SellerCodes:    item.SellerCodes,
				StoreCode:      item.StoreCode,
			})
		}
	}
	newHistory.VoucherAmount = totalVoucherAmount

	if order.PaymentMethodFee != nil {
		if history != nil {
			totalPaymentAmount = int64(-*order.PaymentMethodFee) - history.PaymentAmount
		} else {
			totalPaymentAmount = utils.Ceil(int64(p * float64(-*order.PaymentMethodFee)))
		}
		newHistory.PaymentAmount = totalPaymentAmount
	}
	qUpsertHistory := model.CalOrderDiscountHistoryDB.Upsert(&bson.M{
		"order_id":            order.OrderID,
		"delivery_order_code": doCode,
	}, newHistory)
	if qUpsertHistory.Status != common.APIStatus.Ok {
		return qUpsertHistory
	}
	latestOrder.VoucherAmount = totalVoucherAmount
	latestOrder.PaymentMethodDiscountAmount = totalPaymentAmount

	updateOrderRs.Data = []*model.Order{
		latestOrder,
	}
	return updateOrderRs
}

func handleCalcCompletedAmount(order *model.Order, orderLines map[string]*model.OrderItem) {
	fee, discount, totalPrice := int64(0), 0, 0
	if order.IsSplitDeliveryOrder != nil && *order.IsSplitDeliveryOrder {
		if order.ExtraFee != nil {
			fee = fee + *order.ExtraFee
		}
		for _, do := range order.DeliveryOrderStatuses {
			if do.Status == enum.OrderState.Completed {
				discount = discount + do.PaymentDiscount + do.VoucherDiscount
			}
		}
	} else {
		if order.PaymentMethodFee != nil {
			fee = fee + *order.PaymentMethodFee
		}
		if order.DeliveryMethodFee != nil {
			fee = fee + *order.DeliveryMethodFee
		}

		if order.ExtraFee != nil {
			fee = fee + *order.ExtraFee
		}

		if order.PartnerPaymentMethod != nil && order.PartnerPaymentMethod.TotalCustomerFee > 0 {
			fee += order.PartnerPaymentMethod.TotalCustomerFee
		}

		if order.TotalDiscount != nil {
			discount = *order.TotalDiscount
		}
	}

	totalFee := int(fee) - discount

	ZERO := 0
	for _, item := range orderLines {
		if item.CompletedQuantity == nil {
			item.ActualPrice = &ZERO
			item.ActualSellerPrice = &ZERO
			continue
		}
		itemPrice := item.Price * *item.CompletedQuantity
		itemSellerPrice := item.SellerPrice * *item.CompletedQuantity
		if item.ReturnedQuantity != nil {
			itemPrice = item.Price * (*item.OutboundQuantity - *item.ReturnedQuantity)
			itemSellerPrice = item.SellerPrice * (*item.OutboundQuantity - *item.ReturnedQuantity)
		}
		item.ActualPrice = &itemPrice
		item.ActualSellerPrice = &itemSellerPrice
		totalPrice = totalPrice + itemPrice
	}

	finalPrice := totalPrice + totalFee
	order.ActualTotalPrice = &finalPrice
	order.ActualPrice = &totalPrice
}
