package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

// BrandCartItemDB ...
var BrandCartItemDB = &db.Instance{
	ColName:        "brand_cart_item",
	TemplateObject: &CartItem{},
}

// BrandCartItemDeletedDB ...
var BrandCartItemDeletedDB = &db.Instance{
	ColName:        "brand_cart_item_deleted",
	TemplateObject: &CartItem{},
}

// InitBrandCartItemModel is func init model sale cart
func InitBrandCartItemModel(s *mongo.Database) {
	BrandCartItemDB.ApplyDatabase(s)

	// t := true
	// _ = BrandCartItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "cart_no", Value: 1},
	// 	primitive.E{Key: "cart_id", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "type", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = BrandCartItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "cart_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// t := true
	// _ = BrandCartItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "cart_id", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "type", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = BrandCartItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = BrandCartItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: -1},
	// 	primitive.E{Key: "_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = BrandCartItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "last_updated_time", Value: -1},
	// 	primitive.E{Key: "_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

// InitCartDeletedModel is func init model sale cart deleted
func InitBrandCartItemDeletedModel(s *mongo.Database) {
	BrandCartItemDeletedDB.ApplyDatabase(s)
}
