package reconcile_action

import (
	"fmt"
	"log"
	"strconv"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func fetchOrders(wg *sync.WaitGroup, mu *sync.Mutex, mapOrderIds map[int64]string, complexQuery *bson.M, offset, limit int64, orderStatus enum.OrderStateValue) {
	defer wg.Done()
	for {
		ordersCompletedRes := model.OrderDB.Query(model.Order{
			Status:       orderStatus,
			ComplexQuery: []*bson.M{complexQuery},
		}, offset, limit, &primitive.M{"_id": 1})
		if ordersCompletedRes.Status != common.APIStatus.Ok {
			break
		}

		ordersCompleted := ordersCompletedRes.Data.([]*model.Order)
		for _, order := range ordersCompleted {
			mu.Lock()
			mapOrderIds[order.OrderID] = strconv.FormatInt(order.OrderID, 10)
			mu.Unlock()
		}

		offset += limit
	}
}

func GetOrderIDsReadyForReconciliation(from, to time.Time, nDays int) ([]int64, []string, error) {
	var (
		limit        int64 = 1000
		initCapacity int   = 5000
	)

	fromDelivered := from.AddDate(0, 0, -nDays)
	toDelivered := to.AddDate(0, 0, -nDays)

	mapOrderIds := make(map[int64]string, initCapacity)
	var (
		wg sync.WaitGroup
		mu sync.Mutex
	)
	wg.Add(3)
	go fetchOrders(&wg, &mu, mapOrderIds, &bson.M{"delivered_time": bson.M{"$gte": fromDelivered, "$lt": toDelivered}}, 0, limit, enum.OrderState.Delivered)
	go fetchOrders(&wg, &mu, mapOrderIds, &bson.M{"completed_time": bson.M{"$gte": from, "$lt": to}}, 0, limit, enum.OrderState.Completed)
	go fetchOrders(&wg, &mu, mapOrderIds, &bson.M{"completed_debt_time": bson.M{"$gte": from, "$lt": to}}, 0, limit, enum.OrderState.Completed)
	wg.Wait()

	completedOrderIDs, completedOrderIDsStr := utils.ConvertMapToSlices(mapOrderIds)
	return completedOrderIDs, completedOrderIDsStr, nil
}

func GetOrderIDsReadyForReconciliation2(from, to time.Time) ([]int64, []string, error) {

	// Get completed order ids, average 5000 orders daily
	mapOrderIds := make(map[int64]string, 5000)

	ordersReadyRes := model.OrderDB.Distinct(model.Order{
		Status: enum.OrderState.Completed,
		ComplexQuery: []*bson.M{
			{"completed_time": bson.M{
				"$gte": from,
				"$lt":  to,
			}},
		},
	}, "order_id")
	if ordersReadyRes.Status != common.APIStatus.Ok {
		log.Println("distinct " + model.OrderDB.ColName + "  failed: " + ordersReadyRes.Message)
	} else {
		data := ordersReadyRes.Data.([]interface{})
		completedOrderIDs, completedOrderIDsStr := utils.ConvertToSlices(data)
		for i, orderId := range completedOrderIDs {
			mapOrderIds[orderId] = completedOrderIDsStr[i]
		}
	}

	ordersReadyRes = model.OrderDB.Distinct(model.Order{
		Status: enum.OrderState.Completed,
		ComplexQuery: []*bson.M{
			{"completed_debt_time": bson.M{
				"$gte": from,
				"$lt":  to,
			}},
		},
	}, "order_id")
	if ordersReadyRes.Status != common.APIStatus.Ok {
		log.Println("distinct " + model.OrderDB.ColName + "  failed: " + ordersReadyRes.Message)
	} else {
		data := ordersReadyRes.Data.([]interface{})
		completedOrderIDs, completedOrderIDsStr := utils.ConvertToSlices(data)
		for i, orderId := range completedOrderIDs {
			mapOrderIds[orderId] = completedOrderIDsStr[i]
		}
	}

	completedOrderIDs, completedOrderIDsStr := utils.ConvertMapToSlices(mapOrderIds)

	return completedOrderIDs, completedOrderIDsStr, nil
}

func GetSellerReconcileOrderIDs(sellerCode string, completedOrderIDs []int64) ([]int64, error) {
	orderIDs := make([]int64, 0, 100)

	queryLimit := 100
	for start, end := 0, len(completedOrderIDs); start < end; start += queryLimit {
		iter := start + queryLimit
		if iter > end {
			iter = end
		}

		fSellerOrder := model.OrderDetail{
			ComplexQuery: []*bson.M{
				{"seller_codes": bson.M{"$in": []string{sellerCode}}},
				{
					"order_id": bson.M{
						"$in": completedOrderIDs[start:iter],
					},
				},
			},
		}
		distinctRes := model.OrderDetailDB.Distinct(fSellerOrder, "order_id")
		if distinctRes.Status != common.APIStatus.Ok {
			return nil, fmt.Errorf("%s: %s;", sellerCode, distinctRes.Message)
		}

		data, ok := distinctRes.Data.([]interface{})
		if !ok {
			return nil, fmt.Errorf("GetSellerReconcileOrderIDs assertion error")
		}

		for _, id := range data {
			if orderID, ok := id.(int64); ok {
				orderIDs = append(orderIDs, orderID)
			}
		}
	}

	return orderIDs, nil
}
