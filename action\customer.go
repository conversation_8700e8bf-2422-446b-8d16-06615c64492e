package action

import (
	"errors"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateAccumulateProduct(input *model.AccumulateProductRequest) *common.APIResponse {
	_ = model.AccumulateProductDB.Delete(&model.AccumulateProduct{CustomerID: input.CustomerID})
	limit := 500

	// get all order completed
	// build ordersCompletedMap
	ordersCompletedMap := make(map[string][]int64) // map by completedTime, productID
	cursor := primitive.NilObjectID
	for {
		ordersMap, id, err := getAllOrderCompleted(input, cursor, limit)
		if err != nil {
			break
		}

		for completedTime, productIDs := range ordersMap {
			if len(productIDs) == 0 {
				continue
			}

			if _, ok := ordersCompletedMap[completedTime]; !ok {
				ordersCompletedMap[completedTime] = make([]int64, 0)
			}

			ordersCompletedMap[completedTime] = append(ordersCompletedMap[completedTime], productIDs...)
		}

		cursor = id
		time.Sleep(10 * time.Millisecond)
	}

	// Step 2: Batch update the database by customer and completedTime
	for completedTime, productIDs := range ordersCompletedMap {
		model.AccumulateProductDB.Create(&model.AccumulateProduct{
			CustomerID:    input.CustomerID,
			Version:       completedTime,
			ProductIDs:    productIDs,
			CountProducts: len(productIDs),
		})
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

func getAllOrderCompleted(input *model.AccumulateProductRequest, cursor primitive.ObjectID, limit int) (map[string][]int64, primitive.ObjectID, error) {
	queryOrder := bson.M{
		"customer_id": input.CustomerID,
	}
	if cursor != primitive.NilObjectID {
		queryOrder["_id"] = bson.M{"$gt": cursor}
	} else {
		queryOrder["completed_time"] = bson.M{"$gte": input.DateFrom}
	}

	// let orders
	res := model.OrderDB.Query(queryOrder, 0, int64(limit), &primitive.M{"_id": 1})
	if res.Status != common.APIStatus.Ok {
		return nil, primitive.NilObjectID, errors.New("error when query order")
	}

	orders := res.Data.([]*model.Order)
	if len(orders) == 0 {
		return nil, primitive.NilObjectID, errors.New("end of order")
	}

	ordersMap := make(map[string][]int64)

	for _, order := range orders {
		if order.Status != enum.OrderState.Completed {
			continue
		}
		// todo check completed time in from to
		if order.CompletedTime == nil {
			continue
		}
		if order.CompletedTime.Before(*input.DateFrom) {
			continue
		}
		if order.CompletedTime.After(*input.DateTo) {
			continue
		}
		orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "triggerAccumulateProduct")
		if orderItemPartitionDB == nil {
			continue
		}

		orderItemResult := orderItemPartitionDB.Query(&model.OrderItem{
			OrderID: order.OrderID,
		}, 0, 0, nil)

		if orderItemResult.Status != common.APIStatus.Ok {
			continue
		}

		orderItems := orderItemResult.Data.([]*model.OrderItem)
		completedTime := order.CompletedTime.Format("2006-01")
		productIDs := []int64{}

		for _, orderItem := range orderItems {
			// With Gift type is ignore
			if orderItem.Type == enum.ItemType.GIFT {
				continue
			}

			// sub item for combo type
			if orderItem.Skus != nil && len(*orderItem.Skus) > 0 {
				for _, sku := range *orderItem.Skus {
					if sku == nil {
						continue
					}
					productIDs = append(productIDs, sku.ProductID)
				}
				continue
			}

			productIDs = append(productIDs, orderItem.ProductID)
		}

		ordersMap[completedTime] = append(ordersMap[completedTime], productIDs...)
	}

	return ordersMap, *orders[len(orders)-1].ID, nil
}

// CalcAccumulateProductForOrderCompleted ...
func CalcAccumulateProductForOrderCompleted(orderID int64, customerID int64) (count int, err error) {
	now := time.Now()

	// get number of months to calculate level
	numberOfMonthsCalLevel := 3
	customerSettingConfig, _ := client.Services.Customer.GetSettingConfig()
	if customerSettingConfig != nil {
		numberOfMonthsCalLevel = customerSettingConfig.NumberOfMonthsCalLevel
	}
	dateFromNew := time.Date(now.Year(), now.Month()-time.Month(numberOfMonthsCalLevel), 0, 17, 0, 0, 0, now.Location())

	// calculate dateFrom and dateTo
	dateFrom := time.Date(dateFromNew.Year(), dateFromNew.Month(), 1, 0, 0, 0, 0, dateFromNew.Location())
	dateTo := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthsBetween, _ := utils.MonthsBetween(dateFrom, dateTo)

	res := model.OrderDB.QueryOne(&model.Order{
		OrderID: orderID,
	})
	if res.Status != common.APIStatus.Ok {
		return 0, errors.New("error when query order")
	}

	order := res.Data.([]*model.Order)[0]
	completedTime := order.CompletedTime.Format("2006-01")

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "triggerAccumulateProduct")
	if orderItemPartitionDB == nil {
		return 0, errors.New("error when get order item partition")
	}

	orderItemResult := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID: order.OrderID,
	}, 0, 0, nil)

	if orderItemResult.Status != common.APIStatus.Ok {
		return 0, errors.New("error when query order item")
	}

	orderItems := orderItemResult.Data.([]*model.OrderItem)
	newAccumulatedProducts := []int64{}
	productIDs := []int64{}

	// append productIDs
	for _, orderItem := range orderItems {
		if orderItem.Type == enum.ItemType.GIFT {
			continue
		}

		// sub item for combo type
		if orderItem.Skus != nil && len(*orderItem.Skus) > 0 {
			for _, sku := range *orderItem.Skus {
				if sku == nil {
					continue
				}
				productIDs = append(productIDs, sku.ProductID)
			}
			continue
		}

		productIDs = append(productIDs, orderItem.ProductID)
	}

	if len(productIDs) == 0 {
		return 0, nil
	}

	for _, productID := range productIDs {
		exist := accumulateProductExist(order.CustomerID, productID, monthsBetween)
		if exist {
			continue
		}

		newAccumulatedProducts = append(newAccumulatedProducts, productID)
	}

	// query AccumulateProductDB by customerID, in Version is completedTime
	resAccumulateProduct := model.AccumulateProductDB.QueryOne(&model.AccumulateProduct{
		CustomerID: customerID,
		Version:    completedTime,
	})
	if resAccumulateProduct.Status == common.APIStatus.Ok {
		accumulatedProduct := resAccumulateProduct.Data.([]*model.AccumulateProduct)[0]

		accumulatedProduct.ProductIDs = append(accumulatedProduct.ProductIDs, productIDs...)
		model.AccumulateProductDB.UpdateOne(&model.AccumulateProduct{
			CustomerID: customerID,
			Version:    completedTime,
		}, accumulatedProduct)

	} else {
		model.AccumulateProductDB.Create(&model.AccumulateProduct{
			CustomerID: customerID,
			Version:    completedTime,
			ProductIDs: productIDs,
		})
	}

	return len(newAccumulatedProducts), nil
}

func accumulateProductExist(customerID int64, productID int64, versions []string) bool {
	// need to check if the accumalate productIds exist
	// if exist, return true

	filter := bson.M{
		"customer_id": customerID,
		"version":     bson.M{"$in": versions},
	}

	res := model.AccumulateProductDB.Query(filter, 0, 0, nil)
	if res.Status != common.APIStatus.Ok {
		return false
	}

	accumulatedProducts := res.Data.([]*model.AccumulateProduct)
	productsMap := make(map[int64]struct{})
	for _, accumulatedProduct := range accumulatedProducts {
		for _, productID := range accumulatedProduct.ProductIDs {
			productsMap[productID] = struct{}{}
		}
	}

	_, exist := productsMap[productID]
	return exist
}
