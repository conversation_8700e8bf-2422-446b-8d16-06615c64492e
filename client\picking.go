package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	getPickTicketItems = "/warehouse/picking/v1/pick-ticket-item"
)

type pickingClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewProductServiceClient ...
func NewPickingServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *pickingClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	pickingClient := &pickingClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	pickingClient.svc.SetDBLog(session)
	return pickingClient
}

func (cli *pickingClient) GetPickTicketItems(saleOrderCode string) *[]PickTicketItem {
	params := map[string]string{
		"saleOrderCode": saleOrderCode,
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, getPickTicketItems, nil)
	if err != nil {
		fmt.Println("GetPickTicketItems failed: " + err.Error())
		return nil
	}

	var pickRes *PickTicketItemRes
	err = json.Unmarshal([]byte(result.Body), &pickRes)
	if err != nil {
		fmt.Println("GetPickTicketItems failed: " + err.Error())
		return nil
	}

	if pickRes.Status != common.APIStatus.Ok {
		fmt.Println("[" + saleOrderCode + "] GetPickTicketItems failed with status: " + pickRes.Status)
		return nil
	}

	return &pickRes.Data
}
