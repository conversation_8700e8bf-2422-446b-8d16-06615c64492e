package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// CartLimit ...
type CartLimit struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code      string     `json:"code,omitempty" bson:"code,omitempty"`
	IsActive  bool       `json:"isActive,omitempty" bson:"is_active,omitempty"`
	StartTime *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Width     float64    `json:"width,omitempty" bson:"width,omitempty"`          // cm
	Height    float64    `json:"height,omitempty" bson:"height,omitempty"`        // cm
	Length    float64    `json:"length,omitempty" bson:"length,omitempty"`        // cmd
	MaxWeight float64    `json:"maxWeight,omitempty" bson:"max_weight,omitempty"` // kg
}

// CartLimitDB ...
var CartLimitDB = &db.Instance{
	ColName:        "cart_limit",
	TemplateObject: &CartLimit{},
}

// InitCartLimitModel is func init model sku apply result
func InitCartLimitModel(s *mongo.Database) {
	CartLimitDB.ApplyDatabase(s)
	// t := true
	// _ = CartLimitDB.CreateIndex(bson.D{
	// 	bson.E{Key: "code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
}
