package utils

import (
	"fmt"
	"regexp"
	"strings"
	"unicode"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"golang.org/x/text/transform"
	"golang.org/x/text/unicode/norm"
)

var mapVNICode = map[string]string{
	"a": "[à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ]",
	"ê": "[è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ]",
	"i": "[ì|í|ị|ỉ|ĩ]",
	"o": "[ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ]",
	"u": "[ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ]",
	"y": "[ỳ|ý|ỵ|ỷ|ỹ]",
	"d": "[đ]",
}

// NormalizeString ...
func NormalizeString(val string) string {
	//nolint
	tran := transform.Chain(norm.NFD, transform.RemoveFunc(isMn), norm.NFC)
	normStr, _, _ := transform.String(tran, strings.ToLower(val))
	normStr = formatVNCode(normStr)
	normStr = strings.Replace(normStr, " ", "-", -1)
	normStr = strings.Replace(normStr, ",", "", -1)
	normStr = strings.Replace(normStr, ";", "", -1)
	normStr = strings.Replace(normStr, "(", "", -1)
	normStr = strings.Replace(normStr, ")", "", -1)
	normStr = strings.Replace(normStr, "/", "", -1)
	normStr = strings.Replace(normStr, "&", "", -1)
	normStr = strings.Replace(normStr, "%", "", -1)

	r, _ := regexp.Compile(`(\\W)`) // should use raw string `...`
	qCheck := make(map[string]int)
	for i, v := range r.FindAllString(normStr, -1) {
		if v == "-" || qCheck[v] > 0 {
			continue
		}
		qCheck[v] = i + 1
		normStr = strings.ReplaceAll(normStr, v, ``)
	}

	return strings.Replace(normStr, "--", "-", -1)
}

func isMn(r rune) bool {
	return unicode.Is(unicode.Mn, r)
}

func formatVNCode(str string) string {
	for key, val := range mapVNICode {
		m := regexp.MustCompile(val)
		str = m.ReplaceAllString(str, key)
	}
	return str
}

func IsContains(arr []string, key string) bool {

	if len(arr) == 0 {
		return false
	}

	for i := range arr {
		if key == arr[i] {
			return true
		}
	}

	return false
}

func IsOrderTagContains(arr []enum.TagValue, key enum.TagValue) bool {

	if len(arr) == 0 {
		return false
	}

	for i := range arr {
		if key == arr[i] {
			return true
		}
	}

	return false
}
func RemoveOrderTagInSlice(slice []enum.TagValue, s enum.TagValue) []enum.TagValue {
	var result []enum.TagValue
	for i := range slice {
		if slice[i] != s {
			result = append(result, slice[i])
		}
	}
	return result
}

func IsInt64Contains(arr []int64, key int64) bool {

	if len(arr) == 0 {
		return false
	}

	for i := range arr {
		if key == arr[i] {
			return true
		}
	}

	return false
}

func IsIntContains(arr []int, key int) bool {

	if len(arr) == 0 {
		return false
	}

	for i := range arr {
		if arr[i] == key {
			return true
		}
	}

	return false
}

func ArrayInt64ToString(arr []int64, delim string) string {
	return strings.Trim(strings.Replace(fmt.Sprint(arr), " ", delim, -1), "[]")
}

func ArrayIntToString(arr []int, delim string) string {
	return strings.Trim(strings.Replace(fmt.Sprint(arr), " ", delim, -1), "[]")
}
