package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func SkuChangeAlert(itemCode, skuName, status, priceStatus string) *common.APIResponse {
	if itemCode == "" || skuName == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "SKU_INVALID",
			Message:   "Sku invalid or name invalid",
		}
	}
	priceStatusName := "thay đổi"
	if priceStatus == "up" {
		priceStatusName = "tăng"
	} else if priceStatus == "down" {
		priceStatusName = "giảm"
	}
	qCartItem := model.CartItemDB.Distinct(&model.CartItem{
		ItemCode: itemCode,
	}, "cart_id")
	if qCartItem.Status == common.APIStatus.Ok {
		carts := qCartItem.Data.([]interface{})
		lstCartID := make([]int64, 0)
		for _, cart := range carts {
			if cartID, ok := cart.(int64); ok {
				// fmt.Println(cartID)
				lstCartID = append(lstCartID, cartID)
			}
		}
		if len(lstCartID) > 0 {
			qCarts := model.CartDB.Query(&model.Cart{
				ComplexQuery: []*bson.M{
					&bson.M{
						"cart_id": bson.M{
							"$in": lstCartID,
						},
					},
				},
			}, 0, 0, nil)
			if qCarts.Status == common.APIStatus.Ok {
				lstCart := qCarts.Data.([]*model.Cart)
				for _, item := range lstCart {
					title := fmt.Sprintf("Sản phẩm %s đã có sự %s giá bán so với lúc bạn để vào giỏ hàng, kiểm tra ngay để đặt được giá tốt !", skuName, priceStatusName)
					if priceStatusName == "tăng" {
						title = fmt.Sprintf("Sản phẩm %s đã có sự %s giá bán so với lúc bạn để vào giỏ hàng, vui lòng kiểm tra lại trước khi thanh toán!", skuName, priceStatusName)
					}
					_ = client.Services.Notification.CreateNotification(&client.Notification{
						Username:     fmt.Sprintf("%d", item.AccountID),
						UserID:       item.AccountID,
						ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
						Topic:        "ANNOUNCEMENT",
						Title:        title,
						Link:         "/cart",

						Tags: []enum.NotificationTagEnum{enum.NotificationTag.PRICE, enum.NotificationTag.IMPORTANT},
					})
				}
			}
		}
	}

	return qCartItem
}
