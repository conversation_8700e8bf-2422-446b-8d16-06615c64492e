package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
)

const (
	pathGetReturnTicketCS                  = "/marketplace/ticket/v1/return-ticket/list"
	pathCreateTicketCSForCustomer          = "/marketplace/ticket/v1/ticket"
	pathCreateTicketCSHoldOrderForCustomer = "/marketplace/ticket/v1/ticket/hold-order"
	pathGetCSTicket                        = "/marketplace/ticket/v1/ticket"
	pathUpdateTicketCSForCustomer          = "/marketplace/ticket/v1/ticket"
)

type ticketClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewTicketServiceClient ...
func NewTicketServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *ticketClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	ticketClient := &ticketClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 0, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	ticketClient.svc.SetDBLog(session)
	return ticketClient
}

func (cli *ticketClient) GetCSReturnTickets(query *model.ReturnTicket, offset, limit int, getItems bool, ids string) *model.ReturnTicketResponse {
	params := make(map[string]string)
	params["offset"] = fmt.Sprint(offset)
	params["limit"] = fmt.Sprint(limit)
	params["getItems"] = fmt.Sprint(getItems)
	if ids != "" {
		params["ids"] = ids
	}
	// q
	if query != nil {
		bytes, _ := json.Marshal(query)
		params["q"] = string(bytes)
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetReturnTicketCS, nil)
	if err != nil {
		return &model.ReturnTicketResponse{
			Status:  common.APIStatus.Error,
			Message: fmt.Sprintf("Failed to call api GET %s: %s", pathGetReturnTicketCS, err.Error()),
		}
	}

	var result *model.ReturnTicketResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &model.ReturnTicketResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("%s. WHEN PARSING: %s", err.Error(), res.Body),
		}
	}

	return result
}

func (cli *ticketClient) GetReturnTicketCS(query *model.ReturnTicket, offset, limit int, getItems bool, ids string) ([]*model.ReturnTicket, error) {
	params := make(map[string]string)
	params["offset"] = fmt.Sprint(offset)
	params["limit"] = fmt.Sprint(limit)
	params["getItems"] = fmt.Sprint(getItems)
	if ids != "" {
		params["ids"] = ids
	}
	// q
	if query != nil {
		if query.OrderIDs != "" {
			params["orderIDs"] = query.OrderIDs
			query.OrderIDs = ""
		}
		bytes, _ := json.Marshal(query)
		params["q"] = string(bytes)
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetReturnTicketCS, nil)
	if err != nil {
		return nil, err
	}

	var result *model.ReturnTicketResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	if len(result.Data) > 0 {
		return result.Data, nil
	}

	return nil, fmt.Errorf("%v", "Not found")
}

func (cli *ticketClient) CreateTicketCSHoldOrderForCustomer(order *model.Order) *common.APIResponse {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, order, pathCreateTicketCSHoldOrderForCustomer, nil)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}
	return result
}

func (cli *ticketClient) GetTicketByCode(ticketCode string) (*model.TicketCS, error) {

	params := map[string]string{
		"code": ticketCode,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetCSTicket, nil)
	if err != nil {
		return nil, err
	}

	var result *model.TicketCSResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	if len(result.Data) > 0 {
		return result.Data[0], nil
	}

	return nil, fmt.Errorf("%v", "Not found")
}

func (cli *ticketClient) UpdateTicketCS(ticket *model.TicketCS) *common.APIResponse {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, ticket, pathUpdateTicketCSForCustomer, nil)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}
	return result
}
