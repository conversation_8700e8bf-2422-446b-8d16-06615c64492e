package enum

type ScheduleTypeValue string

type scheduleType struct {
	SCHEDULE_01_TO_15  ScheduleTypeValue
	SCHEDULE_16_TO_END ScheduleTypeValue
	SCHEDULE_01_TO_07  ScheduleTypeValue
	SCHEDULE_08_TO_15  ScheduleTypeValue
	SCHEDULE_16_TO_22  ScheduleTypeValue
	SCHEDULE_23_TO_END ScheduleTypeValue
}

var ScheduleType = &scheduleType{
	SCHEDULE_01_TO_15:  "SCHEDULE_01_TO_15",
	SCHEDULE_16_TO_END: "SCHEDULE_16_TO_END",
	SCHEDULE_01_TO_07:  "SCHEDULE_01_TO_07",
	SCHEDULE_08_TO_15:  "SCHEDULE_08_TO_15",
	SCHEDULE_16_TO_22:  "SCHEDULE_16_TO_22",
	SCHEDULE_23_TO_END: "SCHEDULE_23_TO_END",
}
