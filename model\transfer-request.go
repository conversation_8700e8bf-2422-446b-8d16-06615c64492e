package model

import (
	"time"
)

type TransferRequestTypeEnum string
type transferRequestTypeEnum struct {
	THUOCSIVN_SELLER_RECONCILE TransferRequestTypeEnum // seller reconciliation
}

var TransferRequestType = &transferRequestTypeEnum{
	THUOCSIVN_SELLER_RECONCILE: "THUOCSIVN_SELLER_RECONCILE",
}

type TransferRequest struct {
	CreatedByAccountID  int64 `json:"createdByAccountID,omitempty" bson:"created_by_account_id,omitempty"`
	CreatedByEmployeeID int64 `json:"createdByEmployeeID,omitempty" bson:"-"`

	TransferRequestID   int64                  `json:"transferRequestID,omitempty" bson:"transfer_request_id,omitempty"`
	TransferRequestCode string                 `json:"transferRequestCode,omitempty" bson:"transfer_request_code,omitempty"`
	Type                string                 `json:"type,omitempty" bson:"type,omitempty"`
	Status              string                 `json:"status,omitempty" bson:"status,omitempty"`
	Note                *string                `json:"note,omitempty" bson:"note,omitempty"`
	Items               []*TransferRequestItem `json:"items,omitempty" bson:"items,omitempty"`
}

type TransferRequestItem struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	TransferRequestCode string `json:"transferRequestCode,omitempty" bson:"transfer_request_code,omitempty"` // link with TransferRequest
	RelateType          string `json:"relateType,omitempty" bson:"relate_type,omitempty"`
	RelateCode          string `json:"relateCode,omitempty" bson:"relate_code,omitempty"` // this field save code of object relate with transfer request item. Ex: clck

	// transfer result
	Status          string `json:"status,omitempty" bson:"status,omitempty"` // PROCESSING || SUCCESS || FAILED
	TransactionCode string `json:"transactionCode,omitempty" bson:"transaction_code,omitempty"`
}
