package reconcile_action

import (
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func PushChargePoorQualityProductToQueue(input *model.PoorQualityProductRequest) *common.APIResponse {
	if input.OrderId == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "orderId is required",
			ErrorCode: "INVALID_INPUT",
		}
	}

	if len(input.SkuInfos) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "skus are required",
			ErrorCode: "INVALID_INPUT",
		}
	}

	err := model.ChargePoorQualityProductJob.Push(input, &job.JobItemMetadata{
		Topic: "default",
		Keys: []string{
			strconv.FormatInt(input.OrderId, 10),
			input.TicketCode,
		},
	})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "PUSH_TO_QUEUE_ERROR",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "push to queue success",
	}
}
