package consumer

import (
	"fmt"
	"runtime/debug"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/tool"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func HandleCreateMissingInvoice(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return nil
	}

	var input *model.Order
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return nil
	}

	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic: %s", string(debug.Stack()))
			fmt.Printf("[%d - %s] %s\n", input.OrderID, input.Status, err.Error())
		}
	}()

	tool.ProcessSyncSellerInvoice(input.OrderID)

	return nil
}
