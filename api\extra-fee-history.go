package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"strconv"
)

func GetExtraFeeHistoryByCustomerId(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		customerId = req.GetParam("customerId")
	)

	if customerId == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "ID_REQUIRED",
			Message:   "Customer is required",
		})
	}

	customerIdInt, err := strconv.ParseInt(customerId, 10, 64)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "CUSTOMER_ID_INVALID",
			Message:   "CustomerID invalid",
		})
	}

	if customerIdInt == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Customer is required",
			ErrorCode: "ID_REQUIRED",
		})
	}

	return resp.Respond(action.GetExtraFeeHistoryByCustomerId(customerIdInt))
}

func UpdateExtraFeeHistory(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.UserExtraFeeHistory

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	return resp.Respond(action.UpdateExtraFeeHistory(&input))
}
