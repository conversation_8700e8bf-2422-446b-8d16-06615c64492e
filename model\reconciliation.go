package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var ReconciliationDB = &db.Instance{
	ColName:        "reconciliation",
	TemplateObject: &Reconciliation{},
}

var ReconciliationErrorDB = &db.Instance{
	ColName:        "reconciliation_error",
	TemplateObject: map[string]interface{}{},
}

type Reconciliation struct {
	ID               *primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	CreatedTime      *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime  *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	ReconciledTime   *time.Time          `json:"reconciledTime" bson:"reconciled_time,omitempty"`
	AllowConfirmTime time.Time           `json:"allowConfirmTime" bson:"allow_confirm_time,omitempty"`
	FromTime         string              `json:"fromTime" bson:"from_time,omitempty"`
	ToTime           string              `json:"toTime" bson:"to_time,omitempty"`
	RecCode          string              `json:"recCode" bson:"rec_code,omitempty"`

	SellerID                   int64                     `json:"sellerId" bson:"seller_id,omitempty"`
	SellerCode                 string                    `json:"sellerCode" bson:"seller_code,omitempty"`
	ReconcileScheduleTimeIndex string                    `json:"reconcileScheduleTimeIndex" bson:"reconcile_schedule_time_index,omitempty"`
	ReconciliationStatus       ReconciliationStatusValue `json:"reconciliationStatus" bson:"reconciliation_status,omitempty"`

	TotalBuyerFee  *int    `json:"totalBuyerFee,omitempty" bson:"total_buyer_fee,omitempty"`
	TotalRevenue   *int    `json:"totalRevenue,omitempty" bson:"total_revenue,omitempty"`
	ListingFee     *int    `json:"listingFee,omitempty" bson:"listing_fee,omitempty"`
	FulfillmentFee *int    `json:"fulfillmentFee,omitempty" bson:"fulfillment_fee,omitempty"`
	PenaltyFee     *int    `json:"penaltyFee,omitempty" bson:"penalty_fee,omitempty"`
	BonusAmount    *int    `json:"bonusAmount,omitempty" bson:"bonus_amount,omitempty"`
	TotalPayment   *int    `json:"totalPayment,omitempty" bson:"total_payment,omitempty"`
	SessionPayment *string `json:"sessionPayment,omitempty" bson:"session_payment,omitempty"`

	PaidFor       *PaidForValue `json:"paidFor,omitempty" bson:"paid_for,omitempty"`
	PaidForReason *string       `json:"paidForReason,omitempty" bson:"paid_for_reason,omitempty"`

	CreatedTransferRequestCode *string `json:"createdTransferRequestCode,omitempty" bson:"created_transfer_request_code,omitempty"`

	OperationAnd []*bson.M   `json:"-" bson:"$and,omitempty"`
	OperationOr  interface{} `json:"-" bson:"$or,omitempty"`

	ReconciliationItems []*ReconciliationItem `json:"reconciliationItems" bson:"-"`
	HasOrderID          int64                 `json:"hasOrderID" bson:"-"`
	IsFlagshipStore     *bool                 `json:"isFlagshipStore" bson:"-"`
	ScheduleTimeFrom    string                `json:"scheduleTimeFrom" bson:"-"`
	ScheduleTimeTo      string                `json:"scheduleTimeTo" bson:"-"`
}

// InvoiceStatusValue
type ReconciliationStatusValue string
type reconciliationStatus struct {
	None      ReconciliationStatusValue
	Waiting   ReconciliationStatusValue
	Confirmed ReconciliationStatusValue
	Completed ReconciliationStatusValue
}

var ReconciliationStatus = &reconciliationStatus{
	None:      "NONE", // just for tracking in invoice
	Waiting:   "WAITING",
	Confirmed: "CONFIRMED",
	Completed: "COMPLETED",
}

// PaidForValue
type PaidForValue string

type paidFor struct {
	Buymed PaidForValue
}

var PaidFor = &paidFor{
	Buymed: "BUYMED", //BUYMED = Thanh toán cho Buymed
	// [empty] = Thanh toán cho Seller
}

type ResyncCompletedReconciliationReq struct {
	ReconciledTimeFrom *time.Time `json:"reconciledTimeFrom"`
	ReconciledTimeTo   *time.Time `json:"reconciledTimeTo"`
}

// InitReconciliationModel is func init model reconciliation
func InitReconciliationModel(s *mongo.Database) {
	ReconciliationDB.ApplyDatabase(s)

	// t := true
	// ReconciliationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "reconcile_schedule_time_index", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// ReconciliationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "reconciliation_status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

func InitReconciliationError(s *mongo.Database) {
	ReconciliationErrorDB.ApplyDatabase(s)

	// t := true
	// exp := int32(259200) // 3 days
	// ReconciliationErrorDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "index", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// ReconciliationErrorDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &exp,
	// })
}
