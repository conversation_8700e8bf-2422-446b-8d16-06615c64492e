package utils

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"time"
)

const (
	RFC3339Date = "2006-01-02"
	ISOFormat   = "2006-01-02T15:04:05.000Z" // ISO format with time
)

func MinInt(nums ...int) int {
	if len(nums) == 0 {
		return 0
	}

	min := nums[0]
	for _, num := range nums {
		if num < min {
			min = num
		}
	}
	return min
}

func MinInt64(nums ...int64) int64 {
	if len(nums) == 0 {
		return 0
	}

	min := nums[0]
	for _, num := range nums {
		if num < min {
			min = num
		}
	}
	return min
}

// Split a string into slice with unique elements,
// default will split non word characters
func SplitToUnique(str string, delimiter ...string) []string {
	re := regexp.MustCompile(`\W`)
	if len(delimiter) > 0 {
		re = regexp.MustCompile(delimiter[0])
	}

	splitedTokens := re.Split(str, -1)

	existedTokens := make(map[string]bool, len(splitedTokens))
	result := make([]string, 0, len(splitedTokens))

	for _, token := range splitedTokens {
		if token == "" || existedTokens[token] {
			continue
		}

		existedTokens[token] = true
		result = append(result, token)
	}

	return result
}

func ConvertMapToSlices(m map[int64]string) ([]int64, []string) {
	int64s := make([]int64, 0, len(m))
	strs := make([]string, 0, len(m))

	for k, v := range m {
		int64s = append(int64s, k)
		strs = append(strs, v)
	}

	return int64s, strs
}

func ConvertToSlices(data []interface{}) ([]int64, []string) {
	int64s := make([]int64, 0, len(data))
	strings := make([]string, 0, len(data))

	for _, d := range data {
		if d == nil {
			continue
		}

		if v, ok := d.(int64); ok {
			int64s = append(int64s, v)

			str := strconv.FormatInt(v, 10)
			strings = append(strings, str)
		}
	}
	return int64s, strings
}

func FormatInt(number int) string {
	output := strconv.Itoa(number)
	startOffset := 3
	if number < 0 {
		startOffset++
	}
	for outputIndex := len(output); outputIndex > startOffset; {
		outputIndex -= 3
		output = output[:outputIndex] + "." + output[outputIndex:]
	}
	return output
}

func Contains(str string, array []string) bool {
	for _, pattern := range array {
		if str == pattern {
			return true
		}
	}
	return false
}

func CheckExistInEnum(input interface{}, baseObj interface{}) bool {
	v := reflect.ValueOf(baseObj)
	for i := 0; i < v.NumField(); i++ {
		if input == v.Field(i).Interface() {
			return true
		}
	}

	return false
}

func ParseTimeWithFormats(timeStr string) (time.Time, string, error) {
	formats := []string{RFC3339Date, ISOFormat}
	for _, format := range formats {
		parsedTime, err := time.Parse(format, timeStr)
		if err == nil {
			return parsedTime, format, nil
		}
	}
	return time.Time{}, "", fmt.Errorf("invalid time format: %s", timeStr)
}
