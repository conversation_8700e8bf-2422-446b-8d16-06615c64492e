package generic

// UniqueSlice removes duplicate elements from a slice and returns a new slice with unique elements.
// It uses a map to track the presence of each element.
//
// T: The type of elements in the slice. It must be comparable.
//
// Parameters:
// - arr: The input slice from which duplicates need to be removed.
//
// Returns:
// - A new slice containing only unique elements from the input slice.
//
// Example:
//   UniqueSlice([]int{1, 2, 2, 3, 4, 4, 5})
//   // Output: [1 2 3 4 5]
func UniqueSlice[T comparable](arr []T) []T {
	keys := make(map[T]struct{}, len(arr))
	list := make([]T, 0, len(arr))
	for _, entry := range arr {
		if _, ok := keys[entry]; !ok {
			keys[entry] = struct{}{}
			list = append(list, entry)
		}
	}
	return list
}

// ChunkSlice splits a slice into multiple smaller slices (chunks) of a specified size.
// If the length of the input slice is not perfectly divisible by the chunk size,
// the last chunk will contain the remaining elements.
// Useful for breaking into batches and query concurrently with semaphore
//
// T: The type of elements in the slice.
//
// Parameters:
// - arr: The input slice to be split into chunks.
// - chunkSize: The size of each chunk.
//
// Returns:
// - A slice of slices, where each inner slice is a chunk of the specified size.
//
// Example:
//   ChunkSlice([]int{1, 2, 3, 4, 5, 6, 7, 8, 9}, 3)
//   // Output: [[1 2 3] [4 5 6] [7 8 9]]
func ChunkSlice[T any](arr []T, chunkSize int) [][]T {
	var chunks [][]T
	for i := 0; i < len(arr); i += chunkSize {
		end := i + chunkSize
		if end > len(arr) {
			end = len(arr)
		}
		chunks = append(chunks, arr[i:end])
	}
	return chunks
}
