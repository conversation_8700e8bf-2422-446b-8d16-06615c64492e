package client

import (
	"encoding/json"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
)

const (
	pathSyncOrderInfoToKiotvietPartner = "/api/v1/webhook/buymed/orders"
)

type KiotvietClient struct {
	Partner *client.RestClient
	headers map[string]string
}

// NewClient is func define new order client
func NewKiotvietClient(apiHost, apiKey, logName string, session *mongo.Database) *KiotvietClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	client := &KiotvietClient{
		Partner: client.NewRESTClient(
			apiHost,
			logName,
			time.Duration(3*time.Second),
			1,
			time.Duration(3*time.Second),
		),
		headers: map[string]string{
			"x-api-key": apiKey,
		},
	}

	client.Partner.SetDBLog(session)

	return client
}

func (cli *KiotvietClient) SyncCreateOrderInfoToKiotvietPartner(in *model.SyncOrderCreatePayload) *model.SyncOrderCreateInfoLog {

	params := map[string]string{}
	resp, err := cli.Partner.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathSyncOrderInfoToKiotvietPartner, nil)

	if err != nil {
		return nil
	}

	var result *model.SyncOrderCreateInfoLog
	err = json.Unmarshal([]byte(resp.Body), &result)

	if err != nil {
		return nil
	}

	if !result.Success {
		return &model.SyncOrderCreateInfoLog{
			RequestID: result.RequestID,
			Success:   result.Success,
			ErrorCode: result.ErrorCode,
		}
	}

	return &model.SyncOrderCreateInfoLog{
		RequestID: result.RequestID,
		Success:   result.Success,
	}
}

func (cli *KiotvietClient) SyncUpdateOrderInfoToKiotvietPartner(in *model.SyncOrderUpdatePayload) *model.SyncOrderUpdateInfoLog {
	params := map[string]string{}
	resp, err := cli.Partner.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, in, pathSyncOrderInfoToKiotvietPartner, nil)
	if err != nil {
		return nil
	}

	var result *model.SyncOrderUpdateInfoLog
	err = json.Unmarshal([]byte(resp.Body), &result)

	if err != nil {
		return nil
	}

	if !result.Success {
		return &model.SyncOrderUpdateInfoLog{
			RequestID: result.RequestID,
			Success:   result.Success,
			ErrorCode: result.ErrorCode,
		}
	}

	return &model.SyncOrderUpdateInfoLog{
		RequestID: result.RequestID,
		Success:   result.Success,
	}
}
