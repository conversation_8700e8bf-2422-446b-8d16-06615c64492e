package action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func UpdateCartApplyVoucher(acc *model.Account, input *model.CartUpdateVoucher) *common.APIResponse {
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cart, errCart := getCartFromCustomerID(customer.CustomerID, true)
	if errCart != nil {
		return errCart
	}

	cart.RedeemCode = input.RedeemCode
	cart.AccountID = customer.AccountID
	cart.CustomerCode = customer.CustomerCode
	cart.SourceDetail = acc.SourceDetail

	if len(cart.ProvinceCode) == 0 {
		cart.ProvinceCode = customer.ProvinceCode
	}
	if len(cart.DistrictCode) == 0 {
		cart.DistrictCode = customer.DistrictCode
	}
	if len(cart.WardCode) == 0 {
		cart.WardCode = customer.WardCode
	}
	if len(cart.CustomerScope) == 0 {
		cart.CustomerScope = customer.Scope
	}
	resetCartPrice(cart)
	_ = handleProcessContentItems(cart, customer, nil, true)
	_ = handleProcessPriceItems(customer, cart)
	//_ = handleProcessDeliveryAndPaymentMethod(cart)

	errHandleVoucherCode := handleApplyVoucherCode(cart, customer, "UpdateCartApplyVoucher")
	if errHandleVoucherCode != nil {
		return errHandleVoucherCode
	}

	t := time.Now()
	cart.LastActionTime = &t

	if len(cart.RedeemApplyResult) == 0 {
		model.CartDB.UpdateOne(
			&model.Cart{CartID: cart.CartID},
			model.Cart{
				RedeemApplyResult: make([]*model.PromoApplyResult, 0),
			},
		)
	}

	return model.CartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, cart)
}

// RemoveVoucherCode removes specified voucher codes from the user's cart.
// It validates the input, updates the cart's voucher codes, and updates the cart in the database.
//
// Parameters:
// - acc: A pointer to the user's account information.
// - input: A pointer to the request containing the voucher codes to be removed.
//
// Returns:
// - A pointer to an APIResponse indicating the result of the operation.
//
// The function performs the following steps:
// 1. Validates the input to ensure that redeem codes are provided.
// 2. Updates the last action time in the input.
// 3. Retrieves the customer's profile and cart information.
// 4. Updates the cart's location and scope details based on the customer's profile.
// 5. Resets the cart's price and processes content and price items.
// 6. Removes the specified voucher codes from the cart's redeem codes and apply results.
// 7. Updates the cart's redeem code removed list.
// 8. Updates the cart in the cache and database asynchronously.
// 9. Returns the result of the database update operation.
func RemoveVoucherCode(acc *model.Account, input *model.RemoveVoucherRequest) *common.APIResponse {
	if input.RedeemCodes == nil || len(*input.RedeemCodes) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Redeem code invalid",
			ErrorCode: "REDEEM_CODE_INVALID",
		}
	}

	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cart, errGetCart := getCartFromCustomerID(customer.CustomerID, true)
	if errGetCart != nil {
		return errGetCart
	}
	cart.SourceDetail = acc.SourceDetail

	if len(cart.ProvinceCode) == 0 {
		cart.ProvinceCode = customer.ProvinceCode
	}
	if len(cart.DistrictCode) == 0 {
		cart.DistrictCode = customer.DistrictCode
	}
	if len(cart.WardCode) == 0 {
		cart.WardCode = customer.WardCode
	}
	if len(cart.CustomerScope) == 0 {
		cart.CustomerScope = customer.Scope
	}

	cart.GetVoucherAutoApply = false

	resetCartPrice(cart)

	_ = handleProcessContentItems(cart, customer, nil, true)

	_ = handleProcessPriceItems(customer, cart)

	_ = handleApplyVoucherCode(cart, customer, "RemoveVoucherCode")

	newRedeemCodes := make([]*string, 0)
	newResults := make([]*model.PromoApplyResult, 0)
	if cart.RedeemCode != nil {
		for _, code := range *cart.RedeemCode {
			isExist := false
			for _, codeInput := range *input.RedeemCodes {
				if code != nil && codeInput != nil && *code == *codeInput {
					isExist = true
					break
				}
			}
			if !isExist {
				newRedeemCodes = append(newRedeemCodes, code)
			}
		}
	}
	redeemCodeRemoved := cart.RedeemCodeRemoved
	if cart.RedeemApplyResult != nil {
		for _, result := range cart.RedeemApplyResult {
			isExist := false
			for _, codeInput := range *input.RedeemCodes {
				if codeInput != nil && result.Code == *codeInput {
					isExist = true
					break
				}
			}
			if !isExist {
				newResults = append(newResults, result)
			}
		}
	}
	for _, codeInput := range *input.RedeemCodes {
		// if is not exist in redeem code removed then append to it
		isExist := false
		for _, code := range redeemCodeRemoved {
			if code == *codeInput {
				isExist = true
				break
			}
		}
		if !isExist {
			redeemCodeRemoved = append(redeemCodeRemoved, *codeInput)
		}
	}

	// Update new vouchers to cart
	t := time.Now()
	input.LastActionTime = &t
	input.RedeemCodes = &newRedeemCodes
	input.RedeemApplyResult = &newResults
	input.RedeemCodeRemoved = &redeemCodeRemoved

	go sdk.Execute(func() {
		model.CartCacheDB.UpdateOne(
			model.CartCache{CartID: cart.CartID},
			input,
		)
	})

	res := model.CartDB.UpdateOne(
		model.Cart{CartID: cart.CartID},
		input,
	)

	return res
}

// AddVoucherCode adds a voucher code to the user's cart and processes the cart accordingly.
// It validates the input voucher codes, retrieves the customer profile and cart, and updates the cart details.
// The function handles the application of the voucher code, processes the cart items and prices, and updates the cart in the database.
//
// Parameters:
//   - acc: A pointer to the user's account model.
//   - input: A pointer to the RemoveVoucherRequest model containing the voucher codes to be added.
//
// Returns:
//   - *common.APIResponse: The API response indicating the result of the operation.
func AddVoucherCode(acc *model.Account, input *model.RemoveVoucherRequest) *common.APIResponse {
	// Validate Input
	if input.RedeemCodes == nil || len(*input.RedeemCodes) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Redeem code invalid",
			ErrorCode: "REDEEM_CODE_INVALID",
		}
	}

	// Get customer
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	// Get cart
	cart, errGetCart := getCartFromCustomerID(customer.CustomerID, true)
	if errGetCart != nil {
		return errGetCart
	}

	cart.SourceDetail = acc.SourceDetail
	if len(cart.ProvinceCode) == 0 {
		cart.ProvinceCode = customer.ProvinceCode
	}
	if len(cart.DistrictCode) == 0 {
		cart.DistrictCode = customer.DistrictCode
	}
	if len(cart.WardCode) == 0 {
		cart.WardCode = customer.WardCode
	}
	if len(cart.CustomerScope) == 0 {
		cart.CustomerScope = customer.Scope
	}
	cart.GetVoucherAutoApply = false

	// handle process
	resetCartPrice(cart)

	_ = handleProcessContentItems(cart, customer, nil, true)

	_ = handleProcessPriceItems(customer, cart)

	_ = handleApplyVoucherCode(cart, customer, "AddVoucherCode")

	cart.SubPrice = cart.Price

	selectedCartItems := make([]*model.CartItem, 0)
	for _, item := range cart.Items {
		if item.IsSelected != nil && *item.IsSelected {
			selectedCartItems = append(selectedCartItems, item)
		}
	}
	currentRedeems := cart.RedeemCode
	currentRedeemApplyResult := cart.RedeemApplyResult
	tempCart := *cart
	tempCart.TotalPrice = cart.TotalPrice
	tempCart.TotalQuantity = cart.TotalQuantitySelected
	tempCart.TotalItem = cart.TotalItemSelected
	tempCart.Items = selectedCartItems
	tempCart.RedeemCode = input.RedeemCodes
	countLoop := 0

CHECK_VOUCHER:
	promoApply, errCheck := client.Services.Promotion.CheckVoucherCode(&client.CheckVoucherRequest{
		Customer:            customer,
		Cart:                &tempCart,
		VoucherCode:         *tempCart.RedeemCode,
		AccountID:           cart.AccountID,
		GetVoucherAutoApply: cart.GetVoucherAutoApply,
		SystemDisplay:       cart.SystemDisplay,

		Source:                     "AddVoucherCode",
		SourceDetail:               cart.SourceDetail,
		SkipVoucherByPaymentMethod: true,
	})
	if errCheck != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errCheck.Error(),
			ErrorCode: "VOUCHER_CODE_INVALID",
		}
	}

	for _, redeem := range promoApply {
		if !redeem.CanUse {
			if redeem.ErrorCode == "INVALID_PAYMENT_METHOD" {
				cart.PaymentMethod = redeem.PaymentMethod
				tempCart.PaymentMethod = redeem.PaymentMethod
				if paymentMethods, errRes := handleProcessDeliveryAndPaymentMethod(cart, customer); errRes != nil {
					return errRes
				} else {
					_, errCheckPayment := handleProcessDeliveryAndPaymentMethod(cart, customer)
					if errCheckPayment != nil && errCheckPayment == PaymentMethodError {
						return errCheckPayment
					}
					errCheckPayment = handlePaymentMethodCredit(cart, customer, paymentMethods)
					if errCheckPayment != nil {
						return errCheckPayment
					}

					go func() {
						model.CartCacheDB.UpdateOne(
							model.CartCache{CartID: cart.CartID},
							model.CartCache{PaymentMethod: cart.PaymentMethod},
						)
					}()

					model.CartDB.UpdateOne(
						model.Cart{CartID: cart.CartID},
						model.Cart{PaymentMethod: cart.PaymentMethod},
					)
				}
				countLoop++

				if countLoop > 2 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   redeem.ErrorMessage,
						ErrorCode: redeem.ErrorCode,
					}
				}
				goto CHECK_VOUCHER
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   redeem.ErrorMessage,
				ErrorCode: redeem.ErrorCode,
			}
		}

		currentRedeemApplyResult = append(currentRedeemApplyResult, &model.PromoApplyResult{
			Code:              redeem.VoucherCode,
			CanUse:            redeem.CanUse,
			Gift:              redeem.Gifts,
			AutoApply:         redeem.AutoApply,
			MatchSeller:       redeem.MatchSeller,
			MatchProducts:     redeem.MatchProducts,
			DiscountValue:     redeem.DiscountValue,
			SellerCode:        redeem.SellerCode,
			SellerCodes:       redeem.SellerCodes,
			StoreCode:         redeem.StoreCode,
			PaymentMethod:     redeem.PaymentMethod,
			Name:              redeem.VoucherName,
			NumberOfAutoApply: redeem.NumberOfAutoApply,
			ChargeFee:         redeem.ChargeFee,
		})
		input.RedeemApplyResult = &currentRedeemApplyResult
	}

	if currentRedeems != nil {
		for _, code := range *currentRedeems {
			if code != nil {
				*input.RedeemCodes = append(*input.RedeemCodes, code)
			}
		}
	}

	// remove code in removed voucher list
	if cart.RedeemCodeRemoved != nil && input.RedeemCodes != nil {
		// Create a map for quick lookup of input.RedeemCodes
		redeemCodeMap := make(map[string]bool)
		for _, codeInput := range *input.RedeemCodes {
			redeemCodeMap[*codeInput] = true
		}

		// Filter out codes that exist in input.RedeemCodes
		newRemoved := make([]string, 0)
		for _, code := range cart.RedeemCodeRemoved {
			if !redeemCodeMap[code] {
				newRemoved = append(newRemoved, code)
			}
		}

		// Update input.RedeemCodeRemoved
		input.RedeemCodeRemoved = &newRemoved
	}

	// Update new vouchers to cart
	t := time.Now()
	input.LastActionTime = &t

	go sdk.Execute(func() {
		model.CartCacheDB.UpdateOne(
			model.CartCache{CartID: cart.CartID},
			input,
		)
	})

	res := model.CartDB.UpdateOne(
		model.Cart{CartID: cart.CartID},
		input,
	)

	return res
}
