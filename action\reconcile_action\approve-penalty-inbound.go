package reconcile_action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func ApproveReconcileAutoPenaltyInventory(
	reconcileItem *model.ReconciliationItem,
) *common.APIResponse {
	if reconcileItem == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "nil reconcile item",
			ErrorCode: "NIL_RECONCILE_ITEM",
		}
	}

	reconcileRes := model.ReconciliationDB.QueryOne(&model.Reconciliation{
		SellerCode:                 reconcileItem.SellerCode,
		ReconcileScheduleTimeIndex: reconcileItem.ReconcileScheduleTimeIndex,
	})
	if reconcileRes.Status != common.APIStatus.Ok {
		return reconcileRes
	}

	reconciliation := reconcileRes.Data.([]*model.Reconciliation)[0]
	if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Reconciliation status is not waiting",
			ErrorCode: "INVALID_RECONCILIATION_STATUS",
		}
	}

	penaltyRes := ProcessPenaltyReconciliationItem(*reconcileItem, reconciliation)
	if penaltyRes.Status != common.APIStatus.Ok {
		return penaltyRes
	}

	value, err := CalculateReconciliation(
		reconcileItem.SellerCode,
		reconcileItem.ReconcileScheduleTimeIndex,
	)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_CALCULATE_RECONCILIATION",
		}
	}

	reconciliationF := model.Reconciliation{
		SellerCode:                 reconcileItem.SellerCode,
		ReconcileScheduleTimeIndex: reconcileItem.ReconcileScheduleTimeIndex,
		ReconciliationStatus:       model.ReconciliationStatus.Waiting,
	}
	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}
	updateRecRes := model.ReconciliationDB.UpdateOne(reconciliationF, updater)
	if updateRecRes.Status != common.APIStatus.Ok {
		return updateRecRes
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Approve auto penalty inventory for reconciliation item success",
	}
}
