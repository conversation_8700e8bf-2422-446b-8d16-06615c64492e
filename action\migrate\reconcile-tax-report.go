package migrate

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

func MigrateRemoveBizHouseholdTax() {
	fmt.Println("MigrateRemoveBizHouseholdTax")
	defer fmt.Println("MigrateRemoveBizHouseholdTax finished")

	for {
		itemsRes := model.ReconciliationItemDB.Query(model.ReconciliationItem{
			FeeType: enum.FeeType.BIZ_HOUSEHOLD_TAX,
		}, 0, 100, nil)

		if itemsRes.Status != common.APIStatus.Ok {
			break
		}

		items := itemsRes.Data.([]*model.ReconciliationItem)
		for _, item := range items {
			model.ReconciliationItemDB.Delete(model.ReconciliationItem{ID: item.ID})

			time.Sleep(10 * time.Millisecond)
		}

		time.Sleep(50 * time.Millisecond)
	}
}
