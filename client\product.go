package client

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetListSku         = "/marketplace/product/v2/search/list"
	pathUpdateDealQuantity = "/marketplace/product/v2/deal-quantity"
	pathUpdateSkuQuantity  = "/marketplace/product/v2/sku-item"
	pathSkuItemList        = "/marketplace/product/v2/sku-item/list"
	pathSkuList            = "/marketplace/product/v2/sku/list"
	pathSkuByLocationList  = "/marketplace/product/v2/sku/list-by-location"
	pathGetListSkuLimit    = "/marketplace/product/v2/sku-limit/list"

	pathGetListSkuBrandContract                    = "/marketplace/product/v2/sku-brand-contract/list"
	pathGetCustomerPurchaseConfiguration           = "/marketplace/product/v2/customer-purchase-configuration"
	pathGetListCustomerPurchaseConfigurationDetail = "/marketplace/product/v2/customer-purchase-configuration-detail/list"

	pathGetListManufacturer = "/marketplace/product/v2/manufacturer/list"

	pathGetProductFeeMap = "/marketplace/product/v2/product-fee/concurrently-applied-list"
)

type productClient struct {
	svc *client.RestClient

	feeClient *client.RestClient
	headers   map[string]string
}

// NewProductServiceClient ...
func NewProductServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *productClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	productClient := &productClient{
		svc:       client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		feeClient: client.NewRESTClient(apiHost, logName+"_fee", 3*time.Second, 0, 0),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	productClient.svc.SetDBLog(session)
	productClient.feeClient.SetDBLog(session)

	return productClient

}

type ProductRes struct {
	Status  string    `json:"status"`
	Data    []Product `json:"data,omitempty"`
	Message string    `json:"message"`
	Total   int64     `json:"total,omitempty"`
}
type Product struct {
	ProductID             int64     `json:"productID" bson:"product_id,omitempty"`
	Code                  string    `json:"code,omitempty" bson:"code,omitempty"`
	Name                  string    `json:"name,omitempty"`
	SellerCategoryCode    string    `json:"sellerCategoryCode,omitempty" bson:"seller_category_code,omitempty"`
	SellerSubCategoryCode string    `json:"sellerSubCategoryCode,omitempty" bson:"seller_sub_category_code,omitempty"`
	ImageUrls             *[]string `json:"imageUrls,omitempty"`
	IsFragile             *bool     `json:"isFragile,omitempty"` // hàng dễ vỡ
	IsFrozen              *bool     `json:"isFrozen,omitempty"`

	ManufacturerCode string `json:"manufacturerCode,omitempty" bson:"manufacturer_code,omitempty"`

	Weight float64 `json:"weight,omitempty"`
	Length float64 `json:"length,omitempty"`
	Width  float64 `json:"width,omitempty"`
	Height float64 `json:"height,omitempty"`
	Volume string  `json:"volume,omitempty"`
	Unit   string  `json:"unit,omitempty"` // order unit

	SubstituteProductConfig *SubstituteProductConfig `json:"substituteProductConfig,omitempty" bson:"substitute_product_config,omitempty"`
}
type SubstituteProductConfig struct {
	ProductID             int64       `json:"productId,omitempty" bson:"product_id,omitempty"`
	ProductCode           string      `json:"productCode,omitempty" bson:"product_code,omitempty"`
	InStockDisplayPage    DisplayPage `json:"inStockDisplayPage,omitempty" bson:"in_stock_display_page,omitempty"`
	OutOfStockDisplayPage DisplayPage `json:"outOfStockDisplayPage,omitempty" bson:"out_of_stock_display_page,omitempty"`
}
type DisplayPage struct {
	All               bool `json:"all,omitempty" bson:"all,omitempty"`
	ProductListPage   bool `json:"productListPage,omitempty" bson:"product_list_page,omitempty"`
	ProductDetailPage bool `json:"productDetailPage,omitempty" bson:"product_detail_page,omitempty"`
	CartPage          bool `json:"cartPage,omitempty" bson:"cart_page,omitempty"`
	QuickOrderPage    bool `json:"quickOrderPage,omitempty" bson:"quick_order_page,omitempty"`
}
type SkuRes struct {
	Status  string `json:"status"`
	Data    []SKU  `json:"data,omitempty"`
	Message string `json:"message"`
	Total   int64  `json:"total,omitempty"`
}
type SKU struct {
	CreatedTime *time.Time `json:"createdTime,omitempty"`

	ItemCode    string   `json:"itemCode,omitempty" bson:"item_code,omitempty"` // item code
	SKU         string   `json:"sku,omitempty" bson:"sku,omitempty"`            // sku code
	Code        string   `json:"code,omitempty" bson:"code,omitempty"`          // sku code
	ProductCode string   `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64    `json:"productID,omitempty" bson:"product_id,omitempty"`
	Tags        []string `json:"tags,omitempty" bson:"tags,omitempty"`
	NoneVat     *bool    `json:"noneVat,omitempty" bson:"none_vat,omitempty"`
	VAT         *float64 `json:"vat,omitempty" bson:"vat,omitempty"`

	SellerCode  string `json:"sellerCode,omitempty"`
	SellerClass string `json:"sellerClass,omitempty"` // INTERNAL - EXTERNAL
	IsActive    bool   `json:"isActive,omitempty" bson:"is_active,omitempty"`

	RetailPriceValue int64 `json:"retailPriceValue,omitempty"`

	LocationCodes    []string `json:"locationCodes,omitempty" bson:"location_codes,omitempty"`
	IsSpecialControl bool     `json:"isSpecialControl,omitempty" bson:"is_special_control,omitempty"`
}

func (cli *productClient) GetProductInfo(code string, orderId int64) *Product {
	sku := SKU{Code: code}
	b1, _ := json.Marshal(sku)
	params := map[string]string{
		"q": string(b1),
	}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/sku/list", nil)
	if err != nil {
		println("GetSkuInfo failed: " + err.Error())
		return nil
	}

	var skuRes *SkuRes
	err = json.Unmarshal([]byte(result.Body), &skuRes)
	if err != nil {
		println("GetSkuInfo failed: " + err.Error())
		return nil
	}

	if skuRes.Status != common.APIStatus.Ok {
		println("[" + strconv.Itoa(int(orderId)) + " || " + code + "] GetSkuInfo failed with status: " + skuRes.Status)
		return nil
	}

	skuInfo := skuRes.Data[0]
	product := Product{ProductID: skuInfo.ProductID}
	b2, _ := json.Marshal(product)
	params = map[string]string{
		"q": string(b2),
	}
	result, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/product/list", nil)
	if err != nil {
		println("GetProductInfo failed: " + err.Error())
		return nil
	}

	var productRes *ProductRes
	err = json.Unmarshal([]byte(result.Body), &productRes)
	if err != nil {
		println("GetProductInfo failed: " + err.Error())
		return nil
	}

	if productRes.Status != common.APIStatus.Ok {
		println("[" + strconv.Itoa(int(orderId)) + " || " + code + "] GetProductInfo failed with status: " + productRes.Status)
		return nil
	}
	return &productRes.Data[0]
}

func (cli *productClient) GetSkuInfo(code string) *SKU {
	sku := SKU{Code: code}
	b1, _ := json.Marshal(sku)
	params := map[string]string{
		"q": string(b1),
	}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathSkuList, nil)
	if err != nil {
		return nil
	}

	var skuRes *SkuRes
	err = json.Unmarshal([]byte(result.Body), &skuRes)
	if err != nil {
		return nil
	}

	if skuRes.Status != common.APIStatus.Ok {
		return nil
	}

	return &skuRes.Data[0]
}

func (cli *productClient) GetSkuItemInfo(itemCode string) *SKU {
	sku := SKU{ItemCode: itemCode}
	b1, _ := json.Marshal(sku)
	params := map[string]string{
		"q": string(b1),
	}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathSkuList, nil)
	if err != nil {
		return nil
	}

	var skuRes *SkuRes
	err = json.Unmarshal([]byte(result.Body), &skuRes)
	if err != nil {
		return nil
	}

	if skuRes.Status != common.APIStatus.Ok {
		return nil
	}

	return &skuRes.Data[0]
}

func (cli *productClient) GetSkuList(sellerClass string, offset, limit int64) *SkuRes {
	sku := SKU{
		SellerClass: sellerClass,
	}
	b1, _ := json.Marshal(sku)
	params := map[string]string{
		"q":      string(b1),
		"offset": fmt.Sprintf("%v", offset),
		"limit":  fmt.Sprintf("%v", limit),
	}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathSkuItemList, nil)
	if err != nil {
		return nil
	}

	var skuRes *SkuRes
	err = json.Unmarshal([]byte(result.Body), &skuRes)
	if err != nil {
		return nil
	}

	if skuRes.Status != common.APIStatus.Ok {
		return nil
	}

	return skuRes
}

func (cli *productClient) GetProductInfoByID(productId, orderId int64) *Product {
	product := Product{ProductID: productId}
	b2, _ := json.Marshal(product)
	params := map[string]string{
		"q": string(b2),
	}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/product/list", nil)
	if err != nil {
		println("GetProductInfo failed: " + err.Error())
		return nil
	}

	var productRes *ProductRes
	err = json.Unmarshal([]byte(result.Body), &productRes)
	if err != nil {
		println("GetProductInfo failed: " + err.Error())
		return nil
	}

	if productRes.Status != common.APIStatus.Ok {
		println("[" + strconv.Itoa(int(orderId)) + " || " + strconv.Itoa(int(productId)) + "] GetProductInfo failed with status: " + productRes.Status)
		return nil
	}
	return &productRes.Data[0]
}

func (cli *productClient) GetProductInfoByCodes(codes []string) *[]Product {
	params := map[string]string{
		"codes": strings.Join(codes, ","),
		"limit": fmt.Sprintf("%d", len(codes)),
	}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/product/list", nil)
	if err != nil {
		println("GetProductInfo failed: " + err.Error())
		return nil
	}

	var productRes *ProductRes
	err = json.Unmarshal([]byte(result.Body), &productRes)
	if err != nil {
		println("GetProductInfo failed: " + err.Error())
		return nil
	}

	return &productRes.Data
}

type SellerSubCategoryRes struct {
	Status  string              `json:"status"`
	Data    []SellerSubCategory `json:"data,omitempty"`
	Message string              `json:"message"`
	Total   int64               `json:"total,omitempty"`
}
type SellerSubCategory struct {
	CategoryCode string `json:"categoryCode,omitempty" bson:"category_code,omitempty"`
	Code         string `json:"code,omitempty" bson:"code,omitempty"`
	Name         string `json:"name,omitempty" bson:"name,omitempty"`

	SaleFeePercent        float64 `json:"saleFeePercent,omitempty" bson:"sale_fee_percent,omitempty"`
	FulfillmentFeePercent float64 `json:"fulfillmentFeePercent,omitempty" bson:"fulfillment_fee_percent,omitempty"`
}

func (cli *productClient) GetSellerSubCategories() []SellerSubCategory {
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, nil, nil, "/marketplace/product/v2/seller-sub-category", nil)
	if err != nil {
		return nil
	}

	var response *SellerSubCategoryRes
	err = json.Unmarshal([]byte(result.Body), &response)
	if err != nil {
		return nil
	}

	if response.Status == common.APIStatus.Ok {
		return response.Data
	}
	return nil
}

type CategoryRes struct {
	common.APIResponse ",inline"
	Data               []Category `json:"data"`
}
type Category struct {
	CategoryCode          string  `json:"categoryCode"`
	Level                 string  `json:"level"`
	FulfillmentFeePercent float64 `json:"fulfillmentFeePercent"`
	SaleFeePercent        float64 `json:"saleFeePercent"`
}

func (cli *productClient) GetSellerCategoriesLevel() []Category {

	params := map[string]string{
		"offset": fmt.Sprintf("%v", 0),
		"limit":  fmt.Sprintf("%v", 100),
	}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/seller-category-for-admin", nil)
	if err != nil {
		return nil
	}

	var response *CategoryRes
	err = json.Unmarshal([]byte(result.Body), &response)
	if err != nil {
		return nil
	}

	if response.Status == common.APIStatus.Ok {
		return response.Data
	}
	return nil
}

func (cli *productClient) GetSkuSellerCodes(productID int64) interface{} {
	q := map[string]interface{}{
		"existSellerPrivateSKU": true,
		"productID":             productID,
	}
	b, _ := json.Marshal(q)
	params := map[string]string{
		"q":                 string(b),
		"getSellerCodeOnly": "true",
	}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/sku/list", nil)
	if err != nil {
		return nil
	}

	var response *common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &response)
	if err != nil {
		return nil
	}

	return response.Data
}

// GetListSku ...
func (cli *productClient) GetListSku(skus []string, locationCode string, customerID int64, isBrandSales bool, salesTypeCode string, isBrandGift ...bool) ([]*ProductData, *common.APIResponse) {

	var body = struct {
		Skus          []string `json:"skus"`
		LocationCode  string   `json:"locationCode"`
		CustomerID    int64    `json:"customerId"`
		GetProduct    bool     `json:"getProduct"`
		IsBrandSales  bool     `json:"isBrandSales"`
		IsBrandGift   bool     `json:"isBrandGift"`
		SalesTypeCode string   `json:"salesTypeCode"`
		SystemDisplay string   `json:"systemDisplay"`
		UseCache      bool     `json:"useCache"`
	}{
		Skus:         skus,
		LocationCode: locationCode,
		CustomerID:   customerID,
		GetProduct:   true,
		IsBrandSales: isBrandSales,
		UseCache:     true,
	}

	if salesTypeCode != "" {
		body.SalesTypeCode = salesTypeCode
	}
	if len(isBrandGift) > 0 && isBrandGift[0] {
		body.IsBrandGift = true
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathGetListSku, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetListSku",
		}
	}

	var result *ProductResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetListSku",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data, nil
}

func (cli *productClient) GetSingleSkuMain(sellerCode, skuCode string) *SkuMainResponse {
	skuMain := SkuMain{
		SellerCode: sellerCode,
		Code:       skuCode,
	}
	b, _ := json.Marshal(skuMain)
	params := map[string]string{
		"q": string(b),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/seller/"+sellerCode+"/sku-main", nil)
	if err != nil {
		return &SkuMainResponse{
			BaseAPIResponse: BaseAPIResponse{
				Status:    common.APIStatus.Error,
				Message:   err.Error(),
				ErrorCode: "MAKE_REQUEST_GetSkuMain",
			},
		}
	}

	var result *SkuMainResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &SkuMainResponse{
			BaseAPIResponse: BaseAPIResponse{
				Status:    common.APIStatus.Error,
				Message:   err.Error(),
				ErrorCode: "UNMARSHAL_GetSkuMain",
			},
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return &SkuMainResponse{
			BaseAPIResponse: BaseAPIResponse{
				Status:    result.Status,
				Message:   result.Message,
				ErrorCode: result.ErrorCode,
			},
		}
	}

	return result
}

// UpdateDealQuantity is func ...
func (cli *productClient) UpdateDealQuantity(payload *UpdateDealQuantityRequest) error {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, &payload, pathUpdateDealQuantity, nil)
	if err != nil {
		return err
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return fmt.Errorf("%v", result.Message)
	}

	return nil
}

// UpdateSkuQuantity is func ...
func (cli *productClient) UpdateSkuQuantity(payload *UpdateSkuQuantityRequest) error {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, &payload, pathUpdateSkuQuantity, nil)
	if err != nil {
		return err
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return fmt.Errorf("%v", result.Message)
	}

	return nil
}

type SkuLimitListQuery struct {
	SkuCodes  []string `json:"skuCodes,omitempty"`
	ItemCodes []string `json:"itemCodes,omitempty"`

	ForCustomerID *int64 `json:"forCustomerID,omitempty"`
	IsActive      *bool  `json:"isActive,omitempty"`
}

// GetSKULimitList ...
func (cli *productClient) GetSKULimitList(query *SkuLimitListQuery) ([]*model.SkuLimit, *common.APIResponse) {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, map[string]string{}, query, pathGetListSkuLimit, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetListSkuLimitConfig",
		}
	}

	var result *SkuLimitResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetListSkuLimitConfig",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data, nil
}

func (cli *productClient) GetSkuByLocation(query SKU) *SKU {
	// query := SKU{SellerCode: sellerCode, ProductID: productId}
	// b1, _ := json.Marshal(query)
	// params := map[string]string{
	// 	"q": string(b1),
	// }
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, query, pathSkuByLocationList, nil)
	if err != nil {
		return nil
	}

	var skuRes *SkuRes
	err = json.Unmarshal([]byte(result.Body), &skuRes)
	if err != nil {
		return nil
	}

	if skuRes.Status != common.APIStatus.Ok {
		return nil
	}

	return &skuRes.Data[0]
}

func (cli *productClient) GetSkus(query SKU) *[]SKU {
	// query := SKU{SellerCode: sellerCode, ProductID: productId}
	b1, _ := json.Marshal(query)
	params := map[string]string{
		"q": string(b1),
	}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathSkuByLocationList, nil)
	if err != nil {
		return nil
	}

	var skuRes *SkuRes
	err = json.Unmarshal([]byte(result.Body), &skuRes)
	if err != nil {
		return nil
	}

	if skuRes.Status != common.APIStatus.Ok {
		return nil
	}

	return &skuRes.Data
}

// GetListSkuBrandContract...
func (cli *productClient) GetListSkuBrandContract(skuCodes []string, source string, salesTypeCode *string) ([]*SkuBrandContract, *common.APIResponse) {

	params := map[string]string{
		"skuCodes": strings.Join(skuCodes, ","),
		"limit":    fmt.Sprintf("%d", len(skuCodes)),
	}

	if len(source) > 0 {
		value, exists := enum.SourceValueMap[enum.SourceValue(source)]
		if exists {
			model := SkuBrandContract{
				Source:   value,
				IsActive: true,
			}
			if salesTypeCode != nil && *salesTypeCode != "" {
				model.SalesTypeCode = *salesTypeCode
			}

			paramQ, _ := json.Marshal(model)
			params["q"] = string(paramQ)
		}
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetListSkuBrandContract, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetListSkuLimitConfig",
		}
	}

	var result *SkuBrandContractResponse
	err = json.Unmarshal([]byte(res.Body), &result)

	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetListSkuLimitConfig",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data, nil
}

func (cli *productClient) GetManufacturerByCode(code string) *Manufacturer {

	var manufacturer = Manufacturer{Code: code}
	b, _ := json.Marshal(manufacturer)
	params := map[string]string{
		"q": string(b),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, "/marketplace/product/v2/manufacturer", nil)
	if err != nil {
		return nil
	}

	var result *ManufacturerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil
	}

	return result.Data[0]
}

func (cli *productClient) GetManufacturerByCodes(codes []string) []*Manufacturer {

	if len(codes) == 0 {
		return nil
	}

	stringCodes := strings.Join(codes, ",")
	params := map[string]string{
		"codes": stringCodes,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetListManufacturer, nil)
	if err != nil {
		return nil
	}

	var result *ManufacturerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil
	}

	return result.Data
}
func (cli *productClient) GetCustomerPurchaseConfiguration(customerID int64) (*CustomerPurchaseConfiguration, *common.APIResponse) {
	var model = CustomerPurchaseConfiguration{
		CustomerID: customerID,
		IsActive:   true,
	}
	b, _ := json.Marshal(model)
	params := map[string]string{
		"q": string(b),
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetCustomerPurchaseConfiguration, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetCustomerPurchaseConfiguration",
		}
	}

	var result *CustomerPurchaseConfigurationResponse
	err = json.Unmarshal([]byte(res.Body), &result)

	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetCustomerPurchaseConfiguration",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data[0], nil
}

func (cli *productClient) GetListCustomerPurchaseConfigurationDetail(customerID int64) ([]*CustomerPurchaseConfigurationDetail, *common.APIResponse) {
	var model = CustomerPurchaseConfigurationDetail{
		CustomerID: customerID,
	}
	b, _ := json.Marshal(model)
	params := map[string]string{
		"q": string(b),
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetListCustomerPurchaseConfigurationDetail, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetCustomerPurchaseConfigurationDetail",
		}
	}

	var result *CustomerPurchaseConfigurationDetailResponse
	err = json.Unmarshal([]byte(res.Body), &result)

	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetCustomerPurchaseConfigurationDetail",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data, nil
}

// GetListSku ...
func (cli *productClient) GetSingleSku(sku string, locationCode string, customerID int64) (*ProductData, *common.APIResponse) {
	var body = struct {
		Skus          []string `json:"skus"`
		LocationCode  string   `json:"locationCode"`
		CustomerID    int64    `json:"customerId"`
		GetProduct    bool     `json:"getProduct"`
		IsBrandSales  bool     `json:"isBrandSales"`
		IsBrandGift   bool     `json:"isBrandGift"`
		SalesTypeCode string   `json:"salesTypeCode"`
		SystemDisplay string   `json:"systemDisplay"`
		UseCache      bool     `json:"useCache"`
		GetPrice      bool     `json:"getPrice"`
	}{
		Skus:         []string{sku},
		LocationCode: locationCode,
		CustomerID:   customerID,
		GetProduct:   true,
		UseCache:     true,
		GetPrice:     true,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathGetListSku, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetListSku",
		}
	}

	var result *ProductResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetListSku",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data[0], nil
}

// GetListSku ...
func (cli *productClient) GetProductFee(productIDs []int64, regionCode string, appliedTime *time.Time, keys []string) *ProductFeeConfigConcurrentlyRes {

	request := ProductFeeConfigConcurrentlyQuery{
		AppliedTime:        appliedTime,
		LocationCode:       regionCode,
		ProductIDs:         productIDs,
		AutoFillDefaultFee: true, // mode tự động lấy phí mặc định nếu không tìm thấy cài đặt phí
	}

	res, err := cli.feeClient.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, request, pathGetProductFeeMap, &keys)
	if err != nil {
		return &ProductFeeConfigConcurrentlyRes{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetProductFee",
		}
	}

	var result *ProductFeeConfigConcurrentlyRes
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &ProductFeeConfigConcurrentlyRes{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetProductFee",
		}
	}

	return result
}
