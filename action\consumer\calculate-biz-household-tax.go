package consumer

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func HandleCalculateBizHouseholdTax(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return nil
	}

	var input model.BizHouseholdTaxItem
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return nil
	}

	return reconcile_action.ProcessCalculateBizHouseholdTax(input.SellerCode, input.ReconcileScheduleTimeIndex)
}
