package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// OrderSeller ...
type OrderSeller struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// reference data
	OrderSellerID   int64                  `json:"orderSellerId,omitempty" bson:"order_seller_id,omitempty"`     //
	OrderSellerCode string                 `json:"orderSellerCode,omitempty" bson:"order_seller_code,omitempty"` //
	ParentOrderID   int64                  `json:"parentOrderId,omitempty" bson:"parent_order_id,omitempty"`     //
	ParentOrderCode string                 `json:"parentOrderCode,omitempty" bson:"parent_order_code,omitempty"` //
	SellerCode      string                 `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`            //
	SellerClass     *enum.SellerClassValue `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`

	// customer info
	AccountID                int64   `json:"accountId,omitempty" bson:"account_id,omitempty"`                              // mã tài khoản
	CustomerID               int64   `json:"customerId,omitempty" bson:"customer_id,omitempty"`                            // mã khách hàng
	CustomerCode             string  `json:"customerCode,omitempty" bson:"customer_code,omitempty"`                        // mã khách hàng
	CustomerName             string  `json:"customerName,omitempty" bson:"customer_name,omitempty"`                        // tên người nhận
	CustomerPhone            string  `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`                      // điện thoại người nhận
	CustomerEmail            *string `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`                      // email người nhận
	CustomerShippingAddress  string  `json:"customerShippingAddress,omitempty" bson:"customer_shipping_address,omitempty"` // địa chỉ người nhận
	CustomerDistrictCode     string  `json:"customerDistrictCode,omitempty" bson:"customer_district_code,omitempty"`       // khu vực nhận
	CustomerWardCode         string  `json:"customerWardCode,omitempty" bson:"customer_ward_code,omitempty"`               //
	CustomerProvinceCode     string  `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`       //
	CustomerOrderSellerIndex int     `json:"customerOrderSellerIndex,omitempty" bson:"customer_order_index,omitempty"`     //
	InvoiceRequest           *bool   `json:"invoiceRequest,omitempty" bson:"invoice_request,omitempty"`

	RequiredLicense bool             `json:"requiredLicense,omitempty" bson:"required_license,omitempty"` // yêu cầu giấy phép người mua
	CustomerLicense *CustomerLicense `json:"customerLicense,omitempty" bson:"customer_license,omitempty"` // giấy phép người mua

	// price
	ActualSellerPrice *int `json:"actualSellerPrice,omitempty" bson:"actual_seller_price,omitempty"`
	TotalSellerPrice  *int `json:"totalSellerPrice,omitempty" bson:"total_seller_price,omitempty"`
	ActualPrice       *int `json:"actualPrice,omitempty" bson:"actual_price,omitempty"`
	TotalPrice        *int `json:"totalPrice,omitempty" bson:"total_price,omitempty"`       // tổng tiền đơn hàng sau cùng
	Price             *int `json:"price,omitempty" bson:"price,omitempty"`                  // tổng tiển chưa trừ các khoảng khác
	TotalDiscount     *int `json:"totalDiscount,omitempty" bson:"total_discount,omitempty"` // tổng số tiền được giảm

	RevenueDisplay        *int `json:"revenueDisplay,omitempty" bson:"-"`        // doanh thu
	TotalBookingDisplay   *int `json:"totalBookingDisplay,omitempty" bson:"-"`   // tổng cộng đặt
	TotalDeliveredDisplay *int `json:"totalDeliveredDisplay,omitempty" bson:"-"` // tổng cộng giao

	// order info
	ConfirmationDate *time.Time                 `json:"confirmationDate,omitempty" bson:"confirmation_date,omitempty"` // ngày xác nhận
	Note             *string                    `json:"note,omitempty" bson:"note,omitempty"`                          // ghi chú đơn hàng
	PrivateNote      *string                    `json:"privateNote,omitempty" bson:"private_note,omitempty"`           // ghi chú nội bộ đơn hàng
	SaleOrderCode    *string                    `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`      // so
	Status           enum.OrderStateValue       `json:"status,omitempty" bson:"status,omitempty"`                      // trạng thái đơn hàng
	ProcessingStatus enum.OrderSellerStateValue `json:"processingStatus,omitempty" bson:"processing_status,omitempty"` // trạng thái thao tác đơn hàng
	CompletedTime    *time.Time                 `json:"completedTime,omitempty" bson:"completed_time,omitempty"`       // thời gian hoàn tất đơn hàng -- status = completed
	SessionPayment   *string                    `json:"sessionPayment,omitempty" bson:"session_payment,omitempty"`     //phiên giao dịch kế toán nhập
	// promotion
	RedeemCode []*string `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"` // mã giảm giá

	// items
	OrderItems       []*OrderItem       `json:"orderItems,omitempty" bson:"-"`
	OrderSellerItems []*OrderSellerItem `json:"orderSellerItems,omitempty" bson:"-"`
	ComplexQuery     []*bson.M          `json:"-" bson:"$and,omitempty"`
	ComplexQueryOr   []*bson.M          `json:"-" bson:"$or,omitempty"`

	//
	ReconciliationStatus ReconciliationStatusValue `json:"reconciliationStatus,omitempty" bson:"reconciliation_status,omitempty"`
	ReconciledTime       *time.Time                `json:"reconciledTime,omitempty" bson:"reconciledTime,omitempty"` // thời gian hoàn tất đối soát
	VAT                  float64                   `json:"vat,omitempty" bson:"vat,omitempty"`

	// Return info
	ReturnTickets             *[]*SellerReturnTicket `json:"returnTickets,omitempty" bson:"return_tickets,omitempty"`
	ReturnStatus              *ReturnStatusValue     `json:"returnStatus,omitempty" bson:"return_status,omitempty"`
	TotalReturnAmount         *int                   `json:"totalReturnAmount,omitempty" bson:"total_return_amount,omitempty"`
	TotalCompleteReturnAmount *int                   `json:"totalCompleteReturnAmount,omitempty" bson:"total_complete_return_amount,omitempty"`

	// payment & delivery
	PaymentMethod  string     `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`   // phương thức thanh toán cod/chuyển khoản
	DeliveryMethod string     `json:"deliveryMethod,omitempty" bson:"delivery_method,omitempty"` // hình thức giao hàng
	DeliveryDate   *time.Time `json:"deliveryDate,omitempty" bson:"delivery_date,omitempty"`     // ngày giao mong muốn

	WarmupIndex string `json:"-" bson:"warmup_index,omitempty"`
	/// used only for query, not for saving data
	PriceFrom          *int       `json:"priceFrom,omitempty" bson:"-"`
	PriceTo            *int       `json:"priceTo,omitempty" bson:"-"`
	DateFrom           *time.Time `json:"timeFrom,omitempty" bson:"-"`
	DateTo             *time.Time `json:"timeTo,omitempty" bson:"-"`
	HasProductCode     string     `json:"hasProductCode,omitempty" bson:"-"`
	HasProductID       int64      `json:"hasProductID,omitempty" bson:"-"`
	HasSkuCode         string     `json:"hasSkuCode,omitempty" bson:"-"`
	ReturnTicketID     int64      `json:"returnTicketID,omitempty" bson:"-"`
	ReturnTicketStatus string     `json:"returnTicketStatus,omitempty" bson:"-"`

	StartCompletedTime *time.Time `json:"startCompletedTime,omitempty" bson:"-"`
	EndCompletedTime   *time.Time `json:"endCompletedTime,omitempty" bson:"-"`

	ReceiveInvoiceInfoBy string `json:"receiveInvoiceInfoBy,omitempty" bson:"-"`
}

func (item *OrderSeller) AutoSetSellerClass() {
	internalSeller := SELLER_INTERNALS
	if utils.IsContains(internalSeller, item.SellerCode) {
		item.SellerClass = &enum.SellerClass.INTERNAL
	} else {
		item.SellerClass = &enum.SellerClass.EXTERNAL
	}
}

type CustomerLicense struct {
	Licenses                       *[]*DocumentImage `json:"licenses,omitempty" bson:"licenses,omitempty"`                                                // tài liệu giấy phép kinh doanh
	PharmacyEligibilityLicense     *[]*DocumentImage `json:"pharmacyEligibilityLicense,omitempty" bson:"pharmacy_eligibility_license,omitempty"`          // tài liệu giấy phép đủ điều kiện kinh doanh dược
	ExaminationAndTreatmentLicense *[]*DocumentImage `json:"examinationAndTreatmentLicense,omitempty" bson:"examination_and_treatment_license,omitempty"` // tài liệu chứng nhận khám và chữa bệnh
	Gpp                            *[]*DocumentImage `json:"gpp,omitempty" bson:"gpp,omitempty"`
}

type DocumentImage struct {
	Name      string `json:"name,omitempty" bson:"name,omitempty"`
	PublicURL string `json:"publicURL,omitempty" bson:"public_url,omitempty"`
}

type SellerReturnTicket struct {
	TicketID     int64                   `json:"ticketID,omitempty" bson:"ticket_id,omitempty"`
	Status       ReturnTicketStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	ReturnAmount int                     `json:"returnAmount,omitempty" bson:"return_amount,omitempty"`
	Ticket       *ReturnTicket           `json:"ticket,omitempty" bson:"-"`
}

// ReturnStatusValue
type ReturnStatusValue string
type returnStatusValue struct {
	NEW        ReturnStatusValue
	PROCESSING ReturnStatusValue
	COMPLETED  ReturnStatusValue
	CANCEL     ReturnStatusValue
}

var ReturnStatusState = &returnStatusValue{
	"NEW",
	"PROCESSING",
	"COMPLETED",
	"CANCEL",
}

var ReturnStatusCSMap = map[ReturnTicketStatusValue]ReturnStatusValue{
	ReturnTicketStatus.NEW:                    "",
	ReturnTicketStatus.WAIT_TO_CONFIRM:        "",
	ReturnTicketStatus.CONFIRMED:              ReturnStatusState.NEW,
	ReturnTicketStatus.RETURNING:              ReturnStatusState.NEW,
	ReturnTicketStatus.WAIT_TO_CONFIRM_RETURN: "",
	ReturnTicketStatus.RETURNED:               ReturnStatusState.NEW,
	ReturnTicketStatus.PROCESSING:             ReturnStatusState.PROCESSING,
	ReturnTicketStatus.WAIT_TO_PAY:            ReturnStatusState.PROCESSING,
	ReturnTicketStatus.COMPLETED:              ReturnStatusState.COMPLETED,
	ReturnTicketStatus.CANCELLED:              ReturnStatusState.CANCEL,
}

// OrderSellerDB ...
var OrderSellerDB = &db.Instance{
	ColName:        "order-seller",
	TemplateObject: &OrderSeller{},
}

// OrderSellerDB ...
var OrderSellerReadDB = &db.Instance{
	ColName:        "order-seller",
	TemplateObject: &OrderSeller{},
}

// OrderSellerDeletedDB ...
var OrderSellerDeletedDB = &db.Instance{
	ColName:        "order-seller-deleted",
	TemplateObject: &OrderSeller{},
}

func InitOrderSellerReadModel(s *mongo.Database) {
	OrderSellerReadDB.ApplyDatabase(s)
}

// InitOrderSellerModel is func init model sale order
func InitOrderSellerModel(s *mongo.Database) {
	OrderSellerDB.ApplyDatabase(s)
}

// InitOrderSellerDeletedModel is func init model sale order deleted
func InitOrderSellerDeletedModel(s *mongo.Database) {
	OrderSellerDeletedDB.ApplyDatabase(s)
}
