package tool

import (
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

func getPriceItem(order *model.Order, orderItem *model.OrderItem) int {
	switch {
	case orderItem.Type == enum.ItemType.DEAL && orderItem.ChargeDealFee == "SELLER":
		return getDealPriceInvoice(order, orderItem)
	case orderItem.Type == enum.ItemType.CAMPAIGN:
		return getCampaignPriceInvoice(order, orderItem)
	default:
		return getDefaultPriceInvoice(order, orderItem)
	}
}

func getDealPriceInvoice(order *model.Order, orderItem *model.OrderItem) int {
	itemPrice := orderItem.Price
	if *orderItem.SkuPriceType == enum.PriceType.FIXED_REVENUE {
		for _, feeValue := range orderItem.Fee.Result {
			if feeValue == nil {
				continue
			}
			if feeValue.FeeType == "CHARGE_BUYER" {
				itemPrice -= int(feeValue.FeeValue)
			}
		}
	}

	return getPriceInvoice(order.Status, itemPrice, orderItem)
}

func getCampaignPriceInvoice(order *model.Order, orderItem *model.OrderItem) int {
	itemPrice := orderItem.SellerPrice - orderItem.ChargeCampaignFeeValue

	return getPriceInvoice(order.Status, itemPrice, orderItem)
}

func getDefaultPriceInvoice(order *model.Order, orderItem *model.OrderItem) int {
	itemPrice := orderItem.SellerPrice
	if *orderItem.SkuPriceType == enum.PriceType.FIXED_PRICE && orderItem.Fee != nil {
		for _, feeValue := range orderItem.Fee.Result {
			if feeValue == nil {
				continue
			}
			if feeValue.FeeType == "CHARGE_BUYER" {
				itemPrice -= int(feeValue.FeeValue)
			}
		}
	}

	return getPriceInvoice(order.Status, itemPrice, orderItem)
}

func getPriceInvoice(
	orderStatus enum.OrderStateValue,
	itemPrice int,
	orderItem *model.OrderItem,
) int {
	switch orderStatus {
	case enum.OrderState.Completed:
		if orderItem.CompletedQuantity == nil {
			return 0
		}
		return itemPrice * *orderItem.CompletedQuantity
	case enum.OrderState.Delivered:
		if orderItem.DeliveredQuantity == nil {
			return 0
		}
		return itemPrice * *orderItem.DeliveredQuantity
	}
	return 0
}
