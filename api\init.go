package api

import (
	"encoding/json"
	"log"
	"strings"

	"github.com/mssola/user_agent"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

func wrapActionSource(req sdk.APIRequest) *model.Account {
	account := getActionSource(req)
	if account != nil && (account.Type == "EMPLOYEE" || account.Type == "PARTNER") {
		if accountID := sdk.ParseInt64(req.GetParam("accountId"), 0); accountID > 0 {
			return &model.Account{
				AccountID: accountID,
				Type:      enum.AccountType.CUSTOMER,
			}
		}
		return nil
	}
	return account
}

// GetActionSource ...
func getActionSource(req sdk.APIRequest) *model.Account {
	var source []*model.ActionSource
	sourceAttr := req.GetAttribute("X-Source")
	if sourceAttr != nil {
		source = sourceAttr.([]*model.ActionSource)
		if source == nil || source[0] == nil || source[0].Account == nil || source[0].Account.AccountID <= 0 {
			return nil
		}
		return source[0].Account
	}
	sourceStr := req.GetHeader("X-Source")
	if sourceStr == "" {
		return nil
	}

	var sourceHeader *model.ActionSource
	err := json.Unmarshal([]byte(sourceStr), &sourceHeader)
	if err != nil || sourceHeader == nil || sourceHeader.Account == nil {
		return nil
	}
	return sourceHeader.Account
}

// GetActionSource ...
func getActionSourceAllInfo(req sdk.APIRequest) (*model.Account, *model.Session) {

	var source []*model.ActionSource
	sourceAttr := req.GetAttribute("X-Source")
	if sourceAttr != nil {
		source = sourceAttr.([]*model.ActionSource)
		if source == nil || source[0] == nil || source[0].Account == nil || source[0].Account.AccountID <= 0 {
			return nil, nil
		}
		return source[0].Account, source[0].Session
	}
	sourceStr := req.GetHeader("X-Source")
	if sourceStr == "" {
		return nil, nil
	}

	var sourceHeader *model.ActionSource
	err := json.Unmarshal([]byte(sourceStr), &sourceHeader)
	if err != nil || sourceHeader == nil || sourceHeader.Account == nil {
		return nil, nil
	}
	return sourceHeader.Account, sourceHeader.Session
}

func wrapActionSourceAllInfo(req sdk.APIRequest) (*model.Account, *model.Session) {
	// return &model.Account{
	// 		// AccountID: 10436, // C.Vy
	// 		// AccountID: 1001532, // Thiện
	// 		AccountID: 23278, // A.Phi
	// 		Type:      enum.AccountType.CUSTOMER,
	// 	}, &model.Session{
	// 		EntityCode: "YVXBTCPL",
	// 	}

	account, session := getActionSourceAllInfo(req)
	if account != nil && (account.Type == "EMPLOYEE" || account.Type == "PARTNER") {
		if accountID := sdk.ParseInt64(req.GetParam("accountId"), 0); accountID > 0 {
			return &model.Account{
				AccountID: accountID,
				Type:      enum.AccountType.CUSTOMER,
			}, session
		}
		return nil, nil
	}
	return account, session
}

type UAInfo struct {
	Platform      string // web, mobile-web, mobile-app
	OSName        string
	OSVersion     string
	ClientName    string
	ClientVersion string
}

func getUAInfo(uaStr string) (result *UAInfo) {

	defer func() {
		if rec := recover(); rec != nil {
			log.Println("Panic & Recovered", uaStr, rec)
		}
	}()

	result = &UAInfo{}
	if strings.HasPrefix(uaStr, "thuocsi") {
		parts := strings.Split(uaStr, " ")
		result.Platform = "mobile-app"
		clientInfo := strings.Split(parts[0], "/")
		result.ClientName = clientInfo[0]
		if len(clientInfo) > 1 {
			result.ClientVersion = clientInfo[1]
		} else {
			log.Println("Unknown ClientVersion UA " + uaStr)
		}
		if len(parts) > 3 {
			osInfo := strings.Split(parts[3], "/")
			result.OSName = osInfo[0]
			if len(osInfo) > 1 {
				result.OSVersion = osInfo[1]
			}
		}

	} else {
		ua := user_agent.New(uaStr)
		os := ua.OSInfo()
		result.OSName = os.Name
		result.OSVersion = os.Version
		if ua.Mobile() {
			result.Platform = "mobile-web"
		} else {
			result.Platform = "web-desktop"
		}
		result.ClientName, result.ClientVersion = ua.Browser()
	}

	// normalize os name
	osName := strings.ToLower(result.OSName)
	if strings.Contains(uaStr, "iPad") && strings.Contains(osName, "os") {
		result.OSName = "iPad OS"
	} else if strings.Contains(osName, "iphone") || strings.Contains(osName, "ios") {
		result.OSName = "iOS"
	} else {
		result.OSName = strings.Title(result.OSName)
	}

	return result
}

func verifyOffset(req sdk.APIRequest, offset int64) *common.APIResponse {
	account := getActionSource(req)
	if account.Type != enum.AccountType.PARTNER && account.Type != enum.AccountType.BRAND_SALES && offset >= 2000 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Do not allow a high offset",
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}
