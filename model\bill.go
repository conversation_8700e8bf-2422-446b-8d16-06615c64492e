package model

import "time"

type Bill struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CompletedTime   *time.Time `json:"completedTime,omitempty" bson:"completed_time,omitempty"`

	BillId   int64  `json:"billId,omitempty" bson:"bill_id,omitempty"`
	BillCode string `json:"billCode,omitempty" bson:"bill_code,omitempty"`

	Type             string `json:"type,omitempty" bson:"type,omitempty"`
	CustomerId       int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	OrderId          int64  `json:"orderId,omitempty" bson:"order_id,omitempty"`
	SaleOrderCode    string `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	OrderAmount      int    `json:"orderAmount,omitempty" bson:"order_amount,omitempty"`            // giá trị đơn hàng chưa tính vat
	Vat              int    `json:"vat,omitempty" bson:"vat,omitempty"`                             // tổng giá trị vat
	TotalOrderAmount int    `json:"totalOrderAmount,omitempty" bson:"total_order_amount,omitempty"` // tổng số tiền đơn hàng
	SpentAmount      int    `json:"spentAmount,omitempty" bson:"spent_amount,omitempty"`            // tổng số tiền chi
	ReceivedAmount   int    `json:"receivedAmount,omitempty" bson:"received_amount,omitempty"`      // tổng số tiền đã nhận
	RemainAmount     int    `json:"remainAmount,omitempty" bson:"remain_amount,omitempty"`          // số tiền còn lại
	ActualAmount     int    `json:"actualAmount,omitempty" bson:"actual_amount,omitempty"`          // số tiền thực thu
	LostAmount       int    `json:"lostAmount,omitempty" bson:"lost_amount,omitempty"`              // số tiền mất hàng
	ReturnAmount     int    `json:"returnAmount,omitempty" bson:"return_amount,omitempty"`          // số tiền cấn trừ hàng trả
	Status           string `json:"status,omitempty" bson:"status,omitempty"`
	Note             string `json:"note,omitempty" bson:"omitempty"`
	IsAutoCreate     bool   `json:"isAutoCreate,omitempty" bson:"is_auto_create,omitempty"`
	IsAutoComplete   bool   `json:"isAutoComplete,omitempty" bson:"is_auto_complete,omitempty"`
	AutoCompleteFail string `json:"autoCompleteFail,omitempty" bson:"auto_complete_fail,omitempty"`

	CompletedDebtTime *time.Time `json:"completedDebtTime,omitempty"` // thời gian hoàn tất công nợ
}

type OrderBillResponse struct {
	Status  string  `json:"status"`
	Message string  `json:"message"`
	Data    []*Bill `json:"data"`
}
