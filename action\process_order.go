package action

import (
	"fmt"
	"strconv"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"

	"github.com/panjf2000/ants/v2"
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

type itemError struct {
	Sku       string `json:"sku"`
	ErrorCode string `json:"errorCode"`
	Message   string `json:"message"`
}

var keyMatchItem = "SKU_%s_TYPE_%s"

func ProcessOrder(acc *model.Account, req *model.OrderProcessRequest) *common.APIResponse {
	order, err := getOrder(req.OrderId)
	if req.ActionTime == nil {
		req.ActionTime = utils.ParseTimeToPointer(time.Now())
	}
	if err != nil {
		return err
	}

	t1 := time.Date(2021, time.November, 1, 0, 0, 0, 0, time.UTC)
	t := t1.Add(-7 * time.Hour)
	isSkip := false

	if order.CreatedTime.Before(t) {
		isSkip = true
	}

	if !req.SkipUpdateStatus && !isValidStatus(order.Status, req.Status) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Status invalid, current status = %s", order.Status),
			ErrorCode: "STATUS_INVALID",
		}
	}

	// FILL content
	if req.PrivateNote != "" {
		req.PrivateNote = fmt.Sprintf("%s\n%s", order.PrivateNote, req.PrivateNote)
	}
	if req.Note != "" {
		newNote := order.Note
		if newNote != nil {
			req.Note = fmt.Sprintf("%s\n%s", *newNote, req.Note)
		}
	}
	if (order.IsSplitDeliveryOrder == nil || (order.IsSplitDeliveryOrder != nil && !*order.IsSplitDeliveryOrder)) && req.IsSplitDeliveryOrder != nil && *req.IsSplitDeliveryOrder {
		req.Tags = append(order.Tags, enum.Tag.MULTIPLE_DELIVERY_ORDER)

		if req.SplitType == enum.SplitType.MULTIPLE_WAREHOUSE {
			isContain := false
			for _, tag := range req.Tags {
				if tag == enum.Tag.SPLIT_MULTIPLE_WAREHOUSE {
					isContain = true
					break
				}
			}
			if !isContain {
				req.Tags = append(req.Tags, enum.Tag.SPLIT_MULTIPLE_WAREHOUSE)
			}
		}
	}
	if req.DeliveryOrderCode != "" {
		newDoStatus := make([]*model.DeliveryOrderStatus, 0)
		voucherAmountDetails := make([]*model.VoucherAmountDetail, 0)
		if qCalDiscount := model.CalOrderDiscountHistoryDB.QueryOne(model.CalOrderDiscountHistory{
			OrderID:           order.OrderID,
			DeliveryOrderCode: req.DeliveryOrderCode,
		}); qCalDiscount.Status == common.APIStatus.Ok {
			voucherAmountDetails = qCalDiscount.Data.([]*model.CalOrderDiscountHistory)[0].VoucherAmountDetails
		}
		isMatchCurrentDo := false
		for _, doStatus := range order.DeliveryOrderStatuses {
			if doStatus.Code == req.DeliveryOrderCode {
				doStatus.Status = req.DeliveryOrderStatus
				doStatus.DeliveryStatus = req.DeliveryStatus
				doStatus.DeliveryCarrier = req.DeliveryCarrier
				doStatus.DeliveryTrackingNumber = req.DeliveryTrackingNumber
				doStatus.DeliveryCarrierCode = req.DeliveryCarrierCode
				doStatus.CodAmount = req.CodAmount
				doStatus.VoucherDiscount = req.VoucherDiscount
				doStatus.PaymentDiscount = req.PaymentDiscount
				isMatchCurrentDo = true
				doStatus.VoucherAmountDetails = voucherAmountDetails
			}
			newDoStatus = append(newDoStatus, doStatus)
		}
		if isMatchCurrentDo {
			req.DeliveryOrderStatuses = newDoStatus
		} else {
			req.DeliveryOrderStatuses = append(order.DeliveryOrderStatuses, &model.DeliveryOrderStatus{
				Code:                   req.DeliveryOrderCode,
				Status:                 req.DeliveryOrderStatus,
				DeliveryStatus:         req.DeliveryStatus,
				DeliveryTrackingNumber: req.DeliveryTrackingNumber,
				DeliveryCarrier:        req.DeliveryCarrier,
				DeliveryCarrierCode:    req.DeliveryCarrierCode,
				CodAmount:              req.CodAmount,
				VoucherDiscount:        req.VoucherDiscount,
				PaymentDiscount:        req.PaymentDiscount,
				VoucherAmountDetails:   voucherAmountDetails,
			})
		}
	}

	// END FILL content

	mapItemCurrent := make(map[string]*model.OrderItem)
	mapItemUpdate := make(map[string]*model.OrderItem)
	isItemWithNoType := false
	if len(req.Items) > 0 {
		if len(req.Items[0].Type) == 0 {
			isItemWithNoType = true
		}
	}

	for _, item := range order.Items {
		if isItemWithNoType {
			mapItemCurrent[fmt.Sprintf(keyMatchItem, item.Sku, "")] = item
		} else {
			mapItemCurrent[fmt.Sprintf(keyMatchItem, item.Sku, item.Type)] = item
		}
	}

	var itemErrors = make([]*itemError, 0)
	for _, item := range req.Items {
		/* validate quantity */
		curItem, ok := mapItemCurrent[fmt.Sprintf(keyMatchItem, item.Sku, item.Type)]
		if !ok {
			itemErrors = append(itemErrors, &itemError{Sku: item.Sku, ErrorCode: "NOT_FOUND"})
			continue
		}
		if item.Quantity == nil {
			continue
		}

		if !isSkip {
			if err := isValidQuantity(curItem, item.Quantity, req.Status); err != nil {
				itemErrors = append(itemErrors, err)
				continue
			}
		}

		/* end validate */
		mapSubItemReq := make(map[string]*model.OrderItemProcessRequest)
		if len(item.SubItems) > 0 {
			for _, subItem := range item.SubItems {
				mapSubItemReq[subItem.Sku] = subItem
			}
		}
		switch req.Status {
		case enum.OrderState.Reserving:
			curItem.ReservedQuantity = item.Quantity
			curItem.ReserveInfos = item.ReserveInfos
			if curItem.SubItems != nil {
				for _, subItem := range *curItem.SubItems {
					if data, ok := mapSubItemReq[subItem.Sku]; ok && data != nil {
						subItem.OutboundQuantity = data.Quantity
						subItem.ReserveInfos = data.ReserveInfos
					}
				}
			}
			req.SkipUpdateStatus = true
		case enum.OrderState.Delivering:
			curItem.OutboundQuantity = item.Quantity
			curItem.OutboundInfos = item.OutboundInfos
			if curItem.SubItems != nil {
				for _, subItem := range *curItem.SubItems {
					if data, ok := mapSubItemReq[subItem.Sku]; ok && data != nil {
						subItem.OutboundQuantity = data.Quantity
						subItem.OutboundInfos = data.OutboundInfos
					}
				}
			}
		case enum.OrderState.Delivered:
			curItem.DeliveredQuantity = item.Quantity
		case enum.OrderState.Returned:
			curItem.ReturnedQuantity = item.Quantity
			curItem.ReturnInfos = item.ReturnInfos
			if curItem.SubItems != nil {
				for _, subItem := range *curItem.SubItems {
					if data, ok := mapSubItemReq[subItem.Sku]; ok && data != nil {
						subItem.ReturnedQuantity = data.Quantity
						subItem.ReturnInfos = data.ReturnInfos
					}
				}
			}
			req.SkipUpdateStatus = true
		case enum.OrderState.Completed:
			curItem.CompletedQuantity = item.Quantity
		}
		//mapItemUpdate[curItem.Sku] = curItem
		mapItemUpdate[fmt.Sprintf(keyMatchItem, curItem.Sku, curItem.Type)] = curItem
	}
	if len(itemErrors) > 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      itemErrors,
			Message:   "Item invalid",
			ErrorCode: "ITEM_INVALID",
			Total:     int64(len(itemErrors)),
		}
	}

	if req.Status == enum.OrderState.Returned || req.Status == enum.OrderState.Completed {
		handleCalcCompletedAmount(order, mapItemCurrent)

		req.ActualPrice = order.ActualPrice
		req.ActualTotalPrice = order.ActualTotalPrice
	}
	if order.SaleOrderStatus == enum.SaleOrderStatus.Completed {
		req.SaleOrderStatus = ""
	}

	if req.Status == enum.OrderState.Completed && !req.SkipUpdateStatus {
		now := time.Now()
		req.CompletedTime = &now
		// APM-525: Cập nhập trạng thái hoàn tất của đơn hàng
		orderBill, err := getOrderBill(req.OrderId)

		req.SaleOrderStatus = enum.SaleOrderStatus.Completed
		req.SkipUpdateStatus = err != nil || orderBill == nil || orderBill.Status != enum.BillStatus.Done
		if req.SkipUpdateStatus == true {
			readyTime := time.Now().Add(5 * time.Minute)
			_ = model.ReCheckCompletedOrderJob.Push(
				CheckCompletedOrder{
					OrderId: order.OrderID,
				},
				&job.JobItemMetadata{
					Keys: []string{
						TopicCompleteOrder,
						strconv.Itoa(int(order.OrderID)),
					},
					Topic:     "default",
					ReadyTime: &readyTime,
				},
			)
		}
	}

	if req.SkipUpdateStatus {
		req.StatusUpdate = ""
	} else {
		req.StatusUpdate = req.Status
		switch req.Status {
		case enum.OrderState.Processing:
			if order.ProcessStartTime == nil {
				req.ProcessStartTime = req.ActionTime
			}
		case enum.OrderState.Delivering:
			if order.OutboundDate == nil {
				req.OutboundDate = req.ActionTime
			}
		case enum.OrderState.Delivered:
			if order.DeliveredTime == nil {
				req.DeliveredTime = req.ActionTime
			}
		case enum.OrderState.Canceled:
			if order.CancelTime == nil {
				req.CancelTime = req.ActionTime
			}
		}

	}
	res := updateFullOrder(req, mapItemUpdate)
	if res.Status == common.APIStatus.Ok {
		orderUpdated := res.Data.([]*model.Order)[0]
		orderUpdated.Status = req.Status
		processOrderTask(order, orderUpdated, req)

		if GetKiotvietConfigs().AllowSyncUpdateOrderKiotviet {
			if utils.IsContains(orderUpdated.CustomerTags, "KIOTVIET") {
				if enum.SaleOrderStatus.Confirmed == req.SaleOrderStatus {
					go sdk.Execute(func() {
						SyncCreateOrderInfoToKiotvietPartner(orderUpdated)
					})
				} else {
					isValid := IsContainsT([]enum.SaleOrderStateValue{
						enum.SaleOrderStatus.Returned,
						enum.SaleOrderStatus.Cancel,
					}, req.SaleOrderStatus)
					if isValid {
						go sdk.Execute(func() {
							SyncUpdateOrderStatusToKiotvietPartner(orderUpdated, nil)
						})
					}
					if req.Status == enum.OrderState.Delivered && req.SaleOrderStatus == enum.SaleOrderStatus.Delivered {
						go sdk.Execute(func() {
							SyncUpdateOrderStatusToKiotvietPartner(orderUpdated, nil)
						})
					}
					if req.Status == enum.OrderState.Completed && req.SaleOrderStatus == enum.SaleOrderStatus.Completed {
						go sdk.Execute(func() {
							ticketResp := client.Services.Ticket.GetCSReturnTickets(
								&model.ReturnTicket{
									OrderID: orderUpdated.OrderID,
								},
								0, 1, true, "",
							)

							if ticketResp.Status == common.APIStatus.Ok {
								ticket := ticketResp.Data

								if len(ticket) > 0 {
									SyncUpdateOrderStatusToKiotvietPartner(orderUpdated, ticket[0])
								} else {
									SyncUpdateOrderStatusToKiotvietPartner(orderUpdated, nil)
								}
							} else {
								SyncUpdateOrderStatusToKiotvietPartner(orderUpdated, nil)
							}
						})
					}
				}
			}
		}
	}
	return res
}
func updateOrderItemFunc(updater *model.OrderItem) *common.APIResponse {
	query := &model.OrderItem{OrderID: updater.OrderID, Sku: updater.Sku, Type: updater.Type}
	orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{OrderID: updater.OrderID, OrderCode: updater.OrderCode}, "OrderRequestProcessByCustomer")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	return orderItemPartitionDB.UpdateOne(query, updater)
}

func updateFullOrder(order *model.OrderProcessRequest, mapItems map[string]*model.OrderItem) *common.APIResponse {
	updateOrder := model.OrderDB.UpdateOne(model.Order{OrderID: order.OrderId}, order)
	if updateOrder.Status != common.APIStatus.Ok {
		return updateOrder
	}
	orderUpdated := updateOrder.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(orderUpdated, "updateFullOrder")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	if order.Status == enum.OrderState.Completed {
		completeOrderSeller(order.OrderId)
	}

	if len(mapItems) > 100 {
		// performance normal
		/*
			100 line -> 500ms
			300 line -> 800ms
			1000 line -> 1,5s
		*/
		var wg sync.WaitGroup
		errs := make([]*common.APIResponse, 0)
		p, _ := ants.NewPoolWithFunc(len(mapItems)/4+50, func(updater interface{}) {
			es := updateOrderItemFunc(updater.(*model.OrderItem))
			if es.Status != common.APIStatus.Ok {
				errs = append(errs, es)
			}
			wg.Done()
		})
		defer p.Release()
		for idx, item := range mapItems {
			mapItems[idx].OrderID = order.OrderId
			wg.Add(1)
			_ = p.Invoke(item)
		}
		wg.Wait()

		if len(errs) > 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Update order item failed",
				Data:    errs,
			}
		}
	} else {
		errs := make([]*common.APIResponse, 0)
		for _, item := range mapItems {
			res := orderItemPartitionDB.UpdateOne(model.OrderItem{OrderID: order.OrderId, Sku: item.Sku, Type: item.Type}, item)
			if res != nil && res.Status != common.APIStatus.Ok {
				errs = append(errs, res)
			}
		}
		if len(errs) > 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Update order item failed",
				Data:    errs,
			}
		}
	}

	return updateOrder
}

func isValidQuantity(curItem *model.OrderItem, processQuantity *int, status enum.OrderStateValue) *itemError {
	if status == enum.OrderState.Delivering || status == enum.OrderState.Delivered ||
		status == enum.OrderState.Returned || status == enum.OrderState.Completed {
		if curItem.NotionalQuantity == nil {
			return &itemError{Sku: curItem.Sku, ErrorCode: "INVALID_QUANTITY"}
		}
		if *curItem.NotionalQuantity < *processQuantity {
			return &itemError{Sku: curItem.Sku, ErrorCode: "INVALID_QUANTITY"}
		}
	}
	return nil
}

func isValidStatus(current, update enum.OrderStateValue) bool {
	if current == update || update == "" {
		return true
	}

	switch current {
	case enum.OrderState.WaitConfirm:
		return update == enum.OrderState.Canceled || update == enum.OrderState.Confirmed
	case enum.OrderState.Confirmed:
		return update == enum.OrderState.Canceled || update == enum.OrderState.Processing || update == enum.OrderState.Reserving
	case enum.OrderState.Reserving:
		return true
	case enum.OrderState.Processing:
		return update == enum.OrderState.Canceled || update == enum.OrderState.Delivering ||
			update == enum.OrderState.WaitToDeliver || update == enum.OrderState.Reserving
	case enum.OrderState.WaitToDeliver:
		return update == enum.OrderState.Canceled || update == enum.OrderState.Delivering
	case enum.OrderState.Delivering:
		return update == enum.OrderState.Canceled || update == enum.OrderState.Delivered || update == enum.OrderState.Returned
	case enum.OrderState.Delivered:
		return update == enum.OrderState.Canceled || update == enum.OrderState.Returned || update == enum.OrderState.Completed
	case enum.OrderState.Returned:
		return update == enum.OrderState.Canceled || update == enum.OrderState.Completed
	case enum.OrderState.Completed:
		return true
	case enum.OrderState.Canceled:
		return true

	}
	return false
}

func processOrderTask(oldOrder *model.Order, order *model.Order, req *model.OrderProcessRequest) {
	updateRes := client.Services.SellerMis.UpdateOrderSeller(order)
	if updateRes.Status != common.APIStatus.Ok {
		fmt.Println(updateRes.Message)
	}

	if req.Status == enum.OrderState.Confirmed && oldOrder.Status == enum.OrderState.WaitConfirm {
		_ = model.CreateSaleOrderJob.Push(createSaleOrderData{OrderID: order.OrderID}, &job.JobItemMetadata{
			UniqueKey: fmt.Sprint(order.OrderID),
			Keys: []string{
				strconv.Itoa(int(order.OrderID)),
			},
			Topic: "default",
		})
	}
	if order.Status == enum.OrderState.Delivered && !req.SkipUpdateStatus {

		// call if payment method is credit
		if order.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
			model.ProcessPaymentMethodCreditJob.Push(processPaymentMethodCredit{
				RefundOrderInput: &client.RefundOrderInput{
					CustomerID: order.CustomerID,
					OrderID:    order.OrderID,
					Type:       enum.RefundOrderInputType.DELIVERED,
					RequestID:  time.Now().UnixNano(),
				},
			}, &job.JobItemMetadata{
				Topic: "refund",
				Keys:  []string{fmt.Sprintf("%d", order.OrderID)},
			})
		}

		// create rating for order
		model.CreateOrderRatingJob.Push(
			&model.Order{
				OrderID:      order.OrderID,
				RegionCode:   order.RegionCode,
				ProvinceCode: order.ProvinceCode,
				Status:       order.Status,
				CustomerID:   order.CustomerID,
			},
			&job.JobItemMetadata{
				Keys: []string{
					strconv.Itoa(int(order.OrderID)),
				},
				Topic: "default",
			})
	}
	if order.Status == enum.OrderState.Completed && req.SkipUpdateStatus == false {
		readyTime := time.Now().Add(30 * time.Minute)
		_ = model.CompleteOrderJobExecutor.Push(
			order,
			&job.JobItemMetadata{
				Keys: []string{
					TopicCompleteOrder,
					strconv.Itoa(int(order.OrderID)),
				},
				Topic:     "default",
				ReadyTime: &readyTime,
			},
		)
	}

	go func() {
		// send delivering notification or missing item alert to user
		if order.IsSplitDeliveryOrder != nil && *order.IsSplitDeliveryOrder {
			if req.DeliveryOrderStatus == enum.OrderState.Delivering {
				//for _, do := range oldOrder.DeliveryOrderStatuses {
				//	if do.Code == req.DeliveryOrderCode && do.Status != enum.OrderState.Delivering {
				sendZNSDelivering(oldOrder, order, req.CodAmount)
				//}
				//}
			}
		} else {
			if req.SaleOrderStatus == enum.SaleOrderStatus.Delivering && req.DeliveryOrderStatus == enum.OrderState.Delivering {
				sendZNSDelivering(oldOrder, order, 0)
			}
		}

		isNotiMissingItem := false
		if order.IsSplitDeliveryOrder != nil && *order.IsSplitDeliveryOrder {
			isCompleteReserve := len(order.DeliveryOrderStatuses) == len(order.DeliveryOrderCodes)
			for _, doStatus := range order.DeliveryOrderStatuses {
				if doStatus.Status == enum.OrderState.Reserving || doStatus.Status == enum.OrderState.Processing || doStatus.Status == "" || doStatus.Status == enum.OrderState.WaitToDeliver {
					isCompleteReserve = false
				}
			}
			if req.DeliveryOrderStatus == enum.OrderState.Delivering && isCompleteReserve {
				isNotiMissingItem = true
			}
		} else {
			if req.Status == enum.OrderState.WaitToDeliver && oldOrder.Status != enum.OrderState.WaitToDeliver {
				isNotiMissingItem = true
			}
		}
		if isNotiMissingItem {
			isMissing, payload := checkIfMissingItemAtWaitToDeliver(order)
			if isMissing {
				sendMissingItemAlert(order, payload)
			}
		}
		order.DeliveryOrderCode = req.DeliveryOrderCode
		// If Order is consignment order, do create/check bill/invoice
		if len(order.Tags) > 0 &&
			utils.IsOrderTagContains(order.Tags, enum.Tag.CO_INTERNAL) {
		} else if len(order.Tags) > 0 &&
			utils.IsOrderTagContains(order.Tags, enum.Tag.LO_INTERNAL) {
			processBill(order, false)
		} else {
			processBill(order, false)
			processInvoice(order)
		}

		if order.Status == enum.OrderState.Canceled {
			notifyCancelOrder(order.OrderID, order.CustomerID)
		}
		if (oldOrder.IsSplitDeliveryOrder == nil || (oldOrder.IsSplitDeliveryOrder != nil && !*oldOrder.IsSplitDeliveryOrder)) && req.IsSplitDeliveryOrder != nil && *req.IsSplitDeliveryOrder {
			_ = client.Services.Notification.CreateNotification(&client.Notification{
				Username:     fmt.Sprintf("%d", order.AccountID),
				UserID:       order.AccountID,
				ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
				Topic:        "ANNOUNCEMENT",
				Title:        fmt.Sprintf("Đơn hàng %d của bạn sẽ được giao 2 lần!", order.OrderID),
				Link:         fmt.Sprintf("/my-order/%d", order.OrderID),

				Tags: []enum.NotificationTagEnum{enum.NotificationTag.ORDER, enum.NotificationTag.IMPORTANT},
			})
			sendZNSSplitDeliveryOrder(order)
		}
	}()
	// score mission
	go func() {
		if order.Items == nil || len(order.Items) == 0 {
			orderItemDB := model.GetOrderItemPartitionDB(order, "ProcessOrder")
			qOrderItemUpdated := orderItemDB.Query(model.OrderItem{OrderID: order.OrderID}, 0, 0, &primitive.M{"_id": -1})
			if qOrderItemUpdated.Status == common.APIStatus.Ok {
				order.Items = qOrderItemUpdated.Data.([]*model.OrderItem)
			}
		}
		client.Services.Promotion.ScoreMission("Update order status "+string(order.Status), order)
	}()

	// process invoice DO Seller
	go func(req *model.OrderProcessRequest) {
		if req.DeliveryOrderStatus == enum.OrderState.Delivering ||
			req.DeliveryOrderStatus == enum.OrderState.Delivered ||
			order.Status == enum.OrderState.Delivered ||
			order.Status == enum.OrderState.Completed ||
			req.Status == enum.OrderState.Returned {
			// SELLER_ORDER
			topic := TopicDeliveredOrder
			if req.Status == enum.OrderState.Delivering {
				topic = TopicDeliveringOrder
			} else if req.Status == enum.OrderState.Completed {
				topic = TopicCompleteOrder
			} else if req.Status == enum.OrderState.Returned {
				topic = TopicReturnedOrder
			}

			_ = model.CreateSellerInvoiceJob.Push(req, &job.JobItemMetadata{
				Keys: []string{
					topic,
					strconv.Itoa(int(order.OrderID)),
				},
				Topic: "default",
			})
		}

		// if req.DeliveryOrderStatus == enum.OrderState.Delivering ||
		// 	req.DeliveryOrderStatus == enum.OrderState.Delivered {
		// 	tool.ProcessUpsertInvoiceForDOSeller(req)
		// }
		// if req.Status == enum.OrderState.Returned {
		// 	tool.ProcessUpdateReturnedInvoiceForDOSeller(req)
		// }
	}(req)

	// recreate voucher when order canceled
	sdk.Execute(func() {
		if req.Status == enum.OrderState.Canceled {
			client.Services.Promotion.CreateVoucherReuseOnOrderCancel(&client.CreateVoucherReuseOnOrderCancelReq{
				RedeemCodes: order.RedeemCode,
				AccountID:   order.AccountID,
				CustomerID:  order.CustomerID,
				OrderID:     order.OrderID,
			})
		}
	})
}

func sendZNSDelivering(oldOrder, order *model.Order, codAmout int) {

	getStr := func(value, defaultValue string) string {
		if value != "" {
			return value
		}
		return defaultValue
	}

	// send order-created ZNS to customer
	customer, err := client.Services.Customer.GetCustomerByCustomerID(order.CustomerID)
	if err != nil {
		return
	}

	if !customer.CanReceiveZns() {
		return
	}

	msg := client.ZnsMessage{
		Topic:      client.DELIVERING_TOPIC,
		BusinessID: "thuocsi.vn",
		Receiver:   customer.Phone,
		Data: map[string]string{
			// wrong field name, but registered template use this T_T
			"bussiness_name": getStr(customer.BusinessName, "-"),
			"address":        getStr(order.CustomerShippingAddress, "-"),
			"name_3pl":       getStr(order.DeliveryCarrier, "-"),
			"number":         getStr(order.DeliveryTrackingNumber, "-"),
			"order_id":       strconv.FormatInt(order.OrderID, 10),
		},
	}
	if order.IsSplitDeliveryOrder != nil && *order.IsSplitDeliveryOrder {
		msg.Data["value"] = strconv.Itoa(codAmout)
		//msg.Data["customer_name"] = getStr(customer.Name, "-")
		//msg.Data["business_name"] = getStr(customer.BusinessName, "-")
		msg.Data["number"] = getStr("1", "-")
		msg.Data["tracking_number"] = getStr(order.DeliveryTrackingNumber, "-")
		msg.Topic = "DO_DELIVERING"
		if oldOrder.Status == enum.OrderState.Delivering {
			msg.Data["number"] = getStr("2", "-")
		}
	} else if order.TotalNotionalPrice != nil {
		msg.Data["value"] = strconv.Itoa(*order.TotalNotionalPrice)
	}
	resp := model.RunOneDB.Create(model.RunOne{
		Key: fmt.Sprintf("%d_%s", order.OrderID, msg.Topic),
	})
	if resp.Status == common.APIStatus.Ok {
		_, _ = client.Services.Zns.SendZnsMessage(&msg)
	}
}

func sendZNSSplitDeliveryOrder(order *model.Order) {
	getStr := func(value, defaultValue string) string {
		if value != "" {
			return value
		}
		return defaultValue
	}

	// send order-created ZNS to customer
	customer, err := client.Services.Customer.GetCustomerByCustomerID(order.CustomerID)
	if err != nil {
		return
	}

	if !customer.CanReceiveZns() {
		return
	}

	msg := client.ZnsMessage{
		Topic:      client.SPLIT_DELIVERY_ORDER_TOPIC,
		BusinessID: "thuocsi.vn",
		Receiver:   customer.Phone,
		Data: map[string]string{
			"customer_name": getStr(customer.Name, "-"),
			"order_id":      strconv.FormatInt(order.OrderID, 10),
			"day":           order.CreatedTime.Format("02/01/2006"),
		},
	}
	resp := model.RunOneDB.Create(model.RunOne{
		Key: fmt.Sprintf("%d_%s", order.OrderID, msg.Topic),
	})
	if resp.Status == common.APIStatus.Ok {
		_, _ = client.Services.Zns.SendZnsMessage(&msg)
	}
}
