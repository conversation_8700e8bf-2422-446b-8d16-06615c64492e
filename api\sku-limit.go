package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func UpdateSkuLimitHistory(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.SkuLimitUpdateRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	return resp.Respond(action.UpdateSkuLimitHistory(&input))
}

func GetSkuLimitHistoryToday(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.SkuLimitHistoryListRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if acc := wrapActionSource(req); acc != nil {
		return resp.Respond(action.GetSkuLimitHistoryToday(acc, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func RecountSkuLimit(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = struct {
		CustomerID int64  `json:"customerId" validate:"required"`
		Sku        string `json:"sku" validate:"required"`
		ItemCode   string `json:"itemCode" validate:"required"`
	}{}

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	return resp.Respond(action.RecountSkuLimit(input.CustomerID, input.Sku, input.ItemCode))
}
