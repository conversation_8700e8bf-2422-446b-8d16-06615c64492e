package tool

import (
	"fmt"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

const cacheKey = "DEFAULT"

// CAUTION: do not dereference LCache, because it also copy sync.Mutex
// of sellerSyncCircaAppConfCache, which make Lock() & UnLock() not working
// sellerSyncCircaAppConfCache will refresh every hour
var sellerSyncCircaAppConfCache = utils.NewLCacheRefreshMode(1, 60*60, false)
var minInvoicePenaltyValueCache = utils.NewLCacheRefreshMode(1, 60*60, false)

func RefreshSellersSyncCircaCache(req sdk.APIRequest, res sdk.APIResponder) error {
	sellerCodes := GetSellerCodesAndSaveCache()

	return res.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "success",
		Data: []interface{}{
			sellerCodes,
		},
		Total: int64(len(sellerCodes)),
	})
}

func GetSellersSyncCirca() map[string]bool {
	cacheData, ok := sellerSyncCircaAppConfCache.Get(cacheKey)
	if !ok {
		return GetSellerCodesAndSaveCache()
	}

	sellerCodes, ok := cacheData.(map[string]bool)
	if !ok {
		return map[string]bool{}
	}

	return sellerCodes
}

func GetSellerCodesAndSaveCache() map[string]bool {
	sellerCodes, err := callClientGetSellerCodes()
	if err != nil {
		return map[string]bool{}
	}

	m := make(map[string]bool, len(sellerCodes))
	for _, sellerCode := range sellerCodes {
		m[sellerCode] = true
	}

	sellerSyncCircaAppConfCache.Put(cacheKey, m)
	return m
}

func callClientGetSellerCodes() ([]string, error) {
	confResp := client.Services.ConfigManager.GetAppValueSingle(map[string]string{
		"appCode": "AV84FW96",
	})
	if confResp.Status != common.APIStatus.Ok {
		return []string{}, fmt.Errorf("failed to get app value: %v", confResp.Message)
	}

	for _, appConfig := range confResp.Data {
		if appConfig.Value.Key != "sellerCodeSyncInvoiceToCirca" {
			continue
		}

		return strings.Split(appConfig.Value.SellerCodeSyncInvoiceToCirca, ","), nil
	}

	return []string{}, fmt.Errorf("not found sellerCodeSyncInvoiceToCirca")
}

func callClientGetMinInvoicePenaltySetting() (int, error) {
	confResp := client.Services.Seller.GetSettingForSC("MIN_PENALTY_INVOICE_VALUE")

	if confResp.Status != common.APIStatus.Ok {
		return 0, fmt.Errorf("failed to get config value: %v", confResp.Message)
	}

	settings := confResp.Data.([]client.SettingForSC)[0]
	var minPenaltyInvoiceValue int = 0
	for _, item := range settings.Data {
		if item["Key"] == "minPenaltyInvoiceValue" {
			if value, ok := item["Value"].(float64); ok {
				minPenaltyInvoiceValue = int(value)
			}
		}
	}

	return minPenaltyInvoiceValue, nil
}

func RefreshMinInvoicePenaltyCache(req sdk.APIRequest, res sdk.APIResponder) error {
	minInvoiceValue := GetMinInvoicePenaltyAndSaveCache()

	return res.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "success",
		Data: []map[string]int{
			{
				"minInvoiceValue": minInvoiceValue,
			},
		},
	})
}

func GetMinInvoicePenalty() int {
	cacheData, ok := sellerSyncCircaAppConfCache.Get(cacheKey)
	if !ok {
		return GetMinInvoicePenaltyAndSaveCache()
	}

	minPenaltyInvoiceValue, ok := cacheData.(int)
	if !ok {
		return 0
	}

	return minPenaltyInvoiceValue
}

func GetMinInvoicePenaltyAndSaveCache() int {
	minInvoiceValue, err := callClientGetMinInvoicePenaltySetting()
	if err != nil {
		return 0
	}

	minInvoicePenaltyValueCache.Put(cacheKey, minInvoiceValue)
	return minInvoiceValue
}

func SyncDeliveryTimeFromOrderToInvoice(req sdk.APIRequest, res sdk.APIResponder) error {
	var input struct {
		SellerCode       string `json:"sellerCode,omitempty"`
		DeliveryTimeFrom string `json:"deliveryTimeFrom,omitempty"`
		DeliveryTimeTo   string `json:"deliveryTimeTo,omitempty"`
	}

	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	// // convert DeliveryTimeFrom to time.Time
	// deliveryTimeFrom, _ := time.Parse("2006-01-02", input.DeliveryTimeFrom)
	// from := time.Date(deliveryTimeFrom.Year(), deliveryTimeFrom.Month(), deliveryTimeFrom.Day(), 0, 0, 0, 0, utils.TimeZoneVN)

	// deliveryTimeTo, _ := time.Parse("2006-01-02", input.DeliveryTimeTo)
	// to := time.Date(deliveryTimeTo.Year(), deliveryTimeTo.Month(), deliveryTimeTo.Day(), 23, 59, 59, 0, utils.TimeZoneVN)

	// go actionSyncDeliveryTimeFromOrderToInvoice(input.SellerCode, &from, &to)
	return res.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "success",
	})
}

// func actionSyncDeliveryTimeFromOrderToInvoice(sellerCode string, deliveryTimeFrom *time.Time, deliveryTimeTo *time.Time) {
// 	// get invoice by sellerCode, deliveryTimeFrom, deliveryTimeTo
// 	var complexQuery = []*bson.M{}
// 	filter := model.Invoice{
// 		SellerCode: sellerCode,
// 	}
// 	timeQuery := bson.M{}

// 	if deliveryTimeFrom != nil {
// 		timeQuery["$gte"] = deliveryTimeFrom
// 	}
// 	if deliveryTimeTo != nil {
// 		timeQuery["$lte"] = deliveryTimeTo
// 	}

// 	complexQuery = append(complexQuery, &bson.M{
// 		"delivered_time": timeQuery,
// 	})

// 	if len(complexQuery) > 0 {
// 		filter.OperationAnd = complexQuery
// 	}

// 	invoicesResp := model.InvoiceDB.Query(filter, 0, 1000, &primitive.M{"_id": -1})
// 	if invoicesResp.Status != common.APIStatus.Ok {
// 		return
// 	}

// 	invoices := invoicesResp.Data.([]*model.Invoice)
// 	if len(invoices) == 0 {
// 		return
// 	}

// 	orderIDs := make([]int64, 0, len(invoices))
// 	invoicesOrderIDMap := make(map[int64][]*model.Invoice)
// 	for _, invoice := range invoices {
// 		orderIDs = append(orderIDs, invoice.OrderID)
// 		invoicesOrderIDMap[invoice.OrderID] = append(invoicesOrderIDMap[invoice.OrderID], invoice)
// 	}

// 	// get order by orderIDs
// 	ordersResp := model.OrderDB.Query(model.Order{
// 		ComplexQuery: []*bson.M{
// 			{
// 				"order_id": bson.M{
// 					"$in": orderIDs,
// 				},
// 			},
// 		},
// 	}, 0, 1000, &primitive.M{"_id": -1})
// 	if ordersResp.Status != common.APIStatus.Ok {
// 		return
// 	}

// 	orders := ordersResp.Data.([]*model.Order)
// 	if len(orders) == 0 {
// 		return
// 	}

// 	for _, order := range orders {
// 		if order.DeliveredTime == nil {
// 			continue
// 		}

// 		if invoices, exists := invoicesOrderIDMap[order.OrderID]; exists {
// 			for _, invoice := range invoices {
// 				// update invoice
// 				err := model.InvoiceDB.UpdateOne(&model.Invoice{
// 					ID: invoice.ID,
// 				}, &model.Invoice{
// 					OrderDeliveredTime: order.DeliveredTime,
// 				})
// 				if err != nil {
// 					log.Println("update invoice error", err)
// 				}
// 			}
// 		}
// 	}
// }
