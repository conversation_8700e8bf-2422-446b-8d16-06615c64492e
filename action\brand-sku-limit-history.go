package action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func GetBrandSkuLimitHistoryList(customerID int64, skuCodes []string, source string) *common.APIResponse {
	now := time.Now().In(model.VNTimeZone)
	query := model.BrandSkuLimitHistory{
		CustomerID: customerID,
		Version:    now.Format("2006-01"),
		ComplexQuery: []*bson.M{
			&bson.M{
				"sku": bson.M{
					"$in": skuCodes,
				},
			},
		},
	}

	brandSkuLimitHistories := model.BrandSkuLimitHistoryDB.Query(query, 0, 0, nil)
	skuCodesMap := make(map[string]int)
	if brandSkuLimitHistories.Status == common.APIStatus.Ok {
		brandSkuLimits := brandSkuLimitHistories.Data.([]*model.BrandSkuLimitHistory)
		for _, r := range brandSkuLimits {
			skuCodesMap[r.Sku] = *r.Quantity
		}
	}

	skuCustomerPurchaseConfiguration := make(map[string]*client.CustomerPurchaseConfigurationDetail)
	_, err := client.Services.Product.GetCustomerPurchaseConfiguration(customerID)
	if err == nil {
		customerPurchaseConfigurationDetails, err := client.Services.Product.GetListCustomerPurchaseConfigurationDetail(customerID)
		if err == nil {
			for _, item := range customerPurchaseConfigurationDetails {
				skuCustomerPurchaseConfiguration[item.SKU] = item
			}
		}
	}

	datas := []*model.BrandSkuLimitHistoryResp{}
	skuLimitRes, _ := client.Services.Product.GetListSkuBrandContract(skuCodes, source, nil)
	for _, limit := range skuLimitRes {
		data := &model.BrandSkuLimitHistoryResp{
			CustomerID:            customerID,
			Sku:                   limit.SKU,
			LimitQuantityPerMonth: limit.LimitQuantityPerMonth,
		}
		if item, exists := skuCustomerPurchaseConfiguration[data.Sku]; exists {
			data.LimitQuantityPerMonth = item.LimitQuantityPerMonth
		}
		if qtyOrdered, exists := skuCodesMap[limit.SKU]; exists {
			data.QtyOrdered = qtyOrdered
		}
		datas = append(datas, data)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    datas,
		Message: "Get list brand sku limit history successfully",
	}
}
