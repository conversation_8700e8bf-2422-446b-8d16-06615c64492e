package client

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	ReconciliationEndpoint     = "/billing/analyze/v1/receive-reconciliation-seller"
	ReconciliationItemEndpoint = "/billing/analyze/v1/receive-seller"
	RealtimeEndpoint           = "/billing/analyze/v1/receive-realtime-seller"
	RealtimeCustomEndpoint     = "/billing/analyze/v1/receive-realtime-custom-seller"
	EntityEndpoint             = "/iam/core/v1/entity"

	pathExportHiloInvoice        = "/billing/invoice/v2/invoice/export"
	pathGetBillingInvoice        = "/billing/invoice/v2/invoice/list"
	pathCancelBillingInvoiceSync = "/billing/invoice/v2/invoice/cancel/sync"
)

type billingCenterSvc struct {
	svc        *client.RestClient
	ssoSvc     *client.RestClient
	headers    map[string]string
	ssoHeaders map[string]string
}

type App struct {
	Name  string `json:"name"`
	AppId int64  `json:"appID"`
	Code  string `json:"code"`
}

type EntityTf struct {
	EntityID int64  `json:"entityID"`
	OrgID    int64  `json:"orgID"`
	Name     string `json:"name"`
	App      App    `json:"app"`
}

func NewBillingCenterSvcClient(apiHost, apiKey, ssoHost, ssoToken, logName string, session *mongo.Database) *billingCenterSvc {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	client := &billingCenterSvc{
		svc:    client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		ssoSvc: client.NewRESTClient(ssoHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
		ssoHeaders: map[string]string{
			"Authorization": fmt.Sprintf("Basic %s", ssoToken),
		},
	}

	client.svc.SetDBLog(session)
	client.ssoSvc.SetDBLog(session)

	return client
}

func (b billingCenterSvc) PutReconciliation(data interface{}) error {
	_, err := b.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		b.headers,
		map[string]string{},
		data,
		ReconciliationEndpoint,
		nil,
	)
	if err != nil {
		return err
	}
	return nil
}

type EntityResponse struct {
	BaseAPIResponse
	Data []*EntityTf `json:"data"`
}

func (b billingCenterSvc) GetEntity(entityID int64) (rs EntityTf, err error) {
	res, err := b.ssoSvc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Get,
		b.ssoHeaders,
		map[string]string{
			"entityID":   strconv.Itoa(int(entityID)),
			"getAppInfo": "true",
		},
		nil,
		EntityEndpoint,
		nil,
	)
	if err != nil {
		return rs, err
	}

	var result *EntityResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return rs, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return rs, fmt.Errorf("%v", result.Message)
	}
	return *result.Data[0], nil
}

func (b billingCenterSvc) PutReconciliationItems(data interface{}) error {
	_, err := b.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		b.headers,
		map[string]string{},
		data,
		ReconciliationItemEndpoint,
		nil,
	)
	if err != nil {
		return err
	}
	return nil
}

func (b billingCenterSvc) PutRIsRealTime(data interface{}) error {
	_, err := b.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		b.headers,
		map[string]string{},
		data,
		RealtimeEndpoint,
		nil,
	)
	if err != nil {
		return err
	}
	return nil
}

func (b billingCenterSvc) PutRICustomRealTime(data interface{}) error {
	_, err := b.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		b.headers,
		map[string]string{},
		data,
		RealtimeCustomEndpoint,
		nil,
	)
	if err != nil {
		return err
	}
	return nil
}

func (b billingCenterSvc) PostHiloInvoice(data interface{}, keys *[]string) *common.APIResponse {
	res, err := b.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		b.headers,
		nil,
		data,
		pathExportHiloInvoice,
		keys,
	)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: common.APIStatus.Error,
			Message:   err.Error(),
		}
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: common.APIStatus.Error,
			Message:   err.Error(),
		}
	}
	return result
}

func (b billingCenterSvc) CancelBillingInvoice(code string) *common.APIResponse {
	res, err := b.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		b.headers,
		nil,
		map[string]string{
			"code": code,
		},
		pathCancelBillingInvoiceSync,
		&[]string{code},
	)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: common.APIStatus.Error,
			Message:   err.Error(),
		}
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: common.APIStatus.Error,
			Message:   err.Error(),
		}
	}
	return result
}

// GetBillingInvoice
func (b billingCenterSvc) GetBillingInvoice(data *ReqListInvoice) (*BillingInvoice, error) {
	res, err := b.svc.MakeHTTPRequestWithKey(
		"QUERY",
		b.headers,
		nil,
		data,
		pathGetBillingInvoice,
		nil,
	)
	if err != nil {
		return nil, err
	}

	var result *BillingInvoiceResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}
