package action

import "gitlab.com/thuocsi.vn/marketplace/order-v2/model"

// return in order:
// sellerCodes, skus, itemCodes, productIds, dealCodes, subSkus, subProductIds
func GetOrderDistinctInfo(
	items []*model.OrderItem,
) ([]string, []string, []string, []int64, []string, []string, []int64) {
	sellerCodes := make([]string, 0, 1)
	skus := make([]string, 0, len(items))
	itemCodes := make([]string, 0, len(items))
	productIds := make([]int64, 0, len(items))
	dealCodes := make([]string, 0, len(items))
	subSkus := make([]string, 0)
	subProductIds := make([]int64, 0)

	sellerCodeMap := make(map[string]bool, 1)
	skuMap := make(map[string]bool, len(items))
	itemCodeMap := make(map[string]bool, len(items))
	productIdMap := make(map[int64]bool, len(items))
	dealCodeMap := make(map[string]bool)
	subSkusMap := make(map[string]bool)
	subProductIdsMap := make(map[int64]bool)

	for _, item := range items {
		skuMap[item.Sku] = true
		itemCodeMap[item.ItemCode] = true
		sellerCodeMap[item.SellerCode] = true
		productIdMap[item.ProductID] = true

		if item.Skus != nil {
			for _, sub := range *item.Skus {
				subSkusMap[sub.SKU] = true
				subProductIdsMap[sub.ProductID] = true
			}
		}

		if item.DealCode != nil && *item.DealCode != "" {
			dealCodeMap[*item.DealCode] = true
		}
	}

	for sellerCode := range sellerCodeMap {
		sellerCodes = append(sellerCodes, sellerCode)
	}

	for sku := range skuMap {
		skus = append(skus, sku)
	}

	for itemCode := range itemCodeMap {
		itemCodes = append(itemCodes, itemCode)
	}

	for productId := range productIdMap {
		productIds = append(productIds, productId)
	}

	for dealCode := range dealCodeMap {
		dealCodes = append(dealCodes, dealCode)
	}

	for subSku := range subSkusMap {
		subSkus = append(subSkus, subSku)
	}

	for subProductId := range subProductIdsMap {
		subProductIds = append(subProductIds, subProductId)
	}

	return sellerCodes, skus, itemCodes, productIds, dealCodes, subSkus, subProductIds
}
