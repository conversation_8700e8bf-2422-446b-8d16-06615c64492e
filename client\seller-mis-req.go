package client

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

type OrderSellerUpdate struct {
	ParentOrderID    int64                `json:"parentOrderId,omitempty" bson:"parent_order_id,omitempty"`      //
	SaleOrderCode    *string              `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`      // so
	Status           enum.OrderStateValue `json:"status,omitempty" bson:"status,omitempty"`                      //
	ConfirmationDate *time.Time           `json:"confirmationDate,omitempty" bson:"confirmation_date,omitempty"` //
	CompletedTime    *time.Time           `json:"completedTime,omitempty" bson:"completed_time,omitempty"`       // time order completed -- status = completed
}

type OrderRequest struct {
	OrderID int64 `json:"orderId,omitempty" bson:"order_id,omitempty"`
}

type OrderReconcileRequest struct {
	SellerCode string  `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	OrderIds   []int64 `json:"orderIds,omitempty" bson:"order_ids,omitempty"`

	ReconciledTime *time.Time `json:"reconciledTime,omitempty" bson:"reconciled_time,omitempty"`
	SessionPayment *string    `json:"sessionPayment,omitempty" bson:"session_payment,omitempty"`
}

type SkuAccumulatePoints struct {
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`
	SellerCode  string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`

	ExpiredTime *time.Time `json:"expiredTime,omitempty" bson:"expired_time,omitempty"`
}
