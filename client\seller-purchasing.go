package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathPostAlertMessage = "/seller/purchasing-worker/v1/tool/notify-telegram"
)

type sellerPurchasingClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewProductServiceClient ...
func NewSellerPurchasingClient(apiHost, apiKey, logName string, session *mongo.Database) *sellerPurchasingClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}

	sellerPurchasingClient := &sellerPurchasingClient{
		svc: client.NewRESTClient(apiHost, logName, 10*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	// sellerPurchasingClient.svc.SetDBLog(session)
	return sellerPurchasingClient
}

func (cli *sellerPurchasingClient) PostAlertMessage(
	msg string,
) *common.APIResponse {
	var input struct {
		Message string `json:"msg"`
	}
	input.Message = msg

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, input, pathPostAlertMessage, nil)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	var res *common.APIResponse
	err = json.Unmarshal([]byte(result.Body), res)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	return res
}

func (cli *sellerPurchasingClient) AlertInvoiceError(errInvoices int64) *common.APIResponse {
	if errInvoices < 1 {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "No error invoice",
		}
	}

	msg := fmt.Sprintf("🔔 Thông báo:\nHiện có %d hóa đơn bị lỗi dữ liệu.\nVui lòng kiểm tra", errInvoices)

	return cli.PostAlertMessage(msg)
}

func (cli *sellerPurchasingClient) AlertInvoiceSyncFailed(invoiceID int64, invoiceCode, sellerCode, message string) *common.APIResponse {
	if invoiceID < 1 {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "invoiceID",
		}
	}

	msg := fmt.Sprintf("🔔 Thông báo:\nHóa đơn Hilo %d Không thể sync qua invoice v2.\nVui lòng kiểm tra http://internal.thuocsi.vn/seller/invoices/view?invoiceCode=%s&sellerCode=%s\n%s", invoiceID, invoiceCode, sellerCode, message)

	return cli.PostAlertMessage(msg)
}
