package utils

/*
Slightly adapted from the source to fit go-humanize.
Author: https://github.com/gorhill
Source: https://gist.github.com/gorhill/5285193
*/

import (
	"math"
	"strings"
)

func FormatVNDCurrency(str string) string {
	groups := breakIntoGroupOfThree(str)

	return strings.Join(groups, ".")
}

func breakIntoGroupOfThree(str string) []string {
	var groups []string
	var remainder = len(str) % 3

	if len(str) >= 3 {
		var lengthOfGroup = math.Ceil(float64(len(str) / 3))
		// var lengthOfGroup = len(str) / 3

		if lengthOfGroup >= 3 {
			remainder = len(str) - int(lengthOfGroup*3)
		}
	}

	//Cắt lấy những phần tử không đủ 3 số
	if remainder != 0 {
		groups = append(groups, str[:remainder])
		str = str[remainder:]
	}

	//Lấy những group còn lại
	for {
		if len(str) < 3 {
			break
		}
		groups = append(groups, str[:3])
		str = str[3:]
	}

	return groups
}

func Ceil(val int64) int64 {
	y := val % 100.0
	if y > 49 {
		return val - y + 100
	}
	return val - y
}