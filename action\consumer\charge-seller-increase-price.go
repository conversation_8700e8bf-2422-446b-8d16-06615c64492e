package consumer

import (
	"fmt"
	"runtime/debug"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func HandleChargeSellerIncreasePrice(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return nil
	}

	var input model.IncreaseSkuPriceRequest
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return nil
	}

	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic: %s", string(debug.Stack()))
			fmt.Printf("[%s] %s\n", input.Sku, err.Error())
		}
	}()

	processRes := reconcile_action.ProcessChargeIncreasePrice(&input)
	if processRes.Status == common.APIStatus.Error {
		return fmt.Errorf("process charge increase price error: %s, %s", processRes.ErrorCode, processRes.Message)
	}

	return nil
}
