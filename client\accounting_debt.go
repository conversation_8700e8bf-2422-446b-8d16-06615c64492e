package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetDebtList   = "/accounting/core/v1/debt/list"
	pathOrderRefund   = "/accounting/core/v1/debt/order/refund"
	pathOrderCheckout = "/accounting/core/v1/debt/order/checkout"
	pathDebtCheck     = "/accounting/core/v1/debt/check"
)

type accountingDebtClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewCollectorServiceClient ...
func NewAccountingDebtServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *accountingDebtClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	accountingDebtClient := &accountingDebtClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	accountingDebtClient.svc.SetDBLog(session)
	return accountingDebtClient
}

func (cli *accountingDebtClient) GetDebtContractByCustomerID(customerID int64) ([]*Debt, error) {
	params := map[string]string{}
	body := map[string]interface{}{
		"q": struct {
			CustomerID int64 `json:"customerId"`
		}{
			CustomerID: customerID,
		},
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, pathGetDebtList, nil)
	if err != nil {
		return nil, err
	}
	type myResponse struct {
		Status string  `json:"status"`
		Code   string  `json:"errorCode"`
		Data   []*Debt `json:"data"`
	}
	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}
	return resp.Data, nil
}

func (cli *accountingDebtClient) RefundOrder(input *RefundOrderInput) error {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, input, pathOrderRefund, nil)
	if err != nil {
		return err
	}
	resp := new(common.APIResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return err
	}
	if resp.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", resp.ErrorCode)
	}
	return nil
}

func (cli *accountingDebtClient) CheckoutOrder(input *CheckoutOrderInput) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, input, pathOrderCheckout, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: common.APIStatus.Error,
			Message:   err.Error(),
		}
	}
	type myResponse struct {
		Status    string         `json:"status"`
		ErrorCode string         `json:"errorCode"`
		Data      []*Transaction `json:"data"`
		Message   string         `json:"message"`
	}

	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: common.APIStatus.Error,
			Message:   err.Error(),
		}
	}

	if resp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: resp.ErrorCode,
			Message:   resp.Message,
		}
	}
	return &common.APIResponse{
		Status:    resp.Status,
		ErrorCode: resp.ErrorCode,
		Data:      resp.Data,
		Message:   resp.Message,
	}
}

func (cli *accountingDebtClient) CheckDebtContractByCustomerID(customerID int64) ([]*Debt, error) {
	params := map[string]string{}
	body := map[string]int64{
		"customerId": customerID,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, pathDebtCheck, nil)
	if err != nil {
		return nil, err
	}
	type myResponse struct {
		Status string  `json:"status"`
		Code   string  `json:"errorCode"`
		Data   []*Debt `json:"data"`
	}
	resp := new(myResponse)
	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		return nil, err
	}

	if resp.Status != common.APIStatus.Ok || resp.Data == nil {
		return nil, fmt.Errorf("%v", resp.Code)
	}
	return resp.Data, nil
}
