package client

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

const (
	pathGetRegion             = "/core/master-data/v1/region/list"
	pathGetRegionDeliveryTime = "/core/master-data/v1/region/delivery-time"
	pathGetWard               = "/core/master-data/v1/ward/list"
	pathGetDistrict           = "/core/master-data/v1/district/list"
	pathGetProvince           = "/core/master-data/v1/province/list"
)

type locationClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewLocationServiceClient ...
func NewLocationServiceClient(apiHost, apiKey, logName string) *locationClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	return &locationClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
}

// GetListSku ...
func (cli *locationClient) GetRegion(provinceCode string) (*Region, *common.APIResponse) {
	var params = map[string]string{
		"provinceCodes": provinceCode,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetRegion, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetRegion",
		}
	}

	var result *RegionResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetRegion",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	for _, region := range result.Data {
		if region.Scope == "DELIVERY_REGION" {
			return region, nil
		}
	}

	return nil, &common.APIResponse{
		Status: common.APIStatus.NotFound,
	}
}

func (cli *locationClient) GetRegionList(offset, limit int, provinceCodes []string) ([]*Region, *common.APIResponse) {
	var params = map[string]string{
		"limit":         fmt.Sprintf("%d", limit),
		"offset":        fmt.Sprintf("%d", offset),
		"provinceCodes": strings.Join(provinceCodes, ","),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetRegion, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetRegion",
		}
	}

	var result *RegionResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetRegion",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data, nil
}

func (cli *locationClient) GetWardList(codes []string, offset, limit int) ([]*Ward, *common.APIResponse) {

	var params = map[string]string{}
	if offset >= 0 {
		params["offset"] = strconv.Itoa(offset)
	}
	if limit > 0 {
		params["limit"] = strconv.Itoa(offset)
	}
	if len(codes) > 0 {
		params["wardCodes"] = strings.Join(codes, ",")
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetWard, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST",
		}
	}

	var result *WardResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data, nil
}

// GetListSku ...
func (cli *locationClient) GetRegionGetDeliveryTime(provinceCode string, districtCode string) (*RegionDeliveryTime, *common.APIResponse) {
	var q = map[string]string{
		"districtCode": districtCode,
		"provinceCode": provinceCode,
	}

	qBytes, _ := json.Marshal(q)
	params := map[string]string{
		"q": string(qBytes),
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetRegionDeliveryTime, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetRegionGetDeliveryTime",
		}
	}

	var result *RegionGetDeliveryTimeResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetRegionGetDeliveryTime",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data[0], nil
}
