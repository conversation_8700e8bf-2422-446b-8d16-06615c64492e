package tool

import (
	"fmt"
	"log"
	"math"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ProcessBySchedule(schedule string, now time.Time, specSellers []string) (note string, err error) {
	defer func() {
		fmt.Println(schedule, now.String(), "completed!")
	}()

	scheduleSettings := client.Services.Seller.GetReconciliationScheduleSetting()
	from, scheduleTime, index, appliedBefore, e1 := reconcile_action.GetReconcileTime(schedule, now, scheduleSettings)
	if e1 != nil {
		return "", e1
	}
	if from.IsZero() {
		// Not need to act
		note = "Not need to act"
		return
	}

	fmt.Println("ProcessBySchedule: ", from)
	fmt.Println("ProcessBySchedule: ", scheduleTime)

	sellerCodes, sellerMap, e2 := getSellerCodes(schedule, appliedBefore, now)
	if e2 != nil {
		return "", e2
	}
	if len(sellerCodes) == 0 {
		// No seller matching with schedule
		note = "No seller matching with schedule"
		return
	}

	if len(specSellers) > 0 {
		s := make([]string, 0, len(specSellers))
		for _, seller := range specSellers {
			if FindString(sellerCodes, seller) != -1 {
				s = append(s, seller)
			}
		}

		sellerCodes = s
	}

	configDeliveredDate := GetReconciliationTimeAtDeliveredDateByConfig()
	completedOrderIDs, completedOrderIDsStr, err := reconcile_action.
		GetOrderIDsReadyForReconciliation(from, scheduleTime, configDeliveredDate)
	if err != nil {
		return
	} else if len(completedOrderIDs) == 0 {
		note = "No order completed in this time. " + from.String() + " ~ " + scheduleTime.String()
	}

	fromDate := from.Format("2006-01-02")
	toDate := scheduleTime.AddDate(0, 0, -1).Format("2006-01-02")

	// ======================== prepare data ========================
	categories := client.Services.Product.GetSellerCategoriesLevel()
	subCategories := client.Services.Product.GetSellerSubCategories()

	total := model.ReconciliationSpecRevenueDB.Count(model.ReconciliationSpecRevenue{Schedule: index}).Total
	specRevenues := make([]model.ReconciliationSpecRevenue, 0, total)

	var lastID *primitive.ObjectID
	for {
		query := bson.M{
			"schedule": index,
		}

		if lastID != nil {
			query["_id"] = bson.M{"$gt": lastID}
		}

		res := model.ReconciliationSpecRevenueDB.Query(query, 0, 1000, &primitive.M{"_id": 1})

		if res.Status != common.APIStatus.Ok {
			break
		}

		data := res.Data.([]model.ReconciliationSpecRevenue)

		specRevenues = append(specRevenues, data...)

		if len(data) == 0 || len(data) < 1000 {
			break
		}

		lastID = data[len(data)-1].ID
	}
	fmt.Println(" =================== specRevenues: ", len(specRevenues))

	// specRevRes := model.ReconciliationSpecRevenueDB.Query(model.ReconciliationSpecRevenue{
	// 	Schedule: index,
	// }, 0, total, nil)
	// if specRevRes.Status == common.APIStatus.Ok {
	// 	specRevenues = specRevRes.Data.([]model.ReconciliationSpecRevenue)
	// }

	var feeConfigs []client.SellerReconciliationFeeConfig
	feeConfigRes := client.Services.Seller.GetSellerReconciliationFeeConfigs()
	if feeConfigRes.Status == common.APIStatus.Ok {
		feeConfigs = feeConfigRes.Data.([]client.SellerReconciliationFeeConfig)
	}

	// ======================== return order ========================
	mapOrderArrSKUReturn := getReturnQuantityByTicket(completedOrderIDsStr, from, scheduleTime)
	wg := &sync.WaitGroup{}

	for _, sellerCode := range sellerCodes {
		orderIDs, err := reconcile_action.GetSellerReconcileOrderIDs(sellerCode, completedOrderIDs)
		if err != nil {
			note += err.Error()
			continue
		}

		// if len(orderIDs) > 0 {
		wg.Add(1)
		go processScheduleForSeller(
			wg,
			orderIDs,
			sellerMap[sellerCode],
			fromDate,
			toDate,
			index,
			scheduleTime,
			categories,
			subCategories,
			specRevenues,
			&mapOrderArrSKUReturn,
			feeConfigs,
		)
		// }
	}
	wg.Wait()

	return
}

func getSellerCodes(schedule string, appliedBefore, now time.Time) ([]string, map[string]client.Seller, error) {
	offset := 0
	limit := 1000
	_, total, errGetSellers := client.Services.Seller.GetSellerList("EXTERNAL", offset, 1)
	sellers := make([]*client.Seller, 0, total)
	var data []*client.Seller
	for errGetSellers == nil && offset < total {
		data, _, errGetSellers = client.Services.Seller.GetSellerList("EXTERNAL", offset, limit)
		offset += limit
		sellers = append(sellers, data...)
	}

	_map := make(map[string]client.Seller)
	codes := make([]string, 0, total)

	for _, seller := range sellers {
		switch schedule {
		case "SCHEDULE_01_TO_03", "SCHEDULE_04_TO_06",
			"SCHEDULE_07_TO_09", "SCHEDULE_10_TO_12",
			"SCHEDULE_13_TO_15", "SCHEDULE_16_TO_18",
			"SCHEDULE_19_TO_21", "SCHEDULE_22_TO_24",
			"SCHEDULE_25_TO_27", "SCHEDULE_28_TO_END":
			if reconcile_action.Apply3DaysReconcile(seller, appliedBefore) {
				codes = append(codes, seller.Code)
				_map[seller.Code] = *seller
				continue
			} else {
				continue
			}
		default:
			if reconcile_action.Apply3DaysReconcile(seller, appliedBefore) {
				continue
			}
		}

		if !seller.LevelInfo.Date.IsZero() && appliedBefore.After(seller.LevelInfo.Date) {
			seller.AppliedLevel = string(*seller.Level)
		} else {
			seller.AppliedLevel = seller.LevelInfo.LevelPrev
		}

		if seller.AppliedLevel == "" {
			seller.AppliedLevel = "LEVEL_2"
		}

		switch schedule {
		case "SCHEDULE_01_TO_07", "SCHEDULE_08_TO_15":
			if seller.AppliedLevel != "LEVEL_1" && seller.AppliedLevel != "MARKET" {
				continue
			}
		case "SCHEDULE_16_TO_22", "SCHEDULE_23_TO_END":
			if seller.AppliedLevel != "LEVEL_1" && seller.AppliedLevel != "MARKET" {
				continue
			}
		default:
			// Just apply with LEVEL_2
			if seller.AppliedLevel != "LEVEL_2" {
				continue
			}
		}

		codes = append(codes, seller.Code)
		_map[seller.Code] = *seller
	}

	return codes, _map, nil
}

func processScheduleForSeller(
	wg *sync.WaitGroup,
	orderIDs []int64,
	seller client.Seller,
	fromDate, toDate, timeIndex string,
	_time time.Time,
	categories []client.Category,
	subCategories []client.SellerSubCategory,
	specRevenues []model.ReconciliationSpecRevenue,
	mapOrderArrSKUReturn *map[int64][]*model.ReturnQuantityObject,
	feeConfigs []client.SellerReconciliationFeeConfig,
) {
	sellerCode := seller.Code
	_errors := make([]string, 0, 1000)
	if wg != nil {
		defer wg.Done()
	}

	defer func() {
		// recover
		if r := recover(); r != nil {
			log.Printf("panic: %s - %s\n", r, string(debug.Stack()))
			_errors = append(_errors, fmt.Sprintf("panic: %s - %s", r, string(debug.Stack())))
		}

		reconcile_action.DeleteReconciliationIfEmpty(seller.Code, timeIndex)

		// sum reconciliation
		value, err := reconcile_action.CalculateReconciliation(sellerCode, timeIndex)
		if err != nil {
			if err.Error() == "Not found any matched reconciliation_item." {
				return
			}

			_errors = append(_errors, err.Error())
			return
		}

		reconciliationF := model.Reconciliation{
			SellerCode:                 sellerCode,
			ReconcileScheduleTimeIndex: timeIndex,
			ReconciliationStatus:       model.ReconciliationStatus.Waiting,
		}
		updater := model.Reconciliation{
			TotalBuyerFee:  &value.TotalBuyerFee,
			TotalRevenue:   &value.TotalRevenue,
			ListingFee:     &value.ListingFee,
			FulfillmentFee: &value.FulfillmentFee,
			PenaltyFee:     &value.PenaltyFee,
			BonusAmount:    &value.BonusAmount,
			TotalPayment:   &value.TotalPayment,
		}
		result := model.ReconciliationDB.UpdateOne(reconciliationF, updater)
		if result.Status != common.APIStatus.Ok {
			_errors = append(_errors, fmt.Sprintf("%s-%s ReconciliationDB.UpdateOne]: %s", sellerCode, timeIndex, result.Message))
			return
		}

		// TODO: Turn off SyncRecon to billing
		// go SyncRecon(reconciliationF, &seller)

		// save _errors if any
		if len(_errors) > 0 {
			entry := map[string]interface{}{
				"seller_code": sellerCode,
				"index":       timeIndex,
				"err":         _errors,
			}
			fmt.Println("Errors in reconciliation: ", _errors)
			model.ReconciliationErrorDB.Create(entry)
		}
	}()

	// Get reconciliation if exist
	var reconciliation *model.Reconciliation
	reconciliationRes := model.ReconciliationDB.QueryOne(model.Reconciliation{
		SellerCode:                 sellerCode,
		ReconcileScheduleTimeIndex: timeIndex,
	})
	if reconciliationRes.Status == common.APIStatus.Ok {
		reconciliation = reconciliationRes.Data.([]*model.Reconciliation)[0]
	}

	if reconciliation != nil && reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
		// Just update with WAITING
		return
	}

	mapSkuAccumulatePoints := reconcile_action.GetSellerSkuAccumulatePoints(sellerCode)

	// ======================== in turn ========================
	for _, orderID := range orderIDs {
		fOrder := model.Order{
			OrderID: orderID,
		}
		orderRes := model.OrderDB.QueryOne(fOrder)
		if orderRes.Status != common.APIStatus.Ok {
			_errors = append(_errors, fmt.Sprintf("%d-%s: %s", orderID, sellerCode, orderRes.Message))
			continue
		}

		order := orderRes.Data.([]*model.Order)[0]

		// Nếu đơn hàng chưa được fill phí sản phẩm thì fill phí sản phẩm
		if conf.Config.IsApplyProductFee(order.CreatedTime) && (order.FillProductFee == nil || !*order.FillProductFee) {
			fmt.Println("FillOrderProductFee ", orderID)
			FillOrderProductFee(orderID)
		}

		orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "processScheduleForSeller")
		if orderItemPartitionDB == nil {
			continue
		}

		fOrderItem := model.OrderItem{
			OrderID:    orderID,
			SellerCode: sellerCode,
		}
		result := orderItemPartitionDB.Query(fOrderItem, 0, 0, nil)
		if result.Status == common.APIStatus.NotFound {
			continue
		} else if result.Status != common.APIStatus.Ok {
			_errors = append(_errors, fmt.Sprintf("%d-%s: %s", orderID, sellerCode, result.Message))
			continue
		}

		orderItems := result.Data.([]*model.OrderItem)
		for _, orderItem := range orderItems {
			processOrderItem(
				_errors,
				seller,
				order, orderItem,
				reconciliation,
				fromDate, toDate,
				timeIndex,
				_time, categories, subCategories,
				specRevenues,
				mapOrderArrSKUReturn,
				mapSkuAccumulatePoints[orderItem.Sku],
				feeConfigs,
			)
		}

		reconcile_action.ProcessCalculateApplyVoucher(order, orderItems, reconciliation, mapOrderArrSKUReturn)
	}

	// ======================== out turn ========================
	if reconciliation == nil {
		reconciliationRes := model.ReconciliationDB.QueryOne(model.Reconciliation{
			SellerCode:                 sellerCode,
			ReconcileScheduleTimeIndex: timeIndex,
		})
		if reconciliationRes.Status == common.APIStatus.Ok {
			reconciliation = reconciliationRes.Data.([]*model.Reconciliation)[0]
		}
	}
	{
		for orderIDReturn, arrSKUReturn := range *mapOrderArrSKUReturn {
			var order *model.Order = nil
			// chỉ xét đơn return khác lượt đối soát
			if utils.IsInt64Contains(orderIDs, orderIDReturn) || len(arrSKUReturn) == 0 || orderIDReturn == 0 {
				continue
			}

			processSku := make([]*model.OrderItem, 0)

			for _, skuReturn := range arrSKUReturn {
				if skuReturn.SellerCode != sellerCode {
					continue
				}

				// ReconciliationItem
				reconciliationItemOldResp := model.ReconciliationItemDB.QueryOne(model.ReconciliationItem{
					OrderID: orderIDReturn,
					Sku:     skuReturn.SKU,
					FeeType: enum.FeeType.REVENUE,
				})
				if reconciliationItemOldResp.Status != common.APIStatus.Ok {
					continue
				}
				rItem := reconciliationItemOldResp.Data.([]*model.ReconciliationItem)[0]

				// chỉ xét đơn return khác lượt đối soát
				if rItem.ReconcileScheduleTimeIndex == timeIndex {
					continue
				}

				orderResp := model.OrderDB.QueryOne(model.Order{
					OrderID: orderIDReturn,
				})

				if orderResp.Status != common.APIStatus.Ok {
					continue
				}
				order = orderResp.Data.([]*model.Order)[0]

				orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "processScheduleForSeller")
				if orderItemPartitionDB == nil {
					continue
				}

				// cho sku thường
				orderItemResp := orderItemPartitionDB.QueryOne(model.OrderItem{
					Sku:     rItem.Sku,
					OrderID: orderIDReturn,
				})
				if orderItemResp.Status != common.APIStatus.Ok {
					continue
				}
				orderItem := orderItemResp.Data.([]*model.OrderItem)[0]

				// do action
				processReconcileOutTurn(
					_errors,
					orderItem,
					rItem, reconciliation,
					fromDate, toDate,
					timeIndex,
					_time,
					*skuReturn,
					order,
					mapSkuAccumulatePoints[orderItem.Sku],
				)

				processSku = append(processSku, orderItem)
			}

			reconcile_action.ProcessCalculateRefundVoucher(order, processSku, reconciliation, mapOrderArrSKUReturn)
		}
	}

	reconcile_action.ProcessPenaltyInboundOverdue(
		seller, reconciliation,
		fromDate, toDate, timeIndex,
		_time,
	)
	reconcile_action.ProcessInvoiceOverdueFee(
		seller, reconciliation,
		fromDate, toDate, timeIndex,
		_time,
	)

	if seller.SellerType == client.SellerTypes.BIZ_HOUSEHOLD || seller.SellerType == client.SellerTypes.MARKET {
		reconcile_action.ProcessCalculateBizHouseholdTaxWithOrderList(
			seller.Code,
			timeIndex,
			orderIDs,
		)
	}
}

func processOrderItem(
	_errors []string,
	seller client.Seller,
	order *model.Order,
	orderItem *model.OrderItem,
	reconciliation *model.Reconciliation,
	from, to, timeIndex string,
	_time time.Time,
	categories []client.Category,
	subCategories []client.SellerSubCategory,
	specRevenues []model.ReconciliationSpecRevenue,
	mapOrderArrSKUReturn *map[int64][]*model.ReturnQuantityObject,
	skuAccumulatePoints *client.SkuAccumulatePoints,
	feeConfigs []client.SellerReconciliationFeeConfig,
) {
	filter := model.ReconciliationItem{
		OrderID: orderItem.OrderID,
		Sku:     orderItem.Sku,
		FeeType: enum.FeeType.REVENUE,
		OperationAnd: []bson.M{
			{
				"reconcile_schedule_time_index": bson.M{"$ne": timeIndex},
			},
		},
	}
	itemRes := model.ReconciliationItemDB.QueryOne(filter)
	if itemRes.Status == common.APIStatus.Ok {
		_errors = append(_errors, fmt.Sprintf("%d-%d (%s) in another schedule", orderItem.OrderID, orderItem.ProductID, orderItem.Sku))
		return
	}

	// create reconciliation
	if reconciliation == nil {
		reconciliationRes := model.ReconciliationDB.QueryOne(model.Reconciliation{
			SellerCode:                 orderItem.SellerCode,
			ReconcileScheduleTimeIndex: timeIndex,
		})
		if reconciliationRes.Status == common.APIStatus.Ok {
			reconciliation = reconciliationRes.Data.([]*model.Reconciliation)[0]
		} else {
			result := model.ReconciliationDB.Create(model.Reconciliation{
				RecCode:                    fmt.Sprintf("%s_%s", orderItem.SellerCode, timeIndex),
				ReconciledTime:             &_time,
				AllowConfirmTime:           _time.AddDate(0, 0, conf.Config.AllowConfirmReconciliationAfter),
				FromTime:                   from,
				ToTime:                     to,
				SellerCode:                 orderItem.SellerCode,
				ReconcileScheduleTimeIndex: timeIndex,
				ReconciliationStatus:       model.ReconciliationStatus.Waiting,
			})
			if result.Status != common.APIStatus.Ok {
				_errors = append(_errors, fmt.Sprintf("%d-%s: %s", orderItem.OrderID, orderItem.SellerCode, result.Message))
				return
			}

			reconciliation = result.Data.([]*model.Reconciliation)[0]
		}
	}

	reconciliationItem := OrderItemToRI(orderItem, seller, mapOrderArrSKUReturn, specRevenues, feeConfigs, order, categories, subCategories, timeIndex)

	// update reconciliation item
	riFilter := model.ReconciliationItem{
		FeeType:   enum.FeeType.REVENUE,
		OrderID:   order.OrderID,
		OrderCode: order.OrderCode,
		Sku:       orderItem.Sku,
	}
	riResult := model.ReconciliationItemDB.Upsert(riFilter, reconciliationItem)
	if riResult.Status != common.APIStatus.Ok {
		_errors = append(_errors, fmt.Sprintf("%d-%s: %s", orderItem.OrderID, orderItem.SellerCode, riResult.Message))
		return
	}

	if skuAccumulatePoints != nil {
		reconcile_action.ProcessSkuAccumulatePointsFee(reconciliation, &reconciliationItem, skuAccumulatePoints)
	}
	reconcile_action.ProcessPenaltyFulfillment(order, reconciliation, reconciliationItem)
}

func OrderItemToRI(orderItem *model.OrderItem, seller client.Seller, mapOrderArrSKUReturn *map[int64][]*model.ReturnQuantityObject, specRevenues []model.ReconciliationSpecRevenue, feeConfigs []client.SellerReconciliationFeeConfig, order *model.Order, categories []client.Category, subCategories []client.SellerSubCategory, timeIndex string) model.ReconciliationItem {
	zero := 0
	if orderItem.CompletedQuantity == nil {
		orderItem.CompletedQuantity = &zero
	}
	if orderItem.OutboundQuantity == nil {
		orderItem.OutboundQuantity = &zero
	}

	priceType := enum.PriceType.FIXED_PRICE
	revenue := orderItem.SellerPrice
	totalBuyerFee := 0.0
	totalBuyerFeePerOrder := 0.0
	deal := model.DealInfo{
		Code:           "",
		PricingType:    "NO_DISCOUNT",
		ChargeFee:      "NONE",
		ChargeFeeValue: 0,
	}
	campaign := model.CampaignInfo{}
	var priceRound *model.FeeValue
	if orderItem.SkuPriceType != nil {
		priceType = *orderItem.SkuPriceType
	}
	var feesApply []*model.FeeValue
	if orderItem.Fee != nil && orderItem.Fee.Result != nil {
		feesApply = orderItem.Fee.Result
		for _, fee := range feesApply {
			if fee.FeeCode == "PRICE_ROUND" {
				priceRound = fee
			}
		}
	}
	switch orderItem.Type {
	case enum.ItemType.DEAL:
		processWithDeal(orderItem, priceType, &deal, &revenue)
	case enum.ItemType.CAMPAIGN:
		processWithCampaign(orderItem, &campaign, &revenue)
	}
	returnObj := findOrderItemReturn(orderItem.Sku, orderItem.OrderID, mapOrderArrSKUReturn)
	returnedQuantity := returnObj.ReturnedQuantity
	mainQuantity := returnObj.MainQuantity
	damageQuantity := returnObj.DamageQuantity
	missingQuantity := returnObj.MissingQuantity

	specMainQty := 0
	specRev := findSpecRev(orderItem, specRevenues, "")

	if specRev != nil {
		// if spec revenue => re-calculate value
		if specRev.Revenue != nil {
			revenue = *specRev.Revenue
		}

		// return
		if specRev.ReturnQty != nil {
			returnedQuantity = *specRev.ReturnQty
		}
		if specRev.MainQty != nil {
			mainQuantity = *specRev.MainQty
			specMainQty = *specRev.MainQty
		}
		if specRev.DamageQty != nil {
			damageQuantity = *specRev.DamageQty
		}

		if specRev.Level != nil {
			orderItem.SkuLevel = func() *enum.LevelSKUValue {
				l := enum.LevelSKUValue(*specRev.Level)
				return &l
			}()
		}

		if specRev.PriceType != nil {
			priceType = *specRev.PriceType
		}

		if specRev.DealPricing != nil {
			deal.PricingType = *specRev.DealPricing
		}
		if specRev.DealCharge != nil {
			deal.ChargeFee = *specRev.DealCharge
		}
		if specRev.DealChargeValue != nil {
			deal.ChargeFeeValue = *specRev.DealChargeValue
		}
	}

	listingRate, fulfillmentRate := getSellerFee(order, orderItem, categories, subCategories, specRevenues, feeConfigs, seller)

	bookingRevenue := orderItem.Quantity * revenue
	deliveryQty := *orderItem.OutboundQuantity - returnedQuantity - specMainQty // reconcile quantity
	if timeIndex <= "20230316.20230308" {
		deliveryQty = *orderItem.OutboundQuantity - mainQuantity // - returnedQuantity + damageQuantity + missingQuantity
	}
	totalRevenue := deliveryQty * revenue

	if feesApply != nil {
		for _, fee := range feesApply {
			if fee.FeeType == "CHARGE_BUYER" {
				totalBuyerFee += fee.FeeValue * float64(deliveryQty)
				totalBuyerFeePerOrder += fee.FeeValue
			}
		}

		if totalBuyerFee == 0 {
			feesApply = nil
		}
	}

	if priceType == enum.PriceType.FIXED_REVENUE {
		// Just round price on FIXED_REVENUE
		// FIXED_PRICE has been rounded when create sku
		if priceRound == nil {
			totalBeforeRounding := revenue + int(totalBuyerFeePerOrder)
			diff := float64(orderItem.Price - totalBeforeRounding)
			priceRound = &model.FeeValue{
				FeeCode:  "PRICE_ROUND",
				FeeType:  "CHARGE_BUYER",
				FeeValue: diff,
			}
			feesApply = append(feesApply, priceRound)

			totalBuyerFee += diff * float64(deliveryQty)
		}
	}

	var completedDebtTime *time.Time
	var completedTime *time.Time
	if order != nil {
		completedTime = order.CompletedTime
		bills, err := client.Services.Bill.GetBill(order.OrderID)
		if err == nil && len(bills) > 0 && bills[0] != nil {
			completedDebtTime = bills[0].CompletedDebtTime
		}
	}

	// init reconciliation item
	listingFee := int(math.Round(float64(totalRevenue) * listingRate))
	fulfillmentFee := int(math.Round(float64(totalRevenue) * fulfillmentRate))
	reconciliationItem := model.ReconciliationItem{
		BookingDate:                orderItem.CreatedTime,
		SellerCode:                 orderItem.SellerCode,
		ReconcileScheduleTimeIndex: timeIndex,
		FeeType:                    enum.FeeType.REVENUE,
		OrderID:                    orderItem.OrderID,
		ProductId:                  orderItem.ProductID,
		OrderCode:                  orderItem.OrderCode,
		SaleOrderCode:              order.SaleOrderCode,
		CustomerProvinceCode:       order.CustomerProvinceCode,
		SkuPriceType:               priceType,
		Sku:                        orderItem.Sku,
		Type:                       string(orderItem.Type),
		Level: func() *string {
			for _, conf := range feeConfigs {
				if conf.CanApply(&seller) {
					return utils.ParseStringToPointer(string(enum.LevelSKU.VIP))
				}
			}
			if orderItem.SkuLevel == nil {
				return utils.ParseStringToPointer("")
			}
			return utils.ParseStringToPointer(string(*orderItem.SkuLevel))
		}(),
		Deal:              &deal,
		Campaign:          &campaign,
		Fees:              feesApply,
		SellerPrice:       orderItem.SellerPrice,
		PriceInOrder:      &orderItem.Price,
		Price:             &revenue,
		Quantity:          orderItem.Quantity,
		DeliveryQuantity:  &deliveryQty,
		OrderedPrice:      &bookingRevenue,
		TotalRevenue:      &totalRevenue,
		CompletedQuantity: orderItem.CompletedQuantity,
		OutboundQuantity:  orderItem.OutboundQuantity,
		ReturnQuantity:    &returnedQuantity,
		MainQuantity:      &mainQuantity,
		DamageQuantity:    &damageQuantity,
		MissingQuantity:   &missingQuantity,
		ListingFee:        &listingFee,
		FulfillmentFee:    &fulfillmentFee,
		TotalBuyerFee:     &totalBuyerFee,
		ListingRate:       listingRate,
		FulfillmentRate:   fulfillmentRate,
		OutboundInfos:     orderItem.OutboundInfos,
		CompletedTime:     completedTime,
		CompletedDebtTime: completedDebtTime,
	}

	chargeBuyerFee := reconcile_action.HasToChargeBuyerFee(&reconciliationItem)
	if specRev != nil && specRev.ChargeBuyerFee != nil {
		chargeBuyerFee = *specRev.ChargeBuyerFee
	}
	reconciliationItem.HasChargedBuyerFee = &chargeBuyerFee

	payment := totalRevenue - (listingFee + fulfillmentFee)
	if chargeBuyerFee {
		payment -= int(math.Round(totalBuyerFee))
	}
	reconciliationItem.TotalPayment = &payment

	// check invoice
	invoiceFilter := model.Invoice{
		OrderID:    orderItem.OrderID,
		SellerCode: orderItem.SellerCode,
	}
	invoiceRes := model.InvoiceDB.QueryOne(invoiceFilter)
	if invoiceRes.Status == common.APIStatus.Ok {
		invoice := invoiceRes.Data.([]*model.Invoice)[0]
		t := true
		if invoice.InvoiceStatus == model.InvoiceStatus.Completed {
			reconciliationItem.HasSentInvoice = &t
		} else if invoice.InvoiceDocumentURL != nil && *invoice.InvoiceDocumentURL != "" {
			reconciliationItem.HasSentInvoice = &t
		}
	}
	return reconciliationItem
}

func getSellerFeeFromProductFee(orderItem *model.OrderItem) (listingRate float64, fulfillmentRate float64) {

	if orderItem.SubItems != nil {
		_, priceRatio := getIngredientRatio(orderItem)

		if orderItem.ProductFees == nil || len(orderItem.ProductFees) == 0 {
			return
		}

		feeMap := make(map[int64]*model.ProductFee, len(*orderItem.SubItems))
		for index := range orderItem.ProductFees {
			fee := orderItem.ProductFees[index]
			feeMap[fee.ProductID] = fee
		}

		for i := range *orderItem.SubItems {
			subItem := (*orderItem.SubItems)[i]

			subFee, ok := feeMap[subItem.ProductID]
			if ok && subFee != nil {
				subListingRate := subFee.SaleFeePercent / 100.0
				subFulfillmentRate := subFee.FulfillmentFeePercent / 100.0

				listingRate += subListingRate * priceRatio[subItem.Sku]
				fulfillmentRate += subFulfillmentRate * priceRatio[subItem.Sku]
			} else {
				fmt.Println("[ERROR] - GET_SUB_ITEM_FEE : ", orderItem.OrderID, orderItem.ItemCode, orderItem.ProductID, subItem.ProductID)
			}
		}

	} else {
		if orderItem.ProductFees != nil && len(orderItem.ProductFees) > 0 {
			listingRate = orderItem.ProductFees[0].SaleFeePercent / 100.0
			fulfillmentRate = orderItem.ProductFees[0].FulfillmentFeePercent / 100.0
		} else {
			fmt.Println("[ERROR] - GET_ITEM_FEE : ", orderItem.OrderID, orderItem.ItemCode, orderItem.ProductID)
		}
	}

	return
}

func getSellerFee(order *model.Order, orderItem *model.OrderItem, categories []client.Category, subCategories []client.SellerSubCategory, specRevenues []model.ReconciliationSpecRevenue, feeConfigs []client.SellerReconciliationFeeConfig, seller client.Seller) (listingRate float64, fulfillmentRate float64) {
	for _, conf := range feeConfigs {
		if conf.CanApply(&seller) {
			return conf.SaleFeePercent / float64(100), conf.FulfillmentFeePercent / float64(100)
		}
	}

	// Lấy phí seller từ product fee với trường hợp orderItem đã tạo sau thời điểm áp dụng
	if order.CreatedTime.After(conf.Config.ProductFeeStartTime) {
		return getSellerFeeFromProductFee(orderItem)
	}

	if orderItem.SubItems != nil {
		_, priceRatio := getIngredientRatio(orderItem)
		for i := range *orderItem.SubItems {
			subItem := (*orderItem.SubItems)[i]

			subItem.OrderID = orderItem.OrderID
			subListingRate, subFulfillmentRate := getSellerCategoryFeeWithLevel(order, subItem, categories, subCategories, specRevenues)

			listingRate += subListingRate * priceRatio[(*orderItem.SubItems)[i].Sku]
			fulfillmentRate += subFulfillmentRate * priceRatio[(*orderItem.SubItems)[i].Sku]
		}

		if orderItem.Sku == "I5STYACBVI.AWGHT61P" && orderItem.OrderID == 35492934 {
			fmt.Println("listingRate: ", listingRate)
			fmt.Println("fulfillmentRate: ", fulfillmentRate)
		}

	} else {
		return getSellerCategoryFeeWithLevel(order, orderItem, categories, subCategories, specRevenues)
	}
	return
}

func processReconcileOutTurn(
	_errors []string,
	orderItem *model.OrderItem,
	rItem *model.ReconciliationItem,
	reconciliation *model.Reconciliation,
	from, to, timeIndex string,
	_time time.Time,
	returnObj model.ReturnQuantityObject,
	order *model.Order,
	skuAccumulatePoints *client.SkuAccumulatePoints,
) {
	// create reconciliation
	if reconciliation == nil {
		reconciliationRes := model.ReconciliationDB.QueryOne(model.Reconciliation{
			SellerCode:                 orderItem.SellerCode,
			ReconcileScheduleTimeIndex: timeIndex,
		})
		if reconciliationRes.Status == common.APIStatus.Ok {
			reconciliation = reconciliationRes.Data.([]*model.Reconciliation)[0]
		} else {
			result := model.ReconciliationDB.Create(model.Reconciliation{
				RecCode:                    fmt.Sprintf("%s_%s", orderItem.SellerCode, timeIndex),
				ReconciledTime:             &_time,
				AllowConfirmTime:           _time.AddDate(0, 0, conf.Config.AllowConfirmReconciliationAfter),
				FromTime:                   from,
				ToTime:                     to,
				SellerCode:                 orderItem.SellerCode,
				ReconcileScheduleTimeIndex: timeIndex,
				ReconciliationStatus:       model.ReconciliationStatus.Waiting,
			})
			if result.Status != common.APIStatus.Ok {
				_errors = append(_errors, fmt.Sprintf("%d-%s: %s", orderItem.OrderID, orderItem.SellerCode, result.Message))
				return
			}

			reconciliation = result.Data.([]*model.Reconciliation)[0]
		}
	}

	// init default value
	priceType := rItem.SkuPriceType
	revenue := *rItem.Price
	totalBuyerFee := 0.0
	totalBuyerFeePerOrder := 0.0

	var priceRound *model.FeeValue

	// get return main/damage/missing/return from ticket
	returnedQuantity := returnObj.ReturnedQuantity
	mainQuantity := returnObj.MainQuantity
	damageQuantity := returnObj.DamageQuantity
	missingQuantity := returnObj.MissingQuantity

	listingRate := rItem.ListingRate
	fulfillmentRate := rItem.FulfillmentRate

	reconcileQuantity := returnedQuantity
	if timeIndex <= "20230316.20230308" {
		reconcileQuantity = mainQuantity
	}
	totalRevenue := reconcileQuantity * revenue

	// process for fee applied on sku
	var feesApply []*model.FeeValue
	if orderItem.Fee != nil && orderItem.Fee.Result != nil {
		feesApply = orderItem.Fee.Result
		for _, fee := range feesApply {
			if fee.FeeCode == "PRICE_ROUND" {
				priceRound = fee
			}
		}
	}
	// calculate fee
	if feesApply != nil {
		for _, fee := range feesApply {
			if fee.FeeType == "CHARGE_BUYER" {
				totalBuyerFee += fee.FeeValue * float64(reconcileQuantity)
				totalBuyerFeePerOrder += fee.FeeValue
			}
		}

		if totalBuyerFee == 0 {
			feesApply = nil
		}
	}
	if priceType == enum.PriceType.FIXED_REVENUE {
		// Just round price on FIXED_REVENUE
		// FIXED_PRICE has been rounded when create sku
		if priceRound == nil {
			totalBeforeRounding := revenue + int(totalBuyerFeePerOrder)
			diff := float64(orderItem.Price - totalBeforeRounding)
			priceRound = &model.FeeValue{
				FeeCode:  "PRICE_ROUND",
				FeeType:  "CHARGE_BUYER",
				FeeValue: diff,
			}
			feesApply = append(feesApply, priceRound)

			totalBuyerFee += diff * float64(reconcileQuantity)
		}
	}

	// completed time
	var completedTime *time.Time = nil
	if order != nil && order.CompletedTime != nil {
		completedTime = order.CompletedTime
	}

	// Lấy thời gian hoàn tất công nợ
	var completedDebtTime *time.Time
	bills, err := client.Services.Bill.GetBill(order.OrderID)
	if err == nil && len(bills) > 0 && bills[0] != nil {
		completedDebtTime = bills[0].CompletedDebtTime
	}

	// init reconciliation item
	listingFee := int(math.Round(float64(totalRevenue) * listingRate))
	fulfillmentFee := int(math.Round(float64(totalRevenue) * fulfillmentRate))
	reconciliationItem := model.ReconciliationItem{
		BookingDate:                rItem.BookingDate,
		SellerCode:                 rItem.SellerCode,
		ReconcileScheduleTimeIndex: timeIndex,
		FeeType:                    enum.FeeType.REVENUE_RETURN_OUT_TURN,
		OrderID:                    rItem.OrderID,
		ProductId:                  rItem.ProductId,
		OrderCode:                  rItem.OrderCode,
		SaleOrderCode:              rItem.SaleOrderCode,
		CustomerProvinceCode:       order.CustomerProvinceCode,
		SkuPriceType:               rItem.SkuPriceType,
		Sku:                        rItem.Sku,
		Type:                       rItem.Type,
		Level:                      rItem.Level,

		Deal:     rItem.Deal,
		Campaign: rItem.Campaign,
		Fees:     feesApply,

		SellerPrice:  rItem.SellerPrice,
		PriceInOrder: rItem.PriceInOrder,
		Price:        rItem.Price,
		OrderedPrice: rItem.OrderedPrice,
		TotalRevenue: &totalRevenue,

		Quantity:          rItem.Quantity,
		DeliveryQuantity:  rItem.DeliveryQuantity,
		CompletedQuantity: rItem.CompletedQuantity,
		OutboundQuantity:  rItem.OutboundQuantity,

		ReturnQuantity:  &returnedQuantity,
		MainQuantity:    &mainQuantity,
		DamageQuantity:  &damageQuantity,
		MissingQuantity: &missingQuantity,

		ListingFee:      &listingFee,
		FulfillmentFee:  &fulfillmentFee,
		TotalBuyerFee:   &totalBuyerFee,
		ListingRate:     rItem.ListingRate,
		FulfillmentRate: rItem.FulfillmentRate,

		ReturnInfos: orderItem.ReturnInfos,

		CompletedTime:     completedTime,
		CompletedDebtTime: completedDebtTime,
	}
	chargeBuyerFee := reconcile_action.HasToChargeBuyerFee(&reconciliationItem)
	if rItem.HasChargedBuyerFee != nil {
		chargeBuyerFee = *rItem.HasChargedBuyerFee
	}
	reconciliationItem.HasChargedBuyerFee = &chargeBuyerFee

	payment := totalRevenue - (listingFee + fulfillmentFee)
	if chargeBuyerFee {
		payment -= int(math.Round(totalBuyerFee))
	}
	reconciliationItem.TotalPayment = &payment

	previousReconciliationRes := model.ReconciliationItemDB.Query(model.ReconciliationItem{SellerCode: orderItem.SellerCode, OrderID: orderItem.OrderID, ProductId: orderItem.ProductID}, 0, 0, nil)
	var reconciliationIndexes []string
	for _, item := range previousReconciliationRes.Data.([]*model.ReconciliationItem) {
		if utils.IsContains(reconciliationIndexes, item.ReconcileScheduleTimeIndex) || item.ReconcileScheduleTimeIndex == timeIndex {
			continue
		}
		reconciliationIndexes = append(reconciliationIndexes, item.ReconcileScheduleTimeIndex)
	}
	reconciliationItem.PreviousReconcileScheduleTimeIndexes = reconciliationIndexes

	// update reconciliation item
	riFilter := model.ReconciliationItem{
		FeeType:                    enum.FeeType.REVENUE_RETURN_OUT_TURN,
		OrderID:                    orderItem.OrderID,
		OrderCode:                  orderItem.OrderCode,
		Sku:                        orderItem.Sku,
		ReconcileScheduleTimeIndex: timeIndex,
	}
	riResult := model.ReconciliationItemDB.Upsert(riFilter, reconciliationItem)
	if riResult.Status != common.APIStatus.Ok {
		_errors = append(_errors, fmt.Sprintf("%d-%s: %s", orderItem.OrderID, orderItem.SellerCode, riResult.Message))
	}
}

func processWithDeal(orderItem *model.OrderItem, priceType enum.PriceTypeValue, deal *model.DealInfo, revenue *int) {
	if orderItem.DealCode == nil || *orderItem.DealCode == "" {
		return
	}

	deal.Code = *orderItem.DealCode
	deal.ChargeFee = orderItem.ChargeDealFee
	deal.ChargeFeeValue = orderItem.ChargeDealFeeValue
	if deal.ChargeFee == "" {
		deal.ChargeFee = "SELLER"
	}

	if orderItem.DealPricingType != nil && *orderItem.DealPricingType != "" {
		deal.PricingType = *orderItem.DealPricingType
	}

	switch deal.ChargeFee {
	case "SELLER":
		if deal.ChargeFeeValue == 0 {
			// Can't estimate difference revenue => use order-item revenue
			*revenue = orderItem.SellerPrice
		} else {
			if orderItem.CreatedTime.Unix() < model.ApplyPricingNow.Unix() {
				if priceType == enum.PriceType.FIXED_REVENUE && deal.PricingType == "PERCENTAGE" {
					*revenue = orderItem.SellerPrice - deal.ChargeFeeValue
				} else if priceType == enum.PriceType.FIXED_REVENUE && deal.PricingType == "ABSOLUTE" {
					// Estimate original fee => extra fee applied (must be not included into charge deal on seller revenue)
					// Extra fee must be not included into charge deal on seller revenue
					originalPrice := orderItem.Price + deal.ChargeFeeValue
					extraFee := originalPrice - orderItem.SellerPrice
					*revenue = orderItem.SellerPrice - (deal.ChargeFeeValue - extraFee)
				} else {
					// revenue = revenue - dealChargeFeeValue
					*revenue = orderItem.Price
				}
			} else {
				*revenue = orderItem.SellerPrice - deal.ChargeFeeValue
				// fix lẹ cho đối soát tháng 4
				if priceType == enum.PriceType.FIXED_REVENUE && deal.PricingType == "ABSOLUTE" {
					*revenue = orderItem.SellerRevenue
				}
			}
		}

	case "SELLER_MARKETPLACE":
		*revenue = *revenue - deal.ChargeFeeValue/2

	case "MARKETPLACE":
		*revenue = orderItem.SellerPrice
	}
}

func processWithCampaign(orderItem *model.OrderItem, campaign *model.CampaignInfo, revenue *int) {
	if orderItem.CampaignCode == "" {
		return
	}

	isCombo := orderItem.SubItems != nil && len(*orderItem.SubItems) > 0
	isUseSKUsPrice := orderItem.Fee != nil && orderItem.Fee.Total == 0

	campaign.Code = orderItem.CampaignCode
	campaign.PricingType = orderItem.CampaignPricingType
	campaign.ChargeFee = "SELLER"
	campaign.ChargeFeeValue = orderItem.ChargeCampaignFeeValue

	if isUseSKUsPrice && isCombo {
		campaign.ChargeFeeValue = orderItem.SellerPrice - orderItem.Price
		*revenue = orderItem.SellerPrice - campaign.ChargeFeeValue
		return
	}

	*revenue = orderItem.SellerPrice - campaign.ChargeFeeValue
}

func getReturnQuantityByTicket(orderIds []string, completeFrom, completeTo time.Time) map[int64][]*model.ReturnQuantityObject {
	mapOrderArrSKUReturn := make(map[int64][]*model.ReturnQuantityObject)
	exceptSellerCodes := model.SELLER_INTERNALS

	var ticketsByCompleteTime []*model.ReturnTicket
	for offset, limit := 0, 100; ; offset += limit {
		tickets, err := client.Services.Ticket.GetReturnTicketCS(
			&model.ReturnTicket{
				Status:        model.ReturnTicketStatus.COMPLETED,
				CompletedFrom: &completeFrom,
				CompletedTo:   &completeTo,
			},
			offset, limit, true, "",
		)
		if err != nil {
			break
		}

		ticketsByCompleteTime = append(ticketsByCompleteTime, tickets...)
	}

	ticketCSs := make([]*model.ReturnTicket, 0, len(ticketsByCompleteTime))
	for i := 0; i < len(orderIds); i += 10 {
		rightBound := i + 10
		if rightBound >= len(orderIds) {
			rightBound = len(orderIds)
		}
		idStr := strings.Join(orderIds[i:rightBound], ",")

		for offset, limit := 0, 10; ; offset += limit {
			tickets, err := client.Services.Ticket.GetReturnTicketCS(
				&model.ReturnTicket{
					OrderIDs:    idStr,
					Status:      model.ReturnTicketStatus.COMPLETED,
					CompletedTo: &completeTo,
				},
				offset, limit, true, "",
			)
			if err != nil {
				break
			}

			ticketCSs = append(ticketCSs, tickets...)
		}
	}

	existedCSTickets := make(map[string]bool, len(ticketCSs))
	for _, ticketCS := range ticketCSs {
		key := fmt.Sprintf(
			"%d-%d",
			ticketCS.OrderID,
			ticketCS.CreatedTime.Unix(),
		)
		existedCSTickets[key] = true
	}

	for _, ticketByCompleteTime := range ticketsByCompleteTime {
		key := fmt.Sprintf(
			"%d-%d",
			ticketByCompleteTime.OrderID,
			ticketByCompleteTime.CreatedTime.Unix(),
		)
		if !existedCSTickets[key] {
			ticketCSs = append(ticketCSs, ticketByCompleteTime)
		}
	}

	for _, ticketCS := range ticketCSs {
		if ticketCS.Status != model.ReturnTicketStatus.COMPLETED {
			continue
		}

		if ticketCS.CompletedTime == nil {
			continue
		}

		orderArr := mapOrderArrSKUReturn[ticketCS.OrderID]

		ticketWMSs, err := client.Services.Warehouse.GetReturnTicketWMS(&model.ReturnTicketWMS{
			RefCode: ticketCS.Code,
		})
		if err != nil {
			continue
		}

		// loop ticketWMSs as ticketWMS
		for _, ticketWMS := range ticketWMSs {
			if ticketWMS.Type == nil ||
				ticketWMS.Status == "CANCEL" {
				continue
			}

			processedCombo := map[string]bool{}

			// increase quantity
			for _, itemWMS := range ticketWMS.ReturnTicketItems {

				// skip internal sellers
				var findItemCS *model.ReturnTicketItem = nil
				for _, itemCS := range ticketCS.Items {
					foundSKU := itemCS.SKU == itemWMS.SKU
					foundComboSKU := itemWMS.ComboSKU != "" && itemWMS.ComboSKU == itemCS.SKU
					if foundSKU || foundComboSKU {
						findItemCS = itemCS
						break
					}
				}
				if findItemCS == nil || utils.IsContains(exceptSellerCodes, findItemCS.SellerCode) {
					continue
				}

				// tính sp con trong combo
				if itemWMS.ComboSKU != "" {
					calculateSkuComboReturn(&orderArr, processedCombo, ticketWMS, itemWMS, findItemCS.SellerCode)
					continue
				}

				calculateSkuReturn(&orderArr, ticketWMS, itemWMS, findItemCS.SellerCode)
			}
		}

		mapOrderArrSKUReturn[ticketCS.OrderID] = orderArr
	}

	return mapOrderArrSKUReturn
}

func calculateSkuComboReturn(
	orderArr *[]*model.ReturnQuantityObject,
	processedCombo map[string]bool,
	ticketWMS *model.ReturnTicketWMS,
	itemWMS *model.ReturnTicketItemWMS,
	sellerCode string,
) {
	comboItems := 0
	for _, item := range ticketWMS.ReturnTicketItems {
		if item.ComboSKU == itemWMS.ComboSKU {
			comboItems++
		}
	}

	if comboItems > 1 && processedCombo[itemWMS.ComboSKU] {
		return
	}

	minComboDoneQuantity := 0
	for _, item := range ticketWMS.ReturnTicketItems {
		if item.ComboSKU != itemWMS.ComboSKU {
			continue
		}

		// combo has no item is invalid
		if itemWMS.ItemPerCombo == 0 {
			continue
		}

		comboDoneQuantity := item.DoneQuantity / item.ItemPerCombo
		if comboDoneQuantity == 0 {
			return
		}

		if minComboDoneQuantity == 0 {
			minComboDoneQuantity = comboDoneQuantity
		} else if minComboDoneQuantity > comboDoneQuantity {
			minComboDoneQuantity = comboDoneQuantity
		}
	}

	isExist := false
	for _, orderReturn := range *orderArr {
		if orderReturn.SKU == itemWMS.ComboSKU {
			isExist = true

			orderReturn.ReturnedQuantity += minComboDoneQuantity
			switch *ticketWMS.Type {
			case enum.ReturnType.NORMAL:
				orderReturn.MainQuantity += minComboDoneQuantity
			case enum.ReturnType.DAMAGE:
				orderReturn.DamageQuantity += minComboDoneQuantity
			case enum.ReturnType.MISSING:
				orderReturn.MissingQuantity += minComboDoneQuantity
			}
		}
	}

	if !isExist {
		obj := model.ReturnQuantityObject{
			SellerCode: sellerCode,
			SKU:        itemWMS.ComboSKU,
		}

		obj.ReturnedQuantity += minComboDoneQuantity
		switch *ticketWMS.Type {
		case enum.ReturnType.NORMAL:
			obj.MainQuantity += minComboDoneQuantity
		case enum.ReturnType.DAMAGE:
			obj.DamageQuantity += minComboDoneQuantity
		case enum.ReturnType.MISSING:
			obj.MissingQuantity += minComboDoneQuantity
		}
		*orderArr = append(*orderArr, &obj)
	}

	processedCombo[itemWMS.ComboSKU] = true
}

func calculateSkuReturn(
	orderArr *[]*model.ReturnQuantityObject,
	ticketWMS *model.ReturnTicketWMS,
	itemWMS *model.ReturnTicketItemWMS,
	sellerCode string,
) {
	isExist := false
	for _, orderReturn := range *orderArr {
		if orderReturn.SKU == itemWMS.SKU {
			isExist = true
			orderReturn.ReturnedQuantity += itemWMS.DoneQuantity
			switch *ticketWMS.Type {
			case enum.ReturnType.NORMAL:
				orderReturn.MainQuantity += itemWMS.DoneQuantity
			case enum.ReturnType.DAMAGE:
				orderReturn.DamageQuantity += itemWMS.DoneQuantity
			case enum.ReturnType.MISSING:
				orderReturn.MissingQuantity += itemWMS.DoneQuantity
			}
		}
	}

	if !isExist {
		obj := model.ReturnQuantityObject{
			SellerCode: sellerCode,
			SKU:        itemWMS.SKU,
		}
		obj.ReturnedQuantity += itemWMS.DoneQuantity
		switch *ticketWMS.Type {
		case enum.ReturnType.NORMAL:
			obj.MainQuantity += itemWMS.DoneQuantity
		case enum.ReturnType.DAMAGE:
			obj.DamageQuantity += itemWMS.DoneQuantity
		case enum.ReturnType.MISSING:
			obj.MissingQuantity += itemWMS.DoneQuantity
		}
		*orderArr = append(*orderArr, &obj)
	}
}

func findOrderItemReturn(
	sku string,
	orderID int64,
	mapOrderArrSKUReturn *map[int64][]*model.ReturnQuantityObject,
) model.ReturnQuantityObject {
	obj := model.ReturnQuantityObject{
		SKU: sku,
	}

	arrSKU := (*mapOrderArrSKUReturn)[orderID]
	for _, it := range arrSKU {
		if sku == it.SKU {
			obj.ReturnedQuantity += it.ReturnedQuantity
			obj.MainQuantity += it.MainQuantity
			obj.DamageQuantity += it.DamageQuantity
			obj.MissingQuantity += it.MissingQuantity
		}
	}

	return obj
}

func getReturnQuantityByOrderIds(orderIds []string, completeTo time.Time) map[int64][]*model.ReturnQuantityObject {
	mapOrderArrSKUReturn := map[int64][]*model.ReturnQuantityObject{}
	exceptSellerCodes := model.SELLER_INTERNALS
	var completedReturnTickets []*model.ReturnTicket
	for i := 0; i < len(orderIds); i += 10 {
		rightBound := i + 10
		if rightBound >= len(orderIds) {
			rightBound = len(orderIds)
		}
		idStr := strings.Join(orderIds[i:rightBound], ",")

		for offset, limit := 0, 10; ; offset += limit {
			tickets, err := client.Services.Ticket.GetReturnTicketCS(
				&model.ReturnTicket{
					OrderIDs:    idStr,
					Status:      model.ReturnTicketStatus.COMPLETED,
					CompletedTo: &completeTo,
				},
				offset, limit, true, "",
			)
			if err != nil {
				break
			}

			completedReturnTickets = append(completedReturnTickets, tickets...)
		}
	}
	for _, completedReturnTicket := range completedReturnTickets {
		orderArr := mapOrderArrSKUReturn[completedReturnTicket.OrderID]
		ticketWMSs, err := client.Services.Warehouse.GetReturnTicketWMS(&model.ReturnTicketWMS{
			RefCode: completedReturnTicket.Code,
		})
		if err != nil {
			continue
		}
		// loop ticketWMSs as ticketWMS
		for _, ticketWMS := range ticketWMSs {
			if ticketWMS.Type == nil ||
				ticketWMS.Status == "CANCEL" {
				continue
			}

			processedCombo := map[string]bool{}

			// increase quantity
			for _, itemWMS := range ticketWMS.ReturnTicketItems {

				// skip internal sellers
				var findItemCS *model.ReturnTicketItem = nil
				for _, itemCS := range completedReturnTicket.Items {
					foundSKU := itemCS.SKU == itemWMS.SKU
					foundComboSKU := itemWMS.ComboSKU != "" && itemWMS.ComboSKU == itemCS.SKU
					if foundSKU || foundComboSKU {
						findItemCS = itemCS
						break
					}
				}
				if findItemCS == nil || utils.IsContains(exceptSellerCodes, findItemCS.SellerCode) {
					continue
				}

				// tính sp con trong combo
				if itemWMS.ComboSKU != "" {
					calculateSkuComboReturn(&orderArr, processedCombo, ticketWMS, itemWMS, findItemCS.SellerCode)
					continue
				}

				calculateSkuReturn(&orderArr, ticketWMS, itemWMS, findItemCS.SellerCode)
			}
		}

		mapOrderArrSKUReturn[completedReturnTicket.OrderID] = orderArr
	}
	return mapOrderArrSKUReturn
}

func GetReconciliationTimeAtDeliveredDateByConfig() int {
	defaultValue := 3
	confResp := client.Services.Seller.GetSettingForSC("RECONCILIATION_TIME_AT_DELIVERED_DATE")

	if confResp.Status != common.APIStatus.Ok {
		return defaultValue
	}

	settings := confResp.Data.([]client.SettingForSC)[0]

	for _, item := range settings.Data {
		if item["Key"] == "reconciliationTimeAtDeliveredDate" {
			if value, ok := item["Value"].(float64); ok {
				defaultValue = int(value)
			}
		}
	}

	return defaultValue
}
