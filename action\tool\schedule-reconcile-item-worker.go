package tool

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

func ScheduleReconciliationItemWorker(now *time.Time, config *schedule.Config) (err error, note string, nextRun *time.Time) {
	if now == nil {
		_t := time.Now()
		now = &_t
	}

	RunningScheduleReconciliationItem(now)

	y, m, d := now.Date()
	timeNextRun := time.Date(y, m, d, 2, 0, 0, 0, model.VNTimeZone).AddDate(0, 0, 7)
	nextRun = &timeNextRun

	return
}

func RunningScheduleReconciliationItem(now *time.Time) {
	var scheduleOrderItems []ScheduleOrderItem

	// đơn hàng chưa đối soát
	osFilter := model.OrderSeller{
		ComplexQuery: []*bson.M{
			{"seller_class": "EXTERNAL"},
			{"reconciliation_status": bson.M{"$exists": false}},
			{"status": "COMPLETED"},
		},
	}
	var offset, limit int64
	limit = 1000
	for {
		// turn off for now
		osRes := model.OrderSellerDB.Query(osFilter, offset, limit, nil)
		if osRes.Status != common.APIStatus.Ok {
			break
		}
		offset += limit

		ordSellers := osRes.Data.([]*model.OrderSeller)
		for _, ordSeller := range ordSellers {
			scheduleOrderItems = append(scheduleOrderItems, ScheduleOrderItem{
				SellerCode: ordSeller.SellerCode,
				OrderID:    ordSeller.ParentOrderID,
			})
		}
	}

	// đơn trả hàng chưa đối soát
	osFilter = model.OrderSeller{
		ComplexQuery: []*bson.M{
			{"seller_class": "EXTERNAL"},
			{"return_status": "COMPLETED"},
		},
	}
	offset = 0
	for {
		// turn off for now
		osRes := model.OrderSellerDB.Query(osFilter, offset, limit, nil)
		if osRes.Status != common.APIStatus.Ok {
			break
		}
		offset += limit

		ordSellers := osRes.Data.([]*model.OrderSeller)
		for _, ordSeller := range ordSellers {
			reItmRes := model.ReconciliationItemDB.Query(model.ReconciliationItem{
				OrderID:    ordSeller.ParentOrderID,
				SellerCode: ordSeller.SellerCode,
			}, 0, 0, nil)
			if reItmRes.Status != common.APIStatus.Ok {
				continue
			}

			reItms := reItmRes.Data.([]*model.ReconciliationItem)
			notCreateReconcileYet := hasNotCreateReconcile(reItms)
			if notCreateReconcileYet {
				scheduleOrderItems = append(scheduleOrderItems, ScheduleOrderItem{
					SellerCode: ordSeller.SellerCode,
					OrderID:    ordSeller.ParentOrderID,
				})
			}
		}
	}

	data := ScheduleReconciliationItemReq{
		CompletedTime: now,
		OrderItems:    scheduleOrderItems,
	}
	ProcessScheduleReconciliationItem(&data)
}

func hasNotCreateReconcile(reItms []*model.ReconciliationItem) bool {
	for _, reItm := range reItms {
		if reItm.MainQuantity != nil {
			if *reItm.MainQuantity > 0 {
				return false
			}
		}
		if reItm.FeeType == enum.FeeType.REVENUE_RETURN_OUT_TURN {
			return false
		}
	}
	return true
}
