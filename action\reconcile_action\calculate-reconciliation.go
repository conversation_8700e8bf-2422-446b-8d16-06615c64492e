package reconcile_action

import (
	"errors"
	"math"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ReconciliationValue struct {
	TotalBuyerFee  int
	TotalRevenue   int
	ListingFee     int
	FulfillmentFee int
	PenaltyFee     int
	BonusAmount    int
	TotalPayment   int
}

func CalculateReconciliation(sellerCode string, reconcileScheduleTimeIndex string) (ReconciliationValue, error) {
	query := bson.M{
		"seller_code":                   sellerCode,
		"reconcile_schedule_time_index": reconcileScheduleTimeIndex,
		"fee_type":                      bson.M{"$ne": nil},
	}
	var cursor *primitive.ObjectID
	limit := 1000
	reconciliationItems := make([]*model.ReconciliationItem, 0)
	for {
		res := model.ReconciliationItemDB.Query(query, 0, int64(limit), &primitive.M{"_id": 1})

		if res.Status != common.APIStatus.Ok {
			break
		}

		data := res.Data.([]*model.ReconciliationItem)

		reconciliationItems = append(reconciliationItems, data...)

		if len(data) < limit {
			break
		}

		cursor = data[len(data)-1].ID
		query["_id"] = bson.M{"$gt": cursor}
	}

	if len(reconciliationItems) == 0 {
		return ReconciliationValue{}, errors.New("not found reconciliation items")
	}

	return CalculateViaItems(reconciliationItems), nil
}

func CalculateViaItems(items []*model.ReconciliationItem) ReconciliationValue {
	value := ReconciliationValue{}
	for _, reconciliationItem := range items {
		switch reconciliationItem.FeeType {
		case enum.FeeType.REVENUE:
			if reconciliationItem.TotalRevenue != nil {
				value.TotalRevenue += *reconciliationItem.TotalRevenue
			}
			if reconciliationItem.ListingFee != nil {
				value.ListingFee += *reconciliationItem.ListingFee
			}
			if reconciliationItem.FulfillmentFee != nil {
				value.FulfillmentFee += *reconciliationItem.FulfillmentFee
			}
			if reconciliationItem.HasChargedBuyerFee == nil && HasToChargeBuyerFee(reconciliationItem) {
				value.TotalBuyerFee += int(math.Round(*reconciliationItem.TotalBuyerFee))
			}
			if reconciliationItem.HasChargedBuyerFee != nil && *reconciliationItem.HasChargedBuyerFee {
				value.TotalBuyerFee += int(math.Round(*reconciliationItem.TotalBuyerFee))
			}
			if reconciliationItem.TotalPayment != nil {
				value.TotalPayment += *reconciliationItem.TotalPayment
			}

		case enum.FeeType.REVENUE_RETURN_OUT_TURN:
			if reconciliationItem.TotalRevenue != nil {
				value.TotalRevenue -= *reconciliationItem.TotalRevenue
			}
			if reconciliationItem.ListingFee != nil {
				value.ListingFee -= *reconciliationItem.ListingFee
			}
			if reconciliationItem.FulfillmentFee != nil {
				value.FulfillmentFee -= *reconciliationItem.FulfillmentFee
			}
			if reconciliationItem.HasChargedBuyerFee == nil && HasToChargeBuyerFee(reconciliationItem) {
				value.TotalBuyerFee -= int(math.Round(*reconciliationItem.TotalBuyerFee))
			}
			if reconciliationItem.HasChargedBuyerFee != nil && *reconciliationItem.HasChargedBuyerFee {
				value.TotalBuyerFee -= int(math.Round(*reconciliationItem.TotalBuyerFee))
			}
			if reconciliationItem.TotalPayment != nil {
				value.TotalPayment -= *reconciliationItem.TotalPayment
			}

		case enum.FeeType.BONUS:
			value.BonusAmount += reconciliationItem.BonusAmount + reconciliationItem.BonusBMAmount + reconciliationItem.BonusBMLAmount
		case enum.FeeType.OTHER_BONUS:
			value.BonusAmount += reconciliationItem.BonusAmount + reconciliationItem.BonusBMAmount + reconciliationItem.BonusBMLAmount
		case enum.FeeType.ACCUMULATE_POINTS_BONUS:
			value.BonusAmount += reconciliationItem.BonusAmount + reconciliationItem.BonusBMAmount + reconciliationItem.BonusBMLAmount
		case enum.FeeType.VOUCHER_REFUND:
			value.BonusAmount += reconciliationItem.BonusAmount + reconciliationItem.BonusBMAmount + reconciliationItem.BonusBMLAmount

		case enum.FeeType.PENALTY:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.OTHER_FEE:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.ORDER_RETURN:
			value.PenaltyFee += reconciliationItem.PenaltyFee - reconciliationItem.PenaltyBMFee - reconciliationItem.PenaltyBMLFee
		case enum.FeeType.FULFILLMENT_PENALTY:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.INBOUND_OVERDUE_PENALTY:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.INVOICE_OVERDUE_FEE:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.ACCUMULATE_POINTS_FEE:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.INCREASE_PRICE_FEE:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.POOR_QUALITY_PRODUCT_FEE:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.VOUCHER_DEDUCTION:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee

		case enum.FeeType.BIZ_HOUSEHOLD_TAX:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.REFUND_BIZ_HOUSEHOLD_TAX:
			value.BonusAmount += reconciliationItem.BonusAmount + reconciliationItem.BonusBMAmount + reconciliationItem.BonusBMLAmount

		case enum.FeeType.NO_INVOICE_FEE:
			value.PenaltyFee += reconciliationItem.PenaltyFee + reconciliationItem.PenaltyBMFee + reconciliationItem.PenaltyBMLFee
		case enum.FeeType.NO_INVOICE_BONUS:
			value.BonusAmount += reconciliationItem.BonusAmount + reconciliationItem.BonusBMAmount + reconciliationItem.BonusBMLAmount
		}
	}
	value.TotalPayment += value.BonusAmount
	value.TotalPayment -= value.PenaltyFee
	return value
}

func HasToChargeBuyerFee(reconciliationItem *model.ReconciliationItem) bool {
	if reconciliationItem.TotalBuyerFee != nil {
		if reconciliationItem.BookingDate == nil || reconciliationItem.BookingDate.Unix() < model.ApplyChargeBuyerFee.Unix() {
			return false
		} else if reconciliationItem.BookingDate.Unix() >= model.ApplyPricingNow.Unix() {
			if reconciliationItem.SkuPriceType == "FIXED_PRICE" {
				return true
			}
		} else {
			if reconciliationItem.SkuPriceType == "FIXED_PRICE" {
				return true
			}
			if reconciliationItem.DealChargeFee == "SELLER" && reconciliationItem.SkuPriceType == enum.PriceType.FIXED_REVENUE && reconciliationItem.DealPricingType == "ABSOLUTE" {
				return true
			}
		}
	}
	return false
}
