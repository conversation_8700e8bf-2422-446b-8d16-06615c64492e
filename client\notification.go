package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

const (
	pathCreateNotification = "/integration/notification/v1/notification"
)

type notificationClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewNotificationServiceClient ...
func NewNotificationServiceClient(apiHost, apiKey, logName string) *notificationClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	return &notificationClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
}

// CreateNotification is func create new Notification
func (cli *notificationClient) CreateNotification(in *Notification) *common.APIResponse {
	params := map[string]string{}
	in.Username = fmt.Sprintf("CUSTOMER_%d", in.UserID)
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathCreateNotification, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_NOTIFICATION",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_NOTIFICATION",
		}
	}

	return result
}

func (cli *notificationClient) CreateNotificationToSeller(in *Notification) *common.APIResponse {
	in.Username = fmt.Sprintf("SELLER_%d", in.UserID)
	in.ReceiverType = utils.ParseStringToPointer("SELLER")

	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathCreateNotification, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_NOTIFICATION",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_NOTIFICATION",
		}
	}

	return result
}
