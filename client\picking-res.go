package client

import (
	"time"
)

type PickTicketItemRes struct {
	Status  string           `json:"status"`
	Data    []PickTicketItem `json:"data,omitempty"`
	Message string           `json:"message"`
	Total   int64            `json:"total,omitempty"`
}

type PickTicketItem struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       int64      `json:"createdBy,omitempty" bson:"created_by,omitempty" `
	UpdatedBy       int64      `json:"updatedBy,omitempty" bson:"updated_by,omitempty" `

	VersionNo string `json:"versionNo,omitempty" bson:"version_no,omitempty"`

	// info of parent ticket
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	TicketID      int64  `json:"ticketId,omitempty" bson:"ticket_id,omitempty" `
	SO            string `json:"so,omitempty" bson:"so,omitempty" `

	// product info
	LineId           int64      `json:"lineId,omitempty" bson:"line_id,omitempty"`
	SaleLineId       int64      `json:"saleLineId,omitempty" bson:"sale_line_id,omitempty"`
	ERPProductId     int        `json:"erpProductId,omitempty" bson:"erp_product_id,omitempty"`
	AdminProductId   int        `json:"adminProductId,omitempty" bson:"admin_product_id,omitempty"`
	SKU              string     `json:"sku,omitempty" bson:"sku,omitempty"`
	ProductType      string     `json:"productType,omitempty" bson:"product_type,omitempty"`
	Name             string     `json:"name,omitempty" bson:"name,omitempty"`
	SellerCode       string     `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	IsExternalSeller bool       `json:"isExternalSeller" bson:"is_external_seller"`
	ImageURL         string     `json:"imageUrl,omitempty" bson:"image_url,omitempty"`
	Weight           float64    `json:"weight,omitempty" bson:"weight,omitempty"`
	Packaging        string     `json:"packaging,omitempty" bson:"packaging,omitempty"`
	Quantity         int64      `json:"quantity" bson:"quantity"`
	OrderQuantity    int64      `json:"initQty" bson:"order_quantity"`
	Location         string     `json:"location,omitempty" bson:"location,omitempty"`
	IsRedTag         bool       `json:"isRedTag" bson:"is_red_tag"`
	IsImportant      bool       `json:"isImportant" bson:"is_important,omitempty"`
	IsCombo          bool       `json:"IsCombo" bson:"is_combo"`
	ComboInfo        *ComboInfo `json:"comboInfo,omitempty" bson:"combo_info,omitempty"`
	Zone             string     `json:"zone,omitempty" bson:"zone,omitempty"`

	Point     float64 `json:"point" bson:"point,omitempty"`
	IsFragile bool    `json:"isFragile" bson:"is_fragile,omitempty"`
	IsFrozen  bool    `json:"isFrozen" bson:"is_frozen,omitempty"`
	IsNearExp bool    `json:"isNearExp" bson:"is_near_exp,omitempty"`

	//result
	FulfilledQuantity        int64 `json:"fulfilledQuantity" bson:"fulfilled_quantity"`
	ScannedQuantity          int64 `json:"scannedQuantity" bson:"scanned_quantity"`
	ExpiredQuantity          int64 `json:"expiredQuantity" bson:"expired_quantity"`
	RedundantFulfillQuantity int64 `json:"redundantFulfillQuantity" bson:"redundant_fulfill_quantity"`

	ScheduledDate int64 `json:"scheduledDate,omitempty" bson:"-"`

	LocationDetails []*LocationDetail `json:"locationDetails,omitempty" bson:"location_details"`
}

type ComboInfo struct {
	ComboName     string `json:"comboName,omitempty" bson:"combo_name,omitempty"`
	OrderQuantity int64  `json:"orderQuantity,omitempty" bson:"order_quantity,omitempty"`
}

type LocationDetail struct {
	Zone           string `json:"zone" bson:"zone,omitempty"`
	LocationCode   string `json:"locationCode" bson:"location_code,omitempty"`
	OnHoldQuantity int    `json:"onHoldQuantity" bson:"on_hold_quantity"`
	DoneQuantity   int    `json:"doneQuantity" bson:"done_quantity"`
}
