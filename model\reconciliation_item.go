package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var ReconciliationItemDB = &db.Instance{
	ColName:        "reconciliation_item",
	TemplateObject: &ReconciliationItem{},
}

type ReconciliationItem struct {
	ID              *primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SellerID                             int64             `json:"sellerId" bson:"seller_id,omitempty"`
	SellerCode                           string            `json:"sellerCode" bson:"seller_code,omitempty"`
	ReconcileScheduleTimeIndex           string            `json:"reconcileScheduleTimeIndex" bson:"reconcile_schedule_time_index,omitempty"`
	FeeType                              enum.FeeTypeValue `json:"feeType" bson:"fee_type,omitempty"`
	Description                          string            `json:"description,omitempty" bson:"description,omitempty"`
	PreviousReconcileScheduleTimeIndexes []string          `json:"previousReconcileScheduleTimeIndexes,omitempty" bson:"previous_reconcile_schedule_time_indexes,omitempty"`

	BookingDate          *time.Time          `json:"bookingDate,omitempty" bson:"booking_date,omitempty"`
	OrderID              int64               `json:"orderID,omitempty" bson:"order_id,omitempty"`
	ProductId            int64               `json:"productId" bson:"product_id,omitempty"`
	OrderCode            string              `json:"orderCode,omitempty" bson:"order_code,omitempty"`
	SaleOrderCode        string              `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	CustomerProvinceCode string              `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`
	Type                 string              `json:"type,omitempty" bson:"type,omitempty"`
	Level                *string             `json:"level,omitempty" bson:"level,omitempty"`
	Sku                  string              `json:"sku,omitempty" bson:"sku,omitempty"`
	SkuPriceType         enum.PriceTypeValue `json:"skuPriceType,omitempty" bson:"sku_price_type,omitempty"`

	CompletedTime     *time.Time `json:"completedTime,omitempty" bson:"completed_time,omitempty"`
	CompletedDebtTime *time.Time `json:"completedDebtTime,omitempty" bson:"completed_debt_time,omitempty"`

	Deal *DealInfo `json:"deal,omitempty" bson:"deal,omitempty"`
	// TODO: move to deal{}
	DealCode           *string `json:"dealCode,omitempty" bson:"deal_code,omitempty"`
	DealPricingType    string  `json:"dealPricingType,omitempty" bson:"deal_pricing_type,omitempty"`
	DealChargeFee      string  `json:"dealChargeFee,omitempty" bson:"deal_charge_fee,omitempty"`
	DealChargeFeeValue int     `json:"dealChargeFeeValue,omitempty" bson:"deal_charge_fee_value,omitempty"`

	// TODO: move to campaign{}
	Campaign *CampaignInfo `json:"campaign,omitempty" bson:"campaign,omitempty"`

	Fees               []*FeeValue `json:"fees,omitempty" bson:"fees,omitempty"`
	HasChargedBuyerFee *bool       `json:"hasChargedBuyerFee" bson:"has_charged_buyer_fee,omitempty"`
	HasSentInvoice     *bool       `json:"hasSentInvoice" bson:"has_sent_invoice,omitempty"`
	SellerPrice        int         `json:"sellerPrice,omitempty" bson:"seller_price,omitempty"`
	Quantity           int         `json:"quantity,omitempty" bson:"quantity,omitempty"`
	PriceInOrder       *int        `json:"priceInOrder,omitempty" bson:"price_in_order,omitempty"`
	OrderedPrice       *int        `json:"orderedPrice,omitempty" bson:"ordered_price,omitempty"`
	Price              *int        `json:"price,omitempty" bson:"price,omitempty"`
	DeliveryQuantity   *int        `json:"deliveryQuantity,omitempty" bson:"delivery_quantity,omitempty"`
	OutboundQuantity   *int        `json:"outboundQuantity,omitempty" bson:"outbound_quantity,omitempty"`
	CompletedQuantity  *int        `json:"completedQuantity,omitempty" bson:"completed_quantity,omitempty"`
	ReturnQuantity     *int        `json:"returnQuantity,omitempty" bson:"return_quantity,omitempty"`
	MainQuantity       *int        `json:"mainQuantity,omitempty" bson:"main_quantity,omitempty"`
	DamageQuantity     *int        `json:"damageQuantity,omitempty" bson:"damage_quantity,omitempty"`
	MissingQuantity    *int        `json:"missingQuantity,omitempty" bson:"missing_quantity,omitempty"`
	ListingFee         *int        `json:"listingFee,omitempty" bson:"listing_fee,omitempty"`
	FulfillmentFee     *int        `json:"fulfillmentFee,omitempty" bson:"fulfillment_fee,omitempty"`
	TotalRevenue       *int        `json:"totalRevenue,omitempty" bson:"total_revenue,omitempty"`
	TotalPayment       *int        `json:"totalPayment,omitempty" bson:"total_payment,omitempty"`
	TotalBuyerFee      *float64    `json:"totalBuyerFee,omitempty" bson:"total_buyer_fee,omitempty"`
	ListingRate        float64     `json:"listingRate,omitempty" bson:"listing_rate,omitempty"`
	FulfillmentRate    float64     `json:"fulfillmentRate,omitempty" bson:"fulfillment_rate,omitempty"`

	InvoiceID   int64  `json:"invoiceID,omitempty" bson:"invoice_id,omitempty"`
	InvoiceCode string `json:"invoiceCode,omitempty" bson:"invoice_code,omitempty"`

	PenaltyDescription string `json:"penaltyDescription,omitempty" bson:"penalty_description,omitempty"`
	PenaltyFee         int    `json:"penaltyFee,omitempty" bson:"penalty_fee,omitempty"`        // Số tiền COD
	PenaltyBMFee       int    `json:"penaltyBMFee,omitempty" bson:"penalty_bm_fee,omitempty"`   // Phí Buymed
	PenaltyBMLFee      int    `json:"penaltyBMLFee,omitempty" bson:"penalty_bml_fee,omitempty"` // Phí BuymedLogistics

	AutoPenaltyFee    bool       `json:"autoPenaltyFee,omitempty" bson:"auto_penalty_fee,omitempty"` // Auto charge fee
	TicketID          int64      `json:"ticketID,omitempty" bson:"ticket_id,omitempty"`              // track auto penalty fee
	TicketCode        string     `json:"ticketCode,omitempty" bson:"ticket_code,omitempty"`
	WarehouseCode     string     `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	RetailPrice       int64      `json:"retailPrice,omitempty" bson:"retail_price,omitempty"`
	PenaltyTimeline   int        `json:"penaltyTimeline,omitempty" bson:"penalty_timeline,omitempty"`
	PenaltyRate       float64    `json:"penaltyRate,omitempty" bson:"penalty_rate,omitempty"`
	InboundCode       string     `json:"inboundCode,omitempty" bson:"inbound_code,omitempty"`
	InboundTime       *time.Time `json:"inboundTime,omitempty" bson:"inbound_time,omitempty"`
	DaysInbound       int        `json:"daysInbound,omitempty" bson:"days_inbound,omitempty"`             // thời gian lưu kho
	AvailableQuantity int64      `json:"availableQuantity,omitempty" bson:"available_quantity,omitempty"` // số lượng lưu kho

	BonusDescription string `json:"bonusDescription,omitempty" bson:"bonus_description,omitempty"`
	BonusAmount      int    `json:"bonusAmount,omitempty" bson:"bonus_amount,omitempty"`        // Số tiền COD
	BonusBMAmount    int    `json:"bonusBMAmount,omitempty" bson:"bonus_bm_amount,omitempty"`   // Phí Buymed
	BonusBMLAmount   int    `json:"bonusBMLAmount,omitempty" bson:"bonus_bml_amount,omitempty"` // Phí BuymedLogistics

	// biz household tax report
	TotalTaxRevenue   int64              `json:"totalTaxRevenue,omitempty" bson:"total_tax_revenue,omitempty"`
	VATRate           float64            `json:"vatRate,omitempty" bson:"vat_rate,omitempty"`
	PITRate           float64            `json:"pitRate,omitempty" bson:"pit_rate,omitempty"`
	CreditNoteCode    string             `json:"creditNoteCode,omitempty" bson:"credit_note_code,omitempty"`
	DeliveryOrderCode []string           `json:"deliveryOrderCode,omitempty" bson:"delivery_order_code,omitempty"`
	TaxType           enum.TaxReportType `json:"taxType,omitempty" bson:"tax_type,omitempty"`

	// Voucher
	VoucherCodes []string `json:"voucherCodes,omitempty" bson:"voucher_codes,omitempty"`

	OutboundInfos []*OutboundInfo `json:"outboundInfos,omitempty" bson:"outbound_infos,omitempty"`
	ReturnInfos   []*ReturnInfo   `json:"returnInfos,omitempty" bson:"return_infos,omitempty"`

	// for query
	FeeTypes    []string `json:"feeTypes,omitempty" bson:"-"`
	SellerCodes []string `json:"sellerCodes,omitempty" bson:"-"`

	// query at backend
	OperationAnd []bson.M `json:"-" bson:"$and,omitempty"`
	OperationOr  []bson.M `json:"-" bson:"$or,omitempty"`

	LastQueryID *primitive.ObjectID `json:"lastQueryID,omitempty" bson:"-"`
}

type DealInfo struct {
	Code           string `json:"code,omitempty" bson:"code,omitempty"`
	PricingType    string `json:"pricingType,omitempty" bson:"pricing_type,omitempty"`
	ChargeFee      string `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"`
	ChargeFeeValue int    `json:"chargeFeeValue,omitempty" bson:"charge_fee_value,omitempty"`
}

type CampaignInfo struct {
	Code           string `json:"code,omitempty" bson:"code,omitempty"`
	PricingType    string `json:"pricingType,omitempty" bson:"pricing_type,omitempty"`
	ChargeFee      string `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"`
	ChargeFeeValue int    `json:"chargeFeeValue,omitempty" bson:"charge_fee_value,omitempty"`
}

type IngredientDetail struct {
	TotalOriginalPrice int                         `json:"totalOriginalPrice,omitempty" bson:"total_original_price,omitempty"`
	PriceRatio         float64                     `json:"priceRatio,omitempty" bson:"price_ratio,omitempty"`
	IngredientData     map[string]IngredientDetail `json:"ingredientData,omitempty" bson:"ingredient_data,omitempty"`
}

type ReconciliationItemStatusValue string

type reconciliationItemStatus struct {
	Draft     ReconciliationItemStatusValue
	Waiting   ReconciliationItemStatusValue
	Completed ReconciliationItemStatusValue
}

var ReconciliationItemStatus = &reconciliationItemStatus{
	Draft:     "DRAFT",
	Waiting:   "WAITING",
	Completed: "COMPLETED",
}

// InitReconciliationItemModel is func init model reconciliation item
func InitReconciliationItemModel(s *mongo.Database) {
	ReconciliationItemDB.ApplyDatabase(s)

	t := true

	_ = ReconciliationItemDB.CreateIndex(bson.D{
		primitive.E{Key: "seller_code", Value: 1},
		primitive.E{Key: "reconcile_schedule_time_index", Value: 1},
		primitive.E{Key: "fee_type", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = ReconciliationItemDB.CreateIndex(bson.D{
		primitive.E{Key: "order_id", Value: 1},
		primitive.E{Key: "sku", Value: 1},
		primitive.E{Key: "fee_type", Value: 1},
		primitive.E{Key: "reconcile_schedule_time_index", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = ReconciliationItemDB.CreateIndex(bson.D{
		primitive.E{Key: "reconcile_schedule_time_index", Value: 1},
		primitive.E{Key: "fee_type", Value: 1},
		primitive.E{Key: "last_updated_time", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
