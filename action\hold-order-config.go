package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GetOrderHoldConfiguration ...
func GetOrderHoldConfiguration(acc *model.Account) *common.APIResponse {
	return model.HoldOrderConfigDB.QueryAll()
}

// UpdateOrderHoldConfiguration ...
func UpdateOrderHoldConfiguration(acc *model.Account, input *model.HoldOrderConfigRequest) *common.APIResponse {
	orderConfig := &model.HoldOrderConfig{
		HoldOrderCode: input.Type,
	}

	updateDate := &model.HoldOrderConfig{
		HoldOrderCode: input.Type,
		UpdatedBy:     acc.AccountID,
		IsActive:      input.IsActive,
		DisplayTime:   input.DisplayTime,
	}

	if input.Type == enum.HoldOrder.Hold {
		var ratio int64 = 100
		updateDate.ProcessingTime = input.ProcessingTime
		updateDate.OpenAgainTime = input.OpenAgainTime
		updateDate.CreatedTicketTime = input.CreatedTicketTime
		updateDate.DisplayRatio = &ratio
	} else if input.Type == enum.HoldOrder.Active {
		updateDate.Total = 1000000
	} else if input.Type == enum.HoldOrder.RequestCancel {
		updateDate.DisplayRatio = input.DisplayRatio
	} else if input.Type == enum.HoldOrder.AutoCancelBankOrder {
		if input.BankTransferWaitingTime != nil && *input.BankTransferWaitingTime <= 0 {
			*input.BankTransferWaitingTime = *utils.ParseInt64ToPointer(1)
		}
		updateDate.BankTransferWaitingTime = input.BankTransferWaitingTime
	} else if input.Type == enum.HoldOrder.AutoSendPaymentRemind {
		updateDate.DisplayTime = input.DisplayTime
		updateDate.NotificationConfig = input.NotificationConfig
	}

	returnOpt := options.After
	res := model.HoldOrderConfigDB.UpdateOne(orderConfig, updateDate, &options.FindOneAndUpdateOptions{
		Upsert:         utils.ParseBoolToPointer(true),
		ReturnDocument: &returnOpt,
	})

	return res
}
