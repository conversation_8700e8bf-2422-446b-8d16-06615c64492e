package tool

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func ToolOrderItemsSellerPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	type Input struct {
		OrderId int64  `json:"orderId"`
		SkuCode string `json:"skuCode"`
	}

	var input Input
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Invalid payload",
		})
	}

	if input.OrderId == 0 ||
		input.SkuCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "missing orderId or skuCode",
		})
	}

	oiDb := model.GetOrderItemPartitionDB(&model.Order{
		OrderID: input.OrderId,
	}, "ToolOrderItemsSellerPrice")
	if oiDb == nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Nil order item partition db",
		})
	}

	orderItemRes := oiDb.QueryOne(model.OrderItem{
		OrderID: input.OrderId,
		Sku:     input.SkuCode,
	})
	if orderItemRes.Status != common.APIStatus.Ok {
		return resp.Respond(orderItemRes)
	}

	orderItem := orderItemRes.Data.([]*model.OrderItem)[0]
	if orderItem.SubItems == nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Nil order item sub items",
		})
	}

	noNeedUpdate := true
	for _, subItem := range *orderItem.SubItems {
		if subItem.SellerPrice != 0 {
			continue
		}

		sku := client.Services.Product.GetSkuInfo(subItem.Sku)
		if sku == nil {
			continue
		}

		subItem.SellerPrice = int(sku.RetailPriceValue)
		noNeedUpdate = false
	}

	if noNeedUpdate {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "No need update",
		})
	}

	updateRes := oiDb.UpdateOne(model.OrderItem{
		ID: orderItem.ID,
	}, orderItem)
	return resp.Respond(updateRes)
}
