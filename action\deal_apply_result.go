package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetDealApplyResultList(account *model.Account, query *model.DealApplyResult, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.DealApplyResultDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "NOT_FOUND"
		return result
	}

	if getTotal {
		result.Total = model.DealApplyResultDB.Count(query).Total
	}
	return result
}

func CreateDealApplyResult(input *model.DealApplyResult) *common.APIResponse {
	if input.SKU == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy mã sku",
			ErrorCode: "SKU_NOT_FOUND",
		}
	}
	return model.DealApplyResultDB.Create(input)
}

func UpdateDealApplyResult(sku string, updateData *model.DealApplyResult) *common.APIResponse {
	qResult := model.DealApplyResultDB.QueryOne(model.DealApplyResult{DealCode: updateData.DealCode, SKU: sku})
	if qResult.Status != common.APIStatus.Ok {
		return model.DealApplyResultDB.Create(updateData)
	}
	updateData.ID = primitive.NilObjectID
	updateData.Type = ""
	if updateData.Quantity == nil {
		updateData.Quantity = utils.ParseIntToPointer(0)
	}
	//result := qResult.Data.([]*model.DealApplyResult)[0]
	//isDataUpdate := false
	//if updateData.Quantity != nil {
	//	if result.Quantity == nil || *result.Quantity != *updateData.Quantity {
	//		isDataUpdate = true
	//	}
	//}
	//if updateData.MaxQuantity != nil {
	//	if result.MaxQuantity == nil || *result.MaxQuantity != *updateData.MaxQuantity {
	//		isDataUpdate = true
	//	}
	//}
	//if isDataUpdate {
	return model.DealApplyResultDB.UpdateOne(model.DealApplyResult{DealCode: updateData.DealCode, SKU: sku}, updateData)
	//}
	//return &common.APIResponse{
	//	Status:    common.APIStatus.Invalid,
	//	Message:   "No value change",
	//	ErrorCode: "NO_VALUE_CHANGE",
	//}
}

func DeleteDealApplyResult(sku, dealCode string) *common.APIResponse {
	query := model.DealApplyResult{SKU: sku, DealCode: &dealCode}
	res := model.DealApplyResultDB.QueryOne(query)
	if res.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Deal apply result not found",
			ErrorCode: "NOT_FOUND",
		}
	}

	return model.DealApplyResultDB.Delete(query)
}
