package action

import (
	"fmt"
	"math"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetOrderList is func get list order have pagination
func GetOrderList(account *model.Account, query *model.Order, skuCode string, skuCodes []string, offset, limit int64, getTotal, hasVoucher, getTotalDelivered bool, sort string) *common.APIResponse {

	if offset >= 10000 && offset%10000 == 0 {
		fmt.Println("Offset > 10000: ", account.Type, account.AccountID)
	}

	switch account.Type {
	case enum.AccountType.CUSTOMER:
		{
			customer, errCustomer := getCustomerProfile(account)
			if errCustomer != nil {
				return errCustomer
			}
			// fill to query
			query.CustomerID = customer.CustomerID

			if query.DateFrom != nil {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"created_time": bson.M{
						"$gte": query.DateFrom,
					},
				})
			}

			if query.DateTo != nil {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"created_time": bson.M{
						"$lte": query.DateTo,
					},
				})
			}
			// fill to query
			if len(skuCodes) > 0 {
				qResult := model.OrderDetailDB.Query(&bson.M{
					"customer_code": customer.CustomerCode,
					"skus":          bson.M{"$in": skuCodes},
				}, 0, 0, nil)
				if qResult.Status == common.APIStatus.Ok {
					orderDetails := qResult.Data.([]*model.OrderDetail)
					orderIds := make([]int64, 0, len(orderDetails))
					for _, od := range orderDetails {
						orderIds = append(orderIds, od.OrderID)
					}

					query.ComplexQuery = append(query.ComplexQuery, &bson.M{
						"order_id": bson.M{"$in": orderIds},
					})
				} else if qResult.Status == common.APIStatus.NotFound {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						ErrorCode: "NOT_FOUND",
						Message:   "Not found suitable order",
					}
				}
			}

			result := model.OrderDB.Query(query, offset, limit, &primitive.M{"order_id": -1})

			if result.Status != common.APIStatus.Ok {
				result.ErrorCode = "ORDER_NOT_FOUND"
				return result
			}
			if getTotalDelivered {
				orders := result.Data.([]*model.Order)
				for _, order := range orders {
					var totalDelivered = 0

					orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "GetOrderList")
					if orderItemPartitionDB == nil {
						return model.PARTITION_NOT_FOUND_RESPONSE
					}
					itemsResp := orderItemPartitionDB.Query(model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
					if itemsResp.Status == common.APIStatus.Ok {
						items := itemsResp.Data.([]*model.OrderItem)
						for _, item := range items {
							if order.Status == enum.OrderState.Completed {
								if item.CompletedQuantity != nil {
									totalDelivered += *item.CompletedQuantity
								}
							} else {
								if item.ReservedQuantity != nil {
									totalDelivered += *item.ReservedQuantity
								}
							}
						}
					}
					order.TotalDelivered = &totalDelivered
				}
				result.Data = orders
			}
			if getTotal {
				result.Total = model.OrderDB.Count(query).Total
			}
			return result
		}
	case enum.AccountType.SELLER:
		{
			result := model.OrderDB.Query(query, offset, limit, &primitive.M{"order_id": -1})
			if result.Status != common.APIStatus.Ok {
				result.ErrorCode = "ORDER_NOT_FOUND"
				return result
			}

			if getTotal {
				result.Total = model.OrderDB.Count(query).Total
			}
			return result
		}
	case enum.AccountType.EMPLOYEE:
		{
			var complexQuery = []*bson.M{}
			if query.DateFrom != nil {
				complexQuery = append(complexQuery, &bson.M{
					"created_time": bson.M{
						"$gte": query.DateFrom,
					},
				})
			}

			if query.DateTo != nil {
				complexQuery = append(complexQuery, &bson.M{
					"created_time": bson.M{
						"$lte": query.DateTo,
					},
				})
			}

			if query.PriceFrom != nil || query.PriceTo != nil {
				if query.PriceFrom == nil {
					t := 0
					query.PriceFrom = &t
				} else if query.PriceTo == nil {
					t := *********** // 10 billions is too much
					query.PriceTo = &t
				}
				complexQuery = append(complexQuery, &bson.M{
					"total_price": bson.M{
						"$gte": query.PriceFrom,
						"$lte": query.PriceTo,
					},
				})
			}

			if len(query.StatusIn) > 0 {
				complexQuery = append(
					complexQuery,
					&bson.M{
						"status": bson.M{
							"$in": query.StatusIn,
						},
					},
				)
			}
			if len(query.StatusNotIn) > 0 {
				if len(query.StatusIn) == 1 {
					complexQuery = append(
						complexQuery,
						&bson.M{
							"status": bson.M{
								"$ne": query.StatusIn[0],
							},
						},
					)
				} else {
					complexQuery = append(
						complexQuery,
						&bson.M{
							"status": bson.M{
								"$nin": query.StatusNotIn,
							},
						},
					)
				}
			}
			if len(query.SaleOrderCodeIn) > 0 {
				complexQuery = append(
					complexQuery,
					&bson.M{
						"sale_order_code": bson.M{
							"$in": query.SaleOrderCodeIn,
						},
					},
				)
			}

			if len(skuCode) > 0 {
				qResult := model.OrderDetailDB.Query(&bson.M{
					"skus": bson.M{"$in": []string{skuCode}},
				}, 0, 0, nil)
				if qResult.Status == common.APIStatus.Ok {
					orderDetails := qResult.Data.([]*model.OrderDetail)
					orderIds := make([]int64, 0, len(orderDetails))
					for _, od := range orderDetails {
						orderIds = append(orderIds, od.OrderID)
					}

					complexQuery = append(complexQuery, &bson.M{
						"order_id": bson.M{"$in": orderIds},
					})
				} else if qResult.Status == common.APIStatus.NotFound {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						ErrorCode: "NOT_FOUND",
						Message:   "Not found suitable order",
					}
				}
			}

			if query.RedeemCode != nil && len(*query.RedeemCode) > 0 {
				complexQuery = append(
					complexQuery,
					&bson.M{
						"redeem_code": bson.M{
							"$all": *query.RedeemCode,
						},
					})
				query.RedeemCode = nil
			}

			if hasVoucher {
				complexQuery = append(complexQuery, &bson.M{
					"redeem_code.0": bson.M{
						"$exists": true,
					},
				})
			}

			if len(query.Tags) > 0 {
				complexQuery = append(complexQuery, &bson.M{
					"tags": bson.M{"$in": query.Tags},
				})
				query.Tags = nil
			}

			if len(complexQuery) > 0 {
				query.ComplexQuery = complexQuery
			}

			mSort := primitive.M{"created_time": -1} // uu tien
			if sort == "-created_time" {
				mSort = primitive.M{"created_time": -1}
			} else if sort == "created_time" {
				mSort = primitive.M{"created_time": 1}
			}
			if query.SourceDetail != nil {
				// create query from source detail, then append to complex query
				query.ComplexQuery = append(query.ComplexQuery, createQueryFromSourceDetail(query.SourceDetail))

				// remove source detail to avoid error
				query.SourceDetail = nil
			}
			result := model.OrderDB.Query(query, offset, limit, &mSort)
			if result.Status != common.APIStatus.Ok {
				result.ErrorCode = "ORDER_NOT_FOUND"
				return result
			}

			if getTotal {
				result.Total = model.OrderDB.Count(query).Total
			}
			return result
		}
	case enum.AccountType.PARTNER:
		{
			if len(query.StatusIn) > 0 {
				query.ComplexQuery = append(
					query.ComplexQuery,
					&bson.M{
						"status": bson.M{
							"$in": query.StatusIn,
						},
					},
				)
			}
			if len(query.StatusNotIn) > 0 {
				if len(query.StatusIn) == 1 {
					query.ComplexQuery = append(
						query.ComplexQuery,
						&bson.M{
							"status": bson.M{
								"$ne": query.StatusIn[0],
							},
						},
					)
				} else {
					query.ComplexQuery = append(
						query.ComplexQuery,
						&bson.M{
							"status": bson.M{
								"$nin": query.StatusNotIn,
							},
						},
					)
				}
			}
			if len(query.SaleOrderCodeIn) > 0 {
				query.ComplexQuery = append(
					query.ComplexQuery,
					&bson.M{
						"sale_order_code": bson.M{
							"$in": query.SaleOrderCodeIn,
						},
					},
				)
			}
			if query.DateFrom != nil && query.DateTo != nil {
				query.ComplexQuery = append(
					query.ComplexQuery,
					&bson.M{
						"created_time": bson.M{
							"$gte": query.DateFrom,
							"$lte": query.DateTo,
						},
					})
			}

			sortedField := &primitive.M{"_id": -1}
			switch sort {
			case "-created_time":
				sortedField = &primitive.M{"created_time": -1}
			case "created_time":
				sortedField = &primitive.M{"created_time": 1}
			}

			db := model.OrderDB
			if query.IsQueryReplica {
				db = model.OrderDBReplica
			}
			result := db.Query(query, offset, limit, sortedField)

			if result.Status != common.APIStatus.Ok {
				result.ErrorCode = "ORDER_NOT_FOUND"
				return result
			}

			if result.Status == common.APIStatus.Ok && getTotal {
				result.Total = db.Count(query).Total
			}
			return result
		}

	case enum.AccountType.BRAND_SALES:
		{
			// query by sales brand account id
			query.CreatedByAccountID = account.AccountID
			if query.DateFrom != nil {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"created_time": bson.M{
						"$gte": query.DateFrom,
					},
				})
			}

			if query.DateTo != nil {
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"created_time": bson.M{
						"$lte": query.DateTo,
					},
				})
			}

			result := model.OrderDB.Query(query, offset, limit, &primitive.M{"order_id": -1})
			if result.Status != common.APIStatus.Ok {
				result.ErrorCode = "ORDER_NOT_FOUND"
				return result
			}

			if getTotalDelivered {
				orders := result.Data.([]*model.Order)
				for i, order := range orders {
					var totalDelivered = 0
					orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "GetOrderList")
					if orderItemPartitionDB == nil {
						return model.PARTITION_NOT_FOUND_RESPONSE
					}
					itemsResp := orderItemPartitionDB.Query(model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
					// itemsResp := model.OrderItemDB.QueryOne(model.OrderItem{OrderID: order.OrderID})
					if itemsResp.Status == common.APIStatus.Ok {
						items := itemsResp.Data.([]*model.OrderItem)
						for _, item := range items {

							if order.Status == enum.OrderState.Completed {
								if item.CompletedQuantity != nil {
									totalDelivered = totalDelivered + *item.CompletedQuantity
								}
							} else {
								if item.ReservedQuantity != nil {
									totalDelivered = totalDelivered + *item.ReservedQuantity
								}
							}
						}
					}
					orders[i].TotalDelivered = &totalDelivered
				}
				result.Data = orders
			}

			if getTotal {
				result.Total = model.OrderDB.Count(query).Total
			}
			return result
		}

	default:
		{
			return &common.APIResponse{
				Status:    common.APIStatus.Forbidden,
				Message:   "Thao tác không xác định, vui lòng kiểm tra lại",
				ErrorCode: "NOT_FOUND",
			}
		}
	}
}

func createQueryFromSourceDetail(sourceDetail *model.OrderSourceDetail) *bson.M {
	query := bson.M{}
	if sourceDetail.Os != "" {
		query["source_detail.os"] = sourceDetail.Os
	}
	if sourceDetail.Platform != "" {
		query["source_detail.platform"] = sourceDetail.Platform
	}
	return &query
}

// GetOrderItemList is func get list order item have pagination
func GetOrderItemList(account *model.Account, query *model.OrderItem, offset, limit int64, getTotal bool) *common.APIResponse {

	// query by partition
	var createdTime *time.Time
	if query.CreatedTime != nil {
		createdTime = query.CreatedTime
	} else if query.DateFrom != nil {
		createdTime = query.DateFrom
		// } else if query.DateFromAny != nil {
		// 	createdTime = query.DateFromAny
	}
	if query.OrderID != 0 || query.OrderCode != "" || createdTime != nil {
		orderItemPartitionDB := (*db.Instance)(nil)
		if query.IsQueryReplica {
			orderItemPartitionDB = model.GetOrderItemPartitionDBReplica(&model.Order{OrderID: query.OrderID, OrderCode: query.OrderCode, CreatedTime: createdTime}, "GetOrderItemList")
		} else {
			orderItemPartitionDB = model.GetOrderItemPartitionDB(&model.Order{OrderID: query.OrderID, OrderCode: query.OrderCode, CreatedTime: createdTime}, "GetOrderItemList")
		}

		if orderItemPartitionDB == nil {
			return model.PARTITION_NOT_FOUND_RESPONSE
		}

		result := orderItemPartitionDB.Query(query, offset, limit, &primitive.M{"_id": -1})
		if result.Status != common.APIStatus.Ok {
			result.ErrorCode = "ORDER_ITEM_NOT_FOUND"
			return result
		}

		if getTotal {
			result.Total = orderItemPartitionDB.Count(query).Total
		}
		return result
	} else {
		//
	}

	fmt.Println("================== GetOrderItemList: GetLatestOrderItemPartitionDB ==================")

	var accountID string
	var username string
	if account != nil {
		accountID = fmt.Sprint(account.AccountID)
		username = account.Username
	}

	model.PartitionErrorDB.Create(bson.M{
		"keys": []string{"GetOrderItemList"},
		"messages": []string{
			"GetOrderItemList",
			"GetLatestOrderItemPartitionDB",
			accountID, username,
		},
	})

	orderItemPartitionDB := (*db.Instance)(nil)

	if query.IsQueryReplica {
		orderItemPartitionDB = model.GetLatestOrderItemPartitionDBReplica("GetOrderItemList")
	} else {
		orderItemPartitionDB = model.GetLatestOrderItemPartitionDB("GetOrderItemList")
	}

	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	result := orderItemPartitionDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "ORDER_ITEM_NOT_FOUND"
		return result
	}

	if getTotal {
		result.Total = orderItemPartitionDB.Count(query).Total
	}
	return result
}

// OrderItemByOrderDetailWithPayload is func get list order item have pagination
func OrderItemByOrderDetailWithPayload(orderDetailQuery model.OrderDetailQuery, oiQuery model.OrderItem) *common.APIResponse {

	odResp := model.OrderDetailDB.Query(orderDetailQuery, orderDetailQuery.Offset, orderDetailQuery.Limit, &primitive.M{"_id": -1})
	if orderDetailQuery.GetTotal {
		odResp.Total = model.OrderDetailDB.Count(orderDetailQuery).Total
	}
	if odResp.Status != common.APIStatus.Ok {
		return odResp
	}
	odList := odResp.Data.([]*model.OrderDetail)

	months, orderCodes := ListMonthByListOrderDetail(odList)

	oiQuery.ComplexQuery = append(oiQuery.ComplexQuery, &bson.M{
		"order_code": bson.M{"$in": orderCodes},
	})

	orderItemList := []*model.OrderItem{}
	for _, createdTime := range months {
		orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{CreatedTime: &createdTime}, "OrderItemByOrderDetailWithPayload")
		if orderItemPartitionDB == nil {
			continue
		}

		orderItemResp := orderItemPartitionDB.Query(oiQuery, 0, orderDetailQuery.Limit, &primitive.M{"_id": -1})
		if orderItemResp.Status != common.APIStatus.Ok {
			continue
		}

		orderItems := orderItemResp.Data.([]*model.OrderItem)
		for i := range orderItems {
			orderItemList = append(orderItemList, orderItems[i])
		}
	}

	odResp.Data = orderItemList
	return odResp
}

// GetOrderDetail is func get full order detail by order no or order id
/*
	Input: account + model query (order no, order id)
	Output: Full order detail
*/
func GetOrderDetail(account *model.Account, query *model.Order) *common.APIResponse {
	if query.OrderCode == "" && query.OrderID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order no/order id",
			ErrorCode: "MISSING_ORDER_NO",
		}
	}
	switch account.Type {
	case enum.AccountType.EMPLOYEE, enum.AccountType.PARTNER:
		{
			return model.OrderDB.QueryOne(query)
		}
	case enum.AccountType.CUSTOMER:
		{
			customer, errCustomer := getCustomerProfile(account)
			if errCustomer != nil {
				return errCustomer
			}
			query.CustomerID = customer.CustomerID

			orderRes := model.OrderDB.QueryOne(query)
			if orderRes.Status != common.APIStatus.Ok {
				return orderRes
			}
			order := orderRes.Data.([]*model.Order)[0]

			orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "GetOrderDetail")
			if orderItemPartitionDB == nil {
				return model.PARTITION_NOT_FOUND_RESPONSE
			}

			orderItemRes := orderItemPartitionDB.Query(model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
			if orderItemRes.Status == common.APIStatus.Ok {
				order.Items = orderItemRes.Data.([]*model.OrderItem)
			}
			checkHoldOrderConfig(order)
			orderRes.Data = []*model.Order{order}
			return orderRes
		}
	case enum.AccountType.BRAND_SALES:
		{
			query.CreatedByAccountID = account.AccountID
			orderRes := model.OrderDB.QueryOne(query)
			if orderRes.Status != common.APIStatus.Ok {
				return orderRes
			}
			order := orderRes.Data.([]*model.Order)[0]

			orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "GetOrderDetail")
			if orderItemPartitionDB == nil {
				return model.PARTITION_NOT_FOUND_RESPONSE
			}

			orderItemRes := orderItemPartitionDB.Query(model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
			if orderItemRes.Status == common.APIStatus.Ok {
				order.Items = orderItemRes.Data.([]*model.OrderItem)
			}
			checkHoldOrderConfig(order)
			orderRes.Data = []*model.Order{order}

			return orderRes
		}

	default:
		{
			return &common.APIResponse{
				Status:    common.APIStatus.Forbidden,
				Message:   "Thao tác không xác định, vui lòng kiểm tra lại",
				ErrorCode: "NOT_FOUND",
			}
		}
	}
}

// UpdateOrderDetail is func update only order information such as: customer, shipping, v.v
// Scope: account type EMPLOYEE
func UpdateOrderDetail(account *model.Account, updateData *model.OrderUpdateInfo) *common.APIResponse {
	if updateData.OrderCode == "" && updateData.OrderID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order no/order id",
			ErrorCode: "MISSING_ORDER_NO",
		}
	}
	query := &model.Order{
		OrderCode: updateData.OrderCode,
		OrderID:   updateData.OrderID,
	}

	result := model.OrderDB.QueryOne(query)

	if result.Status != common.APIStatus.Ok {
		return result
	}

	exist := result.Data.([]*model.Order)[0]
	updateData.OrderCode = ""
	updateData.OrderID = 0
	isNeedToUpdateOrderDetail := false
	itemGiftNeedUpdate := make(map[string]int)

	if exist.Status == enum.OrderState.Delivering ||
		exist.Status == enum.OrderState.Delivered ||
		exist.Status == enum.OrderState.Returned ||
		exist.Status == enum.OrderState.Completed {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Order can not update",
			ErrorCode: "ORDER_PROCESSING",
		}
	}

	// check if payment method is credit
	if updateData.PaymentMethod != "" && exist.PaymentMethod == string(enum.PaymentMethod.CREDIT) && updateData.PaymentMethod != string(enum.PaymentMethod.CREDIT) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Order can not update current payment method from credit",
			ErrorCode: "ORDER_UPDATE_PAYMENT_METHOD_CREDIT",
		}
	}
	if updateData.PaymentMethod != "" && exist.PaymentMethod != string(enum.PaymentMethod.CREDIT) && updateData.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Order can not update current payment method to credit",
			ErrorCode: "ORDER_UPDATE_PAYMENT_METHOD_CREDIT",
		}
	}

	if updateData.PaymentMethod != "" && updateData.PaymentMethod != exist.PaymentMethod {

		newTotalPrice := *exist.Price
		if exist.TotalDiscount != nil {
			newTotalPrice -= *exist.TotalDiscount
		}
		if exist.ExtraFee != nil {
			newTotalPrice += int(*exist.ExtraFee)
		}
		if exist.DeliveryMethodFee != nil {
			newTotalPrice += int(*exist.DeliveryMethodFee)
		}

		note := ""
		if exist.Note != nil {
			note = exist.PrivateNote
		}

		if (exist.Status == enum.OrderState.Confirmed ||
			exist.Status == enum.OrderState.Processing) &&
			IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, (exist.PaymentMethod)) &&
			updateData.PaymentMethod == string(enum.PaymentMethod.COD) {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				Message:   "Order can not change payment method from Bank to COD",
				ErrorCode: "ORDER_CHANGE_PAYMENT_METHOD",
			}
		}

		if exist.Status == enum.OrderState.Confirmed ||
			exist.Status == enum.OrderState.Processing ||
			exist.Status == enum.OrderState.WaitToDeliver ||
			exist.Status == enum.OrderState.WaitConfirm {
			if exist.PaymentMethod != "PAYMENT_METHOD_BANK" && updateData.PaymentMethod == "PAYMENT_METHOD_BANK" {
				if exist.Status == enum.OrderState.Confirmed || exist.Status == enum.OrderState.Processing || exist.Status == enum.OrderState.WaitToDeliver {
					note = fmt.Sprintf("%s\nUpdate order %s payment COD->BANK unchange fee", note, exist.Status)
				} else if exist.Source != nil && (*exist.Source == enum.Source.BRAND_PORTAL || *exist.Source == enum.Source.CLINIC_PORTAL) {
					note = fmt.Sprintf("%s\nUpdate order %s payment COD->BANK of order source %s unchange fee", note, exist.Status, *exist.Source)
				} else {
					// get feeDiscountPercentage
					var feeDiscountPercentage float64
					var defaultDiscountPercent, regionDiscountPercent, provinceDiscountPercent, districtDiscountPercent, wardDiscountPercent *float64
					feePaymentConfigs, _ := client.Services.Pricing.GetPaymentFeeConfig()
					for _, fee := range feePaymentConfigs {
						if fee.Code != updateData.PaymentMethod {
							continue
						}
						// wardFeeValue, districtFeeValue, provinceFeeValue, regionFeeValue
						for _, location := range fee.PaymentLocations {
							if location.FeeDiscountPercentage == nil {
								continue
							}
							for _, locCode := range location.LocationCodes {
								if locCode == nil {
									continue
								}
								if *locCode == "00" {
									defaultDiscountPercent = location.FeeDiscountPercentage
								}
								if exist.RegionCode == *locCode {
									regionDiscountPercent = location.FeeDiscountPercentage
								}
								if exist.ProvinceCode == *locCode {
									provinceDiscountPercent = location.FeeDiscountPercentage
								}
								if exist.DistrictCode == *locCode {
									districtDiscountPercent = location.FeeDiscountPercentage
								}
								if exist.WardCode == *locCode {
									wardDiscountPercent = location.FeeDiscountPercentage
								}
							}
						}
						if wardDiscountPercent != nil {
							feeDiscountPercentage = *wardDiscountPercent
						} else if districtDiscountPercent != nil {
							feeDiscountPercentage = *districtDiscountPercent
						} else if provinceDiscountPercent != nil {
							feeDiscountPercentage = *provinceDiscountPercent
						} else if regionDiscountPercent != nil {
							feeDiscountPercentage = *regionDiscountPercent
						} else if defaultDiscountPercent != nil {
							feeDiscountPercentage = *defaultDiscountPercent
						} else {
							// không match dc location
							errRes := PaymentMethodError
							return &common.APIResponse{
								Status:    errRes.Status,
								Message:   errRes.Message,
								ErrorCode: errRes.ErrorCode,
							}
						}
					}
					newFee := -int(feeDiscountPercentage*float64(*exist.Price)) / 100
					updateData.PaymentMethodFee = &newFee
					updateData.PaymentMethodPercentage = &feeDiscountPercentage
					note = fmt.Sprintf("%s\nUpdate order %s payment COD->BANK change fee (%f percent) 0 -> %d", note, exist.Status, feeDiscountPercentage, newFee)
				}
				{
					settingResp := model.HoldOrderConfigDB.Query(model.HoldOrderConfig{
						ComplexQuery: []*bson.M{
							{
								"hold_order_code": bson.M{"$in": []enum.HoldOrderValue{enum.HoldOrder.AutoCancelBankOrder, enum.HoldOrder.AutoSendPaymentRemind}},
							},
						},
					}, 0, 2, nil)
					if settingResp.Status == common.APIStatus.Ok {
						settings := settingResp.Data.([]*model.HoldOrderConfig)
						for _, setting := range settings {
							if setting.HoldOrderCode == enum.HoldOrder.AutoCancelBankOrder && setting.IsActive != nil && *setting.IsActive && setting.BankTransferWaitingTime != nil {
								readyTime := time.Now().Add(time.Duration(*setting.BankTransferWaitingTime) * time.Minute)
								model.AutoCancelPaymentMethodBankJob.Push(CheckPaymentMethodBankOrder{
									OrderId: exist.OrderID,
									Unix:    readyTime.Unix(),
								}, &job.JobItemMetadata{
									Topic:     "default",
									ReadyTime: &readyTime,
								})
								updateData.WaitForTransferTime = *setting.BankTransferWaitingTime
								updateData.AutoCancelTransferPaymentUnix = readyTime.Unix()
							}
							if setting.HoldOrderCode == enum.HoldOrder.AutoSendPaymentRemind && setting.IsActive != nil && *setting.IsActive && setting.DisplayTime != nil {
								readyTime := time.Now().Add(time.Duration(*setting.DisplayTime) * time.Minute)
								model.AutoSendPaymentRemindJob.Push(RemindPaymentMethodOnlineOrder{
									OrderId: exist.OrderID,
									Unix:    readyTime.Unix(),
								}, &job.JobItemMetadata{
									Topic:     "default",
									ReadyTime: &readyTime,
								})
								updateData.AutoSendPaymentNotificationUnix = readyTime.Unix()
							}
						}
					}
				}

			} else if exist.PaymentMethod == "PAYMENT_METHOD_BANK" && updateData.PaymentMethod != "PAYMENT_METHOD_BANK" {
				// Nếu đơn hàng là brand portal vả đơn hàng có CustomerTags không chứa tag khách hàng công nợ
				// thì không được phép thay đổi hình thức thanh toán từ BANK -> COD
				if exist.Source != nil && (*exist.Source == enum.Source.BRAND_PORTAL || *exist.Source == enum.Source.CLINIC_PORTAL) && utils.IsContains(exist.CustomerTags, string(enum.CustomerTag.VipDebt)) {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Order can not change payment method from BANK with debt customer",
						ErrorCode: "ORDER_CHANGE_PAYMENT_METHOD",
					}
				}
				// BANK -> COD
				zero := 0
				updateData.PaymentMethodFee = &zero
				var zeroF float64 = 0
				updateData.PaymentMethodPercentage = &zeroF
				note = fmt.Sprintf("%s\nUpdate order %s payment BANK->COD reset fee ", note, exist.Status)
			}
		}

		if updateData.PaymentMethodFee != nil {
			newTotalPrice += *updateData.PaymentMethodFee
		}

		updateData.TotalPrice = &newTotalPrice

		if len(note) > 0 {
			updateData.PrivateNote = note
		}
		if exist.RedeemApplyResult != nil && len(exist.RedeemApplyResult) > 0 {
			updateData.RedeemApplyResult = &exist.RedeemApplyResult
			updateData.RedeemCode = exist.RedeemCode
			updateData.TotalDiscount = exist.TotalDiscount
			newResult := make([]*model.PromoApplyResult, 0)
			newCodes := make([]*string, 0)
			for _, redeem := range exist.RedeemApplyResult {
				if redeem.PaymentMethod != "" && redeem.PaymentMethod != updateData.PaymentMethod {
					isNeedToUpdateOrderDetail = true
					if redeem.DiscountValue != 0 {
						updateData.TotalDiscount = utils.ParseIntToPointer(*updateData.TotalDiscount - redeem.DiscountValue)
						updateData.TotalPrice = utils.ParseIntToPointer(*updateData.TotalPrice + redeem.DiscountValue)
					}
					if redeem.Gift != nil {
						for _, gift := range redeem.Gift {
							if gift.Sku != "" {
								itemGiftNeedUpdate[gift.Sku] = gift.Quantity
							}
						}
					}
					continue
				}
				newResult = append(newResult, redeem)
				newCodes = append(newCodes, &redeem.Code)
			}
			updateData.RedeemApplyResult = &newResult
			updateData.RedeemCode = &newCodes
		}
		// job: confirmOrderJob
		if (updateData.Status == enum.OrderState.WaitConfirm || updateData.Status == "") && exist.Status == enum.OrderState.WaitConfirm {
			exist.PaymentMethod = updateData.PaymentMethod
			confirmOrderJob(exist)
		}
	}

	// update shipping info
	if exist.SaleOrderCode != "" {
		if updateData.CustomerWardCode != nil && updateData.CustomerWardName != nil &&
			updateData.CustomerDistrictCode != "" && updateData.CustomerDistrictName != "" &&
			updateData.CustomerProvinceCode != "" && updateData.CustomerProvinceName != "" &&
			updateData.CustomerShippingAddress != "" {

			if exist.Status != enum.OrderState.WaitConfirm && exist.CustomerProvinceCode != updateData.CustomerProvinceCode {
				return &common.APIResponse{
					Status:    common.APIStatus.Error,
					Message:   "Order can not change province",
					ErrorCode: "ORDER_CHANGE_PROVINCE",
				}
			}

			customerPhone := exist.CustomerPhone
			customerName := exist.CustomerName

			if updateData.CustomerPhone != "" {
				customerPhone = updateData.CustomerPhone
			}

			if updateData.CustomerName != "" {
				customerName = updateData.CustomerName
			}

			err := client.Services.Warehouse.UpdateShippingInfo(&model.UpdateShippingInfoRequest{
				SaleOrderCode: exist.SaleOrderCode,
				AdminID:       exist.OrderID,
				WardCode:      *updateData.CustomerWardCode,
				WardName:      *updateData.CustomerWardName,
				DistrictCode:  updateData.CustomerDistrictCode,
				DistrictName:  updateData.CustomerDistrictName,
				ProvinceCode:  updateData.CustomerProvinceCode,
				ProvinceName:  updateData.CustomerProvinceName,
				Address:       updateData.CustomerShippingAddress,
				Phone:         customerPhone,
				Name:          customerName,
				BusinessName:  "",
			})
			if err != nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Error,
					Message:   err.Error(),
					ErrorCode: "WAREHOUSE_UPDATE_SHIPPING",
				}
			}

		}
	}

	// Cho phép chỉnh sửa Địa chỉ /Quận huyện / Phường xã (Không cho thay đổi Tỉnh Thành) khi order status = WaitConfirm
	if exist.Status != enum.OrderState.WaitConfirm {
		updateData.CustomerProvinceCode = ""
	}

	res := model.OrderDB.UpdateOne(query, updateData)
	if res.Status == common.APIStatus.Ok {
		orderUpdate := res.Data.([]*model.Order)[0]
		if isNeedToUpdateOrderDetail {
			go updateDiscountDetail(orderUpdate, itemGiftNeedUpdate)
		}
		if orderUpdate.Status == enum.OrderState.WaitConfirm || orderUpdate.Status == enum.OrderState.Confirmed || orderUpdate.Status == enum.OrderState.Processing {
			paymentMethod := enum.PaymentMethodType.COD
			if IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, (orderUpdate.PaymentMethod)) || orderUpdate.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
				paymentMethod = enum.PaymentMethodType.BANK
			}

			customerPhone := exist.CustomerPhone
			customerName := exist.CustomerName

			if updateData.CustomerPhone != "" {
				customerPhone = updateData.CustomerPhone
			}

			if updateData.CustomerName != "" {
				customerName = updateData.CustomerName
			}

			client.Services.Warehouse.UpdateDeliveryInfo(&client.UpdateDeliveryInfoRequest{
				SaleOrderCode: orderUpdate.SaleOrderCode,
				PaymentMethod: &paymentMethod,
				DeliveryInfo: &client.DeliveryInformation{
					Name:  &customerName,
					Phone: &customerPhone,
				},
			})
		} else if orderUpdate.Status == enum.OrderState.Completed {
			completeOrderSeller(orderUpdate.OrderID)
		}

		if updateData.PaymentMethod != "" && updateData.PaymentMethod == string(enum.PaymentMethod.COD) && exist.PaymentMethod != updateData.PaymentMethod {
			if orderUpdate.Status == enum.OrderState.WaitConfirm {
				confirmOrderJob(orderUpdate)
			}
		}
	}
	return res
}

func CustomerOrderUpdateDetail(acc *model.Account, requestData *model.CustomerOrderUpdateDetailRequest) *common.APIResponse {
	if requestData.OrderCode == "" && requestData.OrderID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order no/order id",
			ErrorCode: "MISSING_ORDER_NO",
		}
	}
	orderResp := model.OrderDB.QueryOne(&model.Order{AccountID: acc.AccountID, OrderID: requestData.OrderID})
	if orderResp.Status != common.APIStatus.Ok {
		return orderResp
	}
	order := orderResp.Data.([]*model.Order)[0]

	var (
		updateData     = &model.Order{}
		resultResponse = &common.APIResponse{}
	)

	switch order.Status {
	case enum.OrderState.WaitConfirm:

		// remove tính năng update tags order
		// {
		// 	tags := order.Tags
		// 	if !requestData.IsRefuseSplitOrder {
		// 		for _, tag := range tags {
		// 			if tag != enum.Tag.REFUSE_SPLIT_ORDER {
		// 				updateData.Tags = append(updateData.Tags, tag)
		// 			}
		// 		}

		// 		if len(updateData.Tags) == 0 {
		// 			return model.OrderDB.UpdateOne(model.Order{ID: order.ID}, model.UpdateOrderOmitempty{
		// 				Tags: &updateData.Tags,
		// 			})
		// 		}

		// 	} else if !utils.IsOrderTagContains(tags, enum.Tag.REFUSE_SPLIT_ORDER) {
		// 		updateData.Tags = append(tags, enum.Tag.REFUSE_SPLIT_ORDER)
		// 	}
		// }

		isChangePaymentFee := false
		isExistVoucher, isNeedToUpdateOrderDetail, mapItemGiftNeedUpdate := len(order.RedeemApplyResult) > 0, false, make(map[string]int)
		if requestData.PaymentMethod != "" && order.PaymentMethod != requestData.PaymentMethod {

			isChangePaymentFee = true

			if requestData.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Không cho phép đổi từ phương thức thanh toán khác sang phương thức thanh toán công nợ",
					ErrorCode: "ORDER_UPDATE_PAYMENT_METHOD_CREDIT",
				}
			}

			for _, tag := range order.CustomerTags {
				if requestData.PaymentMethod == string(enum.PaymentMethod.COD) && tag == string(enum.CustomerTag.BlockCod) {
					return NotSupportPaymentMethodError
				} else if requestData.PaymentMethod == string(enum.PaymentMethod.BANK) && tag == string(enum.CustomerTag.BlockBank) {
					return NotSupportPaymentMethodError
				}
			}

			// check case for online payment
			if IsContainsT(PARTNER_PAYMENT_METHOD, (order.PaymentMethod)) &&
				IsContainsT(PARTNER_PAYMENT_METHOD, (requestData.PaymentMethod)) {

				// case change from other method allow online payment to method allow online payment need to recalculate price and set field partner payment status WAIT_TO_PAY
				updateData.PartnerPaymentStatus = enum.PartnerPaymentStatus.WAIT_TO_PAY
			} else if !IsContainsT(PARTNER_PAYMENT_METHOD, (order.PaymentMethod)) &&
				IsContainsT(PARTNER_PAYMENT_METHOD, (requestData.PaymentMethod)) {

				// case change from other method to method allow online payment need to recalculate price and set field partner payment status WAIT_TO_PAY
				updateData.PartnerPaymentStatus = enum.PartnerPaymentStatus.WAIT_TO_PAY
			} else if IsContainsT(PARTNER_PAYMENT_METHOD, (order.PaymentMethod)) &&
				!IsContainsT(PARTNER_PAYMENT_METHOD, (requestData.PaymentMethod)) {

				// case change from method allow online payment to other method need to recalculate price and unset field partner payment status
				updateData.PartnerPaymentStatus = ""
				updateData.PartnerPaymentMethod = nil
			}

			if isChangePaymentFee {
				order.PaymentMethod = requestData.PaymentMethod      // for calculate price in order
				updateData.PaymentMethod = requestData.PaymentMethod // for update data

				var errResp *common.APIResponse
				errResp, isNeedToUpdateOrderDetail, mapItemGiftNeedUpdate = handleUpdateOrderPaymentMethod(order)
				if errResp.Status != common.APIStatus.Ok {
					return errResp
				}
				updateData.PaymentMethodPercentage = order.PaymentMethodPercentage
				updateData.PaymentMethodFee = order.PaymentMethodFee

				updateData.PartnerPaymentMethod = order.PartnerPaymentMethod
				updateData.TotalPrice = order.TotalPrice
				updateData.TotalDiscount = order.TotalDiscount
				updateData.RedeemCode = order.RedeemCode
				updateData.RedeemApplyResult = order.RedeemApplyResult
			}

			if (!IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, order.PaymentMethod) &&
				IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, requestData.PaymentMethod)) ||
				IsContainsT(PAYMENT_METHOD_ORDER_TRANSFER, requestData.PaymentMethod) {
				settingResp := model.HoldOrderConfigDB.Query(model.HoldOrderConfig{
					ComplexQuery: []*bson.M{
						{
							"hold_order_code": bson.M{"$in": []enum.HoldOrderValue{enum.HoldOrder.AutoCancelBankOrder, enum.HoldOrder.AutoSendPaymentRemind}},
						},
					},
				}, 0, 2, nil)
				if settingResp.Status == common.APIStatus.Ok {
					settings := settingResp.Data.([]*model.HoldOrderConfig)
					for _, setting := range settings {
						if setting.HoldOrderCode == enum.HoldOrder.AutoCancelBankOrder && setting.IsActive != nil && *setting.IsActive && setting.BankTransferWaitingTime != nil {
							readyTime := time.Now().Add(time.Duration(*setting.BankTransferWaitingTime) * time.Minute)
							model.AutoCancelPaymentMethodBankJob.Push(CheckPaymentMethodBankOrder{
								OrderId: order.OrderID,
								Unix:    readyTime.Unix(),
							}, &job.JobItemMetadata{
								Topic:     "default",
								ReadyTime: &readyTime,
							})
							updateData.WaitForTransferTime = *setting.BankTransferWaitingTime
							updateData.AutoCancelTransferPaymentUnix = readyTime.Unix()
						}
						if setting.HoldOrderCode == enum.HoldOrder.AutoSendPaymentRemind && setting.IsActive != nil && *setting.IsActive && setting.DisplayTime != nil {
							readyTime := time.Now().Add(time.Duration(*setting.DisplayTime) * time.Minute)
							model.AutoSendPaymentRemindJob.Push(RemindPaymentMethodOnlineOrder{
								OrderId: order.OrderID,
								Unix:    readyTime.Unix(),
							}, &job.JobItemMetadata{
								Topic:     "default",
								ReadyTime: &readyTime,
							})
							updateData.AutoSendPaymentNotificationUnix = readyTime.Unix()
						}
					}
				}
			}

			resultResponse = model.OrderDB.UpdateOne(model.Order{ID: order.ID}, updateData)
			if resultResponse.Status == common.APIStatus.Ok {
				if isExistVoucher && len(updateData.RedeemApplyResult) == 0 {
					model.OrderDB.UpdateOneWithOption(model.Order{
						OrderID: order.OrderID,
					}, bson.M{"$unset": bson.M{"redeem_apply_result": nil}})
				}
				if isNeedToUpdateOrderDetail {
					go updateDiscountDetail(order, mapItemGiftNeedUpdate)
				}
				result := resultResponse.Data.([]*model.Order)[0]
				if isChangePaymentFee {
					result.PartnerPaymentStatus = updateData.PartnerPaymentStatus // for case omitempty
					result.PartnerPaymentMethod = updateData.PartnerPaymentMethod // for case omitempty

					// unassign related field with partner payment
					if updateData.PartnerPaymentStatus == "" {
						model.OrderDB.UpdateOneWithOption(
							model.Order{ID: order.ID},
							bson.M{"$unset": bson.M{
								"partner_payment_status": nil,
								"partner_payment_method": nil,
							}},
						)
					}
					// else if updateData.PartnerPaymentMethod == nil {
					// 	model.OrderDB.UpdateOneWithOption(
					// 		model.Order{ID: order.ID},
					// 		bson.M{"$unset": bson.M{
					// 			"partner_payment_method": nil,
					// 		}},
					// 	)
					// }
				}

				// job: confirmOrderJob
				if result.Status == enum.OrderState.WaitConfirm {
					confirmOrderJob(result)
				}
			}
			return resultResponse
		}

	default:
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Don't allow this action",
			ErrorCode: "INVALID_ACTION_STATUS",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Do nothing",
	}
}

func updateDiscountDetail(order *model.Order, giftUpdateMap map[string]int) {
	currentItems := make(map[string]*model.OrderItem)
	qItems := model.GetOrderItemPartitionDB(order, "updateDiscountDetail").Query(&model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
	if qItems.Status == common.APIStatus.Ok {
		for _, item := range qItems.Data.([]*model.OrderItem) {
			currentItems[fmt.Sprintf("%s_%s", item.Sku, item.Type)] = item
		}
	}

	for _, item := range order.Items {
		if item.Type == "GIFT" {
			// exist in currentItems -> update quantity
			if curItem, ok := currentItems[fmt.Sprintf("%s_%s", item.Sku, item.Type)]; ok {
				if curItem.Quantity != item.Quantity {
					curItem.Quantity = item.Quantity
					model.GetOrderItemPartitionDB(order, "updateDiscountDetail").UpdateOne(model.OrderItem{ID: curItem.ID}, model.OrderItem{
						Quantity: item.Quantity,
					})
				}
			} else {
				// not exist in currentItems -> insert
				model.GetOrderItemPartitionDB(order, "updateDiscountDetail").Create(item)
			}
		} else {
			// exist in currentItems -> update quantity, discount detail, price after discount
			if curItem, ok := currentItems[fmt.Sprintf("%s_%s", item.Sku, item.Type)]; ok {
				if curItem.Quantity != item.Quantity || curItem.DiscountDetail != item.DiscountDetail || curItem.PriceAfterDiscount != item.PriceAfterDiscount {
					model.GetOrderItemPartitionDB(order, "updateDiscountDetail").UpdateOne(model.OrderItem{ID: curItem.ID}, model.OrderItem{
						Quantity:           item.Quantity,
						DiscountDetail:     item.DiscountDetail,
						PriceAfterDiscount: item.PriceAfterDiscount,
					})
				}
			}
		}
	}
}

// RemoveOrderItem ...
func RemoveOrderItem(acc *model.Account, input *model.OrderRemoveItem) *common.APIResponse {
	order, errRes := getOrder(input.OrderId)
	if errRes != nil {
		return errRes
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "RemoveOrderItem")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}
	qDeleteItem := orderItemPartitionDB.Delete(&model.OrderItem{OrderID: order.OrderID, Sku: input.Sku, Type: input.Type})
	if qDeleteItem.Status == common.APIStatus.Ok {
		SyncOrderDetail(*order)

		curItems := order.Items
		newItems := curItems
		for i, item := range curItems {
			if item.Sku == input.Sku && item.Type == input.Type {
				newItems = append(newItems[:i], newItems[i+1:]...)
			}
		}
		order.Items = newItems
		resSyncPrice := syncOrderPrice(order, acc, input.NoExtraFee)
		if resSyncPrice.Status != common.APIStatus.Ok {
			return resSyncPrice
		}
	}
	return qDeleteItem
}

// UpdateOrderItem ...
func UpdateOrderItem(acc *model.Account, input *model.OrderUpdateItem) *common.APIResponse {
	order, errRes := getOrder(input.OrderId)
	if errRes != nil {
		return errRes
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "UpdateOrderItem")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	qItemRes := orderItemPartitionDB.QueryOne(&model.OrderItem{OrderID: order.OrderID, Sku: input.Sku, Type: input.Type})
	if qItemRes.Status != common.APIStatus.Ok {
		return qItemRes
	}
	curItem := qItemRes.Data.([]*model.OrderItem)[0]

	//order.Items = qItemRes.Data.([]*model.SaleOrderItem)

	orderItemUpdate := &model.OrderItem{
		Quantity:   input.Quantity,
		TotalPrice: input.Quantity * curItem.Price,
	}
	qUpdate := orderItemPartitionDB.UpdateOne(&model.OrderItem{OrderID: order.OrderID, Sku: input.Sku, Type: input.Type}, orderItemUpdate)
	if qUpdate.Status == common.APIStatus.Ok {
		for i, item := range order.Items {
			if item.Sku == input.Sku && item.Type == input.Type {
				order.Items[i].Quantity = orderItemUpdate.Quantity
				order.Items[i].TotalPrice = orderItemUpdate.TotalPrice
			}
		}
		resSyncPrice := syncOrderPrice(order, acc, input.NoExtraFee)
		if resSyncPrice.Status != common.APIStatus.Ok {
			return resSyncPrice
		}
		// call if payment method is credit
		if order.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
			model.ProcessPaymentMethodCreditJob.Push(processPaymentMethodCredit{
				RefundOrderInput: &client.RefundOrderInput{
					CustomerID:       order.CustomerID,
					OrderID:          order.OrderID,
					Type:             enum.RefundOrderInputType.EDIT_ORDER_INTERNAL,
					IsVerify:         true,
					TotalOrderAmount: int64(orderItemUpdate.TotalPrice),
					RequestID:        time.Now().UnixNano(),
				},
			}, &job.JobItemMetadata{
				Topic: "refund",
				Keys:  []string{fmt.Sprintf("%d", order.OrderID)},
			})
		}
	}
	return qUpdate
}

// OrderUpdateStatus
func OrderUpdateStatus(acc *model.Account, updateStatus *model.OrderUpdateStatus) *common.APIResponse {
	if updateStatus.OrderID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order no/order id",
			ErrorCode: "MISSING_ORDER_NO",
		}
	}
	order, errRes := getOrder(updateStatus.OrderID)
	// fmt.Printf("Go to update order %d to %s from status %s\n", order.OrderID, updateStatus.Status, order.Status)

	if errRes != nil {
		return errRes
	}

	if updateStatus.Source == "" || updateStatus.Source != "WAREHOUSE" {
		var isOk = false

		switch updateStatus.Status {
		case enum.OrderState.Confirmed:
			isOk = order.Status == enum.OrderState.WaitConfirm

		case enum.OrderState.Canceled, enum.OrderState.CanceledOrderPart:
			isOk = order.Status == enum.OrderState.WaitConfirm ||
				order.Status == enum.OrderState.Confirmed ||
				order.Status == enum.OrderState.Processing ||
				order.Status == enum.OrderState.WaitToDeliver ||
				order.Status == enum.OrderState.Delivering ||
				order.Status == enum.OrderState.Reverting
		}

		if !isOk {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Not allowed order status",
				ErrorCode: "NOT_ALLOWED_ORDER_STATUS",
			}
		}
	}

	if updateStatus.PrivateNote != "" {
		updateStatus.PrivateNote = fmt.Sprintf("%s\n%s", order.PrivateNote, updateStatus.PrivateNote)
	}
	if updateStatus.Note != nil {
		newNote := order.Note
		if newNote == nil {
			updateStatus.Note = utils.ParseStringToPointer(fmt.Sprintf("%s", *updateStatus.Note))
		} else {
			updateStatus.Note = utils.ParseStringToPointer(fmt.Sprintf("%s\n%s", *newNote, *updateStatus.Note))
		}
	}
	t := time.Now()
	if updateStatus.ActionTime == nil {
		updateStatus.ActionTime = &t
	}
	if updateStatus.Status == enum.OrderState.Confirmed && order.Status != enum.OrderState.Confirmed {
		updateStatus.ConfirmType = &enum.OrderConfirmType.Manual
		updateStatus.ConfirmationDate = &t
	}
	if updateStatus.Status == enum.OrderState.Completed {
		if updateStatus.CompletedTime == nil && updateStatus.ActionTime != nil && !updateStatus.ActionTime.IsZero() {
			updateStatus.CompletedTime = updateStatus.ActionTime
		}

		if order.Status != enum.OrderState.Completed && updateStatus.CompletedTime == nil {
			updateStatus.CompletedTime = &t
		}
	}

	var (
		isNotifyCancel             bool = false
		isSyncSkuLimitHistory      bool = false
		isSyncBrandSkuLimitHistory bool = false
	)
	if updateStatus.Status == enum.OrderState.Canceled && updateStatus.Source != "WAREHOUSE" {
		errRes := processCancel(order, updateStatus.CancelRemainDO)
		if errRes != nil {
			return errRes
		} else {
			isSyncSkuLimitHistory = true && order.Status != enum.OrderState.Delivering
			isNotifyCancel = true && order.Status != enum.OrderState.Delivering

			if order.Source != nil && *order.Source == enum.Source.CLINIC_PORTAL {
				isSyncBrandSkuLimitHistory = true && order.Status != enum.OrderState.Delivering
			}
		}
	} else if updateStatus.Status == enum.OrderState.Canceled && updateStatus.Source == "WAREHOUSE" {
		isNotifyCancel = true
	}
	if updateStatus.Status == enum.OrderState.CanceledOrderPart {
		errRes := processCancel(order, updateStatus.CancelRemainDO)
		if errRes != nil {
			return errRes
		}
		updateStatus.Status = ""
	}
	if updateStatus.Status == enum.OrderState.Canceled {
		if order.CancelTime == nil {
			updateStatus.CancelTime = updateStatus.ActionTime
		}
	}
	if updateStatus.Status == enum.OrderState.Canceled && order.Status == enum.OrderState.Delivering && updateStatus.Source != "WAREHOUSE" {
		updateStatus.Status = order.Status
	}
	res := model.OrderDB.UpdateOne(&model.Order{OrderID: updateStatus.OrderID}, updateStatus)
	if res.Status == common.APIStatus.Ok {
		if updateStatus.Status == enum.OrderState.Confirmed && order.Status == enum.OrderState.WaitConfirm {
			_ = model.CreateSaleOrderJob.Push(createSaleOrderData{OrderID: order.OrderID}, &job.JobItemMetadata{
				UniqueKey: fmt.Sprint(order.OrderID),
				Keys: []string{
					strconv.Itoa(int(order.OrderID)),
				},
				SortedKey: fmt.Sprint(order.OrderID),
				Topic:     "default",
			})
		} else if updateStatus.Status == enum.OrderState.Completed {
			completeOrderSeller(updateStatus.OrderID)
		}

		if isSyncSkuLimitHistory {
			updateSkuLimitHistory(order)
		}
		if isSyncBrandSkuLimitHistory {
			updateBrandSkuLimitHistory(order)
			returnQuantitySku(order)
		}

		go sdk.Execute(func() {

			createSellerInvoiceJob(updateStatus, order)
			// call if payment method is credit
			if updateStatus.Status == enum.OrderState.Canceled && order.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
				model.ProcessPaymentMethodCreditJob.Push(processPaymentMethodCredit{
					RefundOrderInput: &client.RefundOrderInput{
						CustomerID: order.CustomerID,
						OrderID:    order.OrderID,
						Type:       enum.RefundOrderInputType.CANCEL,
						RequestID:  time.Now().UnixNano(),
					},
				}, &job.JobItemMetadata{
					Topic: "refund",
					Keys:  []string{fmt.Sprintf("%d", order.OrderID)},
				})
			}
			if updateStatus.Status == enum.OrderState.Canceled && order.Status == enum.OrderState.WaitConfirm {
				ReOrder(&model.Account{AccountID: order.AccountID, Type: "CUSTOMER"}, updateStatus.OrderID)
			}
			updateStatusOrderSeller(updateStatus.OrderID, updateStatus, updateStatus.SaleOrderCode)
			if updateStatus.ConfirmType != nil {
				//sendSMSOrderConfirmed(order.OrderID, order.CustomerID)
				if order.Source == nil || *order.Source != enum.Source.THUOCSI_MOBILE {
					sendZNSOrderConfirmed(order.OrderID, order.CustomerID, order.CreatedTime)
				}

				sendNotifyOrderConfirmed(order.OrderID, order.CustomerID)
			}
			updatedOrder := res.Data.([]*model.Order)[0]
			processInvoice(updatedOrder)
			if order.IsSplitDeliveryOrder != nil && *order.IsSplitDeliveryOrder && updateStatus.CancelRemainDO {
				client.Services.Bill.CallbackOrderPartialCancel(order.OrderID, order.SaleOrderCode)
			}
			//processBill(order, updateStatus)
			if isNotifyCancel {
				notifyCancelOrder(order.OrderID, order.CustomerID)
			}

			// recreate voucher when order canceled
			if updateStatus.Status == enum.OrderState.Canceled {
				client.Services.Promotion.CreateVoucherReuseOnOrderCancel(&client.CreateVoucherReuseOnOrderCancelReq{
					RedeemCodes: order.RedeemCode,
					AccountID:   order.AccountID,
					CustomerID:  order.CustomerID,
					OrderID:     order.OrderID,
				})
			}
		})
	}

	if res.Status == common.APIStatus.Ok {
		if updateStatus.Status == enum.OrderState.Completed && order.Status != enum.OrderState.Completed {
			readyTime := time.Now().Add(30 * time.Minute)
			model.CompleteOrderJobExecutor.Push(
				order,
				&job.JobItemMetadata{
					Keys: []string{
						TopicCompleteOrder,
						strconv.Itoa(int(order.OrderID)),
					},
					Topic:     "default",
					ReadyTime: &readyTime,
				},
			)
		}
		if updateStatus.Status == enum.OrderState.Delivered && order.Status != enum.OrderState.Delivered {
			// call if payment method is credit
			if order.PaymentMethod == string(enum.PaymentMethod.CREDIT) {
				model.ProcessPaymentMethodCreditJob.Push(processPaymentMethodCredit{
					RefundOrderInput: &client.RefundOrderInput{
						CustomerID: order.CustomerID,
						OrderID:    order.OrderID,
						Type:       enum.RefundOrderInputType.DELIVERED,
						RequestID:  time.Now().UnixNano(),
					},
				}, &job.JobItemMetadata{
					Topic: "refund",
					Keys:  []string{fmt.Sprintf("%d", order.OrderID)},
				})
			}
		}

		if GetKiotvietConfigs().AllowSyncUpdateOrderKiotviet {
			if updateStatus.Status == enum.OrderState.Canceled && order.Status != enum.OrderState.WaitConfirm {
				if utils.IsContains(order.CustomerTags, "KIOTVIET") {
					go sdk.Execute(func() {
						SyncUpdateOrderStatusToKiotvietPartner(order, nil)
					})
				}
			}
		}
	}
	// TODO: process order to reconciliation item
	// go tool.ProcessOrderToReconciliationItem(updateStatus.OrderID)
	go func() {
		order.Status = updateStatus.Status
		client.Services.Promotion.ScoreMission("Update order status "+string(order.Status), order)
	}()
	return res
}

func createSellerInvoiceJob(updateStatus *model.OrderUpdateStatus, order *model.Order) {
	if updateStatus.Status != enum.OrderState.Delivered {
		return
	}
	if order.Status == enum.OrderState.Delivered {
		return
	}

	model.CreateSellerInvoiceJob.Push(model.Order{
		OrderID: order.OrderID,
		Status:  order.Status,
	}, &job.JobItemMetadata{
		Keys: []string{
			TopicDeliveredOrder,
			strconv.Itoa(int(order.OrderID)),
		},
		Topic: "default",
	})

	// secondRun := invoiceReadyTime.AddDate(0, 0, 2)
	// model.CreateSellerInvoiceJob.Push(model.Order{
	// 	OrderID: order.OrderID,
	// 	Status:  order.Status,
	// }, &job.JobItemMetadata{
	// 	Keys: []string{
	// 		TopicDeliveredOrder,
	// 		strconv.Itoa(int(order.OrderID)),
	// 	},
	// 	Topic:     "default",
	// 	ReadyTime: &secondRun,
	// })
}

// OrderCounting ...
func OrderCounting(in *model.OrderCounting) *common.APIResponse {
	query := model.Order{}
	for _, data := range in.CountingField {
		query.ComplexQuery = []*bson.M{
			{
				data.FieldName: data.Condition,
			},
		}
		data.Result = model.OrderDB.Count(query).Total
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Counting done",
		Data:    in.CountingField,
	}
}

// CancelOrderByCustomer ...
func CancelOrderByCustomer(acc *model.Account, updateStatus *model.CancelOrderByCustomerRequest) *common.APIResponse {
	qOrder := model.OrderDB.QueryOne(&model.Order{AccountID: acc.AccountID, OrderID: updateStatus.OrderId})
	if qOrder.Status != common.APIStatus.Ok {
		return qOrder
	}
	order := qOrder.Data.([]*model.Order)[0]

	isNotifyCancel := false
	errRes := processCancel(order, false)
	if errRes != nil {
		return errRes
	} else {
		isNotifyCancel = true
	}
	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "CancelOrderByCustomer")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	orderItemRes := orderItemPartitionDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
	if orderItemRes.Status != common.APIStatus.Ok {
		return OrderItemNotFoundError
	}

	order.Items = orderItemRes.Data.([]*model.OrderItem)
	updateStatus.Status = enum.OrderState.Canceled
	res := model.OrderDB.UpdateOne(&model.Order{OrderID: updateStatus.OrderId}, updateStatus)
	if res.Status == common.APIStatus.Ok {
		go func() {
			updateStatusOrderSeller(order.OrderID, &model.OrderUpdateStatus{Status: enum.OrderState.Canceled}, order.SaleOrderCode)
			if isNotifyCancel {
				notifyCancelOrder(order.OrderID, order.CustomerID)
			}
			if order.Source != nil && *order.Source == enum.Source.CLINIC_PORTAL {
				updateBrandSkuLimitHistory(order)
				returnQuantitySku(order)
			}
		}()
	}
	return res
}

func UpdateOrderInvoice(acc *model.Account, input *model.UpdateInvoiceRequest) *common.APIResponse {
	// if !*input.InvoiceRequest {
	// 	return &common.APIResponse{
	// 		Status:  common.APIStatus.Invalid,
	// 		Message: "Yêu cầu xuất hóa đơn không hợp lệ",
	// 	}
	// }
	query := model.Order{OrderID: input.OrderID}
	qOrder := model.OrderDB.QueryOne(query)
	if qOrder.Status != common.APIStatus.Ok {
		return qOrder
	}
	order := qOrder.Data.([]*model.Order)[0]
	requestInvoice := false
	if order.Invoice != nil && order.Invoice.RequestInvoice != nil && *order.Invoice.RequestInvoice {
		// todo compare current invoice request info -> return if no change
		if *input.RequestInvoice == *order.Invoice.RequestInvoice &&
			input.CompanyName == order.Invoice.CompanyName &&
			input.CompanyAddress == order.Invoice.CompanyAddress &&
			input.Email == order.Invoice.Email &&
			input.TaxCode == order.Invoice.TaxCode {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Thông tin yêu cầu hóa đơn không có sự thay đổi",
			}
		}
		requestInvoice = *order.Invoice.RequestInvoice
	}

	filter := model.Invoice{
		OrderID: input.OrderID,
	}
	updater := model.Invoice{
		Request: &requestInvoice,
	}
	model.InvoiceDB.UpdateMany(filter, updater)

	invoiceResp := model.InvoiceDB.Query(filter, 0, 1000, nil)
	if invoiceResp.Status == common.APIStatus.Ok {
		invoices := invoiceResp.Data.([]*model.Invoice)

		for _, invoice := range invoices {
			if invoice.InvoiceCode == "" {
				continue
			}
			invoiceItemResp := model.InvoiceItemDB.Query(model.InvoiceItem{InvoiceCode: invoice.InvoiceCode}, 0, 1000, nil)
			if invoiceItemResp.Status == common.APIStatus.Ok {
				invoiceItems := invoiceItemResp.Data.([]*model.InvoiceItem)
				invoice.InvoiceItems = invoiceItems

				// SyncOrderInvoiceItemResp := client.Services.Invoice.SyncOrderInvoiceItem(*invoice)

				SyncOrderInvoiceItemResp := &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "OK",
				}

				SyncOrderInvoiceItemResp = client.Services.Invoice.SyncSellerExportedOrderInvoiceItem(*invoice)

				// if invoice.InvoiceV2Code != "" {
				// 	// đã xuất từ v2
				// 	SyncOrderInvoiceItemResp = client.Services.Invoice.SyncSellerExportedOrderInvoiceItem(*invoice)
				// } else if invoice.Config != nil {
				// 	// xuất dùm = invoice v1
				// 	SyncOrderInvoiceItemResp = client.Services.Invoice.SyncSellerSupportExportOrderInvoiceHILO(*invoice)
				// } else {
				// 	// seller tự xuất
				// 	SyncOrderInvoiceItemResp = client.Services.Invoice.SyncSellerExportedOrderInvoiceItem(*invoice)
				// }
				if SyncOrderInvoiceItemResp.Status != common.APIStatus.Ok {
					fmt.Println(SyncOrderInvoiceItemResp.Message)
				}
			}
		}
	}

	res := client.Services.Invoice.VerifyUpdateInvoice(&client.UpdateInvoiceRequest{
		OrderID:        input.OrderID,
		RequestInvoice: utils.ParseBoolToPointer(requestInvoice),
	})
	if res.Status == common.APIStatus.Ok {
		// input.InvoiceRequest.IsIncidentTaxCode = res.
		updaterOrder := model.Order{
			Invoice: &input.InvoiceRequest,
		}

		if utils.IsOrderTagContains(order.Tags, enum.Tag.NON_INVOICE_CUSTOMER) {
			if input.RequestInvoice != nil && *input.RequestInvoice {
				updaterOrder.Tags = utils.RemoveOrderTagInSlice(order.Tags, enum.Tag.NON_INVOICE_CUSTOMER)
				if len(updaterOrder.Tags) == 0 {
					updateTagResp := model.OrderDB.UpdateOne(
						model.Order{ID: order.ID},
						model.UpdateOrderOmitempty{Tags: &updaterOrder.Tags},
					)
					if updateTagResp.Status != common.APIStatus.Ok {
						return updateTagResp
					}
				}
			}
		} else {
			if utils.IsContains(order.CustomerTags, "NON_INVOICE_CUSTOMER") && (input.RequestInvoice == nil || !*input.RequestInvoice) {
				if !utils.IsOrderTagContains(order.Tags, enum.Tag.NON_INVOICE_CUSTOMER) {
					updaterOrder.Tags = append(order.Tags, enum.Tag.NON_INVOICE_CUSTOMER)
				}
			}
		}

		qUpdate := model.OrderDB.UpdateOne(query, &updaterOrder)
		if qUpdate.Status != common.APIStatus.Ok {
			return qUpdate
		}

		err := model.UpdateInvoiceInfoJobExecutor.Push(&client.UpdateInvoiceRequest{
			OrderID:        input.OrderID,
			RequestInvoice: utils.ParseBoolToPointer(requestInvoice),
		}, &job.JobItemMetadata{
			Topic: "default",
		})
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: err.Error(),
			}
		}
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Cập nhật thông tin xuất hóa đơn thành công",
		}
	} else {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: res.ErrorCode,
			Message:   "Không thể cập nhật thông tin xuất hóa đơn",
		}
	}
}

//func UpdateProcessingStatus(input *model.RequestUpdateDeliverStatus) *common.APIResponse {
//	orderFilter := model.Order{
//		OrderID: input.OrderID,
//	}
//	orderRes := model.OrderDB.QueryOne(orderFilter)
//	if orderRes.Status != common.APIStatus.Ok {
//		return orderRes
//	}
//
//	zero := 0
//	order := orderRes.Data.([]*model.Order)[0]
//	switch input.Status {
//	case "DELIVERING":
//		for i, _ := range input.OrderItems {
//			if input.OrderItems[i].Quantity == nil && input.OrderItems[i].DeliverQuantity != nil {
//				input.OrderItems[i].Quantity = input.OrderItems[i].DeliverQuantity
//			}
//			if input.OrderItems[i].Quantity == nil {
//				input.OrderItems[i].Quantity = &zero
//			}
//		}
//
//	case "DELIVERED":
//		for i, _ := range input.OrderItems {
//			//if input.OrderItems[i].Quantity == nil && input.OrderItems[i].DeliverQuantity != nil {
//			//	input.OrderItems[i].Quantity = input.OrderItems[i].DeliverQuantity
//			//}
//			//if input.OrderItems[i].Quantity == nil {
//			//	input.OrderItems[i].Quantity = &zero
//			//}
//			if input.OrderItems[i].CompletedQuantity == nil {
//				input.OrderItems[i].CompletedQuantity = &zero
//			}
//		}
//
//	default:
//		return orderRes
//	}
//
//	updateOrder(order, input)
//	updateOrderSeller(order, input)
//
//	orderRes = model.OrderDB.QueryOne(orderFilter)
//	return orderRes
//}

func UpdateProcessingStatus2(input *model.RequestUpdateDeliverStatus) *common.APIResponse {
	orderFilter := model.Order{
		OrderID: input.OrderID,
	}
	orderRes := model.OrderDB.QueryOne(orderFilter)
	if orderRes.Status != common.APIStatus.Ok {
		return orderRes
	}

	zero := 0
	order := orderRes.Data.([]*model.Order)[0]
	switch input.Status {
	case "DELIVERING":
		for i := range input.OrderItems {
			if input.OrderItems[i].OutboundQuantity == nil {
				input.OrderItems[i].OutboundQuantity = &zero
			}
		}

	case "DELIVERED":
		for i := range input.OrderItems {
			if input.OrderItems[i].DeliveredQuantity == nil {
				input.OrderItems[i].DeliveredQuantity = &zero
			}
		}

	case "RETURNED":
		for i := range input.OrderItems {
			if input.OrderItems[i].ReturnedQuantity == nil {
				input.OrderItems[i].ReturnedQuantity = &zero
			}
		}

	case "COMPLETED":
		for i := range input.OrderItems {
			if input.OrderItems[i].CompletedQuantity == nil {
				input.OrderItems[i].CompletedQuantity = &zero
			}
		}
	case "RESERVING":
		for i := range input.OrderItems {
			if input.OrderItems[i].ReservedQuantity == nil {
				input.OrderItems[i].ReservedQuantity = &zero
			}
		}
	default:
		return orderRes
	}

	updateOrder2(order, input)
	//updateOrderSeller2(order, input)

	orderRes = model.OrderDB.QueryOne(orderFilter)
	return orderRes
}

func CountOrderPoint(in *model.RequestCountOrderPoint) *common.APIResponse {
	type data struct {
		Point       float64 `json:"point" bson:"point"`
		ActualPrice int     `json:"actualPrice" bson:"actual_price"`
		CustomerId  int     `json:"customerId" bson:"_id"`
	}
	query := &model.Order{
		Status: enum.OrderState.Completed,
	}

	if in.DateFrom != nil && in.DateTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"completed_time": bson.M{
				"$gte": in.DateFrom,
				"$lte": in.DateTo,
			},
		})
	}

	var customerPoints []*data
	result := model.OrderDB.Aggregate([]primitive.M{
		{
			"$match": query,
		},
		{
			"$group": primitive.M{
				"_id": "$customer_id",
				"point": primitive.M{
					"$sum": "$point",
				},
				"actual_price": primitive.M{
					"$sum": "$actual_price",
				},
			},
		},
	}, &customerPoints)
	result.Data = customerPoints
	return result
}

func CountOrderValue(in *model.RequestCountOrderValue) *common.APIResponse {
	go func() {
		query := &model.Order{}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"status": bson.M{
				"$nin": []enum.OrderStateValue{enum.OrderState.WaitConfirm, enum.OrderState.Canceled},
			},
		})

		// nếu in.systemDisplay == "" thì gán lại in.SystemDisplay = "BUYMED"
		// gán query.systemDisplay = in.SystemDisplay
		if in.SystemDisplay == "" {
			in.SystemDisplay = "BUYMED"
		}

		query.SystemDisplay = in.SystemDisplay
		if in.ConfirmFrom != nil && in.ConfirmTo != nil {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"created_time": bson.M{
					"$gte": in.ConfirmFrom,
					"$lte": in.ConfirmTo,
				},
			})
		}

		var values []*client.CountOrderValue
		model.OrderDB.Aggregate([]primitive.M{
			{
				"$match": query,
			},
			{
				"$group": primitive.M{
					"_id": "$customer_id",
					"order_values": bson.M{"$push": bson.M{
						"order_id":           "$order_id",
						"status":             "$status",
						"total_price":        "$total_price",
						"total_actual_price": "$actual_total_price",
						"created_time":       "$created_time",
						"completed_time":     "$completed_time",
					}},
				},
			},
		}, &values)
		if saveErr := saveGamificationValue(values, in.LogSyncGamificationCode); saveErr != nil {
			fmt.Println("DEBUG 1605", saveErr.Error())
			return
		}
		errCli := client.Services.Promotion.SyncGamification([]*client.CountOrderValue{}, in.LogSyncGamificationCode, in.SystemDisplay)
		if errCli != nil && errCli.Status != common.APIStatus.Ok {
			fmt.Println("DEBUG 1609", errCli.Message)
		}
	}()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Request sync value from order success",
	}
}

func CountOrderValueBySeller(in *model.RequestCountOrderValue) *common.APIResponse {
	go func() {
		query := &model.Order{}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"status": bson.M{
				"$nin": []enum.OrderStateValue{enum.OrderState.WaitConfirm, enum.OrderState.Canceled},
			},
		})

		// nếu in.systemDisplay == "" thì gán lại in.SystemDisplay = "BUYMED"
		// gán query.systemDisplay = in.SystemDisplay
		if in.SystemDisplay == "" {
			in.SystemDisplay = "BUYMED"
		}
		query.SystemDisplay = in.SystemDisplay

		if in.ConfirmFrom != nil && in.ConfirmTo != nil {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"created_time": bson.M{
					"$gte": in.ConfirmFrom,
					"$lte": in.ConfirmTo,
				},
			})
		}

		var orders []*client.CountOrderValue
		model.OrderDB.Aggregate([]primitive.M{
			{
				"$match": query,
			},
			{
				"$group": primitive.M{
					"_id":    "$customer_id",
					"orders": bson.M{"$push": "$order_id"},
					"order_values": bson.M{"$push": bson.M{
						"order_id":       "$order_id",
						"status":         "$status",
						"created_time":   "$created_time",
						"completed_time": "$completed_time",
					}},
				},
			},
		}, &orders)
		var finalValues []*client.CountOrderValue
		orderIds := make([]int, 0)
		orderMap := make(map[int]*model.GamificationOrderValue, 0)
		var orderStartTime, orderEndTime *time.Time
		for _, order := range orders {
			for _, value := range order.OrderValues {
				if value.CreatedTime != nil && (orderStartTime == nil || orderStartTime.After(*value.CreatedTime)) {
					orderStartTime = value.CreatedTime
					// fmt.Println("orderStartTime", orderStartTime)
				}
				if value.CreatedTime != nil && (orderEndTime == nil || orderEndTime.Before(*value.CreatedTime)) {
					orderEndTime = value.CreatedTime
					// fmt.Println("orderStartTime", orderStartTime)
				}
				orderIds = append(orderIds, value.OrderID)
				orderMap[value.OrderID] = value
			}
			if len(orderIds) == 500 {
				finalValues = aggOrderItemValue(finalValues, orderMap, orderIds, in.SellerCode, orderStartTime, orderEndTime)
				orderIds = make([]int, 0)
			}
		}
		if len(orderIds) > 0 {
			finalValues = aggOrderItemValue(finalValues, orderMap, orderIds, in.SellerCode, orderStartTime, orderEndTime)
			orderIds = make([]int, 0)
		}
		if saveErr := saveGamificationValue(uniqueOrderValue(finalValues), in.LogSyncGamificationCode); saveErr != nil {
			return
		}
		client.Services.Promotion.SyncGamification([]*client.CountOrderValue{}, in.LogSyncGamificationCode, in.SystemDisplay)
	}()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Request sync value from order success",
	}
}

func aggOrderItemValue(finalValues []*client.CountOrderValue, orderMap map[int]*model.GamificationOrderValue, orderIds []int, sellerCode string, orderStartTime, orderEndTime *time.Time) []*client.CountOrderValue {
	totalValues := make([]*client.CountOrderValue, 0)
	queryOrderItem := model.OrderItem{
		SellerCode: sellerCode,
		ComplexQuery: []*bson.M{
			{
				"order_id": bson.M{"$in": orderIds},
			},
		},
	}
	listOrderItemDB := make(map[string]*db.Instance, 0)
	fmt.Println(orderEndTime, orderStartTime)
	if orderStartTime != nil && orderEndTime != nil {
		months := ListMonthByStartAndEndTime(*orderStartTime, *orderEndTime)
		for _, date := range months {
			orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{CreatedTime: &date}, "aggOrderItemValue")
			listOrderItemDB[orderItemPartitionDB.ColName] = orderItemPartitionDB
			// fmt.Println("orderItemPartitionDB.ColName", orderItemPartitionDB.ColName)
		}
	}

	for _, db := range listOrderItemDB {
		values := make([]*client.CountOrderValue, 0)
		db.Aggregate([]primitive.M{
			{
				"$match": queryOrderItem,
			},
			{
				"$group": primitive.M{
					"_id": "$customer_id",
					"total_price": primitive.M{
						"$sum": "$total_price",
					},
					"orders": bson.M{"$addToSet": "$order_id"},
					"order_values": bson.M{"$push": bson.M{
						"order_id":           "$order_id",
						"sku":                "$sku",
						"total_price":        "$total_price",
						"total_actual_price": "$actual_price",
					}},
				},
			},
		}, &values)
		if len(values) > 0 {
			totalValues = append(totalValues, values...)
		}
	}
	//model.OrderItemDB.Aggregate([]primitive.M{
	//	{
	//		"$match": queryOrderItem,
	//	},
	//	{
	//		"$group": primitive.M{
	//			"_id": "$customer_id",
	//			"total_price": primitive.M{
	//				"$sum": "$total_price",
	//			},
	//			"orders": bson.M{"$addToSet": "$order_id"},
	//			"order_values": bson.M{"$push": bson.M{
	//				"order_id":           "$order_id",
	//				"sku":                "$sku",
	//				"total_price":        "$total_price",
	//				"total_actual_price": "$actual_price",
	//			}},
	//		},
	//	},
	//}, &totalValues)

	if len(totalValues) > 0 {
		for _, value := range totalValues {
			orderValues := make([]*model.GamificationOrderValue, 0)
			for _, orderValue := range value.OrderValues {
				if data := orderMap[orderValue.OrderID]; data != nil {
					orderValue.CreatedTime = data.CreatedTime
					orderValue.CompletedTime = data.CompletedTime
					orderValue.Status = data.Status
				}
				orderValues = append(orderValues, orderValue)
			}
			value.OrderValues = orderValues
			finalValues = append(finalValues, value)
		}
	}
	return finalValues
}

func saveGamificationValue(values []*client.CountOrderValue, logCode string) error {
	saveValues := make([]*model.GamificationValue, 0)
	for _, value := range values {
		saveValue := &model.GamificationValue{
			CustomerId:              value.CustomerId,
			OrderValues:             value.OrderValues,
			LogSyncGamificationCode: logCode,
		}
		saveValues = append(saveValues, saveValue)
		if len(saveValues) >= 20 {
			saveRes := model.GamificationValueDB.CreateMany(saveValues)
			if saveRes.Status != common.APIStatus.Ok {
				return fmt.Errorf(saveRes.Message)
			}
			time.Sleep(5 * time.Millisecond)
			saveValues = make([]*model.GamificationValue, 0)
		}
	}
	if len(saveValues) == 0 {
		return nil
	}
	saveRes := model.GamificationValueDB.CreateMany(saveValues)
	if saveRes.Status != common.APIStatus.Ok {
		return fmt.Errorf(saveRes.Message)
	}
	return nil

}

func updateOrder2(order *model.Order, input *model.RequestUpdateDeliverStatus) {
	itemFilter := model.OrderItem{
		OrderCode: order.OrderCode,
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "updateOrder2")
	if orderItemPartitionDB == nil {
		return
	}

	itemRes := orderItemPartitionDB.Query(itemFilter, 0, 1000, nil)
	if itemRes.Status != common.APIStatus.Ok {
		fmt.Printf("%v\n", itemRes)
		return
	}

	items := itemRes.Data.([]*model.OrderItem)
	actualPrice := 0
	actualSellerPrice := 0
	t := true
	f := false
	zero := 0
	for i := range items {
		// find input item match with order-item
		item := filterOrderItem(input.OrderItems, items[i])
		if item == nil {
			// if not any => update proceed quantity = 0
			items[i].UpdatedProcessingQuantity = &f

			switch input.Status {
			case "DELIVERING":
				items[i].OutboundQuantity = &zero
				items[i].ActualPrice = &zero
				items[i].ActualSellerPrice = &zero

			case "DELIVERED":
				items[i].DeliveredQuantity = &zero
				items[i].ActualPrice = &zero
				items[i].ActualSellerPrice = &zero

			case "RETURNED":
				// item has no return
				if items[i].ActualPrice != nil {
					actualPrice += *items[i].ActualPrice
				}
			case "RESERVING":
				// no change actual price
				if items[i].ActualPrice != nil {
					actualPrice += *items[i].ActualPrice
				}
			case "COMPLETED":
				items[i].CompletedQuantity = &zero
				items[i].ActualPrice = &zero
				items[i].ActualSellerPrice = &zero
			}
		} else {
			items[i].OutboundInfos = item.OutboundInfos
			if items[i].SubItems != nil && len(*items[i].SubItems) > 0 && item.SubItems != nil && len(*item.SubItems) > 0 {
				mapSubItem := make(map[int64]*model.OrderItem)
				for j := range *item.SubItems {
					mapSubItem[(*item.SubItems)[j].ProductID] = (*item.SubItems)[j]
				}
				for j := range *items[i].SubItems {
					if subItem, ok := mapSubItem[(*items[i].SubItems)[j].ProductID]; ok {
						(*items[i].SubItems)[j].OutboundInfos = subItem.OutboundInfos
						(*items[i].SubItems)[j].ReturnInfos = subItem.ReturnInfos
					}
					//if mapSubItem[(*item.SubItems)[j].ProductID] != nil && mapSubItem[(*item.SubItems)[j].ProductID].OutboundInfos != nil {
					//	(*items[i].SubItems)[j].OutboundInfos = mapSubItem[(*item.SubItems)[j].ProductID].OutboundInfos
					//}
				}
			}
			items[i].UpdatedProcessingQuantity = &t
			//if item.ReservedQuantity != nil {
			//	// just update outbound quantity if any
			//	items[i].ReservedQuantity = item.ReservedQuantity
			//}

			switch input.Status {
			case "DELIVERING":
				if item.OutboundQuantity != nil {
					items[i].OutboundQuantity = item.OutboundQuantity

					tmpActualPrice := *items[i].OutboundQuantity * items[i].Price
					items[i].ActualPrice = &tmpActualPrice
					actualPrice += tmpActualPrice

					tmpActualSellerPrice := *items[i].OutboundQuantity * items[i].SellerPrice
					items[i].ActualSellerPrice = &tmpActualSellerPrice
					actualSellerPrice += tmpActualSellerPrice
				} else {
					items[i].OutboundQuantity = &zero
					items[i].ActualPrice = &zero
					items[i].ActualSellerPrice = &zero
				}
			case "DELIVERED":
				if item.OutboundQuantity != nil {
					// just update outbound quantity if any
					items[i].OutboundQuantity = item.OutboundQuantity
				}
				if item.DeliveredQuantity != nil {

					items[i].DeliveredQuantity = item.DeliveredQuantity

					tmpActualPrice := *items[i].DeliveredQuantity * items[i].Price
					items[i].ActualPrice = &tmpActualPrice
					actualPrice += tmpActualPrice
					tmpActualSellerPrice := *items[i].DeliveredQuantity * items[i].SellerPrice
					items[i].ActualSellerPrice = &tmpActualSellerPrice
					actualSellerPrice += tmpActualSellerPrice

				} else {
					items[i].DeliveredQuantity = &zero
					items[i].ActualPrice = &zero
					items[i].ActualSellerPrice = &zero
				}

			case "RETURNED":
				if item.ReturnInfos != nil {
					items[i].ReturnInfos = item.ReturnInfos
				}
				if item.ReturnedQuantity != nil {
					items[i].ReturnedQuantity = item.ReturnedQuantity

					if items[i].DeliveredQuantity != nil {
						tmpActualPrice := (*items[i].DeliveredQuantity - *items[i].ReturnedQuantity) * items[i].Price
						items[i].ActualPrice = &tmpActualPrice
						actualPrice += tmpActualPrice

						tmpActualSellerPrice := (*items[i].DeliveredQuantity - *items[i].ReturnedQuantity) * items[i].SellerPrice
						items[i].ActualSellerPrice = &tmpActualSellerPrice
						actualSellerPrice += tmpActualSellerPrice
					}
				} else {
					items[i].ReturnedQuantity = &zero
					if items[i].ActualPrice != nil {
						actualPrice = actualPrice + *items[i].ActualPrice
					}
					if items[i].ActualSellerPrice != nil {
						actualSellerPrice = actualSellerPrice + *items[i].ActualSellerPrice
					}
				}
			case "RESERVING":
				// keep price
				if items[i].ActualPrice != nil {
					actualPrice = actualPrice + *items[i].ActualPrice
				}
				if items[i].ActualSellerPrice != nil {
					actualSellerPrice = actualSellerPrice + *items[i].ActualSellerPrice
				}
				if item.ReservedQuantity != nil {
					items[i].ReservedQuantity = item.ReservedQuantity
				} else {
					items[i].ReservedQuantity = &zero
				}
			case "COMPLETED":
				if item.OutboundQuantity != nil {
					// just update outbound quantity if any
					items[i].OutboundQuantity = item.OutboundQuantity
				}

				if item.CompletedQuantity != nil {
					items[i].CompletedQuantity = item.CompletedQuantity

					tmpActualPrice := *items[i].CompletedQuantity * items[i].Price
					items[i].ActualPrice = &tmpActualPrice
					actualPrice += tmpActualPrice

					tmpActualSellerPrice := *items[i].CompletedQuantity * items[i].SellerPrice
					items[i].ActualSellerPrice = &tmpActualSellerPrice
					actualSellerPrice += tmpActualSellerPrice

				} else {
					items[i].CompletedQuantity = &zero
					items[i].ActualPrice = &zero
					items[i].ActualSellerPrice = &zero
				}
			}
		}

		filter := model.OrderItem{
			ID: items[i].ID,
		}
		updater := model.OrderItem{
			UpdatedProcessingQuantity: items[i].UpdatedProcessingQuantity,
			Quantity:                  items[i].Quantity,
			ReservedQuantity:          items[i].ReservedQuantity,
			OutboundQuantity:          items[i].OutboundQuantity,
			DeliveredQuantity:         items[i].DeliveredQuantity,
			CompletedQuantity:         items[i].CompletedQuantity,
			ReturnedQuantity:          items[i].ReturnedQuantity,
			ActualPrice:               items[i].ActualPrice,
			ActualSellerPrice:         items[i].ActualSellerPrice,
			OutboundInfos:             items[i].OutboundInfos,
			ReturnInfos:               items[i].ReturnInfos,
			SubItems:                  items[i].SubItems,
		}
		result := orderItemPartitionDB.UpdateOne(filter, updater)
		if result.Status != common.APIStatus.Ok {
			fmt.Printf("Update order-item %s failed: %s\n", items[i].ID.Hex(), result.Message)
		}
	}

	filter := model.Order{
		ID: order.ID,
	}
	updater := model.Order{
		ProcessingStatus:          enum.OrderStateValue(input.Status),
		ActualPrice:               &actualPrice,
		UpdatedProcessingQuantity: &t,
	}

	result := model.OrderDB.UpdateOne(filter, updater)
	if result.Status != common.APIStatus.Ok {
		fmt.Printf("Update order %d failed: %s\n", order.OrderID, result.Message)
	}
	orderUpdated := result.Data.([]*model.Order)[0]
	orderUpdated.Status = enum.OrderStateValue(input.Status)
	if input.Status == string(enum.OrderState.Delivering) || input.Status == string(enum.OrderState.WaitToDeliver) {
		isMissing, payload := checkIfMissingItemAtWaitToDeliver(order)
		if isMissing {
			sendMissingItemAlert(orderUpdated, payload)
		}
	}

	if input.Status == string(enum.OrderState.Delivering) || input.Status == string(enum.OrderState.Delivered) {
		processBill(orderUpdated, false)
	}
	if input.Status == string(enum.OrderState.Returned) || input.Status == string(enum.OrderState.Delivering) {
		processInvoice(orderUpdated)
	}
}

func filterOrderItem(items []*model.OrderItem, item *model.OrderItem) *model.OrderItem {
	for i := range items {
		if items[i].Sku == item.Sku ||
			items[i].ProductCode == item.ProductCode ||
			items[i].ProductID == item.ProductID {
			return items[i]
		}
	}

	return nil
}

func syncOrderPrice(order *model.Order, acc *model.Account, noExtraFee bool) *common.APIResponse {
	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "syncOrderPrice")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	mapSellerItemCount := make(map[string]int)

	order.TotalPrice = utils.ParseIntToPointer(0)
	order.Price = utils.ParseIntToPointer(0)
	order.TotalFee = utils.ParseIntToPointer(0)
	order.TotalItem = utils.ParseIntToPointer(0)
	order.TotalQuantity = utils.ParseIntToPointer(0)
	order.ExtraFee = utils.ParseInt64ToPointer(0)
	order.TotalDiscount = utils.ParseIntToPointer(0)
	if order.PaymentMethodFee == nil {
		order.PaymentMethodFee = utils.ParseInt64ToPointer(0)
	}
	if order.PaymentMethodPercentage == nil {
		order.PaymentMethodPercentage = utils.ParseFloat64ToPointer(0)
	}
	if order.DeliveryMethodFee == nil {
		order.DeliveryMethodFee = utils.ParseInt64ToPointer(0)
	}
	for _, item := range order.Items {
		order.TotalItem = utils.ParseIntToPointer(*order.TotalItem + 1)
		order.TotalQuantity = utils.ParseIntToPointer(*order.TotalQuantity + item.Quantity)
		item.TotalPrice = item.Quantity * item.Price
		order.Price = utils.ParseIntToPointer(*order.Price + item.TotalPrice)
		if item.Fee != nil {
			order.TotalFee = utils.ParseIntToPointer(*order.TotalFee + item.Fee.Total)
		}

		mapSellerItemCount[item.SellerCode]++
	}
	customer, errCustomer := getCustomerProfile(&model.Account{AccountID: order.AccountID, Type: enum.AccountType.CUSTOMER})
	if errCustomer != nil {
		return errCustomer
	}
	if customer.Level == "LEVEL_CIRCA" {
		return nil
	}

	if customer.OrdersCount != nil && *customer.OrdersCount >= 1 {
		if order.SystemDisplay == "BUYDENTAL" {
			if *order.Price < 2000000 && mapSellerItemCount[conf.DENX_SELLER] == 0 {
				order.ExtraFee = utils.ParseInt64ToPointer(50000)
			}

			// feature: nếu là Ha Noi và HCM thì điều kiện tối thiểu 1tr5, nếu dưới thì phí dịch vụ 50k.
			// Các tỉnh thành còn lại vẫn 2tr
		} else if order.CustomerProvinceCode == "01" || order.CustomerProvinceCode == "79" {
			if *order.Price < 1500000 {
				order.ExtraFee = utils.ParseInt64ToPointer(50000)
			}
		} else {
			if *order.Price < 2000000 {
				order.ExtraFee = utils.ParseInt64ToPointer(50000)
			}
		}
		if noExtraFee {
			order.ExtraFee = utils.ParseInt64ToPointer(0)
		}
	}
	cart := convertOrderToCart(order)
	removeCodes := make([]string, 0)
	mapCodeValid := make(map[string]bool, 0)
	newCodes := make([]*string, 0)
	mapVoucherAutoApply := make(map[string]bool)
	emptySlice := make([]*string, 0)
	curRedeemApplyResult := order.RedeemApplyResult
	if order.RedeemCode != nil && len(*order.RedeemCode) > 0 && len(order.RedeemApplyResult) > 0 {
		newRedeemApplyResult := make([]*model.PromoApplyResult, 0)
		isOrderExistVoucherAuto := false
		order.RedeemCode = &emptySlice
		for _, res := range order.RedeemApplyResult {
			if res.CanUse && res.AutoApply {
				isOrderExistVoucherAuto = true
				mapVoucherAutoApply[res.Code] = true
			}
		}

		promoApply, err := client.Services.Promotion.CheckVoucherCode(&client.CheckVoucherRequest{
			Customer:            customer,
			Cart:                &cart,
			VoucherCode:         *cart.RedeemCode,
			AccountID:           cart.AccountID,
			GetVoucherAutoApply: isOrderExistVoucherAuto,
			SystemDisplay:       order.SystemDisplay,

			Source: "syncOrderPrice",

			SourceDetail: acc.SourceDetail,
		})
		if err == nil {
			for _, promo := range promoApply {
				if promo.AutoApply && !mapVoucherAutoApply[promo.VoucherCode] {
					continue
				}
				if promo.CanUse {
					mapCodeValid[promo.VoucherCode] = true
					newCodes = append(newCodes, &promo.VoucherCode)
					newRedeemApplyResult = append(newRedeemApplyResult, &model.PromoApplyResult{
						Code:          promo.VoucherCode,
						CanUse:        promo.CanUse,
						Gift:          promo.Gifts,
						AutoApply:     promo.AutoApply,
						MatchSeller:   promo.MatchSeller,
						MatchProducts: promo.MatchProducts,
						DiscountValue: promo.DiscountValue,
					})
					order.TotalDiscount = utils.ParseIntToPointer(*order.TotalDiscount + promo.DiscountValue)
				} else {
					removeCodes = append(removeCodes, promo.VoucherCode)
				}
			}
		}
		order.RedeemCode = &newCodes
		order.RedeemApplyResult = newRedeemApplyResult
		if len(newRedeemApplyResult) == 0 {
			model.OrderDB.UpdateOne(model.Order{OrderID: order.OrderID}, bson.M{"redeem_apply_result": nil})
		}
		for _, curResult := range curRedeemApplyResult {
			if !mapCodeValid[curResult.Code] && curResult.CanUse && len(curResult.Gift) > 0 {
				for _, gift := range curResult.Gift {
					query := model.OrderItem{OrderID: order.OrderID, Sku: gift.Sku, Type: enum.ItemType.GIFT}
					qItem := orderItemPartitionDB.QueryOne(query)
					if qItem.Status == common.APIStatus.Ok {
						item := qItem.Data.([]*model.OrderItem)[0]
						if item.Quantity == gift.Quantity {
							orderItemPartitionDB.Delete(query)
							SyncOrderDetail(*order)
						} else {
							orderItemPartitionDB.UpdateOne(query, model.OrderItem{Quantity: item.Quantity - gift.Quantity})
						}
					}
				}
			}
		}
	}

	if len(removeCodes) > 0 {
		voucherStr := strings.Join(removeCodes, ",")
		_ = client.Services.Notification.CreateNotification(&client.Notification{
			Username:     customer.Username,
			UserID:       customer.AccountID,
			ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
			Topic:        "ANNOUNCEMENT",
			Title:        fmt.Sprintf("Đơn hàng #%d thay đổi số lượng thành công. Mã giảm giá %s đã áp dụng cho đơn hàng đã bị gỡ bỏ khỏi đơn hàng do không đủ điều kiện sử dụng.", order.OrderID, voucherStr),
			Link:         fmt.Sprintf("/my-order/%d", order.OrderID),

			Tags: []enum.NotificationTagEnum{enum.NotificationTag.ORDER, enum.NotificationTag.IMPORTANT},
		})
	}
	refundVouchers := make([]*string, 0)
	for _, cur := range curRedeemApplyResult {
		if !mapCodeValid[cur.Code] && cur.CanUse {
			refundVouchers = append(refundVouchers, &cur.Code)
		}
	}
	if len(refundVouchers) > 0 {
		_ = client.Services.Promotion.RefundVoucher(&client.UseVoucherRequest{
			VoucherCodes: refundVouchers,
			AccountID:    order.AccountID,
			OrderID:      order.OrderID,
			ApplyVoucherCount: func() map[string]int {
				m := make(map[string]int)
				for _, code := range curRedeemApplyResult {
					if code.CanUse {
						m[code.Code] = code.NumberOfAutoApply
					}
				}
				return m
			}(),
		})
	}

	_ = client.Services.Notification.CreateNotification(&client.Notification{
		Username:     customer.Username,
		UserID:       customer.AccountID,
		ReceiverType: utils.ParseStringToPointer("CUSTOMER"),
		Topic:        "ANNOUNCEMENT",
		Title:        fmt.Sprintf("Đơn hàng #%d thay đổi số lượng thành công.", order.OrderID),
		Link:         fmt.Sprintf("/my-order/%d", order.OrderID),

		Tags: []enum.NotificationTagEnum{enum.NotificationTag.ORDER, enum.NotificationTag.IMPORTANT},
	})

	feeValue := float64(cart.PaymentMethodFee) - (*order.PaymentMethodPercentage*float64(*order.Price))/100
	order.PaymentMethodFee = utils.ParseInt64ToPointer(int64(feeValue))
	order.TotalPrice = utils.ParseIntToPointer(*order.Price + int(*order.ExtraFee) + int(*order.PaymentMethodFee) + int(*order.DeliveryMethodFee) - *order.TotalDiscount)
	return model.OrderDB.UpdateOne(&model.Order{OrderID: order.OrderID}, order)
}

func getOrder(orderId int64) (*model.Order, *common.APIResponse) {
	qOrderRes := model.OrderDB.QueryOne(&model.Order{OrderID: orderId})
	if qOrderRes.Status != common.APIStatus.Ok {
		return nil, qOrderRes
	}
	order := qOrderRes.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "getOrder")
	if orderItemPartitionDB == nil {
		return nil, model.PARTITION_NOT_FOUND_RESPONSE
	}

	qItemRes := orderItemPartitionDB.Query(&model.OrderItem{OrderID: orderId}, 0, 0, nil)
	if qItemRes.Status != common.APIStatus.Ok {
		return nil, qItemRes
	}
	order.Items = qItemRes.Data.([]*model.OrderItem)
	return order, nil
}

func DelayOrder(orderId int64) *common.APIResponse {
	qOrder := model.OrderDB.QueryOne(&model.Order{OrderID: orderId})
	if qOrder.Status != common.APIStatus.Ok {
		return qOrder
	}

	order := qOrder.Data.([]*model.Order)[0]
	now := time.Now()
	time18July := time.Date(now.Year(), 7, 18, 0, 0, 0, 0, now.Location())
	if order.IsDelayDelivery != nil && *order.IsDelayDelivery {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Chỉ được giữ đơn 1 lần cho 1 đơn hàng",
			ErrorCode: "ORDER_CAN_NOT_DELAY",
		}
	}
	if !(order.Status == enum.OrderState.Confirmed || order.Status == enum.OrderState.WaitConfirm) || order.CreatedTime.After(time18July) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Đơn hàng không thể thay đổi ngày giao",
			ErrorCode: "ORDER_CAN_NOT_DELAY",
		}
	}
	order.DeliveryDate = nil
	feeDeliveryMap := make(map[string]*client.DeliveryFeeConfig)
	feeDeliveryConfigs, err := client.Services.Pricing.GetDeliveryFeeConfig()
	if err != nil {
		return DeliveryAndPaymentError
	}
	for _, fee := range feeDeliveryConfigs {
		feeDeliveryMap[fee.Code] = fee
	}
	if fee, ok := feeDeliveryMap[order.DeliveryMethod]; ok {
		t := time.Now()
		if order.DeliveryMethod == "DELIVERY_PLATFORM_SUPER_QUICK" { // giao siêu tốc, giao trong 24h
			order.DeliveryDate = utils.ParseTimeToPointer(t.Add(12 * time.Hour))
		} else if fee.Condition.TimeToDeliver != nil && *fee.Condition.TimeToDeliver > 0 {
			number := time.Duration(*fee.Condition.TimeToDeliver)
			order.DeliveryDate = utils.ParseTimeToPointer(t.Add(number * time.Hour))
		}
	}

	if order.DeliveryDate == nil {
		t := time.Now()
		order.DeliveryDate = utils.ParseTimeToPointer(t.Add(7 * 24 * time.Hour))

		if len(order.CustomerProvinceCode) == 0 {
			return ProvinceCodeError
		}
		region, err := client.Services.Location.GetRegion(order.CustomerProvinceCode)
		if err == nil {
			deliveryTime := int((region.EstLogistic + region.EstThuocSi) * 24 * 60) // minute
			if deliveryTime > 0 {
				order.DeliveryDate = utils.ParseTimeToPointer(t.Add(time.Duration(deliveryTime) * time.Minute))
			}
		}
	}
	return model.OrderDB.UpdateOne(&model.Order{OrderID: orderId}, model.Order{IsDelayDelivery: utils.ParseBoolToPointer(true), DeliveryDate: order.DeliveryDate})
}

func GetOrdersByCustomer(input *model.Order, offset, limit int64, reverse bool) *common.APIResponse {
	query := model.Order{
		CustomerID: input.CustomerID,
	}
	if input.OrderID != 0 {
		query.OrderID = input.OrderID
	} else {
		var complexQuery = []*bson.M{}
		if len(input.StatusIn) > 0 {
			complexQuery = append(
				complexQuery,
				&bson.M{
					"status": bson.M{
						"$in": input.StatusIn,
					},
				},
			)
		}

		if input.DateFrom != nil {
			complexQuery = append(complexQuery, &bson.M{
				"created_time": bson.M{
					"$gte": input.DateFrom,
				},
			})
		}
		query.ComplexQuery = complexQuery
	}

	sort := &bson.M{"created_time": 1}
	if reverse {
		sort = &bson.M{"created_time": -1}
	}

	return model.OrderDB.Query(query, offset, limit, sort)
}

func UpdateOrderItemData(req *model.UpdateOrderItemDataRequest) *common.APIResponse {
	orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{OrderID: req.OrderId}, "UpdateOrderItemData")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	return orderItemPartitionDB.UpdateOne(
		model.OrderItem{OrderID: req.OrderId, ProductID: req.ProductId},
		model.OrderItem{
			OutboundQuantity: req.OutboundQuantity,
			SkuLevel:         req.SkuLevel,
		},
	)
}

type FilterOrderByNEStatusQuery struct {
	CustomerID int64           `bson:"customer_id,omitempty"`
	Status     *model.NEString `bson:"status,omitempty"`
}

func GetSummationOrderInfoByCustomer(info string, customerID int64) *common.APIResponse {

	infos := strings.Split(info, ",")
	if len(infos) == 0 {
		return &common.APIResponse{Status: common.APIStatus.Invalid, Message: "Require info param"}
	}

	if customerID == 0 {
		return &common.APIResponse{Status: common.APIStatus.Invalid, Message: "Require customerId param"}
	}

	summation := model.SummationOrderInfo{
		CustomerID: customerID,
	}

	for _, infoType := range infos {
		switch infoType {
		case "LAST_ORDER":
			lastOrderQuery := FilterOrderByNEStatusQuery{
				CustomerID: customerID,
			}

			lastOrderResp := model.OrderDB.Query(lastOrderQuery, 0, 1, &bson.M{
				"created_time": -1,
			})

			if lastOrderResp.Status != common.APIStatus.Ok && lastOrderResp.Status != common.APIStatus.NotFound {
				return lastOrderResp
			}

			if lastOrderResp.Status == common.APIStatus.Ok {
				summation.LastOrder = lastOrderResp.Data.([]*model.Order)[0]
			}
		case "TOTAL_ORDER":
			countTotalConfirmed := FilterOrderByNEStatusQuery{
				CustomerID: customerID,
				Status: &model.NEString{
					String: string(enum.OrderState.WaitConfirm),
				},
			}

			countOrderResp := model.OrderDB.Count(countTotalConfirmed)
			if countOrderResp.Status != common.APIStatus.Ok {
				return countOrderResp
			}
			summation.TotalOrder = countOrderResp.Total
		}
	}

	return &common.APIResponse{Status: common.APIStatus.Ok, Message: "Get summation successfully", Data: []*model.SummationOrderInfo{&summation}}
}

func GenOrderID() *common.APIResponse {
	id, _ := model.GetOrderID()
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
		Data:    []int64{id},
	}
}

func UpdateOrderNote(account *model.Account, updateData *model.OrderUpdateNoteRequest) *common.APIResponse {

	idxTk := strings.Index(updateData.Note, "TK")
	runes := []rune(updateData.Note)
	if idx := strings.Index(updateData.Note, " "); idx > 0 && idx < idxTk {
		updateData.Note = string(runes[idx+1 : len(updateData.Note)])
	}

	updateData.Note = updateData.Note + " "
	regParent := `^(TK ************** So tien GD:[+])([0-9,[,]+) So du:([0-9,[,]+) ?((\w.*)|) ?([\s|\S|_|,|-|.])([0-9]{7,11})([\s|\S|,|_|-|.])`
	if updateData.SenderName == "VPB" {
		regParent = `^TK ********* tai VPB (\+[\d,]+VND) luc \d{2}:\d{2} \d{2}/\d{2}/\d{2}\. (So du [\d,]+VND)\. ND: ?((\w.*)|) ?([\s|\S|_|,|-|.])([0-9]{7,11})([\s|\S|,|_|-|.])`
	}
	reParent := regexp.MustCompile(regParent)
	var isReconcileRequest bool
	if !reParent.MatchString(updateData.Note) {
		if !checkRegexSmsReconcile(updateData.Note, updateData.SenderName) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Format sms invalid",
				ErrorCode: "SMS_INVALID",
			}
		}

		isReconcileRequest = true
	}

	regSoDu := `So du:([0-9,[,]+) `
	if updateData.SenderName == "VPB" {
		regSoDu = `So du [\d,]+`
	}
	reSoDu := regexp.MustCompile(regSoDu)
	soDuNotes := reSoDu.FindAllString(updateData.Note, -1)
	if len(soDuNotes) != 1 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Format sms invalid",
			ErrorCode: "SMS_INVALID",
		}
	}

	// regSubOrderID := `([\s|_|,])([0-9]{7,9})([\s|,|_])`
	regSubOrderID := `([\s|\S|_|,|-|.])([0-9]{7,11})([\s|\S|,|_|-|.])`
	reSubOrderID := regexp.MustCompile(regSubOrderID)
	strs := reSubOrderID.FindAllString(updateData.Note, -1)
	if (len(strs) < 1 || len(strs) > 5) && !isReconcileRequest {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Format sms invalid, not match order id",
			ErrorCode: "SMS_INVALID",
		}
	}

	regSubAmount := `[+]([0-9,[,]+)`
	reSubAmount := regexp.MustCompile(regSubAmount)
	subStrs := reSubAmount.FindAllString(updateData.Note, -1)
	if len(subStrs) != 1 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Format sms invalid, not match price",
			ErrorCode: "SMS_INVALID",
		}
	}

	subStr := strings.Replace(subStrs[0], "+", "", -1)
	subStr = strings.Replace(subStr, ",", "", -1)

	amount, err := strconv.Atoi(subStr)
	if err != nil || amount <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Format sms invalid, not match price",
			ErrorCode: "SMS_INVALID",
		}
	}

	subRunes := []rune(updateData.Note)
	finalNoteIdx := strings.Index(updateData.Note, soDuNotes[0])
	finalNote := string(subRunes[finalNoteIdx+len(soDuNotes[0]) : len(updateData.Note)])

	if updateData.SenderName == "VPB" {
		regNote := `ND: ?((\w.*)|)`
		reNote := regexp.MustCompile(regNote)
		contents := reNote.FindAllString(updateData.Note, -1)
		if len(strs) > 0 {
			content := contents[0]
			finalNote = strings.TrimPrefix(content, "ND: ")
		}
	}
	recNote := finalNote
	recNote = strings.ToUpper(strings.TrimSpace(recNote))
	if isReconcileRequest && strings.Contains(recNote, "REC") {
		// Check reconcile hub - company
		recNote = getReconcileCode(recNote)
		if recNote == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Format sms invalid, description has failed",
				ErrorCode: "SMS_INVALID",
			}
		}
		// forward msg to accounting reconcile
		return client.Services.Reconcile.SendReconcileBankMessage(recNote, amount)
	}

	NDIndex := strings.Index(updateData.Note, "ND")

	arrOrderIDs := make([]int, 0)
	for _, str := range strs {
		// orderStrID := strings.TrimSpace(str)
		// orderStrID = strings.Replace(orderStrID, "_", "", -1)
		// orderStrID = strings.Replace(orderStrID, ",", "", -1)
		// `([\s|\S|_|,|-|.])([0-9]{7,9})([\s|,|_|-|.])`

		orderStrIndex := strings.Index(updateData.Note, str)

		if NDIndex > 0 && orderStrIndex > 0 && orderStrIndex < NDIndex {
			// example: NHAN TU ******** TRACE 519293 ND ******** QR CHUYEN KHOAN 92834 => skip ********, 519293 because its indexes is smaller than index of ND
			continue
		}

		orderStrID := strings.NewReplacer(",", "", "\t", "", ".", "", "_", "", "-", "").Replace(strings.TrimSpace(str))
		re := regexp.MustCompile(`\d+`)

		orderStrID = re.FindString(orderStrID)

		// make sure order id is not start with 0
		if strings.HasPrefix(orderStrID, "0") {
			continue
		}

		orderID, err := strconv.Atoi(orderStrID)
		if err != nil {
			continue
		}
		if orderID < conf.Config.MaxOrderIDSkip {
			continue
		}
		arrOrderIDs = append(arrOrderIDs, orderID)
	}

	if len(arrOrderIDs) <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Format sms invalid, not match order id",
			ErrorCode: "SMS_INVALID",
		}
	}

	query := &model.Order{
		ComplexQuery: []*bson.M{
			{
				"order_id": bson.M{
					"$in": arrOrderIDs,
				},
			},
		},
	}

	// try to find the best match order
	var exist *model.Order = nil
	result := model.OrderDB.Query(query, 0, 100, &bson.M{"_id": -1})

	if result.Status != common.APIStatus.Ok {
		return result
	}

	// check order by condition: PAYMENT METHOD -> STATUS -> HOLD TIME -> PRICE
	for _, order := range result.Data.([]*model.Order) {
		if order.PaymentMethod == "NORMAL" || order.PaymentMethod == "PAYMENT_METHOD_NORMAL" || order.TotalPrice == nil {
			continue
		}

		if order.Status != enum.OrderState.WaitConfirm {
			continue
		}

		// there's order which has WAIT_TO_CONFIRM status, payment method BANK, but it was created for a long time
		// => we should check if it's still in hold time or not
		if !isValidOrderHoldTime(order) {
			continue
		}

		if math.Abs(float64(*order.TotalPrice-amount)) > 1000.0 {
			continue
		}

		exist = order
		break
	}

	// if there's no order match the condition, assign the first order to exist and then return error by every case
	if exist == nil {
		exist = result.Data.([]*model.Order)[0]
		if exist.PaymentMethod == "NORMAL" || exist.PaymentMethod == "PAYMENT_METHOD_NORMAL" || exist.TotalPrice == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Order %d invalid payment method", exist.OrderID),
				ErrorCode: "ORDER_INVALID",
			}
		}

		if exist.Status != enum.OrderState.WaitConfirm {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Order %d status invalid", exist.OrderID),
				ErrorCode: "ORDER_CONFIRMED",
			}
		}

		if !isValidOrderHoldTime(exist) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Order %d hold time is over", exist.OrderID),
				ErrorCode: "ORDER_INVALID",
			}
		}

		if math.Abs(float64(*exist.TotalPrice-amount)) > 1000.0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Order %d price not match", exist.OrderID),
				ErrorCode: "ORDER_INVALID",
			}
		}
	}

	finalNote = fmt.Sprintf("%s - %s update: %s\n%s", time.Now().Format("2006-02-01 15:04:05"), account.Username, finalNote, exist.PrivateNote)
	finalNote = fmt.Sprintf("%s - Auto confirmed order bank\n%s", time.Now().Format("2006-02-01 15:04:05"), finalNote)
	t := time.Now()

	updater := &model.Order{
		PrivateNote:      finalNote,
		OrderCode:        "",
		OrderID:          0,
		ConfirmationDate: &t,
		Status:           enum.OrderState.Confirmed,
		TransferAmount:   &amount,
	}

	updateResult := model.OrderDB.UpdateOne(&model.Order{
		OrderID: exist.OrderID,
	}, updater)

	if updateResult.Status == common.APIStatus.Ok {
		// khong the tao bill vi chua du thong tin SO
		orderResult := updateResult.Data.([]*model.Order)[0]
		_ = model.CreateSaleOrderJob.Push(createSaleOrderData{OrderID: orderResult.OrderID, SenderName: updateData.SenderName}, &job.JobItemMetadata{
			UniqueKey: fmt.Sprint(orderResult.OrderID),
			Keys: []string{
				strconv.Itoa(int(orderResult.OrderID)),
			},
			SortedKey: fmt.Sprint(orderResult.OrderID),
			Topic:     "default",
		})

		// score mission
		go func() {
			if orderResult.Items == nil || len(orderResult.Items) == 0 {
				orderItemDB := model.GetOrderItemPartitionDB(orderResult, "BulkOrderConfirm")
				qOrderItemUpdated := orderItemDB.Query(model.OrderItem{OrderID: orderResult.OrderID}, 0, 0, &primitive.M{"_id": -1})
				if qOrderItemUpdated.Status == common.APIStatus.Ok {
					orderResult.Items = qOrderItemUpdated.Data.([]*model.OrderItem)
				}
			}
			client.Services.Promotion.ScoreMission("Update order status "+string(orderResult.Status), orderResult)
		}()
	}

	return updateResult
}

// WebhookHandler
func WebhookHandler(updateData *model.OrderUpdateNoteRequest) *common.APIResponse {
	if strings.Contains(updateData.Note, "REC") {
		// Check reconcile hub - company
		updateData.Note = getReconcileCode(updateData.Note)
		if updateData.Note == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Format sms invalid, description has failed",
				ErrorCode: "SMS_INVALID",
			}
		}
		// forward msg to accounting reconcile
		return client.Services.Reconcile.SendReconcileBankMessage(updateData.Note, int(updateData.BalanceAmount))
	}

	if updateData.BalanceAmount < 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Amount must be greater than zero",
			ErrorCode: "AMOUNT_INVALID",
		}
	}

	needCreatePayment := true
	if updateData.SenderName == "VPB" || updateData.SenderName == "VPBANK_ADAPTER" {
		// Need create payment if master account number is empty or in list NEED_CREATE_PM_BANK_ACCOUNTS
		needCreatePayment = updateData.MasterAccountNumber == "" ||
			utils.Contains(updateData.MasterAccountNumber, NEED_CREATE_PM_BANK_ACCOUNTS)
	}

	var query *model.Order
	if updateData.SenderName != "ONEPAY_ADAPTER" {
		// regSubOrderID := `([0-9]{7,9})([\s|,|_])`
		regSubOrderID := `([\s|\S|_|,|-|.])([0-9]{7,11})([\s|\S|,|_|-|.])`
		reSubOrderID := regexp.MustCompile(regSubOrderID)
		strs := reSubOrderID.FindAllString(updateData.Note, -1)
		if len(strs) < 1 {
			if needCreatePayment {
				client.Services.Bill.CreatePayment(0, updateData.BalanceAmount, 0, updateData.Note, updateData.SenderName, updateData.TransactionId)
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Format sms invalid, not match order id",
				ErrorCode: "SMS_INVALID",
			}
		}

		arrOrderIDs := make([]int, 0)

		NDIndex := strings.Index(updateData.Note, "ND")

		for _, str := range strs {
			// orderStrID := strings.TrimSpace(str)
			// orderStrID = strings.Replace(orderStrID, "_", "", -1)
			// orderStrID = strings.Replace(orderStrID, ",", "", -1)

			orderStrIndex := strings.Index(updateData.Note, str)

			if NDIndex > 0 && orderStrIndex > 0 && orderStrIndex < NDIndex {
				// example: NHAN TU ******** TRACE 519293 ND ******** QR CHUYEN KHOAN 92834 => skip ********, 519293 because its indexes is smaller than index of ND
				continue
			}

			orderStrID := strings.NewReplacer(",", "", "\t", "", ".", "", "_", "", "-", "").Replace(strings.TrimSpace(str))
			re := regexp.MustCompile(`\d+`)
			orderStrID = re.FindString(orderStrID)

			// make sure order id is not start with 0
			if strings.HasPrefix(orderStrID, "0") {
				continue
			}

			orderID, err := strconv.Atoi(orderStrID)

			if err != nil {
				continue
			}
			if orderID < 1000000 {
				continue
			}

			// skip bank code of company
			if orderID == ********* || orderID == ********* || orderID == ********* {
				continue
			}
			arrOrderIDs = append(arrOrderIDs, orderID)
		}

		if len(arrOrderIDs) <= 0 {
			if needCreatePayment {
				client.Services.Bill.CreatePayment(0, updateData.BalanceAmount, 0, updateData.Note, updateData.SenderName, updateData.TransactionId)
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Format sms invalid, not match order id",
				ErrorCode: "SMS_INVALID",
			}
		}

		query = &model.Order{
			ComplexQuery: []*bson.M{
				{
					"order_id": bson.M{
						"$in": arrOrderIDs,
					},
				},
			},
		}
	} else if updateData.SenderName == "ONEPAY_ADAPTER" && updateData.OrderID > 0 {
		query = &model.Order{
			OrderID: updateData.OrderID,
		}
	}
	if query == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "data invalid, not match order id",
			ErrorCode: "QUERY_INVALID",
		}
	}

	// try to find the best match order
	var exist *model.Order = nil
	matchOrdersResult := model.OrderDB.Query(query, 0, 100, &bson.M{"_id": -1})

	if matchOrdersResult.Status != common.APIStatus.Ok {
		if needCreatePayment {
			client.Services.Bill.CreatePayment(0, updateData.BalanceAmount, 0, updateData.Note, updateData.SenderName, updateData.TransactionId)
		}
		return matchOrdersResult
	}

	// check order by condition: PAYMENT METHOD -> STATUS -> HOLD TIME -> PRICE
	if matchOrdersResult.Status == common.APIStatus.Ok {
		for _, order := range matchOrdersResult.Data.([]*model.Order) {
			if order.PaymentMethod == "NORMAL" || order.PaymentMethod == "PAYMENT_METHOD_NORMAL" || order.TotalPrice == nil {
				continue
			}

			if order.Status != enum.OrderState.WaitConfirm {
				continue
			}

			if !isValidOrderHoldTime(order) {
				continue
			}

			if math.Abs(float64(*order.TotalPrice-int(updateData.BalanceAmount))) > 1000.0 {
				continue
			}

			// assign the best match order to exist
			exist = order
			break
		}
	}

	// if there's no order match the condition, assign the first order to exist and then return error by every case
	if exist == nil {
		exist = matchOrdersResult.Data.([]*model.Order)[0]
		if exist.PaymentMethod == "NORMAL" || exist.PaymentMethod == "PAYMENT_METHOD_NORMAL" || exist.TotalPrice == nil {
			if needCreatePayment {
				client.Services.Bill.CreatePayment(exist.OrderID, updateData.BalanceAmount, exist.CustomerID, updateData.Note, updateData.SenderName, updateData.TransactionId)
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Order %d invalid payment method", exist.OrderID),
				ErrorCode: "ORDER_INVALID",
			}
		}

		if exist.Status != enum.OrderState.WaitConfirm {
			if updateData.SenderName == "ONEPAY_ADAPTER" {
				updateData.Note = fmt.Sprintf("%s - KH thanh toán nhiều lần", updateData.Note)
			}
			if needCreatePayment {
				client.Services.Bill.CreatePayment(exist.OrderID, updateData.BalanceAmount, exist.CustomerID, updateData.Note, updateData.SenderName, updateData.TransactionId)
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Order %d status invalid", exist.OrderID),
				ErrorCode: "ORDER_CONFIRMED",
			}
		}

		// there's order which has WAIT_TO_CONFIRM status, payment method BANK, but it was created for a long time
		// => we should check if it's still in hold time or not
		if !isValidOrderHoldTime(exist) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Order %d hold time is over", exist.OrderID),
				ErrorCode: "ORDER_INVALID",
			}
		}

		if math.Abs(float64(*exist.TotalPrice-int(updateData.BalanceAmount))) > 1000.0 {
			if needCreatePayment {
				client.Services.Bill.CreatePayment(exist.OrderID, updateData.BalanceAmount, exist.CustomerID, updateData.Note, updateData.SenderName, updateData.TransactionId)
			}
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Order %d price not match", exist.OrderID),
				ErrorCode: "ORDER_INVALID",
			}
		}
	}

	finalNote := updateData.Note
	finalNote = fmt.Sprintf("%s - %s update: %s\n%s", time.Now().Format("2006-02-01 15:04:05"), updateData.SenderName, finalNote, exist.PrivateNote)
	finalNote = fmt.Sprintf("%s - Auto confirmed order bank\n%s", time.Now().Format("2006-02-01 15:04:05"), finalNote)
	t := time.Now()
	transferAmount := int(updateData.BalanceAmount)

	updater := &model.Order{
		PrivateNote:      finalNote,
		OrderCode:        "",
		OrderID:          0,
		ConfirmationDate: &t,
		Status:           enum.OrderState.Confirmed,
		TransferAmount:   &transferAmount,
	}

	if IsContainsT(PARTNER_PAYMENT_METHOD, (exist.PaymentMethod)) {
		updater.PartnerPaymentStatus = enum.PartnerPaymentStatus.PAID
	}

	updateResult := model.OrderDB.UpdateOne(&model.Order{
		OrderID: exist.OrderID,
	}, updater)

	if updateResult.Status == common.APIStatus.Ok {
		// khong the tao bill vi chua du thong tin SO
		orderResult := updateResult.Data.([]*model.Order)[0]
		_ = model.CreateSaleOrderJob.Push(createSaleOrderData{OrderID: orderResult.OrderID, SenderName: updateData.SenderName, TransactionId: updateData.TransactionId, MasterAccountNumber: updateData.MasterAccountNumber}, &job.JobItemMetadata{
			UniqueKey: fmt.Sprint(orderResult.OrderID),
			Keys: []string{
				strconv.Itoa(int(orderResult.OrderID)),
			},
			SortedKey: fmt.Sprint(orderResult.OrderID),
			Topic:     "default",
		})

		// score mission
		go func() {
			if orderResult.Source == nil || *orderResult.Source != enum.Source.THUOCSI_MOBILE {
				sendZNSOrderConfirmed(orderResult.OrderID, orderResult.CustomerID, orderResult.CreatedTime)
			}
			sendNotifyOrderConfirmed(orderResult.OrderID, orderResult.CustomerID)
			updateStatusOrderSeller(
				orderResult.OrderID,
				&model.OrderUpdateStatus{
					Status:           enum.OrderState.Confirmed,
					ConfirmationDate: &t,
				}, orderResult.SaleOrderCode,
			)
			if orderResult.Items == nil || len(orderResult.Items) == 0 {
				orderItemDB := model.GetOrderItemPartitionDB(orderResult, "BulkOrderConfirm")
				qOrderItemUpdated := orderItemDB.Query(model.OrderItem{OrderID: orderResult.OrderID}, 0, 0, &primitive.M{"_id": -1})
				if qOrderItemUpdated.Status == common.APIStatus.Ok {
					orderResult.Items = qOrderItemUpdated.Data.([]*model.OrderItem)
				}
			}
			client.Services.Promotion.ScoreMission("Update order status "+string(orderResult.Status), orderResult)
		}()
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
	}
}

type createTicketHoldOrder struct {
	OrderCode string `json:"orderCode,omitempty" bson:"order_code,omitempty"`
}

func OrderRequestProcessByCustomer(acc *model.Account, input *model.OrderProcessRequestFromClient) *common.APIResponse {
	orderResp := model.OrderDB.QueryOne(&model.Order{OrderID: input.OrderID, AccountID: acc.AccountID})
	if orderResp.Status != common.APIStatus.Ok {
		return orderResp
	}
	now := time.Now()
	order := orderResp.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "OrderRequestProcessByCustomer")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	oderItemResp := orderItemPartitionDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
	if oderItemResp.Status != common.APIStatus.Ok {
		return oderItemResp
	}
	order.Items = oderItemResp.Data.([]*model.OrderItem)
	checkHoldOrderConfig(order)
	if order.CanClickCancelButton && input.Action == enum.HoldOrder.RequestCancel {
		updateData := &model.Order{
			RequestProcessAction:      enum.HoldOrder.RequestCancel,
			RequestProcessUpdatedTime: &now,
			PrivateNote:               order.PrivateNote + fmt.Sprintf("\n customer-%s-at-%s", input.Action, now),
		}
		if order.RequestProcessCreatedTime == nil {
			updateData.RequestProcessCreatedTime = &now
		}
		return model.OrderDB.UpdateOne(&model.Order{OrderID: order.OrderID}, updateData)
	}

	if order.CanClickHoldButton && input.Action == enum.HoldOrder.Hold {

		warehouseResp := client.Services.Warehouse.RequestProcessing(&model.OrderProcessRequestToWms{
			OrderID:        order.OrderID,
			SaleOrderCode:  order.SaleOrderCode,
			ActionBy:       acc.AccountID,
			ActionByName:   acc.Username,
			RequestFrom:    "MARKETPLACE",
			Action:         input.Action,
			AdditionalTime: utils.ParseInt64ToPointer(order.HoldOrderProcessingTime * 3600),
		})
		if warehouseResp.Status == common.APIStatus.Ok {
			updateData := &model.Order{
				OrderID:       0,
				OrderCode:     "",
				SaleOrderCode: "",
			}
			if order.RequestProcessCreatedTime == nil {
				updateData.RequestProcessCreatedTime = &now
			}
			updateData.RequestProcessAction = input.Action
			updateData.RequestProcessUpdatedTime = &now
			updateData.PrivateNote = order.PrivateNote + fmt.Sprintf("\n customer-%s-at-%s", input.Action, now)
			updateData.RequestProcessHoldCount = order.RequestProcessHoldCount + 1

			// push job tự tạo ticket nếu order chưa được xử lý
			if order.RequestProcessHoldCount == 0 {
				model.CreateTicketCSHoldOrderJob.Push(createTicketHoldOrder{
					OrderCode: order.OrderCode,
				}, &job.JobItemMetadata{
					Topic:     "default",
					Keys:      []string{"HOLD_ORDER", fmt.Sprintf("%d", order.OrderID)},
					ReadyTime: utils.ParseTimeToPointer(now.Add(time.Hour * time.Duration(order.HoldOrderCreateTicketTime))),
				})
			}
			_ = model.OrderDB.UpdateOne(&model.Order{OrderID: order.OrderID}, updateData)
		}
		return warehouseResp
	}
	if order.CanClickActiveButton && input.Action == enum.HoldOrder.Active {
		warehouseResp := client.Services.Warehouse.RequestProcessing(&model.OrderProcessRequestToWms{
			OrderID:       order.OrderID,
			SaleOrderCode: order.SaleOrderCode,
			ActionBy:      acc.AccountID,
			ActionByName:  acc.Username,
			RequestFrom:   "MARKETPLACE",
			Action:        input.Action,
		})
		if warehouseResp.Status == common.APIStatus.Ok {
			updateData := &model.Order{
				OrderID:       0,
				OrderCode:     "",
				SaleOrderCode: "",
			}
			if order.RequestProcessCreatedTime == nil {
				updateData.RequestProcessCreatedTime = &now
			}
			updateData.RequestProcessAction = input.Action
			updateData.RequestProcessUpdatedTime = &now
			updateData.PrivateNote = order.PrivateNote + fmt.Sprintf("\ncustomer-%s-%s", input.Action, now)
			_ = model.OrderDB.UpdateOne(&model.Order{OrderID: order.OrderID}, updateData)
		}
		return warehouseResp
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Thao tác không hợp lệ",
	}
}

func checkHoldOrderConfig(order *model.Order) {
	configResp := model.HoldOrderConfigDB.Query(&model.HoldOrderConfig{}, 0, 0, nil)
	if configResp.Status != common.APIStatus.Ok {
		return
	}
	configs := configResp.Data.([]*model.HoldOrderConfig)
	configMap := make(map[enum.HoldOrderValue]*model.HoldOrderConfig)
	percent, totalReservedAmount := getPercentageAndTotalAmountReservedItems(order)
	zero := int64(0)
	for _, config := range configs {
		if config.DisplayTime == nil {
			config.DisplayTime = &zero
		}
		if config.DisplayRatio == nil {
			config.DisplayRatio = &zero
		}
		if config.OpenAgainTime == nil {
			config.OpenAgainTime = &zero
		}
		if config.IsActive == nil {
			config.IsActive = utils.ParseBoolToPointer(false)
		}
		if config.ProcessingTime == nil {
			config.ProcessingTime = &zero
		}
		if config.CreatedTicketTime == nil {
			config.CreatedTicketTime = &zero
		}
		configMap[enum.HoldOrderValue(config.HoldOrderCode)] = config
	}

	order.HoldOrderProcessingTime = *configMap[enum.HoldOrder.Hold].ProcessingTime      // dùng trong OrderRequestProcessByCustomer
	order.HoldOrderCreateTicketTime = *configMap[enum.HoldOrder.Hold].CreatedTicketTime // dùng trong OrderRequestProcessByCustomer
	now := time.Now()

	if configMap[enum.HoldOrder.Hold] != nil &&
		*configMap[enum.HoldOrder.Hold].IsActive &&
		order.Status == enum.OrderState.Confirmed &&
		now.Sub(*order.CreatedTime).Hours() >= float64(*configMap[enum.HoldOrder.Hold].DisplayTime) &&
		percent < 100 &&
		order.RequestProcessHoldCount == 0 &&
		order.RequestProcessAction != enum.HoldOrder.RequestCancel &&
		order.RequestProcessAction != enum.HoldOrder.Active {
		order.CanClickHoldButton = true
	}
	if configMap[enum.HoldOrder.Hold] != nil &&
		*configMap[enum.HoldOrder.Hold].IsActive &&
		order.Status == enum.OrderState.Confirmed &&
		percent < 100 &&
		order.RequestProcessHoldCount > 0 &&
		order.RequestProcessAction == enum.HoldOrder.Hold &&
		order.RequestProcessUpdatedTime.Sub(*order.RequestProcessCreatedTime).Hours() >= float64(*configMap[enum.HoldOrder.Hold].OpenAgainTime) {
		order.CanClickHoldButton = true
	}
	if configMap[enum.HoldOrder.Active] != nil &&
		*configMap[enum.HoldOrder.Active].IsActive &&
		order.Status == enum.OrderState.Confirmed &&
		percent < 100 &&
		order.RequestProcessAction != enum.HoldOrder.Active &&
		order.RequestProcessAction != enum.HoldOrder.RequestCancel &&
		now.Sub(*order.CreatedTime).Hours() >= float64(*configMap[enum.HoldOrder.Active].DisplayTime) &&
		totalReservedAmount >= 1000000 {
		order.CanClickActiveButton = true
	}
	if configMap[enum.HoldOrder.RequestCancel] != nil &&
		*configMap[enum.HoldOrder.RequestCancel].IsActive &&
		now.Sub(*order.CreatedTime).Hours() >= float64(*configMap[enum.HoldOrder.RequestCancel].DisplayTime) &&
		order.RequestProcessAction != enum.HoldOrder.RequestCancel &&
		percent >= float64(*configMap[enum.HoldOrder.RequestCancel].DisplayRatio) {
		order.CanClickCancelButton = true
	}
}
func getPercentageAndTotalAmountReservedItems(order *model.Order) (float64, int) {
	total := 0
	totalReservedAmount := 0
	if order.TotalQuantity != nil {
		total = *order.TotalQuantity
	}
	totalReservedQuantity := 0
	for _, item := range order.Items {
		if item.ReservedQuantity != nil {
			totalReservedQuantity += *item.ReservedQuantity
			totalReservedAmount += item.Price * *item.ReservedQuantity
		}
	}
	if total == 0 || totalReservedQuantity == 0 {
		return 0, totalReservedAmount
	}
	return float64(totalReservedQuantity) / float64(total) * 100, totalReservedAmount
}

func CreateTicketCSHoldOrder(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var orderJob createTicketHoldOrder
	err = bson.Unmarshal(data, &orderJob)
	if err != nil {
		return err
	}

	orderResp := model.OrderDB.QueryOne(&model.Order{OrderCode: orderJob.OrderCode})
	if orderResp.Status != common.APIStatus.Ok {
		return nil
	}
	order := orderResp.Data.([]*model.Order)[0]
	if order.RequestProcessAction == enum.HoldOrder.Hold &&
		order.Status == enum.OrderState.Confirmed {
		if ticketResp := client.Services.Ticket.CreateTicketCSHoldOrderForCustomer(order); ticketResp.Status != common.APIStatus.Ok {
			return fmt.Errorf(ticketResp.Message)
		}
	}
	return nil
}

func RePushSyncSaleOrder(orderIds []int64, resetSaleOrderCode bool) *common.APIResponse {
	now := time.Now()
	query := model.Order{
		Status: enum.OrderState.Confirmed,
		ComplexQuery: []*bson.M{
			{
				"$or": []*bson.M{
					{
						"sale_order_code": nil,
					},
					{
						"sale_order_code": "",
					},
				},
			},
			{
				"created_time": bson.M{
					"$gte": now.AddDate(0, 0, -30),
				},
			},
		},
	}
	if len(orderIds) > 0 {
		type unsetSaleOrderCode struct {
			SaleOrderCode *string `json:"-" bson:"sale_order_code,omitempty"`
		}
		if resetSaleOrderCode {
			if res := model.OrderDB.UpdateMany(model.Order{
				ComplexQuery: []*bson.M{
					{
						"order_id": bson.M{
							"$in": orderIds,
						},
					},
				},
			}, unsetSaleOrderCode{
				SaleOrderCode: utils.ParseStringToPointer(""),
			}); res.Status != common.APIStatus.Ok {
				return res
			}
		}
		query.ComplexQuery = []*bson.M{
			{
				"$or": []*bson.M{
					{
						"sale_order_code": nil,
					},
					{
						"sale_order_code": "",
					},
				},
			},
			{
				"order_id": bson.M{
					"$in": orderIds,
				},
			},
		}
	}
	offset, limit := int64(0), int64(1000)
	for {
		qOrder := model.OrderDB.Query(query, offset*limit, limit, &primitive.M{"created_time": -1})
		if qOrder.Status != common.APIStatus.Ok {
			break
		}
		orders := qOrder.Data.([]*model.Order)
		for _, order := range orders {
			_ = model.CreateSaleOrderJob.Push(createSaleOrderData{OrderID: order.OrderID}, &job.JobItemMetadata{
				UniqueKey: fmt.Sprint(order.OrderID),
				Keys: []string{
					strconv.Itoa(int(order.OrderID)),
				},
				SortedKey: fmt.Sprint(order.OrderID),
				Topic:     "default",
			})
		}
		offset++
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "In progress",
	}
}

// compeleted order bill
func CompleteOrderBill(orderBill *model.OrderBill) *common.APIResponse {
	if orderBill.OrderId == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing Order ID",
			ErrorCode: "MISSING_ORDER_ID",
		}
	}

	if orderBill.BillCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing Bill Code",
			ErrorCode: "MISSING_BILL_CODE",
		}
	}

	if orderBill.Status == "" || orderBill.Status != enum.BillStatus.Done {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Incorrect Bill Status",
			ErrorCode: "INCORRECT_BILL_STATUS",
		}
	}

	qResult := model.OrderBillDB.QueryOne(model.OrderBill{
		OrderId:  orderBill.OrderId,
		BillCode: orderBill.BillCode,
	})

	if qResult.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Existed,
			Message:   "Bill with code " + orderBill.BillCode + " already existed.",
			ErrorCode: "ORDER_BILL_STATUS_EXISTED",
		}
	}

	now := time.Now()
	orderBill.CreatedTime = &now
	orderBill.LastUpdatedTime = &now

	orderBillRes := model.OrderBillDB.Create(orderBill)

	if orderBillRes.Status == common.APIStatus.Ok {
		go completeOrder(int64(orderBill.OrderId))
	}

	return orderBillRes
}

func completeOrder(orderId int64) *common.APIResponse {
	orderRes := model.OrderDB.QueryOne(&model.Order{OrderID: orderId})
	if orderRes.Status != common.APIStatus.Ok {
		return orderRes
	}

	order := orderRes.Data.([]*model.Order)[0]

	if order.Status == enum.OrderState.Delivered && order.SaleOrderStatus == enum.SaleOrderStatus.Completed {
		now := time.Now()
		updateStatusRes := model.OrderDB.UpdateOne(
			&model.Order{OrderID: order.OrderID},
			&model.Order{
				Status:            enum.OrderState.Completed,
				CompletedDebtTime: &now,
			},
		)
		if updateStatusRes.Status == common.APIStatus.Ok {
			completeOrderSeller(order.OrderID)

			readyTime := time.Now().Add(30 * time.Minute)
			_ = model.CompleteOrderJobExecutor.Push(
				order,
				&job.JobItemMetadata{
					Keys: []string{
						TopicCompleteOrder,
						strconv.Itoa(int(order.OrderID)),
					},
					Topic:     "default",
					ReadyTime: &readyTime,
				},
			)
		}
		return updateStatusRes
	}

	readyTime := time.Now().Add(5 * time.Minute)
	_ = model.ReCheckCompletedOrderJob.Push(
		CheckCompletedOrder{
			OrderId: order.OrderID,
		},
		&job.JobItemMetadata{
			Keys: []string{
				TopicCompleteOrder,
				strconv.Itoa(int(order.OrderID)),
			},
			Topic:     "default",
			ReadyTime: &readyTime,
		},
	)

	return orderRes
}

func getOrderBill(orderId int64) (*model.OrderBill, *common.APIResponse) {
	orderBillRes := model.OrderBillDB.QueryOne(&model.OrderBill{OrderId: orderId})
	if orderBillRes.Status != common.APIStatus.Ok {
		return nil, orderBillRes
	}

	return orderBillRes.Data.([]*model.OrderBill)[0], nil
}

func convertOrderToCart(order *model.Order) *model.Cart {
	if order.Price == nil {
		order.Price = utils.ParseIntToPointer(0)
	}
	cart := &model.Cart{
		CartID:                  order.OrderID,
		CartNo:                  order.OrderCode,
		AccountID:               order.AccountID,
		CustomerID:              order.CustomerID,
		CustomerCode:            order.CustomerCode,
		CustomerName:            order.CustomerName,
		CustomerPhone:           order.CustomerPhone,
		CustomerEmail:           order.CustomerEmail,
		CustomerShippingAddress: order.CustomerShippingAddress,
		CustomerDistrictCode:    order.CustomerDistrictCode,
		CustomerWardCode:        order.CustomerWardCode,
		CustomerProvinceCode:    order.CustomerProvinceCode,
		CustomerRegionCode:      order.CustomerRegionCode,
		PaymentMethod:           order.PaymentMethod,
		DeliveryMethod:          order.DeliveryMethod,
		Source:                  order.Source,
		Note:                    order.Note,
		RedeemCode:              order.RedeemCode,
		Invoice:                 order.Invoice,
		ProvinceCode:            order.ProvinceCode,
		DistrictCode:            order.DistrictCode,
		WardCode:                order.WardCode,
		CanExportInvoice:        order.CanExportInvoice,
		Price:                   *order.Price,
		ValidateOrder:           utils.ParseBoolToPointer(true),
	}
	if cart.RedeemCode == nil {
		cart.RedeemCode = &[]*string{}
	}
	for _, orderItem := range order.Items {
		if orderItem.Type == enum.ItemType.GIFT {
			continue
		}
		cartItem := &model.CartItem{
			CartID:                 orderItem.OrderID,
			CartNo:                 orderItem.OrderCode,
			Sku:                    orderItem.Sku,
			Quantity:               orderItem.Quantity,
			MaxQuantity:            orderItem.MaxQuantity,
			IsImportant:            orderItem.IsImportant,
			Type:                   orderItem.Type,
			DealCode:               orderItem.DealCode,
			SellerCode:             orderItem.SellerCode,
			Price:                  orderItem.Price,
			TotalPrice:             orderItem.TotalPrice,
			Fee:                    orderItem.Fee,
			SellerPrice:            orderItem.SellerPrice,
			SellerRevenue:          orderItem.SellerRevenue,
			ProductCode:            orderItem.ProductCode,
			ProductID:              orderItem.ProductID,
			VAT:                    orderItem.VAT,
			NoneVAT:                orderItem.NoneVat,
			Skus:                   orderItem.Skus,
			SkuStatus:              orderItem.SkuStatus,
			SkuStatusData:          orderItem.SkuStatusData,
			SkuPriceType:           orderItem.SkuPriceType,
			ChargeDealFeeValue:     orderItem.ChargeDealFeeValue,
			ChargeDealFee:          orderItem.ChargeDealFee,
			DealPricingType:        orderItem.DealPricingType,
			Tags:                   orderItem.Tags,
			SkuVersion:             orderItem.SkuVersion,
			Source:                 orderItem.Source,
			SkuLevel:               orderItem.SkuLevel,
			CampaignCode:           orderItem.CampaignCode,
			CampaignPricingType:    orderItem.CampaignPricingType,
			ChargeCampaignFeeValue: orderItem.ChargeCampaignFeeValue,
			Point:                  orderItem.Point,
			PointMultiplier:        orderItem.PointMultiplier,
			SkuContractCode:        orderItem.SkuContractCode,
			SkuContractDetailCode:  orderItem.SkuContractDetailCode,
			IsSkuLimitExisted:      orderItem.IsSkuLimitExisted,
			SkuLimitQuantity:       orderItem.SkuLimitQuantity,
		}
		cart.Items = append(cart.Items, cartItem)
	}
	return cart
}

// Check regex sms is reconcile or wrong format
func checkRegexSmsReconcile(smsBody, senderName string) bool {
	// Check sms send to
	var smsRegexPattern = []string{
		`^(TK 19037091688011 So tien GD:[+])([0-9,[,]+) So du:([0-9,[,]+) ([A-Za-z0-9 _-|.|()\/].*)`,
		`^(TK 19036607509031 So tien GD:[+])([0-9,[,]+) So du:([0-9,[,]+) ([A-Za-z0-9 _-|.|()\/].*)`,
	}
	if senderName == "VPB" {
		smsRegexPattern = []string{
			`^TK ********* tai VPB (\+[\d,]+VND) luc \d{2}:\d{2} \d{2}/\d{2}/\d{2}\. (So du [\d,]+VND)\. ND: ([A-Za-z0-9 _-|.|()\/].*)`,
			`^TK ********* tai VPB (\+[\d,]+VND) luc \d{2}:\d{2} \d{2}/\d{2}/\d{2}\. (So du [\d,]+VND)\. ND: ([A-Za-z0-9 _-|.|()\/].*)`,
		}
	}
	for _, smsPattern := range smsRegexPattern {
		reCheckPattern := regexp.MustCompile(smsPattern)
		if reCheckPattern.MatchString(smsBody) {
			return true
		}
	}

	return false
}

// Get true reconcile code from sms body...
func getReconcileCode(transferDescription string) (code string) {
	// get index of Reconcile data
	transferDescription = transferDescription[strings.Index(transferDescription, "REC")+3:]
	transferDescription = strings.ReplaceAll(transferDescription, "_", " ")
	transferDescription = strings.ReplaceAll(transferDescription, "-", " ")
	transferDescription = strings.ReplaceAll(transferDescription, ",", " ")
	transferDescription = strings.ReplaceAll(transferDescription, ".", " ")
	notes := strings.Split(transferDescription, " ")
	if len(notes) > 0 {
		for _, ele := range notes {
			ele = strings.ReplaceAll(ele, " ", "")
			if ele != "" {
				code = ele
				break
			}
		}
	}
	return
}

func SearchOrderByOrderID(account *model.Account, searchQuery *model.SearchOrderQuery, offset, limit int64, getTotal bool) *common.APIResponse {
	var query model.Order
	var queryOrderSeller model.OrderSeller
	queryType := "ORDER"

	switch account.Type {
	case enum.AccountType.EMPLOYEE, enum.AccountType.PARTNER:
		{
			if searchQuery.SellerCode != "" {
				queryType = "ORDER_SELLER"
				queryOrderSeller.SellerCode = searchQuery.SellerCode

				if searchQuery.CustomerID != 0 {
					queryOrderSeller.CustomerID = searchQuery.CustomerID
				}

			} else if searchQuery.CustomerID != 0 {
				query.CustomerID = searchQuery.CustomerID
			}

		}

	case enum.AccountType.CUSTOMER:
		{
			customer, errCustomer := getCustomerProfile(account)
			if errCustomer != nil {
				return errCustomer
			}
			// fill to query
			query.CustomerID = customer.CustomerID

			if searchQuery.SellerCode != "" {
				queryType = "ORDER_SELLER"
				queryOrderSeller.SellerCode = searchQuery.SellerCode
			}
		}

	case enum.AccountType.SELLER:
		{
			queryType = "ORDER_SELLER"
			seller, errSeller := getSellerInfo(account)
			if errSeller != nil {
				return errSeller
			}
			queryOrderSeller.SellerCode = seller.Code

			if searchQuery.CustomerID != 0 {
				queryOrderSeller.CustomerID = searchQuery.CustomerID
			}

		}

	}

	if queryType == "ORDER_SELLER" {
		return client.Services.SellerMis.SearchOrderSeller(&model.SearchOrderQuery{
			CustomerID:  queryOrderSeller.CustomerID,
			SellerCode:  queryOrderSeller.SellerCode,
			Search:      searchQuery.Search,
			AccountType: account.Type,
		}, offset, limit, getTotal)
	}

	if searchQuery.Search != "" {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{"matchedIndex": primitive.M{"$gte": 0}})
	}

	var orderData []*model.Order
	result := model.OrderDB.Aggregate([]primitive.M{
		{
			"$addFields": primitive.M{
				"matchedIndex": primitive.M{
					"$indexOfCP": []primitive.M{
						{
							"$toString": "$order_id",
						},
						{
							"$toString": searchQuery.Search,
						},
					},
				},
			},
		},
		{
			"$match": query,
		},
		{"$sort": primitive.M{"matchedIndex": 1, "order_id": -1}},
		{"$skip": offset},
		{"$limit": limit},
	}, &orderData)

	if len(orderData) <= 0 || result.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Order not found",
			ErrorCode: "ORDER_NOT_FOUND",
		}
	}

	if getTotal {
		type total struct {
			Count int64 `json:"-" bson:"count,omitempty"`
		}
		totalRes := make([]*total, 0)
		model.OrderDB.Aggregate([]primitive.M{
			{
				"$addFields": primitive.M{
					"matchedIndex": primitive.M{
						"$indexOfCP": []primitive.M{
							{
								"$toString": "$order_id",
							},
							{
								"$toString": searchQuery.Search,
							},
						},
					},
				},
			},
			{
				"$match": query,
			},
			{"$group": bson.M{"_id": nil, "count": bson.M{"$sum": 1}}},
		}, &totalRes)
		result.Total = totalRes[0].Count
	}

	result.Data = orderData
	//result.Total = int64(len(orderData))

	return result

}

func OrderBankToCredit(input *model.MigratePaymentMethodRequest) *common.APIResponse {
	customer, err := client.Services.Customer.GetCustomerByCustomerID(input.CustomerID)
	if err != nil {
		return &common.APIResponse{
			Message:   err.Error(),
			Status:    common.APIStatus.Invalid,
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}
	if customer.TagMap != nil && customer.TagMap[string(enum.CustomerTag.VipDebt)] {
		return PaymentMethodCredit_CustomerColorError
	}
	go func() {
		for {
			orderResp := model.OrderDB.QueryOne(model.Order{
				CustomerID:    customer.CustomerID,
				PaymentMethod: string(enum.PaymentMethod.BANK),
				ComplexQuery: []*bson.M{
					{
						"created_time": bson.M{
							"$gt": input.ContractStartTime,
						},
					},
					{
						"status": bson.M{
							"$nin": []string{string(enum.OrderState.Canceled), string(enum.OrderState.Completed)},
						},
					},
				},
			})
			if orderResp.Status == common.APIStatus.Ok {
				order := orderResp.Data.([]*model.Order)[0]
				model.OrderDB.UpdateOne(model.Order{OrderID: order.OrderID}, model.Order{PaymentMethod: string(enum.PaymentMethod.CREDIT)})
			} else {
				break
			}
		}
	}()
	return &common.APIResponse{
		Message: "Success",
		Status:  common.APIStatus.Ok,
	}
}

func GetOrderWithWaitConfirmStatusAndWarehouseInfoEmpty() ([]*model.Order, error) {
	limit := int64(1000)
	offset := int64(0)
	orders := make([]*model.Order, 0)

	for {
		resp := model.OrderDB.Query(&model.Order{
			Status:        enum.OrderState.WaitConfirm,
			WarehouseCode: "",
		}, offset*limit, limit, nil)
		offset++

		if resp.Status != common.APIStatus.Ok {
			break
		}

		orders = append(orders, resp.Data.([]*model.Order)...)
	}

	return orders, nil
}

func SyncOrderDetail(order model.Order) {

	if order.OrderID == 0 || order.OrderCode == "" {
		return
	}

	updater := model.OrderDetail{}
	updater.AccountID = order.AccountID
	updater.CustomerID = order.CustomerID
	updater.CustomerCode = order.CustomerCode
	updater.CreatedTime = order.CreatedTime

	orderItems := getOrderItems(order.OrderID)
	if len(orderItems) > 0 {
		sellerCodes, skus, itemCodes, productIds, dealCodes, subSkus, subProductIds := GetOrderDistinctInfo(orderItems)
		updater.SellerCodes = sellerCodes
		updater.Skus = skus
		updater.ItemCodes = itemCodes
		updater.ProductIds = productIds
		updater.DealCodes = dealCodes
		updater.SubSkus = &subSkus
		updater.SubProductIds = &subProductIds
	}

	model.OrderDetailDB.UpdateOne(&model.OrderDetail{
		OrderID:   order.OrderID,
		OrderCode: order.OrderCode,
	}, &updater)
}

func getOrderItems(orderId int64) []*model.OrderItem {
	orderItems := make([]*model.OrderItem, 0)
	var offset, limit int64 = 0, 500

	orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{
		OrderID: orderId,
	}, "getOrderItems")

	for {
		orderItemsRes := orderItemPartitionDB.Query(&model.OrderItem{
			OrderID: orderId,
		}, offset, limit, &primitive.M{"_id": 1})
		if orderItemsRes.Status != common.APIStatus.Ok {
			break
		}

		offset += limit

		orderItems = append(orderItems, orderItemsRes.Data.([]*model.OrderItem)...)
		if len(orderItemsRes.Data.([]*model.OrderItem)) < int(limit) {
			break
		}
	}

	return orderItems
}

func BulkOrderConfirm(account *model.Account, updateData *model.BulkOrderConfirmRequest) *common.APIResponse {

	if updateData.OrderID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order id",
			ErrorCode: "MISSING_ORDER_ID",
		}
	}

	result := model.OrderDB.QueryOne(&model.Order{OrderID: updateData.OrderID})
	if result.Status != common.APIStatus.Ok {
		return result
	}

	exist := result.Data.([]*model.Order)[0]

	// accept PaymentMethod PAYMENT_METHOD_NORMAL || NORNAL
	if exist.PaymentMethod != "PAYMENT_METHOD_NORMAL" && exist.PaymentMethod != "NORNAL" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Order invalid",
			ErrorCode: "ORDER_INVALID",
		}
	}

	if exist.Status != enum.OrderState.WaitConfirm {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Order status invalid",
			ErrorCode: "ORDER_CONFIRMED",
		}
	}

	t := time.Now()

	updater := &model.Order{
		OrderCode:        "",
		OrderID:          0,
		ConfirmationDate: &t,
		Status:           enum.OrderState.Confirmed,
		PrivateNote:      exist.PrivateNote + fmt.Sprintf("\n Auto confirmed order bulk %s", time.Now().Format("2006-02-01 15:04:05")),
	}
	updateResult := model.OrderDB.UpdateOne(&model.Order{
		OrderID: exist.OrderID,
	}, updater)

	if updateResult.Status == common.APIStatus.Ok {
		orderResult := updateResult.Data.([]*model.Order)[0]
		_ = model.CreateSaleOrderJob.Push(createSaleOrderData{OrderID: orderResult.OrderID}, &job.JobItemMetadata{
			UniqueKey: fmt.Sprint(orderResult.OrderID),
			Keys: []string{
				strconv.Itoa(int(orderResult.OrderID)),
			},
			SortedKey: fmt.Sprint(orderResult.OrderID),
			Topic:     "default",
		})

		// score mission
		go func() {
			//sendSMSOrderConfirmed(order.OrderID, order.CustomerID)
			if orderResult.Source == nil || *orderResult.Source != enum.Source.THUOCSI_MOBILE {
				sendZNSOrderConfirmed(orderResult.OrderID, orderResult.CustomerID, orderResult.CreatedTime)
			}
			sendNotifyOrderConfirmed(orderResult.OrderID, orderResult.CustomerID)

			updateStatusOrderSeller(
				orderResult.OrderID,
				&model.OrderUpdateStatus{
					Status:           enum.OrderState.Confirmed,
					ConfirmationDate: &t,
				}, orderResult.SaleOrderCode,
			)

			if orderResult.Items == nil || len(orderResult.Items) == 0 {
				orderItemDB := model.GetOrderItemPartitionDB(orderResult, "BulkOrderConfirm")
				qOrderItemUpdated := orderItemDB.Query(model.OrderItem{OrderID: orderResult.OrderID}, 0, 0, &primitive.M{"_id": -1})
				if qOrderItemUpdated.Status == common.APIStatus.Ok {
					orderResult.Items = qOrderItemUpdated.Data.([]*model.OrderItem)
				}
			}
			client.Services.Promotion.ScoreMission("Update order status "+string(orderResult.Status), orderResult)
		}()
	}

	return updateResult
}

func handleUpdateOrderPaymentMethod(order *model.Order) (errRes *common.APIResponse, isNeedToUpdateOrderDetail bool, mapItemGiftUpdate map[string]int) {
	var (
		locationOffset = []string{order.CustomerWardCode, order.CustomerDistrictCode, order.CustomerProvinceCode, order.CustomerRegionCode, "00"}
		feePaymentMap  = make(map[string]*client.PaymentFeeConfig)
	)

	feePaymentConfigs, err := client.Services.Pricing.GetPaymentFeeConfig()
	if err != nil {
		return DeliveryAndPaymentError, isNeedToUpdateOrderDetail, mapItemGiftUpdate
	}
	for _, fee := range feePaymentConfigs {
		feePaymentMap[fee.Code] = fee
	}

	// PAYMENT
	if fee, ok := feePaymentMap[order.PaymentMethod]; ok {

		if order.Price == nil {
			order.Price = utils.ParseIntToPointer(0)
		}

		temp := 0
		if order.Price != nil {
			temp = *order.Price
		}
		order.TotalPrice = &temp // reset total price

		// payment fee
		{

			isSkipPaymentMethodFee := false
			if order.PaymentMethod == string(enum.PaymentMethod.BANK) {
				for _, tag := range order.CustomerTags {
					if tag == "CIRCA" {
						isSkipPaymentMethodFee = true
						break
					}
				}
			}

			if !isSkipPaymentMethodFee {

				var discountPercent *float64
				// reset payment method fee and percentage before apply new value
				order.PaymentMethodPercentage = utils.ParseFloat64ToPointer(0.0)
				order.PaymentMethodFee = utils.ParseInt64ToPointer(0)

				for _, locationCode := range locationOffset {
					discountPercent = calculatePaymentMethod(locationCode, fee)
					if discountPercent != nil {
						break
					}
				}
				if discountPercent != nil {
					order.PaymentMethodPercentage = discountPercent
					feeValue := int64(float64(*order.PaymentMethodFee) - (*discountPercent*float64(*order.Price))/100)
					order.PaymentMethodFee = &feeValue
				}
			}
		}

		if order.ExtraFee != nil {
			temp := *order.TotalPrice + int(*order.ExtraFee)
			order.TotalPrice = &temp
		}

		if order.PaymentMethodFee != nil {
			temp := *order.TotalPrice + int(*order.PaymentMethodFee)
			order.TotalPrice = &temp
		}

		if order.DeliveryMethodFee != nil {
			temp := *order.TotalPrice + int(*order.DeliveryMethodFee)
			order.TotalPrice = &temp
		}
		if order.TotalDiscount != nil {
			temp := *order.TotalPrice - int(*order.TotalDiscount)
			order.TotalPrice = &temp
		}

		//if order.RedeemApplyResult != nil && len(order.RedeemApplyResult) > 0 {
		//	newResult := make([]*model.PromoApplyResult, 0)
		//	newCodes := make([]*string, 0)
		//	for _, redeem := range order.RedeemApplyResult {
		//		if redeem.PaymentMethod != "" && redeem.PaymentMethod != order.PaymentMethod {
		//			isNeedToUpdateOrderDetail = true
		//			if redeem.DiscountValue != 0 {
		//				order.TotalDiscount = utils.ParseIntToPointer(*order.TotalDiscount - redeem.DiscountValue)
		//				order.TotalPrice = utils.ParseIntToPointer(*order.TotalPrice + redeem.DiscountValue)
		//			}
		//			if redeem.Gift != nil {
		//				for _, gift := range redeem.Gift {
		//					mapItemGiftUpdate[gift.Sku] = gift.Quantity
		//				}
		//			}
		//			continue
		//		}
		//		newResult = append(newResult, redeem)
		//		newCodes = append(newCodes, &redeem.Code)
		//	}
		//	order.RedeemApplyResult = newResult
		//	order.RedeemCode = &newCodes
		//}
		currentRedeem := order.RedeemCode
		// add new redeem code
		tmpCart := model.Cart{
			CustomerID: order.CustomerID,
			CartID:     order.OrderID,
			CartNo:     order.OrderCode,
			Items: func() []*model.CartItem {
				items := make([]*model.CartItem, 0)
				if len(order.Items) == 0 {
					qOrderItem := model.GetOrderItemPartitionDB(&model.Order{OrderID: order.OrderID}, "handleUpdateOrderPaymentMethod").Query(model.OrderItem{OrderID: order.OrderID}, 0, 0, &primitive.M{"_id": -1})
					if qOrderItem.Status == common.APIStatus.Ok {
						order.Items = qOrderItem.Data.([]*model.OrderItem)
					}
				}
				for _, item := range order.Items {
					items = append(items, &model.CartItem{
						CartID:      order.OrderID,
						CartNo:      order.OrderCode,
						Sku:         item.Sku,
						ItemCode:    item.ItemCode,
						Quantity:    item.Quantity,
						Type:        item.Type,
						Price:       item.Price,
						TotalPrice:  item.TotalPrice,
						SellerCode:  item.SellerCode,
						ProductCode: item.ProductCode,
						ProductID:   item.ProductID,
						StoreCode:   item.StoreCode,
						Source:      item.Source,
						Page:        item.Page,
						Tags:        item.Tags,
						IsSelected:  utils.ParseBoolToPointer(true),
					})
				}
				return items
			}(),
			AccountID:             order.AccountID,
			Price:                 *order.Price,
			TotalPrice:            *order.Price,
			RedeemCode:            order.RedeemCode,
			RedeemApplyResult:     order.RedeemApplyResult,
			TotalItemSelected:     order.TotalItem,
			TotalQuantitySelected: order.TotalQuantity,
			Screen:                "Payment",
			PaymentMethod:         order.PaymentMethod,
			ValidateOrder:         utils.ParseBoolToPointer(true),
		}

		regions, err := client.Services.Location.GetRegionList(0, 100, []string{tmpCart.ProvinceCode})
		tmpCart.RegionCodes = []string{}
		tmpCart.SaleRegionCodes = []string{}
		if err == nil {
			for _, region := range regions {
				tmpCart.RegionCodes = append(tmpCart.RegionCodes, region.Code)
				if region.Scope == "SALE_REGION" {
					tmpCart.SaleRegionCodes = append(tmpCart.SaleRegionCodes, region.Code)
				}
			}
		}

		checkVoucherRes := handleApplyVoucherCode(&tmpCart, nil, "handleUpdateOrderPaymentMethod")
		if checkVoucherRes == nil {
			_ = handleProcessContentGiftItems(&tmpCart, &model.Customer{ProvinceCode: order.ProvinceCode, CustomerID: order.CustomerID}, nil)
			order.RedeemApplyResult = tmpCart.RedeemApplyResult
			order.RedeemCode = tmpCart.RedeemCode
			order.TotalDiscount = &tmpCart.Discount
			order.TotalPrice = &tmpCart.TotalPrice
			order.Price = &tmpCart.Price
			order.TotalItem = tmpCart.TotalItemSelected
			order.TotalQuantity = tmpCart.TotalQuantitySelected
			order.Items, _, _, _, _ = handleCartItemToOrderItem(tmpCart.Items, &tmpCart, order)
			if !reflect.DeepEqual(currentRedeem, order.RedeemCode) {
				isNeedToUpdateOrderDetail = true
			}
		}

		// calculate partner payment fee after all fee
		{
			// chỉ tính với 1 số phương thức được cài đặt
			if !IsContainsT(PARTNER_PAYMENT_METHOD, (order.PaymentMethod)) {
				order.PartnerPaymentMethod = nil
				return &common.APIResponse{
					Status:  common.APIStatus.Ok,
					Message: "Calculate payment method success",
				}, isNeedToUpdateOrderDetail, mapItemGiftUpdate
			}

			order.TotalPriceBeforePartnerPaymentFee = order.TotalPrice
			var partnerPaymentMethod *model.PartnerPaymentMethod
			for _, locationCode := range locationOffset {
				partnerPaymentMethod = calculateOnepayPaymentMethod(locationCode, *order.TotalPrice, fee)
				if partnerPaymentMethod != nil {
					break
				}
			}
			if partnerPaymentMethod != nil {
				order.PartnerPaymentMethod = partnerPaymentMethod

				if order.PartnerPaymentMethod != nil {
					if order.PartnerPaymentMethod.TotalCustomerFee > 0 {
						temp := *order.TotalPrice + int(order.PartnerPaymentMethod.TotalCustomerFee)
						order.TotalPrice = &temp
					}

					// // không tính phí này cho KH
					// if order.PartnerPaymentMethod.TotalBuymedSponsorFee > 0 {
					// 	temp := *order.TotalPrice - int(order.PartnerPaymentMethod.TotalBuymedSponsorFee)
					// 	order.TotalPrice = &temp
					// }
				}
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Calculate payment method success",
	}, isNeedToUpdateOrderDetail, mapItemGiftUpdate
}

func CustomerCreateOrderPaymentLink(acc *model.Account, requestData *model.Order, headers map[string]string) *common.APIResponse {
	if requestData.OrderCode == "" && requestData.OrderID <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order no/order id",
			ErrorCode: "MISSING_ORDER_NO",
		}
	}
	customer, err := getCustomerProfile(acc)
	if err != nil || customer == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "customer not found",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}
	orderResp := model.OrderDB.QueryOne(&model.Order{AccountID: acc.AccountID, OrderID: requestData.OrderID})
	if orderResp.Status != common.APIStatus.Ok {
		return orderResp
	}
	order := orderResp.Data.([]*model.Order)[0]

	if order.Status != enum.OrderState.WaitConfirm {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Trạng thái không hợp lệ",
			ErrorCode: "INVALID_STATUS",
		}
	}

	feePaymentMap := make(map[string]*client.PaymentFeeConfig)
	feePaymentConfigs, err := client.Services.Pricing.GetPaymentFeeConfig()
	if err != nil {
		return DeliveryAndPaymentError
	}
	for _, fee := range feePaymentConfigs {
		feePaymentMap[fee.Code] = fee
	}

	if _, ok := feePaymentMap[order.PaymentMethod]; !ok {
		return PaymentMethodError
	}

	if !IsContainsT(PARTNER_PAYMENT_METHOD, (order.PaymentMethod)) {
		order.PaymentLink = fmt.Sprintf("%s/thankyou/%d", conf.Config.FrontendURL, order.OrderID)
		return orderResp
	}

	params := map[string]interface{}{
		"orderId":    order.OrderID,
		"customerId": order.CustomerID,

		"returnUrl": fmt.Sprintf("%s/thankyou/%d", conf.Config.FrontendURL, order.OrderID),

		"paymentMethod": order.PaymentMethod,

		"totalPrice": order.TotalPrice,
	}
	paymentLinkResp := client.Services.OnepayClient.GetPaymentLink(params, headers)
	if paymentLinkResp.Status != common.APIStatus.Ok {
		return paymentLinkResp
	}

	paymentLink := paymentLinkResp.Data.([]*model.OnepayGetList)[0]
	order.PaymentLink = paymentLink.Url
	return orderResp
}

func CreateInternalOrder(input *model.CreateInternalOrderRequest, createBy *model.Account) *common.APIResponse {
	customer, errCustomerRes := client.Services.Customer.GetCustomerByCustomerID(input.CustomerId)
	if errCustomerRes != nil || customer == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy khách hàng",
			ErrorCode: "CUSTOMER_NOT_FOUND",
		}
	}

	if customer.Level == "LEVEL_BLACKLIST" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Tài khoản của bạn đã bị khóa",
			ErrorCode: "LOCKED_CUSTOMER",
		}
	}

	if customer.TagMap != nil && customer.TagMap[string(enum.CustomerTag.Ban)] {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Tài khoản của bạn đã bị khóa",
			ErrorCode: "LOCKED_CUSTOMER",
		}
	}

	if input.InternalOrderType == nil ||
		!utils.CheckExistInEnum(*input.InternalOrderType, *enum.InternalOrderType) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Loại đơn hàng không hợp lệ",
			ErrorCode: "INTERNAL_ORDER_TYPE_INVALID",
		}
	}
	now := time.Now()
	orderId, orderCode, err := model.GetOrderIdAndErr()
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Create order failed",
			ErrorCode: "CREATE_ORDER_FAILED",
		}
	}

	order := &model.Order{
		OrderID:                 orderId,
		OrderCode:               orderCode,
		AccountID:               customer.AccountID,
		CustomerID:              customer.CustomerID,
		CustomerCode:            customer.CustomerCode,
		CustomerName:            customer.Name,
		CustomerPhone:           input.DeliveryInfo.Phone,
		CustomerEmail:           &customer.Email,
		CustomerShippingAddress: input.DeliveryInfo.Address,
		CustomerDistrictCode:    input.DeliveryInfo.DistrictCode,
		CustomerWardCode:        input.DeliveryInfo.WardCode,
		CustomerProvinceCode:    input.DeliveryInfo.ProvinceCode,
		PaymentMethod:           input.PaymentMethod,
		Status:                  enum.OrderState.WaitConfirm,
		Source:                  &enum.Source.INTERNAL_PORTAL,
		ProvinceCode:            customer.ProvinceCode,
		DistrictCode:            customer.DistrictCode,
		WardCode:                customer.WardCode,
		CustomerTags:            customer.Tags,
		CustomerScope:           customer.Scope,
		CustomerLevel:           customer.Level,
		CreatedTime:             &now,
		InternalOrderType:       input.InternalOrderType,
		Tags:                    []enum.TagValue{},
		Invoice:                 input.Invoice,
		IsDropOffAtWarehouse:    input.DeliveryInfo.IsDropOffAtWarehouse,
	}
	if input.Invoice != nil {
		t := true
		order.CanExportInvoice = &t
		order.Invoice.RequestInvoice = &t
	}

	if *input.InternalOrderType == enum.InternalOrderType.CO_INTERNAL {
		order.Tags = append(order.Tags, enum.Tag.CO_INTERNAL)
	}

	if *input.InternalOrderType == enum.InternalOrderType.SO_INTERNAL {
		order.Tags = append(order.Tags, enum.Tag.SO_INTERNAL)
	}

	if *input.InternalOrderType == enum.InternalOrderType.LO_INTERNAL {
		order.Tags = append(order.Tags, enum.Tag.LO_INTERNAL)
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "CheckoutCart")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	// Validate order items
	skuCodes := make([]string, 0, len(input.OrderItems))
	for _, item := range input.OrderItems {
		if item.Sku == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Sản phẩm không hợp lệ",
				ErrorCode: "INVALID_PRODUCT",
			}
		}

		if item.Quantity == 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Số lượng không hợp lệ",
				ErrorCode: "INVALID_QUANTITY",
			}
		}

		if item.LotDates != nil && len(*item.LotDates) > 0 {
			// Merge item with the same lot date
			lotDateMap := map[string]*model.LotDates{}
			sum := 0
			for _, lotDate := range *item.LotDates {
				if lotDate.Lot == "" ||
					lotDate.ExpiredDate == nil ||
					*lotDate.ExpiredDate == "" ||
					lotDate.Quantity == 0 {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Lô date không hợp lệ",
						ErrorCode: "INVALID_LOT_DATE",
					}
				}
				key := lotDate.Lot + *lotDate.ExpiredDate
				ld, ok := lotDateMap[key]
				if !ok {
					lotDateMap[key] = &model.LotDates{
						Lot:         lotDate.Lot,
						ExpiredDate: lotDate.ExpiredDate,
						Quantity:    lotDate.Quantity,
					}
				} else {
					ld.Quantity += lotDate.Quantity
				}
				sum += lotDate.Quantity
			}
			if sum > item.Quantity {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Số lượng lô date không hợp lệ",
					ErrorCode: "INVALID_LOT_DATE",
				}
			}
			mergedLotDate := make([]model.LotDates, 0, len(lotDateMap))
			for _, ld := range lotDateMap {
				mergedLotDate = append(mergedLotDate, *ld)
			}
			item.LotDates = &mergedLotDate
		}

		skuCodes = append(skuCodes, item.Sku)
	}

	skus, resp := client.Services.Product.GetListSku(skuCodes, "00", input.CustomerId, false, "")
	if (resp != nil &&
		resp.Status != common.APIStatus.Ok) ||
		len(skus) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Sản phẩm không hợp lệ",
			ErrorCode: "INVALID_PRODUCT",
		}
	}

	skuMap := map[string]*client.ProductData{}
	for _, sku := range skus {
		if sku.SKU == nil {
			continue
		}
		if sku.Product == nil {
			continue
		}
		skuMap[sku.SKU.Code] = sku
	}

	totalOrderPrice := 0
	for _, item := range input.OrderItems {
		sku := skuMap[item.Sku]
		if sku == nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Sản phẩm không hợp lệ",
				ErrorCode: "INVALID_PRODUCT",
			}
		}
		if sku.SKU.IsCombo != nil && *sku.SKU.IsCombo {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Chưa hỗ trợ tạo đơn có hàng combo",
				ErrorCode: "INVALID_PRODUCT",
			}
		}

		if sku.SKU.SKUs != nil && len(*sku.SKU.SKUs) > 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Chưa hỗ trợ tạo đơn có hàng combo",
				ErrorCode: "INVALID_PRODUCT",
			}
		}

		item.CustomerID = customer.CustomerID
		item.OrderID = orderId
		item.OrderCode = orderCode
		item.CreatedTime = &now
		item.ProductID = sku.Product.ProductID
		item.ProductCode = sku.Product.Code
		item.LastUpdatedTime = &now
		item.TotalPrice = item.Price * item.Quantity
		item.SellerCode = sku.SKU.SellerCode
		item.Type = "NORMAL"
		totalOrderPrice += item.TotalPrice
	}
	order.TotalPrice = &totalOrderPrice

	if input.PaymentMethod == "PAYMENT_METHOD_CREDIT" {
		creditErr := handlePaymentMethodCreditForInternalOrder(order)
		if creditErr != nil {
			return resp
		}
	}

	createOrderResp := model.OrderDB.Create(order)
	if createOrderResp.Status != common.APIStatus.Ok {
		return createOrderResp
	}
	createItemResp := orderItemPartitionDB.CreateMany(input.OrderItems)
	if createItemResp.Status != common.APIStatus.Ok {
		return createItemResp
	}

	updateWarehouseInfoOfPlacedOrder(order)
	// TODO: Uncomment here when tested properly
	//if *input.InternalOrderType == enum.InternalOrderType.SO_INTERNAL {
	//	triggerCreateOrderSeller(order.OrderID)
	//}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
	}
}

func handlePaymentMethodCreditForInternalOrder(order *model.Order) *common.APIResponse {
	debtContractResp, err := client.Services.AccountingDebt.CheckDebtContractByCustomerID(order.CustomerID)
	if err != nil {
		return PaymentMethodCredit_DebtContractNotFound
	}
	totalPrice := int64(0)
	if order.TotalPrice != nil {
		totalPrice = int64(*order.TotalPrice)
	}
	debtContract := debtContractResp[0]
	if !debtContract.IsValid {
		return PaymentMethodCredit_NotAllowAnotherMethod
	}
	isAllow := client.Services.AccountingDebt.CheckoutOrder(&client.CheckoutOrderInput{
		OrderCode:   order.OrderCode,
		OrderID:     order.OrderID,
		CustomerID:  order.CustomerID,
		OrderAmount: totalPrice,
		RequestID:   time.Now().UnixNano(),
	})
	if isAllow.Status != common.APIStatus.Ok {
		return isAllow
	}
	return nil
}

// GetOrderList is func get list order have pagination
func OrderGetTotalPrice(account *model.Account, query *model.Order) *common.APIResponse {

	switch account.Type {

	case enum.AccountType.BRAND_SALES:
		{
			query.Source = &enum.Source.BRAND_PORTAL
			query.CreatedByAccountID = account.AccountID
			dateFrom := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
			if query.DateFrom != nil {
				dateFrom = *query.DateFrom
			}
			dateTo := time.Now()
			if query.DateTo != nil {
				dateTo = *query.DateTo
			}
			_id_offset := primitive.NilObjectID
			var totalPriceByCreatedAccount int64
			var offset, limit int64 = 0, 500
			for {
				query.ComplexQuery = []*primitive.M{
					{"_id": primitive.M{"$gt": _id_offset}},
					{
						"created_time": primitive.M{
							"$gt":  dateFrom,
							"$lte": dateTo,
						},
					},
				}

				result := model.OrderDB.Query(query, offset, limit, &primitive.M{"_id": 1})
				if result.Status != common.APIStatus.Ok {
					break
				}
				orders := result.Data.([]*model.Order)
				for _, order := range orders {
					_id_offset = *order.ID
					if order.TotalPrice != nil {
						totalPriceByCreatedAccount = totalPriceByCreatedAccount + int64(*order.TotalPrice)
					}
				}
			}
			// create struct TotalResult with field total
			type TotalResult struct {
				Total int64 `json:"total"`
			}
			var totalResult = &TotalResult{
				Total: totalPriceByCreatedAccount,
			}
			return &common.APIResponse{
				Status: common.APIStatus.Ok,
				Data:   []interface{}{totalResult},
			}
		}

	default:
		{
			return &common.APIResponse{
				Status:    common.APIStatus.Forbidden,
				Message:   "Thao tác không xác định, vui lòng kiểm tra lại",
				ErrorCode: "NOT_FOUND",
			}
		}
	}
}

func getDeliveryTimeFromWarehouse(provinceCode, districtCode, wardCode string, customerID int64) (deliveryTime *time.Time) {
	now := time.Now()
	if seconds, ok := deliverySecondsByLocationCode.Get(wardCode); ok && seconds.(int) > 0 {
		deliveryTime = &now
		deliveryTime = utils.ParseTimeToPointer(deliveryTime.Add(time.Second * time.Duration(seconds.(int))))
	} else if seconds, ok := deliverySecondsByLocationCode.Get(districtCode); ok && seconds.(int) > 0 {
		deliveryTime = &now
		deliveryTime = utils.ParseTimeToPointer(deliveryTime.Add(time.Second * time.Duration(seconds.(int))))
	} else if seconds, ok := deliverySecondsByLocationCode.Get(provinceCode); ok && seconds.(int) > 0 {
		deliveryTime = &now
		deliveryTime = utils.ParseTimeToPointer(deliveryTime.Add(time.Second * time.Duration(seconds.(int))))
	} else if wardLeadTimeResp, err := client.Services.Warehouse.GetLeadTimeByLocationCode(provinceCode, districtCode, wardCode, customerID); err == nil {
		leadtime := wardLeadTimeResp.Data[0].AvgTotalTime
		deliveryTime = &now
		deliveryTime = utils.ParseTimeToPointer(deliveryTime.Add(time.Second * time.Duration(leadtime)))
		deliverySecondsByLocationCode.Put(wardCode, leadtime)
	} else if districtLeadTimeResp, err := client.Services.Warehouse.GetLeadTimeByLocationCode(provinceCode, districtCode, "", customerID); err == nil {
		leadtime := districtLeadTimeResp.Data[0].AvgTotalTime
		deliveryTime = &now
		deliveryTime = utils.ParseTimeToPointer(deliveryTime.Add(time.Second * time.Duration(leadtime)))
		deliverySecondsByLocationCode.Put(districtCode, leadtime)
	} else if provinceLeadTimeResp, err := client.Services.Warehouse.GetLeadTimeByLocationCode(provinceCode, "", "", customerID); err == nil {
		leadtime := provinceLeadTimeResp.Data[0].AvgTotalTime
		deliveryTime = &now
		deliveryTime = utils.ParseTimeToPointer(deliveryTime.Add(time.Second * time.Duration(leadtime)))
		deliverySecondsByLocationCode.Put(provinceCode, leadtime)
	}
	return
}

func isValidOrderHoldTime(order *model.Order) bool {
	maxTimeHoldOrder := time.Duration(24) * time.Hour

	settingResp := model.HoldOrderConfigDB.QueryOne(model.HoldOrderConfig{HoldOrderCode: enum.HoldOrder.AutoCancelBankOrder})
	if settingResp.Status == common.APIStatus.Ok {
		setting := settingResp.Data.([]*model.HoldOrderConfig)[0]
		if setting.IsActive != nil && *setting.IsActive && setting.BankTransferWaitingTime != nil {
			// maxTimeHoldOrder = BankTransferWaitingTime + default (24h)
			maxTimeHoldOrder += time.Duration(*setting.BankTransferWaitingTime) * time.Minute
		}
	}

	// calculate diff between now and created time of exist order
	diff := time.Since(*order.CreatedTime)

	return diff <= maxTimeHoldOrder
}

func OrderCountAccumulateProduct(in *model.AccumalateProductRequest) *common.APIResponse {
	type data struct {
		CountProducts int   `json:"countProducts"  bson:"count_products"`
		CustomerId    int64 `json:"customerId" bson:"_id"`
	}
	query := &model.AccumulateProduct{}

	dateFrom := in.DateFrom.Format("2006-01")
	dateTo := in.DateTo.Format("2006-01")

	if in.DateFrom != nil && in.DateTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"version": bson.M{
				"$gte": dateFrom,
				"$lte": dateTo,
			},
		})
	}
	if in.CustomerId != 0 {
		query.CustomerID = in.CustomerId
	}
	var result = &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
	}
	var accumulateProducts []*data
	// query data from db accumulate product
	limit := int64(300)
	lastID := primitive.NilObjectID
	mapCustomerResult := make(map[int64][]*model.AccumulateProduct)
	for {
		if lastID != primitive.NilObjectID {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{"_id": bson.M{"$gt": lastID}})
		}
		qResult := model.AccumulateProductDB.Query(query, 0, limit, &bson.M{"_id": 1})
		if qResult.Status != common.APIStatus.Ok {
			break
		}
		for _, item := range qResult.Data.([]*model.AccumulateProduct) {
			if _, ok := mapCustomerResult[item.CustomerID]; !ok {
				mapCustomerResult[item.CustomerID] = make([]*model.AccumulateProduct, 0)
			}
			mapCustomerResult[item.CustomerID] = append(mapCustomerResult[item.CustomerID], item)
			lastID = *item.ID
		}
	}

	for customerId, items := range mapCustomerResult {
		// todo unique product ids
		fnProductIDs := make([]int64, 0)
		mapCheckExistProduct := make(map[int64]bool)
		for _, item := range items {
			for _, productID := range item.ProductIDs {
				if _, ok := mapCheckExistProduct[productID]; !ok {
					mapCheckExistProduct[productID] = true
					fnProductIDs = append(fnProductIDs, productID)
				}
			}
		}
		// fill result
		accumulateProducts = append(accumulateProducts, &data{
			CountProducts: len(fnProductIDs),
			CustomerId:    customerId,
		})
	}
	result.Data = accumulateProducts
	return result
}
