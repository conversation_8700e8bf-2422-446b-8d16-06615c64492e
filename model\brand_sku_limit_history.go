package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type BrandSkuLimitHistory struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Version    string `json:"version,omitempty" bson:"version,omitempty"` // only month
	CustomerID int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	Sku        string `json:"sku,omitempty" bson:"sku,omitempty"`

	CustomerAccountID int64 `json:"customerAccountId,omitempty" bson:"customer_account_id,omitempty"`

	Quantity    *int `json:"quantity,omitempty" bson:"quantity,omitempty"`
	OldQuantity *int `json:"oldQuantity,omitempty" bson:"old_quantity,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// BrandSkuLimitHistoryDB ...
var BrandSkuLimitHistoryDB = &db.Instance{
	ColName:        "brand_sku_limit_history",
	TemplateObject: &BrandSkuLimitHistory{},
}

// InitBrandSkuLimitHistoryDBModel is func init model sku apply result
func InitBrandSkuLimitHistoryDBModel(s *mongo.Database) {
	BrandSkuLimitHistoryDB.ApplyDatabase(s)
}

type BrandSkuLimitHistoryResp struct {
	CustomerID            int64  `json:"customerId,omitempty"`
	Sku                   string `json:"sku,omitempty"`
	QtyOrdered            int    `json:"qtyOrdered,omitempty"`
	LimitQuantityPerMonth *int   `json:"limitQuantityPerMonth,omitempty"`
}
