package api

import (
	"encoding/json"
	"strconv"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

// RevenueSellerGetList is handler get list revenue seller with pagination
func RevenueSellerGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode = req.GetVar("sellerCode")
		q          = req.GetParam("q")
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
	)
	var query = model.Reconciliation{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}
	query.SellerCode = sellerCode
	return resp.Respond(action.GetRevenueSellerList(getActionSource(req), query, offset, limit, getTotal))
}

// RevenueItemSellerGetList is handler get list revenue item seller with pagination
func RevenueItemSellerGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode = req.GetVar("sellerCode")
		q          = req.GetParam("q")
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
		orderIDs   = req.GetParam("orderIDs")
	)

	var query = model.ReconciliationItem{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}

	if len(orderIDs) > 0 {
		idArr := strings.Split(orderIDs, ",")
		var orderIdArr []int64
		for _, id := range idArr {
			intID, _ := strconv.Atoi(id)
			if intID > 0 {
				orderIdArr = append(orderIdArr, int64(intID))
			}
		}
		query.OperationAnd = []bson.M{
			{
				"order_id": &bson.M{
					"$in": orderIdArr,
				},
			},
		}
	}

	query.SellerCode = sellerCode
	return resp.Respond(action.GetRevenueItemSellerList(getActionSource(req), query, offset, limit, getTotal))
}

// RevenueGetList is handler get list revenue with pagination
func RevenueGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
	)
	var query = model.Reconciliation{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}
	return resp.Respond(action.GetRevenueList(query, offset, limit, getTotal))
}

func RevenueGetScheduleList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")

		query = model.Reconciliation{}
	)
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}

	result := model.ReconciliationDB.Distinct(query, "reconcile_schedule_time_index")
	return resp.Respond(result)
}

// RevenueGet is handler get revenue
func RevenueGet(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")
	var query = model.Reconciliation{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}
	return resp.Respond(action.GetRevenue(query))
}

// RevenueItemGetList is handler get list revenue item with pagination
func RevenueItemGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		orderIDs = req.GetParam("orderIDs")
	)

	var query = model.ReconciliationItem{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}

	if len(orderIDs) > 0 {
		idArr := strings.Split(orderIDs, ",")
		var orderIdArr []int64
		for _, id := range idArr {
			intID, _ := strconv.Atoi(id)
			if intID > 0 {
				orderIdArr = append(orderIdArr, int64(intID))
			}
		}
		query.OperationAnd = []bson.M{
			{
				"order_id": &bson.M{
					"$in": orderIdArr,
				},
			},
		}
	}

	return resp.Respond(action.GetRevenueItemList(query, offset, limit, getTotal))
}
