package api

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/migrate"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

// OrderGetList is handler get list order with pagination
func OrderGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q                 = req.GetParam("q")
		offset            = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit             = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal          = req.GetParam("getTotal") == "true"
		skuCode           = req.GetParam("skuCode")
		sort              = req.GetParam("sort")
		hasVoucher        = req.GetParam("hasVoucher") == "true"
		ids               = req.GetParam("ids")
		skuCodes          = req.GetParam("skuCodes")
		customerCode      = req.GetParam("customerCode")
		getTotalDelivered = req.GetParam("getTotalDelivered") == "true"
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	checkOffsetResp := verifyOffset(req, offset)
	if checkOffsetResp.Status != common.APIStatus.Ok {
		return resp.Respond(checkOffsetResp)
	}

	var query = model.Order{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			})
		}
	}

	if customerCode != "" {
		query.CustomerCode = customerCode
	}

	if len(ids) > 0 {
		idsArr := strings.Split(ids, ",")
		listIDs := []int64{}
		for _, id := range idsArr {
			intID, _ := strconv.Atoi(id)
			if intID > 0 {
				listIDs = append(listIDs, int64(intID))
			}
		}
		query.ComplexQuery = []*bson.M{
			{
				"order_id": &bson.M{
					"$in": listIDs,
				},
			},
		}
	}
	skus := make([]string, 0)
	if len(skuCodes) > 0 {
		skus = strings.Split(skuCodes, ",")
	}

	if string(query.Status) == "-WAIT_TO_CONFIRM" {
		query.Status = enum.OrderState.WaitConfirm
	}

	if query.StartLastUpdatedTime != nil && query.EndLastUpdatedTime != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"last_updated_time": bson.M{
					"$gte": query.StartLastUpdatedTime,
					"$lte": query.EndLastUpdatedTime,
				},
			})
	}

	if query.StartCompletedTime != nil && query.EndCompletedTime != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"completed_time": bson.M{
					"$gte": query.StartCompletedTime,
					"$lte": query.EndCompletedTime,
				},
			})
	}

	if query.StartCreatedTime != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"created_time": bson.M{
					"$gte": query.StartCreatedTime,
				},
			})
	}

	if query.EndCreatedTime != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"created_time": bson.M{
					"$lte": query.EndCreatedTime,
				},
			})
	}

	return resp.Respond(action.GetOrderList(getActionSource(req), &query, skuCode, skus, offset, limit, getTotal, hasVoucher, getTotalDelivered, sort))
}

// OrderGetListWithPayload is handler get list order with payload
func OrderGetListWithPayload(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.GetListOrderRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	var query = model.Order{}
	if len(input.Ids) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"order_id": bson.M{"$in": input.Ids},
		})
	}
	if len(input.CustomerIds) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"customer_id": bson.M{"$in": input.CustomerIds},
		})
	}
	if len(input.SaleOrderCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"sale_order_code": bson.M{"$in": input.SaleOrderCodes},
		})
	}
	if len(input.Codes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"order_code": bson.M{"$in": input.Codes},
		})
	}
	return resp.Respond(action.GetOrderList(getActionSource(req), &query, "", nil, 0, 1000, true, false, false, ""))

}

// OrderItemGetListWithPayload is handler get list order with payload
func OrderItemGetListWithPayload(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.GetListOrderItemRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	var query = model.OrderItem{}
	if input.Limit == 0 {
		input.Limit = 20
	}
	if len(input.Ids) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"order_id": bson.M{"$in": input.Ids},
		})
	}

	return resp.Respond(action.GetOrderItemList(getActionSource(req), &query, input.Offset, input.Limit, true))
}

// OrderItemByOrderDetailWithPayload is handler get list order with payload
func OrderItemByOrderDetailWithPayload(req sdk.APIRequest, resp sdk.APIResponder) error {

	var query model.OrderDetailQuery
	if err := req.GetContent(&query); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	// validate
	if len(query.ItemCode) == 0 && len(query.Sku) == 0 && len(query.DealCode) == 0 &&
		len(query.ItemCodeIn) == 0 && len(query.SkuIn) == 0 && // && len(query.DealCodeIn) == 0
		len(query.OrderIDIn) == 0 {
		// return resp.Respond(&common.APIResponse{
		// 	Status:    common.APIStatus.Invalid,
		// 	Message:   "Vui lòng chọn ít nhất 1 trong các trường item_code, sku, deal_code, order_id",
		// 	ErrorCode: "PAYLOAD_INVALID",
		// })

		if query.Limit > 100 {
			query.Limit = 100
		}
	}
	if len(query.OrderIDIn) > 100 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Số lượng order_id không được quá 100",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	// ================ fill query ================
	oiQuery := model.OrderItem{}

	if query.Limit == 0 {
		query.Limit = 20
	}

	if len(query.OrderIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"order_id": &bson.M{
				"$in": query.OrderIDIn,
			},
		})

		// item
		oiQuery.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"order_id": &bson.M{
				"$in": query.OrderIDIn,
			},
		})
	}

	// query field
	if len(query.ItemCode) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"item_codes": query.ItemCode,
		})

		// item
		oiQuery.ItemCode = query.ItemCode
	}
	if len(query.Sku) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"skus": query.Sku,
		})

		// item
		oiQuery.Sku = query.Sku
	}
	if len(query.DealCode) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"deal_codes": query.DealCode,
		})

		// item
		oiQuery.DealCode = &query.DealCode
	}

	// $in
	if len(query.ItemCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"item_codes": &bson.M{
				"$in": query.ItemCodeIn,
			},
		})

		// item
		oiQuery.ComplexQuery = append(oiQuery.ComplexQuery, &bson.M{
			"item_code": &bson.M{
				"$in": query.ItemCodeIn,
			},
		})
	}
	if len(query.SkuIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"skus": &bson.M{
				"$in": query.SkuIn,
			},
		})

		// item
		oiQuery.ComplexQuery = append(oiQuery.ComplexQuery, &bson.M{
			"sku": &bson.M{
				"$in": query.SkuIn,
			},
		})
	}
	// if len(query.DealCodeIn) > 0 {
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
	// 		"deal_codes": &bson.M{
	// 			"$in": query.DealCodeIn,
	// 		},
	// 	})

	// 	// item
	// 	oiQuery.ComplexQuery = append(oiQuery.ComplexQuery, &bson.M{
	// 		"deal_code": &bson.M{
	// 			"$in": query.DealCodeIn,
	// 		},
	// 	})
	// }

	// time
	if query.DateFrom != nil && query.DateTo != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"created_time": bson.M{
					"$gte": query.DateFrom,
					"$lte": query.DateTo,
				},
			},
		)

		// // item
		// oiQuery.ComplexQuery = append(
		// 	query.ComplexQuery,
		// 	&bson.M{
		// 		"created_time": bson.M{
		// 			"$gte": query.DateFrom,
		// 			"$lte": query.DateTo,
		// 		},
		// 	},
		// )
	}
	if query.DateFromAny != nil || query.DateToAny != nil {
		dateQuery := bson.M{}
		if query.DateFromAny != nil {
			dateQuery["$gte"] = query.DateFromAny
		}
		if query.DateToAny != nil {
			dateQuery["$lt"] = query.DateToAny
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": dateQuery,
		})

		// // item
		// oiQuery.ComplexQuery = append(query.ComplexQuery, &bson.M{
		// 	"created_time": dateQuery,
		// })
	}

	return resp.Respond(action.OrderItemByOrderDetailWithPayload(query, oiQuery))
}

// OrderItemGetList is handler get list order seller with pagination
func OrderItemGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
		orderIDs = req.GetParam("orderIDs")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 1000 {
		limit = 1000
	}

	if offset < 0 {
		offset = 0
	}

	var query = model.OrderItem{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			})
		}
	}

	if len(orderIDs) > 0 {
		idArr := strings.Split(orderIDs, ",")
		var orderIdArr []int64
		for _, id := range idArr {
			intID, _ := strconv.Atoi(id)
			if intID > 0 {
				orderIdArr = append(orderIdArr, int64(intID))
			}
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"order_id": &bson.M{
				"$in": orderIdArr,
			},
		})
	}

	if len(query.ItemCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"item_code": &bson.M{
				"$in": query.ItemCodeIn,
			},
		})
	}

	if query.DateFrom != nil && query.DateTo != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"created_time": bson.M{
					"$gte": query.DateFrom,
					"$lte": query.DateTo,
				},
			})
	}

	if query.DateFromAny != nil || query.DateToAny != nil {
		dateQuery := bson.M{}
		if query.DateFromAny != nil {
			dateQuery["$gte"] = query.DateFromAny
		}
		if query.DateToAny != nil {
			dateQuery["$lt"] = query.DateToAny
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": dateQuery,
		})
	}

	if query.StartLastUpdatedTime != nil && query.EndLastUpdatedTime != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"last_updated_time": bson.M{
					"$gte": query.StartLastUpdatedTime,
					"$lte": query.EndLastUpdatedTime,
				},
			})
	}

	// SubSkuCode ...
	if query.SubSkuCode != "" {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"skus.sku": query.SubSkuCode,
			})
	}
	// SubItemCode ... reporting
	if query.SubItemCode != "" {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"sub_items.sku": query.SubItemCode,
			})
	}

	return resp.Respond(action.GetOrderItemList(getActionSource(req), &query, offset, limit, getTotal))
}

// OrderGetDetail ...
func OrderGetDetail(req sdk.APIRequest, resp sdk.APIResponder) error {
	var q = req.GetParam("q")
	if q != "" {
		var query model.Order
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data." + err.Error(),
			})
		}
		return resp.Respond(action.GetOrderDetail(getActionSource(req), &query))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Query missing field.",
		ErrorCode: "MISSING_FIELD",
	})
}

// OrderUpdateDetail ...
func OrderUpdateDetail(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderUpdateInfo
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateOrderDetail(getActionSource(req), &input))
}

// CustomerOrderUpdateDetail ...
func CustomerOrderUpdateDetail(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CustomerOrderUpdateDetailRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	// validate payment method
	if input.PaymentMethod == "" || !action.IsValidPaymentMethodEnumValue(input.PaymentMethod) {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Phương thúc thanh toán không hợp lệ",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	return resp.Respond(action.CustomerOrderUpdateDetail(getActionSource(req), &input))
}

// OrderUpdateItem ...
func OrderUpdateItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderUpdateItem
	if input.Type == "" {
		input.Type = "NORMAL"
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.UpdateOrderItem(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// OrderRemoveItem ...
func OrderRemoveItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderRemoveItem
	if input.Type == "" {
		input.Type = "NORMAL"
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		acc.SourceDetail = getOrderSourceDetail(req.GetHeader("User-Agent"), req.GetIP())
		return resp.Respond(action.RemoveOrderItem(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// OrderUpdateStatus ...
func OrderUpdateStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderUpdateStatus
	content := req.GetContentText()
	if err := json.Unmarshal([]byte(content), &input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	model.OrderUpdateLog.Create(map[string]interface{}{
		"action":   "PUT/order/status",
		"order_id": input.OrderID,
		"content":  content,
		"status":   input.Status,
	})

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.OrderUpdateStatus(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// OrderCounting ...
func OrderCounting(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderCounting
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.OrderCounting(&input))
}

func UpdateProcessingStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input *model.RequestUpdateDeliverStatus
	content := req.GetContentText()
	err := json.Unmarshal([]byte(content), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	_ = model.OrderUpdateLog.Create(map[string]interface{}{
		"action":   req.GetMethod().Value + req.GetPath(),
		"order_id": input.OrderID,
		"status":   input.Status,
		"content":  content,
	})

	if input.OrderID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing orderID",
		})
	}
	if input.Status == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing status",
		})
	}
	if input.OrderItems == nil || len(input.OrderItems) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Empty orderItems",
		})
	}

	response := action.UpdateProcessingStatus2(input)
	return resp.Respond(response)
}

func UpdateProcessingStatusWithQueue(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input *model.RequestUpdateDeliverStatus
	content := req.GetContentText()
	err := json.Unmarshal([]byte(content), &input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	_ = model.OrderUpdateLog.Create(map[string]interface{}{
		"action":   req.GetMethod().Value + req.GetPath(),
		"order_id": input.OrderID,
		"status":   input.Status,
		"content":  content,
	})

	if input.OrderID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing orderID",
		})
	}
	if input.Status == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing status",
		})
	}
	if input.OrderItems == nil || len(input.OrderItems) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Empty orderItems",
		})
	}

	err = model.UpdateDeliveryJob.Push(input, &job.JobItemMetadata{
		SortedKey: strconv.Itoa(int(input.OrderID)),
		Keys: []string{
			strconv.Itoa(int(input.OrderID)),
			input.Status,
		},
		Topic: "default",
	})
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status: common.APIStatus.Error,
			Data: []string{
				err.Error(),
			},
			Message:   "Enqueue update delivery failed",
			ErrorCode: "PUSH_INTO_JOB_FAILED",
		})
	}
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Enqueue update delivery successfully",
	})
}

// OrderDelayDelivery ...
func OrderDelayDelivery(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.RequestDelayDelivery
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.DelayOrder(input.OrderID))
}

// OrderUpdateInvoice ...
func OrderUpdateInvoice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.UpdateInvoiceRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateOrderInvoice(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// OrderCountPoint ...
func OrderCountPoint(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.RequestCountOrderPoint

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.CountOrderPoint(&input))
}

// OrderCountValue ...
func OrderCountValue(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.RequestCountOrderValue

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.CountOrderValue(&input))
}

// OrderCountValueBySeller ...
func OrderCountValueBySeller(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.RequestCountOrderValue

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.CountOrderValueBySeller(&input))
}

// OrderCountValueGamificationTourDL ...
func OrderCountValueGamificationTourDL(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.RequestCountOrderValue

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.OrderCountValueGamificationTourDL(&input))
}

func OrderCountAccumulateProduct(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.AccumalateProductRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.OrderCountAccumulateProduct(&input))
}

// GetOrdersByCustomer is handler get list order for POS system
func GetOrdersByCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q       = req.GetParam("q")
		offset  = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit   = sdk.ParseInt64(req.GetParam("limit"), 1000)
		reverse = req.GetParam("reverse") == "true"
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 1000 {
		limit = 1000
	}

	if offset < 0 {
		offset = 0
	}

	var query = model.Order{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}

	return resp.Respond(action.GetOrdersByCustomer(&query, offset, limit, reverse))
}

func UpdateOrderItemData(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.UpdateOrderItemDataRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.UpdateOrderItemData(&input))
}

func CancelOrderByCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CancelOrderByCustomerRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CancelOrderByCustomer(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// OrderSummationOrderInfoByCustomer:
func OrderSummationOrderInfoByCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		customerID = sdk.ParseInt64(req.GetParam("customerId"), 0)
		info       = req.GetParam("info")
	)

	return resp.Respond(action.GetSummationOrderInfoByCustomer(info, customerID))
}

func ProcessOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderProcessRequest

	content := req.GetContentText()
	if err := json.Unmarshal([]byte(content), &input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		start := time.Now()
		logCode := model.GenCodeWithTime()
		go func() {
			model.OrderUpdateLog.Create(map[string]interface{}{
				"action":   "PUT/process-order",
				"order_id": input.OrderId,
				"content":  content,
				"status":   input.Status,
				"code":     logCode,
			})
		}()
		res := action.ProcessOrder(acc, &input)
		processingTime := time.Now().Sub(start).Milliseconds()
		go func() {
			model.OrderUpdateLog.UpdateOneWithOption(bson.M{"code": logCode},
				bson.M{"$set": bson.M{"resp": res, "resp_status": res.Status, "processing_time": processingTime}})
		}()
		return resp.Respond(res)
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func OrderGetGenID(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(action.GenOrderID())
}

// OrderUpdateNote ...
func OrderUpdateNote(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderUpdateNoteRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	if input.SenderName == "VPBANK_ADAPTER" || input.SenderName == "ONEPAY_ADAPTER" {
		return resp.Respond(action.WebhookHandler(&input))
	}

	return resp.Respond(action.UpdateOrderNote(getActionSource(req), &input))
}
func OrderRequestProcessByCustomer(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderProcessRequestFromClient
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.OrderRequestProcessByCustomer(acc, &input))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func RePushSyncSaleOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = struct {
		OrderIds           []int64 `json:"orderIds"`
		ResetSaleOrderCode bool    `json:"resetSaleOrderCode,omitempty"`
	}{}

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	return resp.Respond(action.RePushSyncSaleOrder(input.OrderIds, input.ResetSaleOrderCode))
}

func CompleteBill(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderBill

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CompleteOrderBill(&input))
}

func SearchOrderByOrderID(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
	)

	var input = model.SearchOrderQuery{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &input)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			})
		}
	}

	if len(input.Search) > 0 {
		_, err := strconv.Atoi(input.Search)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data: " + err.Error(),
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.SearchOrderByOrderID(acc, &input, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func OrderBankToCredit(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.MigratePaymentMethodRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "INVALID_PAYLOAD",
		})
	}
	if input.CustomerID <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing customerID",
			ErrorCode: "MISSING_CUSTOMER_ID",
		})
	}
	if input.ContractStartTime == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing contractStartTime",
			ErrorCode: "MISSING_CONTRACT_START_TIME",
		})
	}
	return resp.Respond(action.OrderBankToCredit(&input))
}

func SetTimeoutCancelOrderBank(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		OrderCodes              []string `json:"orderCodes,omitempty"`
		BankTransferWaitingTime int      `json:"bankTransferWaitingTime,omitempty"`
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "INVALID_PAYLOAD",
		})
	}
	settingResp := model.HoldOrderConfigDB.QueryOne(model.HoldOrderConfig{HoldOrderCode: enum.HoldOrder.AutoCancelBankOrder})
	if settingResp.Status != common.APIStatus.Ok {
		return resp.Respond(settingResp)
	}
	setting := settingResp.Data.([]*model.HoldOrderConfig)[0]
	if setting.IsActive == nil || (setting.IsActive != nil && !*setting.IsActive) {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Config is disabled",
		})
	}
	go func() {
		for _, code := range input.OrderCodes {
			orderResp := model.OrderDB.QueryOne(model.Order{OrderCode: code})
			if orderResp.Status == common.APIStatus.Ok {
				order := orderResp.Data.([]*model.Order)[0]
				if action.IsContainsT(action.PAYMENT_METHOD_ORDER_TRANSFER, (order.PaymentMethod)) && order.Status == enum.OrderState.WaitConfirm && setting.BankTransferWaitingTime != nil {
					readyTime := utils.ParseTimeToPointer(time.Now().Add(time.Duration(*setting.BankTransferWaitingTime) * time.Minute))
					updateData := &model.Order{WaitForTransferTime: *setting.BankTransferWaitingTime}
					if input.BankTransferWaitingTime > 0 {
						readyTime = utils.ParseTimeToPointer(time.Now().Add(time.Duration(input.BankTransferWaitingTime) * time.Minute))
						updateData.WaitForTransferTime = int64(input.BankTransferWaitingTime)
						updateData.AutoCancelTransferPaymentUnix = readyTime.Unix()
					}
					updateRs := model.OrderDB.UpdateOne(model.Order{OrderCode: code}, updateData)

					if updateRs.Status == common.APIStatus.Ok {
						model.AutoCancelPaymentMethodBankJob.Push(action.CheckPaymentMethodBankOrder{
							OrderId: order.OrderID,
							Unix:    readyTime.Unix(),
						}, &job.JobItemMetadata{
							Topic:     "default",
							ReadyTime: readyTime,
						})
					}
				}
			}
		}
	}()
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "success",
	})
}

func ToolChangeNonVATOrderItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		OrderIDs []int64 `json:"orderIds,omitempty"`
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "INVALID_PAYLOAD",
		})
	}
	go migrate.MigrateChangeNonVATOrderItem(input.OrderIDs)
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Processing",
	})
}

func ToolRevertNonVATOrderItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		OrderIDs []int64 `json:"orderIds,omitempty"`
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "INVALID_PAYLOAD",
		})
	}
	go migrate.ToolRevertNonVATOrderItem(input.OrderIDs)
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Processing",
	})
}

// BulkOrderConfirm ...
func BulkOrderConfirm(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.BulkOrderConfirmRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.BulkOrderConfirm(getActionSource(req), &input))
}

func ToolGetOrderSellerByOrderID(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		OrderID int64 `json:"orderId,omitempty"`
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		})
	}

	orderSellers := reconcile_action.GetOrderSellers(input.OrderID)

	return resp.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   orderSellers,
	})
}

func CustomerCreateOrderPaymentLink(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Order
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CustomerCreateOrderPaymentLink(getActionSource(req), &input, req.GetHeaders()))
}

func CreateInternalOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.CreateInternalOrderRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateInternalOrder(&input, acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

// OrderGetTotalPrice is handler get list order with pagination
func OrderGetTotalPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q = req.GetParam("q")
	)

	var query = model.Order{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			})
		}
	}

	if string(query.Status) == "-WAIT_TO_CONFIRM" {
		query.Status = enum.OrderState.WaitConfirm
	}

	return resp.Respond(action.OrderGetTotalPrice(getActionSource(req), &query))
}

func GetReturnTicketInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ReturnTicket

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.GetReturnTicketInfo(&input))
}

func ReconciliationSyncOrderInfoKiotvietPartner(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
	)

	lastID, _ := primitive.ObjectIDFromHex(req.GetParam("lastID"))

	query := model.ReconciliationOrderKiotviet{
		StartTime:  req.GetParam("startTime"),
		EndTime:    req.GetParam("endTime"),
		LastID:     lastID,
		SortType:   req.GetParam("sortType"),
		OrderID:    sdk.ParseInt64(req.GetParam("orderId"), 0),
		CustomerID: sdk.ParseInt64(req.GetParam("customerId"), 0),
	}

	if limit > 100 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Limit must be less than 100",
		})
	}

	return resp.Respond(action.ReconciliationSyncOrderInfoKiotvietPartner(&query, limit, getTotal))
}
