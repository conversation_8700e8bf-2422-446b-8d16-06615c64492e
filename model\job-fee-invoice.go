package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"go.mongodb.org/mongo-driver/mongo"
)

var FeeInvoiceJob = job.Executor{ColName: "fee_invoice_job"}

func InitFeeInvoiceJob(database *mongo.Database, consumer job.ExecutionFn) {
	FeeInvoiceJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
		ChannelCount:     100,
	})

	FeeInvoiceJob.SetTopicConsumer("FEE_INVOICE", consumer)
}
