package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func ToolUpdatePoint(input *model.ToolUpdatePointRequest) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Input is empty",
			ErrorCode: "PAYLOAD_INVALID",
		}
	}

	// get order by customerID, orderID
	orderResp := model.OrderDB.QueryOne(&model.Order{
		CustomerID: input.CustomerID,
		OrderID:    input.OrderID,
	})

	if orderResp.Status != common.APIStatus.Ok {
		return orderResp
	}

	order := orderResp.Data.([]*model.Order)[0]

	// update point
	order.Point = input.Point
	updatedResp := model.OrderDB.UpdateOne(&model.Order{
		OrderID:    input.OrderID,
		CustomerID: input.CustomerID,
	}, &order)
	if updatedResp.Status != common.APIStatus.Ok {
		return updatedResp
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}
