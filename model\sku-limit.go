package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SkuLimit struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time         `json:"-" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SkuLimitID int64  `json:"skuLimitId,omitempty" bson:"sku_limit_id,omitempty"`
	Code       string `json:"code,omitempty" bson:"code,omitempty"`

	Sku      string `json:"sku,omitempty" bson:"sku,omitempty"`
	ItemCode string `json:"itemCode,omitempty" bson:"item_code,omitempty"`

	SegmentCode *string `json:"segmentCode,omitempty" bson:"segment_code,omitempty"`
	SegmentName *string `json:"segmentName,omitempty" bson:"-"`

	LimitPerOrder int64 `json:"limitPerOrder,omitempty" bson:"limit_per_order,omitempty"`

	Quantity     int   `json:"quantity,omitempty" bson:"quantity,omitempty"`
	NumberOfDays int   `json:"numberOfDays,omitempty" bson:"number_of_days,omitempty" validate:"omitempty,gt=0"`
	IsActive     *bool `json:"isActive,omitempty" bson:"is_active,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`

	// TODO limit
	LimitQuantityPerMonth int `json:"limitQuantityPerMonth,omitempty" bson:"limit_quantity_per_month,omitempty"`
}

func (s *SkuLimit) CanApplyLimitPerDay() bool {
	return s != nil && s.IsActive != nil && *s.IsActive
}
