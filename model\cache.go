package model

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

type GamificationValue struct {
	CreatedTime             *time.Time                `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime         *time.Time                `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CustomerId              int                       `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	OrderValues             []*GamificationOrderValue `json:"orderValues,omitempty" bson:"order_values,omitempty"`
	LogSyncGamificationCode string                    `json:"logSyncGamificationCode" bson:"log_sync_gamification_code,omitempty"`
	SystemDisplay           string                    `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
}

type GamificationOrderValue struct {
	OrderID          int        `json:"orderId,omitempty" bson:"order_id,omitempty"`
	Status           string     `json:"status,omitempty" bson:"status,omitempty"`
	Sku              string     `json:"sku,omitempty" bson:"sku,omitempty"`
	Type             string     `json:"type,omitempty" bson:"type,omitempty"`
	TotalPrice       int        `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
	TotalActualPrice int        `json:"totalActualPrice,omitempty" bson:"total_actual_price,omitempty"`
	CreatedTime      *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CompletedTime    *time.Time `json:"completedTime,omitempty" bson:"completed_time,omitempty"`
	SystemDisplay    string     `json:"systemDisplay,omitempty" bson:"system_display,omitempty"` // thời gian hoàn tất đơn hàng -- status = completed
}

// GamificationValueDB ...
var GamificationValueDB = &db.Instance{
	ColName:        "gamification_value",
	TemplateObject: &GamificationValue{},
}

// InitGamificationValueModel is func init model GamificationValue
func InitGamificationValueModel(s *mongo.Database) {
	GamificationValueDB.ApplyDatabase(s)

	// ttl after 3h
	ttl := int32(3 * 60 * 60)
	GamificationValueDB.CreateIndex(bson.D{
		bson.E{Key: "created_time", Value: 1},
	}, &options.IndexOptions{
		ExpireAfterSeconds: &ttl,
	})
}
