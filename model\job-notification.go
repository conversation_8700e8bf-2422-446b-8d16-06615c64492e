package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/mongo"
)

var PushNotificationJob = &job.Executor{ColName: "push_notification_job"}

func InitPushNotificationJob(database *mongo.Database, consumer job.ExecutionFn) {
	PushNotificationJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
		ChannelCount:     10,
	})

	PushNotificationJob.SetConsumer(consumer)
}

type PushNotificationItem struct {
	SellerCode       string
	NotificationType enum.NotificationTypeValue

	InvoiceId       int64
	InvoiceCode     string
	InvoiceDeadline time.Time
	OrderId         int64
	OrderCode       string

	SkuCode string

	ReconcileFromTime string
	ReconcileToTime   string

	Title       string
	Description string
	Link        string
}
