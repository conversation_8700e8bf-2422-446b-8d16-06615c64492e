package consumer

import (
	"encoding/json"
	"fmt"
	"runtime/debug"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/tool"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func CreateSellerInvoice(item *job.JobItem) (returnErr error) {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		returnErr = err
		return returnErr
	}

	var input *model.OrderProcessRequest
	returnErr = bson.Unmarshal(data, &input)
	if returnErr != nil {
		var input2 *model.Order
		if err := bson.Unmarshal(data, &input2); err != nil {
			return returnErr
		}

		input = &model.OrderProcessRequest{
			OrderId: input2.OrderID,
		}
	}

	defer func() {
		if r := recover(); r != nil {
			returnErr = fmt.Errorf("panic: %s", string(debug.Stack()))
			fmt.Printf("[%d - %s] %s\n", input.OrderId, input.Status, returnErr.Error())
		}
	}()

	invoiceResponse := tool.ProcessSyncSellerInvoice(input.OrderId)

	jsonData, err := json.MarshalIndent(invoiceResponse, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling invoice response to JSON: %v\n", err)
		jsonData = []byte(fmt.Sprintf("%s: %s", invoiceResponse.Status, invoiceResponse.Message))
	}

	logEntry := fmt.Sprintf("Invoice sync for order %d:\n%s", input.OrderId, string(jsonData))

	if item.Log == nil {
		item.Log = &[]string{logEntry}
	} else {
		log := append(*item.Log, logEntry)
		item.Log = &log
	}

	return returnErr
}
