package consumer

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// ONLY handle notification to push.
// DO NOT process any business logic here,
// such as validate or filter which item to send or not.
func HandlePushNotification(item *job.JobItem) error {
	consumeData, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}

	var data *model.PushNotificationItem
	err = bson.Unmarshal(consumeData, &data)
	if err != nil {
		return err
	}

	sellersRes := client.Services.Seller.GetSellerListWithParams(client.ReqSellerList{
		SellerCode: data.SellerCode,
	})
	if sellersRes.Status != common.APIStatus.Ok {
		return nil
	}

	seller := sellersRes.Data[0]

	switch data.NotificationType {
	case enum.NotificationType.INVOICE_DEADLINE:
		deadline := data.InvoiceDeadline.Format("02/01/2006")
		description := fmt.Sprintf("Hóa đơn #%d của đơn hàng #%d sẽ hết hạn gửi hóa đơn vào %s", data.InvoiceId, data.OrderId, deadline)
		link := fmt.Sprintf("/order/invoice?invoiceID=%d", data.InvoiceId)

		client.Services.Notification.CreateNotificationToSeller(&client.Notification{
			UserID: seller.AccountID,
			Topic:  "ANNOUNCEMENT",

			Title:       "Bạn có 1 hóa đơn sắp quá hạn!",
			Description: description,
			Link:        link,
			Tags: []enum.NotificationTagEnum{
				enum.NotificationTag.ORDER_INVOICE,
			},
		})
	case enum.NotificationType.SKU_FIRST_ORDER:
		skuName := getSkuNameFromCode(data.SkuCode)
		description := fmt.Sprintf("Sản phẩm %s của bạn vừa có đơn hàng đầu tiên. Đơn hàng #%d", skuName, data.OrderId)

		client.Services.Notification.CreateNotificationToSeller(&client.Notification{
			UserID: seller.AccountID,
			Topic:  "ANNOUNCEMENT",

			Title:       "Sản phẩm mới của bạn vừa có đơn hàng đầu tiên!",
			Description: description,
			Link:        "/order/view-order/" + data.OrderCode,
			Tags: []enum.NotificationTagEnum{
				enum.NotificationTag.ORDER_INVOICE,
			},
		})
	case enum.NotificationType.RECONCILIATION_COMPLETE: // ASC-1092 currently disable
		dbLayout := "2006-01-02"
		displayLayout := "02/01/2006"
		fromTime, _ := time.Parse(dbLayout, data.ReconcileFromTime)
		toTime, _ := time.Parse(dbLayout, data.ReconcileToTime)

		title := fmt.Sprintf("Lượt đối soát %s ~ %s đã thanh toán", fromTime.Format(displayLayout), toTime.Format(displayLayout))
		description := fmt.Sprintf("Lượt đối soát %s ~ %s đã thanh toán. Xem ngay", fromTime.Format(displayLayout), toTime.Format(displayLayout))

		client.Services.Notification.CreateNotificationToSeller(&client.Notification{
			UserID: seller.AccountID,
			Topic:  "ANNOUNCEMENT",

			Title:       title,
			Description: description,
			Link:        "/revenue",
		})
	}
	return nil
}

func getSkuNameFromCode(skuCode string) string {
	product := client.Services.Product.GetProductInfo(skuCode, 0)
	if product == nil {
		return ""
	}

	return product.Name
}
