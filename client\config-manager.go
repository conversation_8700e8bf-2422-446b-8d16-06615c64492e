package client

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetAppValueSingle = "/core/config-manager/v1/app-value/single"
)

// Client is model define account client
type configManager struct {
	account *client.RestClient
	headers map[string]string
}

// NewClient is func define new account client
func NewConfigManagerClient(apiHost, apiKey, logName string, session *mongo.Database) *configManager {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	client := &configManager{
		account: client.NewRESTClient(
			apiHost,
			logName,
			time.Duration(3*time.Second),
			1,
			time.Duration(3*time.Second),
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}

	client.account.SetDBLog(session)

	return client
}

// ChangeLog ...
func (cli *configManager) GetAppValueSingle(params map[string]string) *model.AppValueResponse {

	resp := &model.AppValueResponse{
		Status: common.APIStatus.Invalid,
	}

	res, err := cli.account.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetAppValueSingle, nil)
	if err != nil {
		resp.Message = err.Error()
		return resp
	}

	err = json.Unmarshal([]byte(res.Body), &resp)
	if err != nil {
		resp.Message = err.Error()
		return resp
	}

	if len(resp.Data) == 0 {
		resp.Status = common.APIStatus.Invalid
		return resp
	}

	return resp
}
