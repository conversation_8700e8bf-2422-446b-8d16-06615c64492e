package reconcile_action

import (
	"fmt"
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

func ProcessInvoiceOverdueFee(
	seller client.Seller,
	reconciliation *model.Reconciliation,
	from, to, timeIndex string,
	_time time.Time,
) {
	if reconciliation == nil {
		var err error
		reconciliation, err = CreateReconciliationFromIndex(seller.Code, timeIndex, from, to, _time)
		if err != nil {
			log.Println("ProcessInvoiceOverdueFee", err.Error())
			return
		}
	}

	if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
		return
	}

	// prevent update reconciliation from long ago
	if time.Now().After(_time.Add(12 * time.Hour)) {
		return
	}

	var offset, limit int64 = 0, 100

	query := bson.M{
		"seller_code": reconciliation.SellerCode,
		"invoice_status": bson.M{
			"$in": []string{
				string(model.InvoiceStatus.LateDeadline),
				string(model.InvoiceStatus.Completed),
			},
		},
	}

	for {
		invoicesRes := model.InvoiceDB.Query(query, offset, limit, &bson.M{"created_time": 1})

		if invoicesRes.Status != common.APIStatus.Ok {
			break
		}

		invoices := invoicesRes.Data.([]*model.Invoice)
		for _, invoice := range invoices {
			if invoice.Request == nil ||
				!*invoice.Request {
				continue
			}

			if invoice.IsFined == nil || !*invoice.IsFined {
				continue
			}

			if invoice.ReconcileScheduleTimeIndex != nil &&
				len(*invoice.ReconcileScheduleTimeIndex) > 0 {
				continue
			}

			res := model.ReconciliationItemDB.Upsert(model.ReconciliationItem{
				SellerCode:                 reconciliation.SellerCode,
				ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
				AutoPenaltyFee:             true,
				FeeType:                    enum.FeeType.INVOICE_OVERDUE_FEE,

				InvoiceID:   invoice.InvoiceID,
				InvoiceCode: invoice.InvoiceCode,
				OrderID:     invoice.OrderID,
				OrderCode:   invoice.OrderCode,
			}, model.ReconciliationItem{
				PenaltyDescription: fmt.Sprintf("Phí xuất hoá đơn trễ cho đơn hàng #%d với mã hoá đơn #%d",
					invoice.OrderID, invoice.InvoiceID),
				PenaltyBMFee: 50000,
			})

			if res.Status == common.APIStatus.Ok {
				reconcileItem := res.Data.([]*model.ReconciliationItem)[0]
				model.InvoiceDB.UpdateOne(model.Invoice{
					InvoiceID: invoice.InvoiceID,
				}, model.Invoice{
					ReconcileScheduleTimeIndex: &reconciliation.ReconcileScheduleTimeIndex,
					ReconcileItemID:            reconcileItem.ID,
				})
			}

			time.Sleep(20 * time.Millisecond)
		}

		if len(invoices) < int(limit) {
			break
		}

		latest := invoices[len(invoices)-1].CreatedTime
		query["created_time"] = bson.M{"$gt": latest}
	}
}
