package reconcile_action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

type SyncRIFunc func(*model.ReconciliationItem)

func CreateReconciliationItem(input *model.ReconciliationItem, as *model.ActionSource, callback SyncRIFunc) *common.APIResponse {
	reconciliationF := model.Reconciliation{
		SellerCode:                 input.SellerCode,
		ReconcileScheduleTimeIndex: input.ReconcileScheduleTimeIndex,
	}
	result := model.ReconciliationDB.QueryOne(reconciliationF)
	if result.Status != common.APIStatus.Ok {
		return result
	}
	reconciliation := result.Data.([]*model.Reconciliation)[0]

	if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      result.Data,
			Message:   "Trạng thái đối soát không phù hợp để chính sửa",
			ErrorCode: "INVALID_RECONCILIATION_STATUS",
		}
	}

	reconciliationItem := model.ReconciliationItem{
		SellerCode:                 input.SellerCode,
		ReconcileScheduleTimeIndex: input.ReconcileScheduleTimeIndex,
		FeeType:                    input.FeeType,
		Description:                input.Description,
		PenaltyDescription:         input.PenaltyDescription,
		PenaltyFee:                 input.PenaltyFee,
		PenaltyBMFee:               input.PenaltyBMFee,
		PenaltyBMLFee:              input.PenaltyBMLFee,
		BonusDescription:           input.BonusDescription,
		BonusAmount:                input.BonusAmount,
		BonusBMAmount:              input.BonusBMAmount,
		BonusBMLAmount:             input.BonusBMLAmount,
	}
	result = model.ReconciliationItemDB.Create(reconciliationItem)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	if callback != nil {
		// TODO: turn off billing
		// items := result.Data.([]*model.ReconciliationItem)
		// go callback(items[0])
	}

	value, err := CalculateReconciliation(reconciliation.SellerCode, reconciliation.ReconcileScheduleTimeIndex)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: "Cập nhật dữ liệu đối soát không thành công",
		}
	}

	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}
	result = model.ReconciliationDB.UpdateOne(reconciliationF, updater)
	// reconciliation = result.Data.([]*model.Reconciliation)[0]

	// reconciliationItemF := model.ReconciliationItem{
	// 	SellerCode:                 reconciliation.SellerCode,
	// 	ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
	// }
	// total := model.ReconciliationItemDB.Count(reconciliationItemF).Total
	// itemResult := model.ReconciliationItemDB.Query(reconciliationItemF, 0, total, nil)
	// if itemResult.Status != common.APIStatus.Ok {
	// 	return result
	// }

	// items := itemResult.Data.([]*model.ReconciliationItem)
	// reconciliation.ReconciliationItems = items
	// result.Data = []*model.Reconciliation{reconciliation}
	return result
}
