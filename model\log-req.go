package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var LogReqDb = &db.Instance{
	ColName:        "log_req",
	TemplateObject: &LogReq{},
}

var LogOrderWrongInvoiceDb = &db.Instance{
	ColName:        "log_order_wrong_invoice",
	TemplateObject: &LogOrderWrongInvoice{},
}

func InitLogReqModel(s *mongo.Database) {
	LogReqDb.ApplyDatabase(s)
	LogOrderWrongInvoiceDb.ApplyDatabase(s)

	LogReqDb.CreateIndex(bson.D{
		{Key: "created_time", Value: 1},
	}, &options.IndexOptions{
		Background:         &[]bool{true}[0],
		ExpireAfterSeconds: &[]int32{86400}[0], // one day
	})

	LogReqDb.CreateIndex(bson.D{
		{Key: "req_url", Value: 1},
		{Key: "req_method", Value: 1},
	}, &options.IndexOptions{
		Background: &[]bool{true}[0],
	})

	LogOrderWrongInvoiceDb.CreateIndex(bson.D{
		{Key: "created_time", Value: 1},
	}, &options.IndexOptions{
		Background:         &[]bool{true}[0],
		ExpireAfterSeconds: &[]int32{86400}[0], // one day
	})
}

type LogReq struct {
	ID          primitive.ObjectID `json:"id" bson:"_id,omitempty" `
	CreatedTime *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`

	ReqURL       string      `json:"reqUrl,omitempty" bson:"req_url,omitempty"`
	ReqMethod    string      `json:"reqMethod,omitempty" bson:"req_method,omitempty"`
	ActionSource *Account    `json:"actionSource,omitempty" bson:"action_source,omitempty"`
	Data         interface{} `json:"data,omitempty" bson:"data,omitempty"`
	Message      string      `json:"message,omitempty" bson:"message,omitempty"`
}

type LogOrderWrongInvoice struct {
	ID          primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty" `
	CreatedTime *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`

	OrderID    int64  `json:"orderId,omitempty" bson:"order_id,omitempty"`
	SellerCode string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	Note       string `json:"note,omitempty" bson:"note,omitempty"`
}
