package action

import (
	"fmt"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func getListSku(skus []string) map[string]string {
	lst := make(map[string]string)
	data, es := client.Services.Product.GetListSku(skus, "00", 0, false, "")
	if es != nil && es.Status != common.APIStatus.Ok {
		return lst
	}

	for _, item := range data {
		if item.SKU != nil {
			mType := item.SKU.RetailPriceType
			lst[item.SKU.Code] = fmt.Sprintf("%v", *mType)
		}
	}
	return lst
}

func MigrateSendEmailOrderDelivering(orderID int64) *common.APIResponse {
	if orderID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order no/order id",
			ErrorCode: "MISSING_ORDER_NO",
		}
	}
	order, errRes := getOrder(orderID)

	if errRes != nil {
		return errRes
	}
	if order.Status == enum.OrderState.Delivering {
		isMissing, payload := checkIfMissingItemAtWaitToDeliver(order)
		if isMissing {
			sendMissingItemAlert(order, payload)
		}
	}

	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Order status invalid",
		ErrorCode: "ORDER_INVALID",
	}
}

func CopyNewOrder(orderID int64, withNoVoucher, withNoExtraFee, withCodMethod, withNoSplitDO bool, noteCopy string, tag string) *common.APIResponse {
	if orderID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order no/order id",
			ErrorCode: "MISSING_ORDER_NO",
		}
	}
	orderResult := model.OrderDB.QueryOne(&model.Order{
		OrderID: orderID,
	})
	if orderResult.Status != common.APIStatus.Ok {
		return orderResult
	}
	order := *orderResult.Data.([]*model.Order)[0]

	// last order-item partition
	orderItemPartitionDB := model.GetOrderItemPartitionDB(&order, "CopyNewOrder")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	if isBrandOrClinic(order.Source) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Order is brand or clinic is not allow to copy",
			ErrorCode: "ORDER_INVALID",
		}
	}

	note := fmt.Sprintf("Copy order %d/%s/%s", order.OrderID, order.OrderCode, order.SaleOrderCode)
	if noteCopy != "" {
		note = noteCopy
	}

	order.Tags = append(order.Tags, "copy")
	if withNoSplitDO {
		newTags := make([]enum.TagValue, 0)
		newTags = append(newTags, enum.Tag.REFUSE_SPLIT_ORDER)
		for _, tag := range order.Tags {
			if tag == enum.Tag.MULTIPLE_DELIVERY_ORDER || tag == enum.Tag.REFUSE_SPLIT_ORDER {
				continue
			}
			newTags = append(newTags, tag)
		}
		order.Tags = newTags
	}
	order.Tags = append(order.Tags, enum.TagValue(tag))

	ZERO := 0
	t := time.Now()
	emptyStrs := make([]*string, 0)
	newOrderID, newOrderCode := model.GetOrderID()
	order.SaleOrderCode = ""
	order.ID = &primitive.NilObjectID
	order.OrderID = newOrderID
	order.OrderCode = newOrderCode
	order.Status = enum.OrderState.WaitConfirm
	order.Note = &note
	order.ActualPrice = &ZERO
	order.ActualTotalPrice = &ZERO
	order.NotionalPrice = &ZERO
	order.NotionalPriceTime = nil
	order.DeliveryDate = nil
	order.ConfirmationDate = nil
	order.CompletedTime = nil
	order.OutboundDate = nil
	order.CreatedTime = &t
	order.DeliveryOrderCodes = make([]string, 0)
	order.IsSplitDeliveryOrder = utils.ParseBoolToPointer(false)
	order.DeliveryOrderStatuses = make([]*model.DeliveryOrderStatus, 0)
	order.PrivateNote = note
	order.DeliveryTrackingNumber = ""
	order.DeliveryOrderCode = ""
	order.DeliveryCarrier = ""
	order.DeliveryCarrierCode = ""
	order.SaleOrderStatus = ""
	order.Point = utils.ParseFloat64ToPointer(0)
	if withNoVoucher {
		order.RedeemCode = &emptyStrs
		order.RedeemApplyResult = make([]*model.PromoApplyResult, 0)
		if order.TotalDiscount != nil && *order.TotalDiscount > 0 {
			order.TotalDiscount = &ZERO
			order.TotalPrice = utils.ParseIntToPointer(*order.TotalPrice - *order.TotalDiscount)
		}
	}
	if withNoExtraFee {
		if order.ExtraFee != nil && *order.ExtraFee > 0 {
			order.TotalPrice = utils.ParseIntToPointer(*order.TotalPrice - int(*order.ExtraFee))
			order.ExtraFee = utils.ParseInt64ToPointer(0)
		}
	}

	if withCodMethod {
		order.TotalPrice = utils.ParseIntToPointer(*order.TotalPrice + int(*order.PaymentMethodFee))
		order.PaymentMethod = string(enum.PaymentMethod.COD)
		order.PaymentMethodFee = utils.ParseInt64ToPointer(0)
		order.PaymentMethodPercentage = utils.ParseFloat64ToPointer(0)
	}

	createOrderResult := model.OrderDB.Create(&order)
	if createOrderResult.Status != common.APIStatus.Ok {
		return createOrderResult
	}

	orderItemResult := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID: orderID,
	}, 0, 0, nil)
	if orderItemResult.Status != common.APIStatus.Ok {
		return orderItemResult
	}
	orderItem := orderItemResult.Data.([]*model.OrderItem)
	orderNewItem := make([]*model.OrderItem, 0)

	for _, item := range orderItem {
		if withNoVoucher && item.Type == enum.ItemType.GIFT {
			continue
		}
		p := *item
		p.OrderID = newOrderID
		p.ID = &primitive.NilObjectID
		p.OrderCode = newOrderCode
		p.CreatedTime = &t
		p.NotionalQuantity = &ZERO
		p.ReservedQuantity = &ZERO
		p.DeliveredQuantity = &ZERO
		p.OutboundQuantity = &ZERO
		p.CompletedQuantity = &ZERO
		p.ReturnedQuantity = &ZERO
		p.ActualPrice = &ZERO
		p.ActualSellerPrice = &ZERO
		p.ActualSellerRevenue = &ZERO
		p.NotionalPrice = &ZERO
		p.OutboundInfos = nil
		p.ReturnInfos = nil
		orderNewItem = append(orderNewItem, &p)
	}

	// current order-item partition
	orderItemPartitionDB = model.GetOrderItemPartitionDB(&model.Order{CreatedTime: &t}, "CopyNewOrder")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	orderCreateItemResult := orderItemPartitionDB.CreateMany(orderNewItem)
	if orderCreateItemResult.Status != common.APIStatus.Ok {
		return orderCreateItemResult
	}

	sellerCodes, skus, itemCodes, productIds, dealCodes, subSkus, subProductIds := GetOrderDistinctInfo(orderNewItem)
	model.OrderDetailDB.Create(&model.OrderDetail{
		CreatedTime:   order.CreatedTime,
		OrderID:       order.OrderID,
		OrderCode:     order.OrderCode,
		AccountID:     order.AccountID,
		CustomerID:    order.CustomerID,
		CustomerCode:  order.CustomerCode,
		SellerCodes:   sellerCodes,
		Skus:          skus,
		ItemCodes:     itemCodes,
		ProductIds:    productIds,
		DealCodes:     dealCodes,
		SubSkus:       &subSkus,
		SubProductIds: &subProductIds,
	})

	return orderItemResult
}

func MigrateOrderPoint(orderID int64, point float64) *common.APIResponse {
	if orderID > 0 && point > 0 {
		q := model.OrderDB.QueryOne(&model.Order{
			OrderID: orderID,
			Status:  enum.OrderState.Completed,
		})
		if q.Status == common.APIStatus.Ok {
			// o := q.Data.([]*model.Order)[0]
			return model.OrderDB.UpdateOne(&model.Order{
				OrderID: orderID,
			}, &model.Order{
				Point: &point,
			})
			// return q
		}
	}
	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Order id invalid",
		ErrorCode: "ORDER_INVALID",
	}
}

func MigrateRecCodeForReconcile() *common.APIResponse {
	var offset, limit int64 = 0, 100

	for {
		queryRes := model.ReconciliationDB.Query(&model.Reconciliation{}, offset, limit, &primitive.M{"_id": 1})
		if queryRes.Status != common.APIStatus.Ok {
			break
		}

		reconciliationList := queryRes.Data.([]*model.Reconciliation)
		for _, reconciliation := range reconciliationList {
			if reconciliation.RecCode == "" {
				recCode := fmt.Sprintf("%s_%s", reconciliation.SellerCode, reconciliation.ReconcileScheduleTimeIndex)
				model.ReconciliationDB.UpdateOne(&model.Reconciliation{
					ID: reconciliation.ID,
				}, &model.Reconciliation{
					RecCode: recCode,
				})
			}
		}

		offset += limit
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate finish",
	}
}
