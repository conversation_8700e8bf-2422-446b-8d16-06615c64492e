package tool

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"text/template"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type InputRequestUpdateOrder struct {
	OrderID int64 `json:"order_id"`
}

type InputRequestUpdateOrderPoint struct {
	OrderID int64     `json:"order_id"`
	From    time.Time `json:"from"`
	To      time.Time `json:"to"`
}

const (
	INVOICE_BY_DO         string = "INVOICE_BY_DO"
	INVOICE_BY_SO         string = "INVOICE_BY_SO"
	INVOICE_DEADLINE_DAYS int    = 8
)

func getInvoiceDeadlineSettingForSC() int {
	confResp := client.Services.Seller.GetSettingForSC("SELLER_INVOICE_CONFIG")
	if confResp.Status != common.APIStatus.Ok {
		return INVOICE_DEADLINE_DAYS
	}

	settings := confResp.Data.([]client.SettingForSC)[0]

	for _, item := range settings.Data {
		if item["Key"] == "invoiceDeadline" {
			if value, ok := item["Value"].(float64); ok {
				return int(value)
			}
		}
	}

	return INVOICE_DEADLINE_DAYS
}

func PopOrderSeller(orderSellers []*model.OrderSeller, sellerCode string) ([]*model.OrderSeller, *model.OrderSeller) {
	var orderSeller *model.OrderSeller
	newOrderSellers := make([]*model.OrderSeller, 0, len(orderSellers))
	for _, os := range orderSellers {
		if os.SellerCode == sellerCode {
			orderSeller = os
		} else {
			newOrderSellers = append(newOrderSellers, os)
		}
	}
	return newOrderSellers, orderSeller
}

func RepushQueueSellerInvoice(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *InputRequestUpdateOrderPoint
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	filter := model.Order{
		OrderID: input.OrderID,
		Status:  enum.OrderState.Completed,
	}
	if !input.From.IsZero() {
		if input.To.IsZero() {
			input.To = time.Now()
		}

		filter.ComplexQuery = []*bson.M{
			{
				"delivered_time": bson.M{
					"$gte": input.From,
					"$lte": input.To,
				},
			},
		}
	}

	total := model.OrderDB.Count(filter).Total
	result := model.OrderDB.Query(filter, 0, total, nil)
	if result.Status != common.APIStatus.Ok {
		return res.Respond(result)
	}

	orders := result.Data.([]*model.Order)
	ids := make([]int64, 0, total)
	errors := make([]bson.M, 0, total)
	for i := range orders {
		ids = append(ids, orders[i].OrderID)

		pErr := model.CreateSellerInvoiceJob.Push(orders[i], &job.JobItemMetadata{
			Keys: []string{
				"repush",
				strconv.Itoa(int(orders[i].OrderID)),
			},
			Topic: "default",
		})
		if pErr != nil {
			errors = append(errors, bson.M{
				"order_id": orders[i].OrderID,
				"error":    pErr.Error(),
			})
		}
	}
	return res.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data: []interface{}{
			errors, ids,
		},
		Message: fmt.Sprintf("%d / %d", len(errors), total),
	})
}

func hasInvoiceTag(item *model.OrderItem) bool {
	if len(item.Tags) > 0 {
		for _, tag := range item.Tags {
			if tag == "HOADONNHANH" {
				return true
			}
		}
	}

	return false
}

func CanExportInvoice(item *model.OrderItem) bool {
	if item.Type == enum.ItemType.GIFT {
		return false
	}
	if !hasInvoiceTag(item) {
		return false
	}

	if (item.NoneVat == nil || !*item.NoneVat) && (item.VAT != nil && *item.VAT > 0) {
		return true
	}
	return false
}

// updateInvoicePartnerConfig sets up the invoice partner configuration based on seller info
func updateInvoicePartnerConfig(seller *client.Seller, invoice *model.Invoice) (needUpdate bool) {
	if seller == nil || seller.HiloInfo == nil || !seller.HiloInfo.IsActive || seller.HiloInfo.Series == "" {
		return
	}

	invoice.Config = &model.InvoicePartnerConfig{
		PartnerCode:          "HILO", // Hardcode HILO for now. Will support more partners in the future
		Pattern:              seller.HiloInfo.Pattern,
		Series:               seller.HiloInfo.Series,
		SignedBy:             seller.HiloInfo.SignedBy,
		InvoiceTemplateCode:  seller.HiloInfo.InvoiceTemplateCode,
		ExportBuyerNoRequest: seller.HiloInfo.ExportBuyerNoRequest,
	}

	if seller.InvoiceInfo != nil {
		invoice.Config.TaxOfCode = *seller.InvoiceInfo.Tax
		invoice.Config.SellerTax = *seller.InvoiceInfo.Tax
		invoice.Config.SellerEmail = *seller.InvoiceInfo.Email
		invoice.Config.SellerPhoneNumber = *seller.InvoiceInfo.Phone
	}
	needUpdate = true

	return
}

func ProcessUpdateInvoiceForSeller(input *InputRequestUpdateOrder) *common.APIResponse {
	if input.OrderID == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing order_id",
		}
	}

	// Fetch order
	orderResult := model.OrderDB.QueryOne(model.Order{OrderID: input.OrderID})
	if orderResult.Status != common.APIStatus.Ok {
		return orderResult
	}
	order := orderResult.Data.([]*model.Order)[0]

	// Dont create invoice for old orders
	if order.DeliveredTime != nil && order.DeliveredTime.Before(time.Date(2024, 9, 30, 0, 0, 0, 0, time.FixedZone("+07", 7*60*60))) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Order delivered before 2024-09-30T17:58:33.445+07:00",
			ErrorCode: "ORDER_TOO_OLD",
		}
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ProcessUpdateInvoiceForSeller")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	if !order.CanCreateInvoice() {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Order status is %s. Not ready for creating invoice", order.Status),
			ErrorCode: "NOT_DELIVERED",
		}
	}

	// Fetch order sellers
	orderSellers := reconcile_action.GetOrderSellers(input.OrderID)
	if len(orderSellers) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("No EXTERNAL seller found in order: %d", input.OrderID),
			ErrorCode: "EXTERNAL_SELLER_NOT_FOUND",
		}
	}

	// Fetch unique sellerCode
	orderItemFilter := model.OrderItem{
		OrderID:     input.OrderID,
		SellerClass: &enum.SellerClass.EXTERNAL,
		ComplexQuery: []*bson.M{
			{
				"outbound_quantity": bson.M{"$gt": 0},
			},
		},
	}
	result := orderItemPartitionDB.Distinct(orderItemFilter, "seller_code")
	if result.Status != common.APIStatus.Ok {
		result.Message = "No seller-item can be proceed invoice (outbound_quantity > 0)"
		result.ErrorCode = "NO_ITEM_ABLE"
		return result
	}

	sellerCodesData := result.Data.([]interface{})
	sellerCodes := make([]string, len(sellerCodesData))
	for i, sellerCode := range sellerCodesData {
		sellerCodes[i] = sellerCode.(string)
	}
	if len(sellerCodes) == 0 {
		result.Message = "No seller-item can be proceed invoice (outbound_quantity > 0)"
		result.ErrorCode = "NO_ITEM_ABLE"
		return result
	}
	sellerCodes = utils.UniqueStringSlice(sellerCodes)

	// Fetch sellers
	sellersRes := client.Services.Seller.GetSellerListWithParams(client.ReqSellerList{
		SellerCodes:    sellerCodes,
		Limit:          len(sellerCodes),
		GetInvoiceInfo: true,
		GetConfigInfo:  true,
	})
	if sellersRes.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    sellersRes.Status,
			Message:   sellersRes.Message,
			ErrorCode: sellersRes.ErrorCode,
		}
	}
	sellerMap := map[string]*client.Seller{}
	for _, s := range sellersRes.Data {
		sellerMap[s.Code] = s
	}

	listErr := []string{}
	listMessage := []string{}
	for _, sellerCode := range sellerCodes {
		if sellerCode == "" {
			continue
		}

		filter := model.Invoice{
			OrderCode:  order.OrderCode,
			SellerCode: sellerCode,
		}
		invoiceRes := model.InvoiceDB.QueryOne(filter)
		if invoiceRes.Status == common.APIStatus.Ok {
			invoice := invoiceRes.Data.([]*model.Invoice)[0]
			if invoice.ReceiveInvoiceInfoBy != nil && *invoice.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
				continue
			}
		}

		// Fetch orderItems
		itemFilter := model.OrderItem{
			OrderID:    input.OrderID,
			SellerCode: sellerCode,
			ComplexQuery: []*bson.M{
				{
					"outbound_quantity": bson.M{"$gt": 0},
				},
			},
		}
		total := orderItemPartitionDB.Count(itemFilter).Total
		itemRes := orderItemPartitionDB.Query(itemFilter, 0, total, nil)
		if itemRes.Status != common.APIStatus.Ok {
			continue
		}
		orderItems := itemRes.Data.([]*model.OrderItem)

		// =================== Check data integrity =====================
		// if process item by SO
		// all item have to be invoiced by SO
		// if have any item invoiced by DO -> fail
		haveSOItems := false
		haveDOItems := false
		for _, orderItem := range orderItems {
			if orderItem.SellerCode == sellerCode && (orderItem.ReceiveInvoiceInfoBy == nil || *orderItem.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_SO) {
				haveSOItems = true
			}

			if orderItem.SellerCode == sellerCode && orderItem.ReceiveInvoiceInfoBy != nil && *orderItem.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
				haveDOItems = true
			}
		}

		if !haveSOItems {
			continue
		}

		if haveDOItems {
			listErr = append(listMessage, fmt.Sprintf("Some items of order %d, seller %s are invoiced by DO", input.OrderID, sellerCode))
			go func() {
				model.LogOrderWrongInvoiceDb.Create(&model.LogOrderWrongInvoice{
					OrderID:    input.OrderID,
					SellerCode: sellerCode,
					Note:       fmt.Sprintf("Invoice type of order %d, seller %s is not consistent", input.OrderID, sellerCode),
				})
			}()
			continue
		}
		// =================== END Check data integrity =====================

		// Prepare InvoiceItems (sku that can be included in invoice)
		var (
			invoiceItems []*model.InvoiceItem
			priceInvoice int
		)

		for _, orderItem := range orderItems {
			if orderItem.ReceiveInvoiceInfoBy != nil && *orderItem.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
				continue
			}

			if orderItem.OutboundQuantity == nil || *orderItem.OutboundQuantity == 0 {
				continue
			}

			// If item is not combo & item has tags => check HOADONNHANH
			if orderItem.SubItems == nil && orderItem.Tags != nil && len(orderItem.Tags) > 0 {
				if !hasInvoiceTag(orderItem) {
					continue
				}

				priceInvoice += getPriceItem(order, orderItem)
			}

			// If item is combo & check its sub items
			if orderItem.SubItems != nil && len(*orderItem.SubItems) > 0 {
				// Check sub item in combo
				subItems := *orderItem.SubItems

				isTrue := false
				for _, subItem := range subItems {
					if CanExportInvoice(subItem) {
						isTrue = true
						break
					}
				}
				if isTrue {
					priceInvoice += getPriceItem(order, orderItem)
				}

				totalPriceItem := 0
				for _, subItem := range subItems {
					if subItem.NoneVat != nil && !*subItem.NoneVat && subItem.VAT != nil && *subItem.VAT > 0 {
						totalPriceItem += subItem.SellerPrice * subItem.Quantity
					}
				}
				for _, subItem := range subItems {
					if totalPriceItem == 0 {
						continue
					}

					totalPrice := orderItem.SellerPrice
					if totalPrice < totalPriceItem {
						totalPrice = totalPriceItem
					}
					rate := totalPrice / totalPriceItem
					priceItem := subItem.SellerPrice * rate
					subItem.Price = priceItem
					if orderItem.OutboundQuantity != nil {
						p1 := *orderItem.OutboundQuantity * subItem.Quantity
						subItem.OutboundQuantity = &p1

						p2 := subItem.Price * *subItem.OutboundQuantity
						subItem.ActualPrice = &p2
					}
					subItem.OrderID = orderItem.OrderID
					subItem.SellerPrice = subItem.Price

					invoiceItems = append(invoiceItems, &model.InvoiceItem{OrderItem: *subItem})
				}
			} else if CanExportInvoice(orderItem) {
				invoiceItems = append(invoiceItems, &model.InvoiceItem{OrderItem: *orderItem})
			}
		}

		if len(invoiceItems) == 0 {
			listMessage = append(listMessage, fmt.Sprintf("No items are qualified for invoice OrderID=%d, sellerCode=%s", input.OrderID, sellerCode))
			deleteInvoiceIfExist(order.OrderCode, sellerCode)
			continue
		}

		seller := sellerMap[sellerCode]
		if seller == nil {
			listMessage = append(listMessage, fmt.Sprintf("Nil seller, sellerCode=%s", sellerCode))
			deleteInvoiceIfExist(order.OrderCode, sellerCode)
			continue
		}

		if seller.ConfigInfo != nil && priceInvoice < seller.ConfigInfo.MinInvoiceValue {
			listMessage = append(listMessage, fmt.Sprintf(
				"Invoice price: %v is less than min config: %v, sellerCode=%s",
				priceInvoice, seller.ConfigInfo.MinInvoiceValue, sellerCode,
			))
			deleteInvoiceIfExist(order.OrderCode, sellerCode)
			continue
		}

		var selectedOS *model.OrderSeller
		orderSellers, selectedOS = PopOrderSeller(orderSellers, sellerCode)
		if selectedOS == nil {
			listMessage = append(listMessage, fmt.Sprintf("Not found order-seller sellerCode=%s", sellerCode))
			deleteInvoiceIfExist(order.OrderCode, sellerCode)
			continue
		}

		// Create or Update Invoice
		updater := model.Invoice{
			InvoiceDeadline:      nil,
			IssuedTime:           nil,
			OrderID:              order.OrderID,
			OrderSellerID:        selectedOS.OrderSellerID,
			CustomerID:           order.CustomerID,
			OrderCode:            order.OrderCode,
			SellerCode:           sellerCode,
			OrderSellerCode:      selectedOS.OrderSellerCode,
			CustomerCode:         order.CustomerCode,
			InvoiceDocumentName:  nil,
			InvoiceDocumentURL:   nil,
			InvoiceStatus:        "",
			IsConfirmed:          nil,
			InvoicePrice:         priceInvoice,
			AutoChangeToOverdue:  utils.ParseBoolToPointer(true),
			ReceiveInvoiceInfoBy: &enum.ReceiveInvoiceInfoBy.INVOICE_BY_SO,
		}

		request := false
		if order.Invoice != nil && order.Invoice.RequestInvoice != nil {
			request = *order.Invoice.RequestInvoice
			updater.Request = order.Invoice.RequestInvoice
		}

		// Check customer tax info
		{
			customerTaxInfo := model.CustomerTaxInfo{
				CustomerScope: order.CustomerScope,
			}

			if order.Invoice != nil {
				requestInfo := order.Invoice

				customerTaxInfo.RequestInfo = *requestInfo
				customerTaxInfo.BuyerName = order.CustomerName

				taxCode := order.Invoice.TaxCode

				taxCodeInfo := client.Services.Invoice.GetTaxInfo(taxCode)
				if taxCodeInfo != nil {
					customerTaxInfo.TaxGov = *taxCodeInfo

					// We use HILO to verify tax code, but if there
					// is an incident, we consider INVALID tax as VALID
					// to allow customer to checkout, but we won't
					// create seller invoice for this order
					if taxCodeInfo.IsIncidentTaxCode != nil && *taxCodeInfo.IsIncidentTaxCode {
						listMessage = append(listMessage, fmt.Sprintf("Order %d has incident tax code", input.OrderID))
						continue
					}
				}

				customerTaxInfo.CustomerTaxGOVStatus = customerTaxInfo.TaxGov.CustomerTaxGOVStatus
				if customerTaxInfo.CustomerTaxGOVStatus != enum.CustomerTaxStatus.VALID {
					request = false
				}
			}

			updater.CustomerTax = &customerTaxInfo
		}

		minInvoicePenaltyValue := GetMinInvoicePenalty()
		if priceInvoice < minInvoicePenaltyValue {
			updater.AutoChangeToOverdue = utils.ParseBoolToPointer(false)
		}

		circaIssuance, noNeedInvoice := getCircaIssuance(request, sellerCode, order.CustomerTags)
		updater.CircaIssuance = &circaIssuance
		updater.NoNeedInvoice = &noNeedInvoice

		// NOTFOUND -> Create invoice
		if invoiceRes.Status == common.APIStatus.NotFound {
			invoiceDeadLineDate := getInvoiceDeadlineSettingForSC()
			deadline := order.DeliveredTime.AddDate(0, 0, invoiceDeadLineDate)
			id, code := model.GetInvoiceID()

			f := false
			updater.DownloadedFile = &f
			updater.SellerDownloadedFile = &f
			updater.SellerSentMail = &f

			updater.DeliveredTime = order.DeliveredTime
			updater.OutboundTime = order.OutboundDate
			updater.InvoiceDeadline = &deadline
			updater.InvoiceStatus = model.InvoiceStatus.Waiting
			updater.InvoiceID = id
			updater.InvoiceCode = code
			updater.Logs = []string{"Create ProcessUpdateInvoiceForSeller"}
		} else
		// Else -> Update invoice
		if invoiceRes.Status == common.APIStatus.Ok {
			invoice := invoiceRes.Data.([]*model.Invoice)[0]

			updater.InvoiceDeadline = invoice.InvoiceDeadline
			updater.InvoiceStatus = invoice.InvoiceStatus
			updater.InvoiceID = invoice.InvoiceID
			updater.InvoiceCode = invoice.InvoiceCode
			updater.DownloadedFile = invoice.DownloadedFile
			updater.SellerDownloadedFile = invoice.SellerDownloadedFile
			updater.Logs = append(invoice.Logs, "Update ProcessUpdateInvoiceForSeller")

			if invoice.InvoiceStatus == model.InvoiceStatus.Waiting && invoice.IssuedTime != nil && !invoice.IssuedTime.IsZero() {
				sel, err := bson.Marshal(filter)
				if err == nil {
					obj := bson.M{}
					bson.Unmarshal(sel, &obj)

					model.InvoiceDB.GetClient().
						Database(model.InvoiceDB.DBName).
						Collection(model.InvoiceDB.ColName).
						FindOneAndUpdate(context.TODO(), obj, bson.M{"$unset": bson.M{"issued_time": 1}})
				}
			}
		} else {
			listErr = append(listErr, "Get invoice for "+sellerCode+" failed: "+invoiceRes.Message)
			continue
		}

		// Add HILO info to invoice
		updateInvoicePartnerConfig(seller, &updater)

		invoiceRes = model.InvoiceDB.Upsert(filter, updater)
		if invoiceRes.Status == common.APIStatus.Ok {
			invoice := invoiceRes.Data.([]*model.Invoice)[0]
			HandleSyncInvoice(invoice)
		}
	}

	if len(listErr) == 0 {
		messages := strings.Join(listMessage, ". ")
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Create/Update Invoice for seller successfully. " + messages,
			Data:    listMessage,
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Invalid,
		Data:   listErr,
		Total:  int64(len(listErr)),
	}
}

func ProcessUpsertInvoiceForDOSeller(input *model.OrderProcessRequest) *common.APIResponse {
	if input.OrderId == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing order_id",
		}
	}
	if input.DeliveryOrderStatus != enum.OrderState.Delivering &&
		input.DeliveryOrderStatus != enum.OrderState.Delivered &&
		input.DeliveryOrderStatus != enum.OrderState.Completed {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Invalid status by OrderID: %v, status: %v", input.OrderId, input.DeliveryOrderStatus),
		}
	}
	if len(input.Items) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Items is empty",
		}
	}

	if input.DeliveryOrderCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing delivery_order_code",
		}
	}

	// Fetch order sellers
	orderSellers := reconcile_action.GetOrderSellers(input.OrderId)
	if len(orderSellers) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("No EXTERNAL seller found in order: %d", input.OrderId),
			ErrorCode: "EXTERNAL_SELLER_NOT_FOUND",
		}
	}

	skus := make([]string, 0, len(input.Items))
	for _, item := range input.Items {
		if item == nil {
			continue
		}

		skus = append(skus, item.Sku)
	}

	// Fetch order
	orderResult := model.OrderDB.QueryOne(model.Order{OrderID: input.OrderId})
	if orderResult.Status != common.APIStatus.Ok {
		return orderResult
	}
	order := orderResult.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ProcessUpsertInvoiceForDOSeller")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	orderItemResults := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID: input.OrderId,
		ComplexQuery: []*bson.M{
			{
				"sku": bson.M{"$in": skus},
			},
		},
	}, 0, 0, nil)
	if orderItemResults.Status != common.APIStatus.Ok {
		return orderItemResults
	}

	orderItems := orderItemResults.Data.([]*model.OrderItem)

	sellerCodes := make([]string, 0, len(orderItems))
	sellerOrderItemMap := make(map[string]*model.OrderItem)
	orderItemsByDOCodeMap := make(map[string]*model.OrderItem)
	for _, orderItem := range orderItems {
		if orderItem == nil {
			continue
		}

		for _, outboundInfo := range orderItem.OutboundInfos {
			if outboundInfo == nil || outboundInfo.DoCode != input.DeliveryOrderCode {
				continue
			}

			orderItemsByDOCodeMap[orderItem.Sku] = orderItem
			sellerOrderItemMap[orderItem.SellerCode] = orderItem
			sellerCodes = append(sellerCodes, orderItem.SellerCode)
		}

		// combo
		if orderItem.SubItems != nil {
			for _, subItem := range *orderItem.SubItems {
				for _, outboundInfo := range subItem.OutboundInfos {
					if outboundInfo == nil || outboundInfo.DoCode != input.DeliveryOrderCode {
						continue
					}

					subItem.ReceiveInvoiceInfoBy = orderItem.ReceiveInvoiceInfoBy
					orderItemsByDOCodeMap[subItem.Sku] = subItem
					sellerOrderItemMap[subItem.SellerCode] = subItem
					sellerCodes = append(sellerCodes, subItem.SellerCode)
				}
			}
		}
	}
	sellerCodes = utils.UniqueStringSlice(sellerCodes)

	// Fetch sellers
	sellersRes := client.Services.Seller.GetSellerListWithParams(client.ReqSellerList{
		SellerCodes:    sellerCodes,
		Limit:          len(sellerCodes),
		GetInvoiceInfo: true,
		GetConfigInfo:  true,
	})
	if sellersRes.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    sellersRes.Status,
			Message:   sellersRes.Message,
			ErrorCode: sellersRes.ErrorCode,
		}
	}

	sellerMap := map[string]*client.Seller{}
	for _, s := range sellersRes.Data {
		sellerMap[s.Code] = s
	}

	for _, sellerCode := range sellerCodes {
		seller, exists := sellerMap[sellerCode]
		if !exists {
			continue
		}

		// ================ START Check data integrity =============
		haveDoItems := false
		haveSOItems := false
		for _, item := range orderItems {
			if item.SellerCode == sellerCode && item.ReceiveInvoiceInfoBy != nil && *item.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
				haveDoItems = true
			}

			if item.SellerCode == sellerCode && (item.ReceiveInvoiceInfoBy == nil || *item.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_SO) {
				haveSOItems = true
			}
		}

		if !haveDoItems {
			continue
		}

		// If have item invoice by DO
		// all item in order much be invoiced by DO
		if haveSOItems {
			go func() {
				model.LogOrderWrongInvoiceDb.Create(&model.LogOrderWrongInvoice{
					OrderID:    input.OrderId,
					SellerCode: sellerCode,
					Note:       fmt.Sprintf("Invoice type of order %d, seller %s is not consistent", input.OrderId, sellerCode),
				})
			}()
			continue
		}
		// ================ END Check data integrity =============

		var (
			invoiceItems []*model.InvoiceItem
			priceInvoice int
		)

		for _, orderItem := range orderItems {
			if orderItem.ReceiveInvoiceInfoBy == nil || *orderItem.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_SO {
				continue
			}

			// If item is combo & check its sub items
			if orderItem.SubItems != nil && len(*orderItem.SubItems) > 0 {
				// Check sub item in combo
				subItems := *orderItem.SubItems

				isTrue := false
				for _, subItem := range subItems {
					if CanExportInvoice(subItem) {
						isTrue = true
						break
					}
				}
				if isTrue {
					priceInvoice += getPriceItem(order, orderItem)
				}

				totalPriceItem := 0
				for _, subItem := range subItems {
					if subItem.NoneVat != nil && !*subItem.NoneVat && subItem.VAT != nil && *subItem.VAT > 0 {
						totalPriceItem += subItem.SellerPrice * subItem.Quantity
					}
				}
				for _, subItem := range subItems {
					if totalPriceItem == 0 {
						continue
					}

					totalPrice := orderItem.SellerPrice
					if totalPrice < totalPriceItem {
						totalPrice = totalPriceItem
					}
					rate := totalPrice / totalPriceItem
					priceItem := subItem.SellerPrice * rate
					subItem.Price = priceItem
					if orderItem.OutboundQuantity != nil {
						p1 := *orderItem.OutboundQuantity * subItem.Quantity
						subItem.OutboundQuantity = &p1

						p2 := subItem.Price * *subItem.OutboundQuantity
						subItem.ActualPrice = &p2
					}
					subItem.OrderID = orderItem.OrderID
					subItem.SellerPrice = subItem.Price

					invoiceItems = append(invoiceItems, &model.InvoiceItem{OrderItem: *subItem})
				}
			} else if CanExportInvoice(orderItem) {

				if _, exists := orderItemsByDOCodeMap[orderItem.Sku]; !exists {
					continue
				}

				priceInvoice += getPriceItem(order, orderItem)
				invoiceItems = append(invoiceItems, &model.InvoiceItem{OrderItem: *orderItem})
			}

		}

		if len(invoiceItems) == 0 {
			deleteInvoiceIfExist(order.OrderCode, sellerCode, input.DeliveryOrderCode)
			continue
		}

		if seller.ConfigInfo != nil && priceInvoice < seller.ConfigInfo.MinInvoiceValue {
			deleteInvoiceIfExist(order.OrderCode, sellerCode, input.DeliveryOrderCode)
			continue
		}

		var selectedOS *model.OrderSeller
		orderSellers, selectedOS = PopOrderSeller(orderSellers, sellerCode)
		if selectedOS == nil {
			deleteInvoiceIfExist(order.OrderCode, sellerCode, input.DeliveryOrderCode)
			continue
		}

		filter := model.Invoice{
			OrderCode:         order.OrderCode,
			SellerCode:        sellerCode,
			DeliveryOrderCode: &input.DeliveryOrderCode,
		}
		invoiceDeadLineDate := getInvoiceDeadlineSettingForSC()
		deadline := input.ActionTime.AddDate(0, 0, invoiceDeadLineDate)
		updater := model.Invoice{
			InvoiceDeadline:      &deadline,
			IssuedTime:           nil,
			OrderID:              order.OrderID,
			OrderSellerID:        selectedOS.OrderSellerID,
			CustomerID:           order.CustomerID,
			OrderCode:            order.OrderCode,
			SellerCode:           sellerCode,
			OrderSellerCode:      selectedOS.OrderSellerCode,
			CustomerCode:         order.CustomerCode,
			InvoiceDocumentName:  nil,
			InvoiceDocumentURL:   nil,
			InvoiceStatus:        "",
			IsConfirmed:          nil,
			InvoicePrice:         priceInvoice,
			AutoChangeToOverdue:  utils.ParseBoolToPointer(true),
			ReceiveInvoiceInfoBy: &enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO,
			DeliveryOrderCode:    &input.DeliveryOrderCode,
		}

		request := false
		if order.Invoice != nil && order.Invoice.RequestInvoice != nil {
			request = *order.Invoice.RequestInvoice
			updater.Request = order.Invoice.RequestInvoice
		}

		// Check customer tax info
		{
			customerTaxInfo := model.CustomerTaxInfo{
				CustomerScope: order.CustomerScope,
			}

			if order.Invoice != nil {
				requestInfo := order.Invoice

				customerTaxInfo.RequestInfo = *requestInfo
				customerTaxInfo.BuyerName = order.CustomerName

				taxCode := order.Invoice.TaxCode

				taxCodeInfo := client.Services.Invoice.GetTaxInfo(taxCode)
				if taxCodeInfo != nil {
					customerTaxInfo.TaxGov = *taxCodeInfo

					// We use HILO to verify tax code, but if there
					// is an incident, we consider INVALID tax as VALID
					// to allow customer to checkout, but we won't
					// create seller invoice for this order
					if taxCodeInfo.IsIncidentTaxCode != nil && *taxCodeInfo.IsIncidentTaxCode {
						return &common.APIResponse{
							Status:  common.APIStatus.Invalid,
							Message: fmt.Sprintf("Order %d has incident tax code", order.OrderID),
						}
					}
				}

				customerTaxInfo.CustomerTaxGOVStatus = customerTaxInfo.TaxGov.CustomerTaxGOVStatus
				if customerTaxInfo.CustomerTaxGOVStatus != enum.CustomerTaxStatus.VALID {
					request = false
				}
			}

			updater.CustomerTax = &customerTaxInfo
		}

		minInvoicePenaltyValue := GetMinInvoicePenalty()
		if priceInvoice < minInvoicePenaltyValue {
			updater.AutoChangeToOverdue = utils.ParseBoolToPointer(false)
		}

		circaIssuance, noNeedInvoice := getCircaIssuance(request, sellerCode, order.CustomerTags)
		updater.CircaIssuance = &circaIssuance
		updater.NoNeedInvoice = &noNeedInvoice

		invoiceRes := model.InvoiceDB.QueryOne(filter)
		if invoiceRes.Status == common.APIStatus.NotFound {
			id, code := model.GetInvoiceID()

			f := false
			updater.DownloadedFile = &f
			updater.SellerDownloadedFile = &f
			updater.SellerSentMail = &f

			updater.InvoiceStatus = model.InvoiceStatus.Waiting
			updater.InvoiceID = id
			updater.InvoiceCode = code

			updater.OutboundTime = input.ActionTime
			updater.Logs = []string{"Create ProcessUpsertInvoiceForDOSeller"}

		} else if input.DeliveryOrderStatus == enum.OrderState.Delivered {
			invoice := invoiceRes.Data.([]*model.Invoice)[0]
			if invoice.ReceiveInvoiceInfoBy != nil && *invoice.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
				updater.DeliveredTime = input.ActionTime
				updater.InvoiceStatus = invoice.InvoiceStatus
				updater.InvoiceID = invoice.InvoiceID
				updater.InvoiceCode = invoice.InvoiceCode
				updater.DownloadedFile = invoice.DownloadedFile
				updater.SellerDownloadedFile = invoice.SellerDownloadedFile
				updater.Logs = append(invoice.Logs, "Update ProcessUpsertInvoiceForDOSeller")
				if invoice.InvoiceStatus == model.InvoiceStatus.Waiting && invoice.IssuedTime != nil && !invoice.IssuedTime.IsZero() {
					sel, err := bson.Marshal(filter)
					if err == nil {
						obj := bson.M{}
						bson.Unmarshal(sel, &obj)

						model.InvoiceDB.GetClient().
							Database(model.InvoiceDB.DBName).
							Collection(model.InvoiceDB.ColName).
							FindOneAndUpdate(context.TODO(), obj, bson.M{"$unset": bson.M{"issued_time": 1}})
					}
				}
			}
		}

		// Add HILO info to invoice
		updateInvoicePartnerConfig(seller, &updater)

		if input.DeliveredTime != nil {
			updater.DeliveredTime = input.DeliveredTime
		}

		if input.OutboundDate != nil {
			updater.OutboundTime = input.OutboundDate
		}

		invoiceRes = model.InvoiceDB.Upsert(filter, updater)
		if invoiceRes.Status == common.APIStatus.Ok {
			invoice := invoiceRes.Data.([]*model.Invoice)[0]
			HandleSyncInvoice(invoice)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Create Invoice DO for seller successfully. ",
	}
}

func deleteInvoiceIfExist(orderCode, sellerCode string, doCode ...string) {
	query := model.Invoice{
		OrderCode:  orderCode,
		SellerCode: sellerCode,
	}

	if len(doCode) > 0 && doCode[0] != "" {
		query.DeliveryOrderCode = &doCode[0]
	}

	res := model.InvoiceDB.QueryOne(query)
	if res.Status == common.APIStatus.Ok {
		invoice := res.Data.([]*model.Invoice)[0]
		res := model.InvoiceDB.Delete(&model.Invoice{InvoiceID: invoice.InvoiceID})
		if res.Status != common.APIStatus.Ok {
			fmt.Printf("ERROR Delete Invoice orderCode=%s,sellerCode=%s\n: %#v", orderCode, sellerCode, res)
			return
		}
		res = model.InvoiceItemDB.Delete(&model.InvoiceItem{InvoiceCode: invoice.InvoiceCode})
		if res.Status != common.APIStatus.Ok {
			fmt.Printf("ERROR Delete InvoiceItem invoiceID=%d\n: %#v", invoice.InvoiceID, res)
			return
		}
	}
}

func getCircaIssuance(
	request bool,
	sellerCode string,
	customerTags []string,
) (circaIssuance bool, noNeedInvoice bool) {
	if request {
		return
	}

	// if !utils.IsContains(customerTags, "NON_INVOICE_CUSTOMER") {
	// 	noNeedInvoice = true
	// 	return
	// }

	mapSellerCodes := GetSellersSyncCirca()
	if mapSellerCodes[sellerCode] {
		circaIssuance = true
		return
	}

	noNeedInvoice = true
	return
}

func UpdateItem(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *model.OrderItem
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	filter := model.OrderItem{
		OrderID:   input.OrderID,
		ProductID: input.ProductID,
		Type:      input.Type,
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{OrderID: input.OrderID, OrderCode: input.OrderCode, CreatedTime: input.CreatedTime}, "UpdateItem")
	if orderItemPartitionDB == nil {
		return errors.New(model.PARTITION_NOT_FOUND_RESPONSE.Message)
	}

	response := orderItemPartitionDB.Upsert(filter, input)
	return res.Respond(response)
}

func GetNeedUpdateOrderPoint(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *InputRequestUpdateOrderPoint
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	if input.OrderID == 0 && (input.From.IsZero()) {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing order_id || from",
		})
	}

	return res.Respond(getNeedUpdatePoint(input))
}

func getNeedUpdatePoint(input *InputRequestUpdateOrderPoint) *common.APIResponse {
	if input.OrderID != 0 {
		result := model.OrderDB.QueryOne(model.Order{OrderID: input.OrderID})
		return result
	} else {
		filter := model.Order{
			Status: enum.OrderState.Completed,
		}

		filter.ComplexQuery = append(filter.ComplexQuery, &bson.M{
			"completed_time": bson.M{
				"$gte": input.From,
				"$lt":  input.To,
			},
		})

		filter.ComplexQueryOr = append(filter.ComplexQueryOr, &bson.M{
			"point": bson.M{"$exists": false},
		})
		filter.ComplexQueryOr = append(filter.ComplexQueryOr, &bson.M{
			"actual_price": bson.M{"$exists": false},
		})
		filter.ComplexQueryOr = append(filter.ComplexQueryOr, &bson.M{
			"actual_price": bson.M{"$gt": 0},
			"point":        bson.M{"$lt": 1},
		})

		total := model.OrderDB.Count(filter).Total
		result := model.OrderDB.Query(filter, 0, total, &primitive.M{
			"completed_time": 1,
			"point":          1,
		})
		return result
	}
}

func CreateInvoice(req sdk.APIRequest, res sdk.APIResponder) error {
	type InvalidOrder struct {
		OrderID int64  `json:"order_id"`
		Reason  string `json:"reason"`
	}
	var input *InputRequestUpdateOrderPoint
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	if input.OrderID == 0 && (input.From.IsZero()) {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing order_id || from",
		})
	}

	filter := model.Order{
		Status: enum.OrderState.Completed,
	}
	filter.ComplexQuery = append(filter.ComplexQuery, &bson.M{
		"completed_time": bson.M{
			"$gte": input.From,
			"$lt":  input.To,
		},
	})

	total := model.OrderDB.Count(filter).Total
	result := model.OrderDB.Query(filter, 0, total, nil)
	if result.Status != common.APIStatus.Ok {
		return res.Respond(result)
	}

	orders := result.Data.([]*model.Order)
	invalidOrder := make([]InvalidOrder, 0, result.Total)
	for i := range orders {
		if !orders[i].CanCreateInvoice() {
			invalidOrder = append(invalidOrder, InvalidOrder{
				OrderID: orders[i].OrderID,
				Reason:  "status = " + string(orders[i].Status),
			})
			continue
		}

		go func(order *model.Order) {
			ProcessUpdateInvoiceForSeller(&InputRequestUpdateOrder{OrderID: order.OrderID})
		}(orders[i])
	}

	if len(invalidOrder) > 0 {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data:   invalidOrder,
			Total:  int64(len(invalidOrder)),
		})
	}

	return res.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
	})
}

func GetValidInvoiceMethodForVoucher(sellerConfig *client.SellerConfig) *enum.InvoiceMethodForVoucherValue {
	if sellerConfig != nil && sellerConfig.InvoiceMethodForVoucher != nil && *sellerConfig.InvoiceMethodForVoucher != "" {
		if method, exists := enum.InvoiceMethodForVoucherMap[*sellerConfig.InvoiceMethodForVoucher]; exists {
			return method
		}
	}

	return &enum.InvoiceMethodForVoucher.TRADE_DISCOUNT
}

func GetValidReceiveInvoiceInfoBy(sellerConfig *client.SellerConfig) *enum.ReceiveInvoiceInfoByValue {
	if sellerConfig != nil && sellerConfig.ReceiveInvoiceInfoBy != nil && *sellerConfig.ReceiveInvoiceInfoBy != "" {
		if method, exists := enum.ReceiveInvoiceInfoByMap[*sellerConfig.ReceiveInvoiceInfoBy]; exists {
			return method
		}
	}

	val := GetReceiveInvoiceInfoByConfig()
	return &val
}

func GetReceiveInvoiceInfoByConfig() enum.ReceiveInvoiceInfoByValue {
	defaultValue := enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO
	confResp := client.Services.Seller.GetSettingForSC("RECEIVE_INVOICE_INFO_BY")

	if confResp.Status != common.APIStatus.Ok {
		return defaultValue
	}

	settings := confResp.Data.([]client.SettingForSC)[0]

	for _, item := range settings.Data {
		if item["Key"] == "receiveInvoiceInfoBy" {
			if value, ok := item["Value"].(string); ok {
				defaultValue = enum.ReceiveInvoiceInfoByValue(value)
			}
		}
	}

	return defaultValue
}

func UpdateInvoice(req sdk.APIRequest, res sdk.APIResponder) error {
	var input model.Invoice
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		})
	}

	if input.InvoiceID == 0 {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing invoice_id",
		})
	}

	return res.Respond(model.InvoiceDB.UpdateOne(model.Invoice{InvoiceID: input.InvoiceID}, input))
}

func getOrder(orderId int64) (*model.Order, *common.APIResponse) {
	qOrderRes := model.OrderDB.QueryOne(&model.Order{OrderID: orderId})
	if qOrderRes.Status != common.APIStatus.Ok {
		return nil, qOrderRes
	}
	order := qOrderRes.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "getOrder")
	if orderItemPartitionDB == nil {
		return nil, model.PARTITION_NOT_FOUND_RESPONSE
	}

	qItemRes := orderItemPartitionDB.Query(&model.OrderItem{OrderID: orderId}, 0, 0, nil)
	if qItemRes.Status != common.APIStatus.Ok {
		return nil, qItemRes
	}
	order.Items = qItemRes.Data.([]*model.OrderItem)
	return order, nil
}

func ProcessUpdateReturnedInvoiceForDOSeller(input *model.OrderProcessRequest) {
	if input == nil {
		return
	}

	order, errRes := getOrder(input.OrderId)
	if errRes != nil {
		return
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ProcessUpdateReturnedInvoiceForDOSeller")
	if orderItemPartitionDB == nil {
		return
	}

	invoiceResp := model.InvoiceDB.Query(&model.Invoice{
		OrderID: order.OrderID,
	}, 0, 1000, nil)

	if invoiceResp.Status != common.APIStatus.Ok {
		return
	}
	invoices := invoiceResp.Data.([]*model.Invoice)
	for _, invoice := range invoices {
		if invoice == nil || invoice.ReceiveInvoiceInfoBy == nil || *invoice.ReceiveInvoiceInfoBy != enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
			continue
		}

		orderItemResult := orderItemPartitionDB.Query(&model.OrderItem{
			OrderID:     order.OrderID,
			SellerClass: &enum.SellerClass.EXTERNAL,
			SellerCode:  invoice.SellerCode,
		}, 0, 0, nil)

		if orderItemResult.Status != common.APIStatus.Ok {
			return
		}
		orderItems := orderItemResult.Data.([]*model.OrderItem)
		returnedQtyInfosByDO := make(map[string][]*model.ReturnedQuantityInfoByDO)
		for _, orderItem := range orderItems {
			if orderItem.ReturnedQuantity == nil {
				continue
			}

			remainingQty := *orderItem.ReturnedQuantity
			for _, outboundInfo := range orderItem.OutboundInfos {
				quantity := outboundInfo.Quantity
				if remainingQty < quantity {
					quantity = remainingQty
				}

				returnedQtyInfosByDO[orderItem.Sku] = append(returnedQtyInfosByDO[orderItem.Sku], &model.ReturnedQuantityInfoByDO{
					SKU:      orderItem.Sku,
					DoCode:   outboundInfo.DoCode,
					Quantity: quantity,
				})

				remainingQty -= quantity
				if remainingQty == 0 {
					break
				}
			}

			filter := &model.OrderItem{
				Sku:         orderItem.Sku,
				SellerClass: &enum.SellerClass.EXTERNAL,
				SellerCode:  orderItem.SellerCode,
				OrderID:     orderItem.OrderID,
			}
			updater := &model.OrderItem{
				ReturnedQuantityInfosByDO: returnedQtyInfosByDO[orderItem.Sku],
			}
			orderItemPartitionDB.UpdateOne(filter, updater)
		}

		flag := true
		for _, orderItem := range orderItems {
			if orderItem.ReceiveInvoiceInfoBy == nil || *orderItem.ReceiveInvoiceInfoBy != enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
				continue
			}

			returnedQuantityInfosByDO := returnedQtyInfosByDO[orderItem.Sku]
			if len(returnedQuantityInfosByDO) == 1 {
				first := returnedQuantityInfosByDO[0]
				if first == nil || first.DoCode == "" || invoice.DeliveryOrderCode == nil || first.DoCode != *invoice.DeliveryOrderCode {
					continue
				}

				for _, outboundQty := range orderItem.OutboundInfos {
					if outboundQty == nil {
						continue
					}
					if first.DoCode == outboundQty.DoCode && first.Quantity < outboundQty.Quantity {
						flag = false
						break
					}
				}
			}
		}
		if flag {
			query := model.Invoice{
				InvoiceID: invoice.InvoiceID,
			}
			updater := &model.Invoice{}
			if invoice.InvoiceStatus == model.InvoiceStatus.Completed {
				updater.ExcludeTags = []enum.InvoiceExcludeTagValue{enum.InvoiceExcludeTag.FailedDelivery}
			} else {
				updater.InvoiceStatus = model.InvoiceStatus.Returned
			}

			model.InvoiceDB.UpdateOne(query, updater)

			now := time.Now()
			if invoice.Config != nil {
				mailDataItem := model.InvoiceEmailItem{
					OrderID:         invoice.OrderID,
					InvoiceCode:     invoice.InvoiceV2Code,
					DeliveredTime:   invoice.DeliveredTime,
					InvoiceDeadline: invoice.InvoiceDeadline,
					ReturnedTime:    &now,
				}

				mailData := model.InvoiceEmail{
					Invoices: []model.InvoiceEmailItem{mailDataItem},
				}

				emailTmpl, err := template.ParseFiles("template/hilo-invoice-returned.html")
				if err != nil {
					return
				}

				var contentBytes bytes.Buffer
				if err := emailTmpl.Execute(&contentBytes, mailData); err != nil {
					return
				}

				content := []model.Content{
					{
						Type:  "text/html",
						Value: contentBytes.String(),
					},
				}

				subject := "[Buymed] Thông báo phát sinh trả hàng – Yêu cầu kiểm tra và điều chỉnh hóa đơn"

				to := []model.Email{
					{
						Email: invoice.Config.SellerEmail,
						Name:  invoice.SellerCode,
					},
				}

				client.Services.MailService.SendMailHilo(to, subject, content, &[]string{
					"SEND_RETURNED_HILO_INVOICE",
					fmt.Sprintf("%d", invoice.InvoiceID),
				})

			}
		}
	}
}

func ProcessSyncSellerInvoice(orderID int64) *common.APIResponse {
	if orderID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order_id",
			ErrorCode: "MISSING_ORDER_ID",
		}
	}

	doResults := processDeliveryOrderInvoices(orderID)
	soResults := ProcessUpdateInvoiceForSeller(&InputRequestUpdateOrder{OrderID: orderID})

	if len(doResults) == 0 {
		return soResults
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Invoice sync completed successfully",
		Data: []map[string]interface{}{
			{
				"do_processes": doResults,
				"so_processes": soResults,
			},
		},
	}
}

func processDeliveryOrderInvoices(orderID int64) []map[string]interface{} {
	orderResult := model.OrderDB.QueryOne(model.Order{OrderID: orderID})
	results := make([]map[string]interface{}, 0)

	if orderResult.Status != common.APIStatus.Ok {
		results = append(results, map[string]interface{}{
			"status":   "error",
			"message":  "failed to fetch order",
			"error":    orderResult.Message,
			"order_id": orderID,
		})

		return results
	}

	order := orderResult.Data.([]*model.Order)[0]
	if order.Status != enum.OrderState.Delivering && order.Status != enum.OrderState.Delivered && order.Status != enum.OrderState.Completed {
		results = append(results, map[string]interface{}{
			"status":   "error",
			"message":  "invalid order status",
			"error":    "INVALID_ORDER_STATUS",
			"order_id": orderID,
		})
	}

	// Dont create invoice for old orders
	if order.DeliveredTime != nil && order.DeliveredTime.Before(time.Date(2024, 9, 30, 0, 0, 0, 0, time.FixedZone("+07", 7*60*60))) {
		results = append(results, map[string]interface{}{
			"status":   "error",
			"message":  "Order delivered before 2024-09-30T17:58:33.445+07:00",
			"error":    "ORDER_TOO_OLD",
			"order_id": orderID,
		})

		return results
	}

	doQuery := &model.DeliveryOrderReq{
		SaleOrderCode: order.SaleOrderCode,
	}

	dos, err := client.Services.Warehouse.GetDeliveryOrders(doQuery)
	if err != nil {
		results = append(results, map[string]interface{}{
			"status":     "error",
			"message":    "failed to fetch delivery orders",
			"error":      err.Error(),
			"order_code": order.OrderCode,
		})

		return results
	}

	dbPartition := model.GetOrderItemPartitionDB(order, "ProcessSyncSellerInvoice")
	if dbPartition == nil {
		results = append(results, map[string]interface{}{
			"status":   "error",
			"message":  "failed to get order item partition",
			"error":    "PARTITION_NOT_FOUND",
			"order_id": orderID,
		})

		return results
	}

	orderItemsRes := dbPartition.Query(&model.OrderItem{OrderID: orderID}, 0, 0, nil)
	if orderItemsRes.Status != common.APIStatus.Ok {
		results = append(results, map[string]interface{}{
			"status":   "error",
			"message":  "failed to fetch order items",
			"error":    orderItemsRes.Message,
			"order_id": orderID,
		})

		return results
	}

	orderItems := orderItemsRes.Data.([]*model.OrderItem)

	for _, do := range dos {
		doResult := map[string]interface{}{
			"do_code": do.DeliveryOrderCode,
			"status":  do.Status,
		}

		res := &model.OrderProcessRequest{
			OrderId:             order.OrderID,
			DeliveryOrderCode:   do.DeliveryOrderCode,
			DeliveryOrderStatus: enum.OrderStateValue(*do.Status),
			ActionTime:          do.LastUpdatedTime,
		}

		if do.OutboundTime != nil {
			res.ActionTime = do.OutboundTime
			res.OutboundDate = do.OutboundTime
		}

		if do.DeliveredTime != nil {
			res.ActionTime = do.DeliveredTime
			res.DeliveredTime = do.DeliveredTime
		}

		for _, item := range orderItems {
			if item.SubItems != nil {
			SUB_ITEM_LOOP:
				for _, subItem := range *item.SubItems {
					if len(subItem.OutboundInfos) == 0 {
						continue
					}

					for _, outboundInfo := range subItem.OutboundInfos {
						if outboundInfo.DoCode == do.DeliveryOrderCode {
							res.Items = append(res.Items, &model.OrderItemProcessRequest{
								Sku:      item.Sku,
								Quantity: &outboundInfo.Quantity,
							})
							break SUB_ITEM_LOOP
						}
					}
				}
			} else {
				if len(item.OutboundInfos) == 0 {
					continue
				}
				for _, outboundInfo := range item.OutboundInfos {
					if outboundInfo.DoCode == do.DeliveryOrderCode {
						res.Items = append(res.Items, &model.OrderItemProcessRequest{
							Sku:      item.Sku,
							Quantity: &outboundInfo.Quantity,
						})
					}
				}
			}
		}

		var processResult *common.APIResponse
		switch *do.Status {
		case enum.SaleOrderStatus.Returned:
			ProcessUpdateReturnedInvoiceForDOSeller(res)
			doResult["process_type"] = "returned"
			processResult = &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Processed returned invoice",
			}
		case enum.SaleOrderStatus.Delivering, enum.SaleOrderStatus.Delivered, enum.SaleOrderStatus.Completed:
			processResult = ProcessUpsertInvoiceForDOSeller(res)
			doResult["process_type"] = "normal"
		default:
			doResult["process_type"] = "skipped"
			processResult = &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Skipped processing",
			}
		}

		doResult["process_result"] = processResult
		results = append(results, doResult)
	}

	return results
}

func SyncSellerInvoice(req sdk.APIRequest, res sdk.APIResponder) error {
	var input struct {
		OrderID int64 `json:"order_id"`
	}

	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	response := ProcessSyncSellerInvoice(input.OrderID)
	return res.Respond(response)
}

func TriggerSyncInvoice(invoiceID int64) *common.APIResponse {
	if invoiceID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing invoice_id",
			ErrorCode: "MISSING_INVOICE_ID",
		}
	}

	invoiceRes := model.InvoiceDB.QueryOne(model.Invoice{InvoiceID: invoiceID})
	if invoiceRes.Status != common.APIStatus.Ok {
		return invoiceRes
	}

	invoice := invoiceRes.Data.([]*model.Invoice)[0]

	invoice.PartnerStatus = model.InvoicePartnerStatus.None

	HandleSyncInvoice(invoice)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Trigger Sync Invoice successfully",
	}
}

func HandleSyncInvoice(invoice *model.Invoice) {
	var (
		wg sync.WaitGroup

		orderItems = make([]*model.OrderItem, 0)
	)

	// Update partner config
	wg.Add(1)
	sdk.Execute(func() {
		defer wg.Done()

		sellersRes := client.Services.Seller.GetSellerListWithParams(client.ReqSellerList{
			SellerCodes:    []string{invoice.SellerCode},
			Limit:          1,
			GetInvoiceInfo: true,
		})
		seller := sellersRes.Data[0]

		needUpdate := updateInvoicePartnerConfig(seller, invoice)
		if needUpdate {
			model.InvoiceDB.UpdateOne(model.Invoice{InvoiceID: invoice.InvoiceID}, invoice)
		}
	})

	// Fetch order items
	wg.Add(1)
	sdk.Execute(func() {
		defer wg.Done()

		orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{OrderID: invoice.OrderID}, "TriggerSyncInvoice")
		if orderItemPartitionDB == nil {
			client.Services.SellerPurchasing.AlertInvoiceSyncFailed(invoice.InvoiceID, invoice.InvoiceCode, invoice.SellerCode, fmt.Sprintf("Sync error Không fetch được order item for orderID=%d", invoice.OrderID))
			return
		}

		qItemRes := orderItemPartitionDB.Query(&model.OrderItem{OrderID: invoice.OrderID, SellerCode: invoice.SellerCode}, 0, 0, nil)
		if qItemRes.Status != common.APIStatus.Ok {
			client.Services.SellerPurchasing.AlertInvoiceSyncFailed(invoice.InvoiceID, invoice.InvoiceCode, invoice.SellerCode, fmt.Sprintf("Sync error Không fetch được order item %s", qItemRes.Message))
			return
		}
		orderItems = qItemRes.Data.([]*model.OrderItem)
	})

	wg.Wait()

	CalculateInvoiceReport(invoice, orderItems)

	if invoice.InvoiceItems == nil {
		return
	}

	// đã xuất từ v2
	client.Services.Invoice.SyncSellerExportedOrderInvoiceItem(*invoice)
	// Sync to Invoice v1
	// client.Services.Invoice.SyncOrderInvoiceItem(*invoice)
	// if invoice.InvoiceV2Code != "" {
	// 	// đã xuất từ v2
	// 	client.Services.Invoice.SyncSellerExportedOrderInvoiceItem(*invoice)
	// } else if invoice.Config != nil {
	// 	// xuất dùm = invoice v1
	// 	client.Services.Invoice.SyncSellerSupportExportOrderInvoiceHILO(*invoice)
	// } else {
	// 	// seller tự xuất
	// 	client.Services.Invoice.SyncSellerExportedOrderInvoiceItem(*invoice)
	// }

	// Sync to Invoice v2 - using HILO
	if invoice.Config == nil {
		return
	}

	// Check if invoice is already created and in processing -> if yes, cancel it
	billingInvoice, err := client.Services.Billing.GetBillingInvoice(&client.ReqListInvoice{
		Query: &client.BillingInvoice{
			OrderID: invoice.OrderID,
		},
		Offset: 0,
		Limit:  1,
	})
	if err == nil {
		// Don't sync processed invoices
		if billingInvoice.Status == enum.InvoiceStatus.Processed {
			return
		}

		client.Services.Billing.CancelBillingInvoice(billingInvoice.Code)
	}

	if invoice.PartnerStatus == model.InvoicePartnerStatus.None {
		orderRes := model.OrderDB.QueryOne(&model.Order{OrderID: invoice.OrderID})
		if orderRes.Status != common.APIStatus.Ok {
			client.Services.SellerPurchasing.AlertInvoiceSyncFailed(invoice.InvoiceID, invoice.InvoiceCode, invoice.SellerCode, fmt.Sprintf("Notfound Order %s", orderRes.Message))
			return
		}

		order := orderRes.Data.([]*model.Order)[0]

		items := make([]*client.ExportInvoiceItem, 0, len(invoice.InvoiceItems))
		isDoInvoice := invoice.DeliveryOrderCode != nil && *invoice.DeliveryOrderCode != ""
		var totalAmount int
		var totalAmountWithoutVAT int
		var maxVat float64
		mapSkuReturnQty := make(map[string]int)
		for _, item := range invoice.InvoiceItems {
			if item == nil {
				continue
			}

			if item.VAT != nil && *item.VAT > maxVat {
				maxVat = *item.VAT
			}

			if item.ReturnedQuantity != nil {
				mapSkuReturnQty[item.Sku] = *item.ReturnedQuantity
			}
		}

		for _, item := range invoice.InvoiceItems {
			if item == nil {
				continue
			}

			product := client.Services.Product.GetProductInfoByID(item.ProductID, invoice.OrderID)

			if product == nil {
				client.Services.SellerPurchasing.AlertInvoiceSyncFailed(invoice.InvoiceID, invoice.InvoiceCode, invoice.SellerCode, fmt.Sprintf("Notfound Product %d", item.ProductID))
				continue
			}

			for _, outboundInfo := range item.OutboundInfos {
				if isDoInvoice && outboundInfo.DoCode != *invoice.DeliveryOrderCode {
					continue
				}

				quantity := outboundInfo.Quantity
				if qty, ok := mapSkuReturnQty[item.Sku]; ok {
					if qty > 0 {
						if quantity-qty < 0 {
							quantity = 0
							remain := qty - quantity
							mapSkuReturnQty[item.Sku] = remain
						} else {
							quantity -= qty
							mapSkuReturnQty[item.Sku] = 0
						}
					}
				}

				price := item.SellerPrice
				priceWithoutVAT := int(item.UnitPrice)
				amount := price * quantity
				amountWithoutVAT := priceWithoutVAT * quantity
				vatAmount := amount - amountWithoutVAT

				totalAmount += amount
				totalAmountWithoutVAT += amountWithoutVAT

				var vat float64
				if item.VAT != nil {
					vat = *item.VAT
				}

				items = append(items, &client.ExportInvoiceItem{
					Characteristic: 1,
					ProductID:      item.ProductID,
					ProductCode:    item.ProductCode,
					ProductName:    product.Name,
					SKUCode:        item.Sku,
					OrderItemType:  string(item.Type),
					Unit:           product.Unit,
					Lot:            outboundInfo.Lot,
					ExpiryDate:     outboundInfo.ExpDate,

					Quantity:         quantity,
					VAT:              vat,
					Price:            price,
					PriceWithoutVAT:  priceWithoutVAT,
					Amount:           amount,
					AmountWithoutVAT: amountWithoutVAT,
					VATAmount:        vatAmount,
				})
			}
		}

		if invoice.TradeDiscountAmount > 0 {
			price := int(invoice.TradeDiscountAmount)
			unitPrice := float64(price) / (1 + maxVat/100)
			roundUnitPrice := int(math.Round(unitPrice))
			vatAmount := price - roundUnitPrice

			// TODO: Check logic làm tròn chỗ này
			items = append(items, &client.ExportInvoiceItem{
				Characteristic: 3,
				ProductID:      0,
				ProductCode:    "",
				ProductName:    "Chiết khấu thương mại",
				SKUCode:        "",
				OrderItemType:  "",
				Unit:           "",
				Lot:            "",
				ExpiryDate:     "",

				Quantity:         1,
				VAT:              maxVat,
				Price:            price,
				Amount:           price,
				PriceWithoutVAT:  roundUnitPrice,
				AmountWithoutVAT: roundUnitPrice,
				VATAmount:        vatAmount,
			})
		}

		payload := client.ExportInvoiceRequest{
			InvoiceTemplateCode: invoice.Config.InvoiceTemplateCode,

			RequestDraft: false,

			OrderID:       int(invoice.OrderID),
			OrderCode:     invoice.OrderCode,
			SaleOrderCode: order.SaleOrderCode,

			OrgId:        2,
			OriginSource: "THUOCSIVN_ORDER_V2",
			Source:       "SELLER",
			CompanyCode:  "BUYMED",
			Provider:     "HILO",

			PaymentMethod:    order.PaymentMethod,
			AmountWithoutVAT: totalAmountWithoutVAT,
			Amount:           totalAmount,

			SellerCode:        invoice.SellerCode,
			SellerPhoneNumber: invoice.Config.SellerPhoneNumber,
			SellerEmail:       invoice.Config.SellerEmail,

			OutboundTime: invoice.OutboundTime,
			BoughtTime:   order.CreatedTime,

			Details: items,
		}

		if invoice.Config.SignedBy == enum.SignedBy.SELLER {
			payload.RequestDraft = true
		}

		if invoice.DeliveryOrderCode != nil {
			payload.DoCode = *invoice.DeliveryOrderCode
		}

		if order.Invoice != nil && order.Invoice.RequestInvoice != nil && *order.Invoice.RequestInvoice {
			payload.BuyerRequest = *order.Invoice.RequestInvoice
		}

		notRequestName := "Khách hàng không lấy hóa đơn (nhà thuốc/quầy thuốc/phòng khám.....)"
		if payload.BuyerRequest {
			// payload.BuyerTaxCode = "**********"
			// payload.BuyerName = "CIRCA PHARMACY CO., LTD"
			// payload.BuyerAddress = "207 Lê Đại Hành, Phường 13, Quận 11, Thành phố Hồ Chí Minh, Việt Nam"
			if invoice.CustomerTax != nil && invoice.CustomerTax.TaxGov.CustomerTaxGOVStatus == enum.CustomerTaxStatus.VALID {
				payload.PurchaserName = invoice.CustomerTax.BuyerName
				payload.BuyerName = invoice.CustomerTax.TaxGov.CustomerName
				payload.BuyerTaxCode = invoice.CustomerTax.TaxGov.CustomerTaxCode
				payload.BuyerAddress = invoice.CustomerTax.TaxGov.CustomerAddress
			} else {
				payload.PurchaserName = invoice.CustomerTax.BuyerName
				payload.BuyerName = notRequestName
				payload.BuyerRequestWhenInvalidTaxCode = true
				payload.BuyerRequest = false
				payload.BuyerNameWhenInvalidTaxCode = notRequestName
			}
		} else {
			payload.BuyerName = notRequestName

			if invoice.Config == nil || !invoice.Config.ExportBuyerNoRequest {
				return
			}
		}

		res := client.Services.Billing.PostHiloInvoice(payload, &[]string{
			fmt.Sprintf("%d", invoice.InvoiceID),
			"Sync Invoice V2",
		})

		if res.Status != common.APIStatus.Ok {
			go func() {
				content, err := json.Marshal(payload)
				if err != nil {
					fmt.Printf("Error marshalling payload: %v", err)
					return
				}
				model.LogReqDb.Create(model.LogReq{
					ReqURL:    "/invoice/export",
					ReqMethod: "POST",
					Data:      string(content),
				})
			}()

			client.Services.SellerPurchasing.AlertInvoiceSyncFailed(invoice.InvoiceID, invoice.InvoiceCode, invoice.SellerCode, fmt.Sprintf("Sync error %s", res.Message))
			return
		}

		model.InvoiceDB.UpdateOne(model.Invoice{InvoiceID: invoice.InvoiceID}, model.Invoice{PartnerStatus: model.InvoicePartnerStatus.Synced})
	}
}
