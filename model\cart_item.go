package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// CartItem ...
type CartItem struct {
	// Note: set cartItem.ID=nil if you clone the cart (E.g. Before calling `model.CartItemDeletedDB.CreateMany(cart.Items)``)
	ID primitive.ObjectID `json:"-" bson:"_id,omitempty"`

	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// Last time add to cart use for sorting by the last time user add item to cart
	LastAdded *time.Time `json:"lastAdded,omitempty" bson:"last_added,omitempty"`

	// reference data
	RefCart int64  `json:"-" bson:"ref_cart,omitempty"`
	CartID  int64  `json:"cartId,omitempty" bson:"cart_id,omitempty"`
	CartNo  string `json:"cartNo,omitempty" bson:"cart_no,omitempty"`

	Sku                        string             `json:"sku,omitempty" bson:"sku,omitempty"`
	ItemCode                   string             `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	Quantity                   int                `json:"quantity,omitempty" bson:"quantity,omitempty"`
	MaxQuantity                int                `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`
	TotalDealQuantity          int                `json:"-" bson:"-"`
	DealMaxQuantityPerCustomer int                `json:"-" bson:"-"`
	IsImportant                *bool              `json:"isImportant,omitempty" bson:"is_important,omitempty"`
	Type                       enum.ItemTypeValue `json:"type,omitempty" bson:"type,omitempty"`

	Price                int                `json:"price,omitempty" bson:"-"`                              // save order item & view web
	CurPrice             int                `json:"currentPrice,omitempty" bson:"current_price,omitempty"` // save order item & view web
	OldType              enum.ItemTypeValue `json:"-" bson:"old_type,omitempty"`                           //
	OldLevel             string             `json:"-" bson:"old_level,omitempty"`                          //
	IsFirstAdd           bool               `json:"-" bson:"-"`                                            // save order item & view web
	SalePrice            int                `json:"salePrice,omitempty" bson:"-"`                          // save order item & view web
	DealPrice            int                `json:"-" bson:"-"`                                            // save order item & view web
	DealDiscountPercent  *float64           `json:"-" bson:"-"`
	DealMaxDiscountValue *int               `json:"-" bson:"-"`
	DealPricingType      *string            `json:"-" bson:"-"`
	DealVendorPrice      float64            `json:"-" bson:"-"`
	Weight               float64            `json:"weight,omitempty" bson:"-"`
	Volume               float64            `json:"volume,omitempty" bson:"-"`

	CampaignPrice            int    `json:"-" bson:"-"`
	CampaignDiscountPercent  *int64 `json:"-" bson:"-"`
	CampaignMaxDiscountValue *int64 `json:"-" bson:"-"`
	CampaignPricingType      string `json:"-" bson:"-"`
	CampaignDiscountValue    *int64 `json:"-" bson:"-"`

	SubsidyType  string `json:"-" bson:"-"`
	SubsidyValue int64  `json:"-" bson:"-"`
	SubsidyPrice int64  `json:"-" bson:"-"`

	TotalPrice    int                  `json:"total,omitempty" bson:"-"`        // save order item & view web
	IsAvailable   bool                 `json:"isAvailable,omitempty" bson:"-"`  // view web
	SkuStatusData *SkuStatusData       `json:"-" bson:"-"`                      // view web
	SkuStatus     *enum.SkuStatusValue `json:"skuStatus,omitempty" bson:"-"`    // view web
	SkuPriceType  *enum.PriceTypeValue `json:"skuPriceType,omitempty" bson:"-"` // save order item
	SkuVersion    string               `json:"skuVersion,omitempty" bson:"-"`
	Owner         string               `json:"owner,omitempty" bson:"owner,omitempty"`
	SkuLevel      *enum.LevelSKUValue  `json:"skuLevel,omitempty" bson:"sku_level,omitempty"`
	TotalWeight   float64              `json:"totalWeight,omitempty" bson:"-"`
	TotalVolume   float64              `json:"totalVolume,omitempty" bson:"-"`

	DealCode            *string    `json:"-" bson:"deal_code,omitempty"`                        // save order item
	CampaignCode        string     `json:"campaignCode,omitempty" bson:"-"`                     // save order item
	CampaignProductCode string     `json:"campaignProductCode,omitempty" bson:"-"`              // save order item
	SellerCode          string     `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`   // save order item
	ProductCode         string     `json:"productCode,omitempty" bson:"product_code,omitempty"` // save order item
	ProductID           int64      `json:"productID,omitempty" bson:"product_id,omitempty"`     // save order item
	SellerPrice         int        `json:"-" bson:"-"`                                          // save order item
	SellerRevenue       int        `json:"-" bson:"-"`                                          // save order item
	Fee                 *FeesApply `json:"-" bson:"fee,omitempty"`
	VAT                 *float64   `json:"-" bson:"-"` // save order item
	NoneVAT             *bool      `json:"-" bson:"-"` // save order item
	ProductName         string     `json:"productName,omitempty" bson:"product_name,omitempty"`
	StoreCode           string     `json:"storeCode,omitempty" bson:"-"`

	RetailPriceType        string    `json:"-" bson:"-"`                                // get price
	IsDynamicPricingLevel  *bool     `json:"-" bson:"-"`                                // get price
	DynamicPricingLevel    *int64    `json:"-" bson:"-"`                                // get price
	PricingStrategy        *float64  `json:"-" bson:"-"`                                // get price
	FeeCodes               *[]string `json:"-" bson:"-"`                                // get price
	ChargeDealFee          string    `json:"-" bson:"-"`                                // MARKETPLACE . SELLER . SELLER_MARKETPLACE
	ChargeCampaignFee      string    `json:"-" bson:"-"`                                // MARKETPLACE . SELLER . SELLER_MARKETPLACE
	ChargeDealFeeValue     int       `json:"chargeDealFeeValue,omitempty" bson:"-"`     // số tiền được giảm khi mua deal
	ChargeCampaignFeeValue int       `json:"chargeCampaignFeeValue,omitempty" bson:"-"` // số tiền được giảm khi mua campaign

	// source
	Source enum.SourceValue `json:"source,omitempty" bson:"source,omitempty"`

	Page      string `json:"page,omitempty" bson:"page,omitempty"`
	SearchKey string `json:"searchKey,omitempty" bson:"search_key,omitempty"`
	// combo
	IsCombo  *bool        `json:"-" bson:"-"`
	Skus     *[]*SubSku   `json:"-" bson:"skus,omitempty"`     // save order item
	SubItems *[]*CartItem `json:"subItems,omitempty" bson:"-"` // save order item
	// combo price
	UseSKUsPrice          *bool  `json:"-" bson:"-"`
	ComboDiscountType     string `json:"-" bson:"-"`
	ComboDiscountValue    *int64 `json:"-" bson:"-"`
	ComboMaxDiscountValue *int64 `json:"-" bson:"-"`

	ErrorCode      string `json:"errorCode,omitempty" bson:"-"`
	WarningCode    string `json:"warningCode,omitempty" bson:"-"`
	WarningMessage string `json:"warningMessage,omitempty" bson:"-"`
	ErrorMessage   string `json:"errorMessage,omitempty" bson:"-"`

	// tags: use json tag: productTags for api check voucher
	Tags         []string  `json:"productTags,omitempty" bson:"-"`
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`

	Point                 int64          `json:"point,omitempty" bson:"point,omitempty"`
	PointMultiplier       int64          `json:"pointMultiplier,omitempty" bson:"point_multiplier,omitempty"`
	SkuContractCode       *string        `json:"skuContractCode,omitempty" bson:"sku_contract_code,omitempty"`
	SkuContractDetailCode *string        `json:"skuContractDetailCode,omitempty" bson:"sku_contract_detail_code,omitempty"`
	ContractPrice         int            `json:"-" bson:"-"`
	IsSkuLimitExisted     bool           `json:"-" bson:"-"`
	SkuLimitQuantity      int            `json:"-" bson:"-"`
	IsSelected            *bool          `json:"isSelected,omitempty" bson:"is_selected,omitempty"`
	VoucherCode           string         `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	LotDates              *[]LotDates    `json:"lotDates,omitempty" bson:"lot_dates,omitempty"`
	IsNearExpired         bool           `json:"isNearExpired,omitempty" bson:"is_near_expired,omitempty"`
	EventSource           string         `json:"eventSource,omitempty" bson:"event_source,omitempty"`
	EventScreen           string         `json:"eventScreen,omitempty" bson:"event_screen,omitempty"`
	BlockCode             string         `json:"blockCode,omitempty" bson:"block_code,omitempty"`
	Notification          map[string]int `json:"notification,omitempty" bson:"notification,omitempty"`

	Unit    string `json:"unit,omitempty" bson:"-"`    // order unit
	SkuUnit string `json:"skuUnit,omitempty" bson:"-"` // standard unit

	//Brand
	LimitQuantityPerMonth int    `json:"limitQuantityPerMonth" bson:"-"`
	QtyOrdered            int    `json:"qtyOrdered" bson:"-"`
	ManufacturerCode      string `json:"manufacturerCode,omitempty" bson:"manufacturer_code,omitempty"`

	// discount detail
	DiscountDetail *DiscountDetail `json:"discountDetail,omitempty" bson:"-"`
	SellerClass    string          `json:"sellerClass,omitempty" bson:"-"`

	ProductData  interface{}       `json:"productData,omitempty" bson:"-"`
	DealInfo     DiscountPromoInfo `json:"-" bson:"-"`
	ContractInfo ContractInfo      `json:"-" bson:"-"`
	CampaignInfo DiscountPromoInfo `json:"-" bson:"-"`
}

// SkuStatusData is data of sku type
type SkuStatusData struct {
	Note         *string    `json:"note" bson:"note,omitempty"`
	Date         *time.Time `json:"date" bson:"date,omitempty"`
	Quantity     *int       `json:"quantity,omitempty" bson:"quantity,omitempty"`
	UsedQuantity *int       `json:"usedQuantity,omitempty" bson:"used_quantity,omitempty"`
	// next
	NextStatus   *enum.SkuStatusValue `json:"nextStatus,omitempty" bson:"next_status,omitempty"`      // trạng thái mong muốn sau khi hết số lượng
	NextIsActive *bool                `json:"nextIsActive,omitempty" bson:"next_is_active,omitempty"` // trạng thái mong muốn sau khi hết số lượng
	// auto tính số lượng từ tồn kho
	IsAutoCheckStock     bool  `json:"isAutoCheckStock,omitempty" bson:"is_auto_check_stock,omitempty"`
	MinQuantityAutoLimit int64 `json:"minQuantityAutoLimit,omitempty" bson:"min_quantity_auto_limit,omitempty"`
}

// SubSku sub sku and quantity of its
type SubSku struct {
	SKU         string `json:"sku" bson:"sku,omitempty"`
	Quantity    int    `json:"quantity" bson:"quantity,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`

	OldSKU      string       `json:"-" bson:"old_sku,omitempty"`
	ProductData *ProductData `json:"productData,omitempty"`
}

type LotDates struct {
	IsNearExpired *bool   `json:"isNearExpired" bson:"is_near_expired,omitempty"`
	ExpiredDate   *string `json:"expiredDate" bson:"expired_date,omitempty"`
	Lot           string  `json:"lot" bson:"lot,omitempty"`
	Quantity      int     `json:"quantity" bson:"quantity,omitempty"`
}

type DiscountDetail struct {
	TotalDiscount  int              `json:"totalDiscount,omitempty" bson:"total_discount,omitempty"`
	Message        string           `json:"message,omitempty" bson:"message,omitempty"`
	VoucherDetails []*VoucherDetail `json:"voucherDetails,omitempty" bson:"voucher_details,omitempty"`
}

type VoucherDetail struct {
	VoucherCode   string   `json:"voucherCode,omitempty" bson:"voucher_code,omitempty"`
	DiscountValue int      `json:"discountValue,omitempty" bson:"discount_value,omitempty"`
	DiscountType  string   `json:"discountType,omitempty" bson:"discount_type,omitempty"`
	IsApply       bool     `json:"isApply,omitempty" bson:"is_apply,omitempty"`
	SellerCodes   []string `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	StoreCode     string   `json:"storeCode,omitempty" bson:"store_code,omitempty"`
}

type DiscountPromoInfo struct {
	StartTime        *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime          *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Type             *string    `json:"type,omitempty" bson:"type,omitempty"`
	Discount         int        `json:"discount,omitempty" bson:"discount,omitempty"`
	MaxDiscount      *int       `json:"maxDiscount,omitempty" bson:"max_discount,omitempty"`
	DiscountPercent  *float64   `json:"discountPercent,omitempty" bson:"discount_percent,omitempty"`
	Discounted       int        `json:"discounted,omitempty" bson:"discounted,omitempty"` // Absolute discount value
	ChargeFee        string     `json:"chargeFee,omitempty" bson:"charge_fee,omitempty"`
	Tags             []string   `json:"tags,omitempty" bson:"tags,omitempty"`
	SegmentationCode string     `json:"segmentationCode,omitempty" bson:"segmentation_code,omitempty"`
}

type ContractInfo struct {
	StartTime *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Price     int        `json:"price,omitempty" bson:"price,omitempty"`
}

// CartItemDB ...
var CartItemDB = &db.Instance{
	ColName:        "cart_item",
	TemplateObject: &CartItem{},
}

// CartItemDeletedDB ...
var CartItemDeletedDB = &db.Instance{
	ColName:        "cart_item_deleted",
	TemplateObject: &CartItem{},
}

// InitCartItemModel is func init model sale cart
func InitCartItemModel(s *mongo.Database) {
	CartItemDB.ApplyDatabase(s)

	// t := true
	// _ = CartItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "cart_no", Value: 1},
	// 	primitive.E{Key: "cart_id", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "type", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = CartItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "cart_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

// InitCartDeletedModel is func init model sale cart deleted
func InitCartItemDeletedModel(s *mongo.Database) {
	CartItemDeletedDB.ApplyDatabase(s)
}
