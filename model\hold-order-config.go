package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// HoldOrderConfig ...
type HoldOrderConfig struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	UpdatedBy int64 `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`

	HoldOrderID   int64               `json:"holdOrderID,omitempty" bson:"hold_order_id,omitempty"`
	HoldOrderCode enum.HoldOrderValue `json:"holdOrderCode,omitempty" bson:"hold_order_code,omitempty"`

	IsActive *bool               `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Type     enum.HoldOrderValue `json:"type,omitempty" bson:"type,omitempty" validate:"required"`

	DisplayTime             *int64                     `json:"displayTime,omitempty" bson:"display_time,omitempty" validate:"min=0"`
	ProcessingTime          *int64                     `json:"processingTime,omitempty" bson:"processing_time,omitempty" validate:"min=0"`
	OpenAgainTime           *int64                     `json:"openAgainTime,omitempty" bson:"open_again_time,omitempty" validate:"min=0"`
	CreatedTicketTime       *int64                     `json:"createdTicketTime,omitempty" bson:"created_ticket_time,omitempty" validate:"min=0"`
	DisplayRatio            *int64                     `json:"displayRatio,omitempty" bson:"display_ratio,omitempty" validate:"min=0,max=100"`
	BankTransferWaitingTime *int64                     `json:"bankTransferWaitingTime,omitempty" bson:"bank_transfer_waiting_time,omitempty"`
	Total                   int64                      `json:"total,omitempty" bson:"total,omitempty" validate:"min=0"`
	NotificationConfig      *RemindPaymentNotification `json:"notificationConfig,omitempty" bson:"notification_config,omitempty"`
	ComplexQuery            []*primitive.M             `json:"-" bson:"$and,omitempty"`
}

// HoldOrderConfigDB ...
var HoldOrderConfigDB = &db.Instance{
	ColName:        "hold_order_config",
	TemplateObject: &HoldOrderConfig{},
}

// InitHoldOrderConfigModel is func init model hold order configuration
func InitHoldOrderConfigModel(s *mongo.Database) {
	HoldOrderConfigDB.ApplyDatabase(s)
}

type RemindPaymentNotification struct {
	Topic       string `json:"topic,omitempty" bson:"topic,omitempty"`
	Link        string `json:"link,omitempty" bson:"link,omitempty" `
	Description string `json:"description,omitempty" bson:"description,omitempty"` //  chuông báo
	Title       string `json:"title,omitempty" bson:"title,omitempty"`             // app push
	Content     string `json:"content,omitempty" bson:"content,omitempty"`         // app push
	// Tags []enum.NotificationTagEnum `json:"tags,omitempty" bson:"tags,omitempty"`
}
