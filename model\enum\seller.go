package enum

// Define who will sign invoice
type SignedByValue string
type signedByValue struct {
	MEDX   SignedByValue
	SELLER SignedByValue
}

var SignedBy = &signedByValue{
	MEDX:   "MEDX",
	SELLER: "SELLER",
}

type LevelSellerValue string
type levelSellerValue struct {
	LEVEL_1 LevelSellerValue
	LEVEL_2 LevelSellerValue
	MARKET  LevelSellerValue
}

var LevelSeller = &levelSellerValue{
	"LEVEL_1",
	"LEVEL_2",
	"MARKET",
}

// Status of Hilo invoice contract
type HiloStatusValue string
type hiloStatusValue struct {
	INACTIVE          HiloStatusValue
	WAIT_FOR_CONTRACT HiloStatusValue
	ACTIVE            HiloStatusValue
}

var HiloStatus = &hiloStatusValue{
	INACTIVE:          "INACTIVE",
	WAIT_FOR_CONTRACT: "WAIT_FOR_CONTRACT",
	ACTIVE:            "ACTIVE",
}
