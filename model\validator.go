package model

import (
	"errors"
	"strings"
	"unicode"

	"github.com/go-playground/locales/en"
	ut "github.com/go-playground/universal-translator"
	validator "github.com/go-playground/validator/v10"
	en_translations "github.com/go-playground/validator/v10/translations/en"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

// Checker ...
var (
	uni     *ut.UniversalTranslator
	Checker *CustomValidator
)

// CustomValidator ...
type CustomValidator struct {
	Validator *validator.Validate
	Trans     ut.Translator
}

// Validate is func check valid struct
func (cv *CustomValidator) Validate(i interface{}) error {
	err := cv.Validator.Struct(i)
	if err != nil {

		errs := err.(validator.ValidationErrors)

		for _, e := range errs {
			return errors.New(e.Translate(cv.Trans))
		}
	}
	return nil
}

var validOrderStatus validator.Func = func(fieldLevel validator.FieldLevel) bool {
	if val, ok := fieldLevel.Field().Interface().(string); ok {
		return enum.IsOrderStatus(val)
	}
	return false
}

// InitCustomValidator ...
func InitCustomValidator() *CustomValidator {
	en := en.New()
	uni = ut.New(en, en)
	trans, _ := uni.GetTranslator("en")
	validate := validator.New()
	_ = en_translations.RegisterDefaultTranslations(validate, trans)

	_ = validate.RegisterTranslation("required", trans, func(ut ut.Translator) error {
		return ut.Add("required", "Vui lòng nhập {0}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("required", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]))
		return t
	})

	_ = validate.RegisterTranslation("numeric", trans, func(ut ut.Translator) error {
		return ut.Add("numeric", "Vui lòng nhập {0} dưới dạng kí tự số từ 0 đến 9", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("numeric", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]))
		return t
	})

	_ = validate.RegisterTranslation("min", trans, func(ut ut.Translator) error {
		return ut.Add("min", "Vui lòng nhập  {0} có độ dài lớn hơn {1}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("min", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]), fe.Param())
		return t
	})

	_ = validate.RegisterTranslation("max", trans, func(ut ut.Translator) error {
		return ut.Add("max", "Vui lòng nhập giá trị {0} có độ dài nhỏ hơn {1}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("max", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]), fe.Param())
		return t
	})

	_ = validate.RegisterTranslation("gt", trans, func(ut ut.Translator) error {
		return ut.Add("gt", "Vui lòng nhập giá trị {0} lớn hơn {1}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("gt", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]), fe.Param())
		return t
	})

	_ = validate.RegisterTranslation("gte", trans, func(ut ut.Translator) error {
		return ut.Add("gte", "Vui lòng nhập giá trị {0} lớn hơn bằng {1}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("gte", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]), fe.Param())
		return t
	})

	_ = validate.RegisterTranslation("lt", trans, func(ut ut.Translator) error {
		return ut.Add("lt", "Vui lòng nhập giá trị {0} nhỏ hơn {1}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("lt", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]), fe.Param())
		return t
	})

	_ = validate.RegisterTranslation("lte", trans, func(ut ut.Translator) error {
		return ut.Add("lte", "Vui lòng nhập giá trị {0} nhỏ hơn bằng {1}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("lte", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]), fe.Param())
		return t
	})

	_ = validate.RegisterTranslation("email", trans, func(ut ut.Translator) error {
		return ut.Add("email", "{0} không hợp lệ.", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("email", lowerFirst(fe.Field()))
		return t
	})

	_ = validate.RegisterTranslation("excludesall", trans, func(ut ut.Translator) error {
		return ut.Add("excludesall", "Vui lòng nhập {0} không tồn tại kí tự {1}.", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("excludesall", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]), fe.Param())
		return t
	})

	// validate order status
	_ = validate.RegisterValidation("orderStatus", validOrderStatus)
	_ = validate.RegisterTranslation("orderStatus", trans, func(ut ut.Translator) error {
		return ut.Add("orderStatus", "Thông tin dữ liệu {0} không tồn tại, vui lòng kiểm tra lại.", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("orderStatus", lowerField(fe.StructNamespace()[strings.Index(fe.StructNamespace(), ".")+1:]), fe.Param())
		return t
	})

	return &CustomValidator{Validator: validate, Trans: trans}
}

func lowerField(fieldName string) string {
	var field []string
	subs := strings.Split(fieldName, ".")
	for _, str := range subs {
		field = append(field, lowerFirst(str))
	}
	return strings.Join(field, ".")
}
func lowerFirst(str string) string {
	for i := range str {
		if i+1 < len(str) && unicode.IsUpper(rune(str[i+1])) {
			continue
		}
		return strings.ToLower(str[:i+1]) + str[i+1:]
	}
	return strings.ToLower(str)
}

func init() {
	Checker = InitCustomValidator()
}
