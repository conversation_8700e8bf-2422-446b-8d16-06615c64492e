package client

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

const (
	pathGetListPrice             = "/marketplace/pricing/v2/price"
	pathGetListPaymentFeeConfig  = "/marketplace/pricing/v2/payment-method/list"
	pathGetListDeliveryFeeConfig = "/marketplace/pricing/v2/delivery-platform/list"
)

type pricingClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewPricingServiceClient ...
func NewPricingServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *pricingClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	c := &pricingClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": api<PERSON><PERSON>,
		},
	}
	c.svc.SetDBLog(session)
	return c
}

// GetPrice ...
func (cli *pricingClient) GetPrice(body *LookupSKUPriceRequest) (map[string]*SKUPrice, *common.APIResponse) {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathGetListPrice, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetPrice",
		}
	}

	var result *PriceResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetPrice",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return *result.Data[0], nil
}

// GetPrice ...
func (cli *pricingClient) GetDeliveryFeeConfig() ([]*DeliveryFeeConfig, *common.APIResponse) {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetListDeliveryFeeConfig, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetFeeConfig",
		}
	}

	var result *DeliveryFeeConfigResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetFeeConfig",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data, nil
}

// GetPrice ...
func (cli *pricingClient) GetPaymentFeeConfig() ([]*PaymentFeeConfig, *common.APIResponse) {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetListPaymentFeeConfig, nil)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetFeeConfig",
		}
	}

	var result *PaymentFeeConfigResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetFeeConfig",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data, nil
}
