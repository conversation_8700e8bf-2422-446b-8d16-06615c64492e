package migrate

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateUpdateOrdersForCreatedByAccountID() *common.APIResponse {
	fmt.Println("MigrateOrders START")
	defer fmt.Println("MigrateOrders FINISH")

	var limit int64 = 1000
	var updatedCount int64
	cursor := primitive.NewObjectIDFromTimestamp(time.Date(2000, 01, 01, 0, 0, 0, 0, utils.VNTimeZone))

	for {
		ordersRes := model.OrderDB.Query(bson.M{
			"_id": bson.M{
				"$gt": cursor,
			},
		}, 0, limit, &primitive.M{"_id": 1})
		if ordersRes.Status != common.APIStatus.Ok {
			break
		}

		orders := ordersRes.Data.([]*model.Order)
		cursor = *orders[len(orders)-1].ID

		for _, order := range orders {
			if order.AccountID == 0 || order.CreatedByAccountID != 0 {
				continue
			}
			model.OrderDB.UpdateOne(&model.Order{
				ID:      order.ID,
				OrderID: order.OrderID,
			}, &model.Order{
				CreatedByAccountID: order.AccountID,
			})
		}

		updatedCount += limit
		if updatedCount%10000 == 0 {
			fmt.Println("MigrateOrders", orders[len(orders)-1].CreatedTime, orders[len(orders)-1].ID)
			fmt.Println("MigrateOrders updated", updatedCount)
		}
		time.Sleep(time.Millisecond * 100)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateUpdateOrdersForCreatedByAccountID finish",
	}
}

func MigrateUpdateOrderItemLevel(skuCode, sellerCode, timeStr string) *common.APIResponse {
	seller, err := client.Services.Seller.GetSeller(sellerCode)
	if err != nil || seller == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	level := enum.LevelSKU.LEVEL_2
	if seller.Level != nil && *seller.Level == enum.LevelSeller.MARKET {
		level = enum.LevelSKU.MARKET
	}

	// get partition 11/2023
	date := time.Date(2023, 11, 15, 0, 0, 0, 0, utils.VNTimeZone)
	if timeStr != "" {
		date, err = time.Parse("2006-01-02", timeStr)
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: err.Error(),
			}
		}
	}

	order := model.Order{
		CreatedTime: &date,
	}

	orderItemDb := model.GetOrderItemPartitionDB(&order, "MigrateOrderItemLevel")

	updater := orderItemDb.UpdateMany(model.OrderItem{Sku: skuCode}, model.OrderItem{
		SkuLevel: &level,
	})

	return updater
}
