package enum

// CartStateValue ...
type CartStateValue string

type cartStateValue struct {
	DRAFT     CartStateValue
	CHECKOUT  CartStateValue
	PAID      CartStateValue
	CANCELLED CartStateValue
	DELETED   CartStateValue
	PENDING   CartStateValue
}

// CartState ...
var CartState = &cartStateValue{
	"DRAFT",
	"CHECKOUT",
	"PAID",
	"CANCELLED",
	"DELETED",
	"PENDING",
}

// CartActionValue ...
type CartActionValue string

type cartAction struct {
	ADD    CartActionValue // add more quality
	REMOVE CartActionValue // remove some quality
	DELETE CartActionValue // delete a item
}

// CartAction ...
var CartAction = &cartAction{
	"ADD",
	"REMOVE",
	"DELETE",
}

// ItemTypeValue ...
type ItemTypeValue string

type itemType struct {
	NORMAL   ItemTypeValue
	IN_COMBO ItemTypeValue
	DEAL     ItemTypeValue
	CAMPAIGN ItemTypeValue
	COMBO    ItemTypeValue
	GIFT     ItemTypeValue
}

// ItemType
var ItemType = &itemType{
	NORMAL:   "NORMAL",
	IN_COMBO: "IN_COMBO",
	DEAL:     "DEAL",
	CAMPAIGN: "CAMPAIGN",
	COMBO:    "COMBO",
	GIFT:     "GIFT",
}

// PaymentMethodValue ...
type PaymentMethodValue string

type paymentMethodEnt struct {
	COD    PaymentMethodValue
	BANK   PaymentMethodValue
	CREDIT PaymentMethodValue

	// partner payment method
	ATM          PaymentMethodValue
	MASTERCARD   PaymentMethodValue
	VISA         PaymentMethodValue
	CREDIT_DEBIT PaymentMethodValue

	QR PaymentMethodValue

	MOMO    PaymentMethodValue
	VIETQR  PaymentMethodValue
	ZALOPAY PaymentMethodValue
}

// PaymentMethod
var PaymentMethod = &paymentMethodEnt{
	COD: "PAYMENT_METHOD_NORMAL",

	BANK:   "PAYMENT_METHOD_BANK",
	CREDIT: "PAYMENT_METHOD_CREDIT",

	// partner payment method
	ATM:          "ATM_BANK_ACCOUNT",
	MASTERCARD:   "MASTERCARD",
	VISA:         "VISA",
	CREDIT_DEBIT: "CREDIT_DEBIT", // Credit card

	QR: "QR_CODE",

	MOMO:    "MOMO",
	VIETQR:  "VIETQR",
	ZALOPAY: "ZALOPAY",
}

// CartTypeValue
type CartTypeValue string
type cartType struct {
	BRAND_SALES CartTypeValue
}

var CartType = &cartType{
	BRAND_SALES: "BRAND_SALES",
}
