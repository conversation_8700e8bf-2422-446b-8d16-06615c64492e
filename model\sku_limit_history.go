package model

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type SkuLimitHistory struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code string `json:"code,omitempty" bson:"code,omitempty"`

	CustomerID int64  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	AccountID  int64  `json:"accountId,omitempty" bson:"account_id,omitempty"`
	Sku        string `json:"sku,omitempty" bson:"sku,omitempty"`
	ItemCode   string `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	Quantity   *int   `json:"quantity,omitempty" bson:"quantity,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`

	// DEPRECATED: should combine to `timeKey` field, like "********", "202501" or "2025".
	// To query skuLimitHistory today, we can query timeKey="YYYYMMDD". We can avoid query mongoDB with range operation, for performance boost.
	// In the future, if we need to query skuLimit with NumberOfDays > 1, we can query by timeKey
	StartTime *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`

	// DEPRECATED: SkuLimitHistory should only store info about the quantity purchased today of an sku, by a customer. No need to track which skuLimitCode is used
	Key           string `json:"key,omitempty" bson:"key,omitempty"` // sku - customer - number of days - date
	SkuLimitCode  string `json:"skuLimitCode,omitempty" bson:"sku_limit_code,omitempty"`
	LimitQuantity int    `json:"limitQuantity,omitempty" bson:"limit_quantity,omitempty"`
	NumberOfDays  int    `json:"numberOfDays,omitempty" bson:"number_of_days,omitempty"`
	IsActive      *bool  `json:"isActive,omitempty" bson:"is_active,omitempty"`
}

func (s *SkuLimitHistory) GetKey() string {
	return ""
}

func (s *SkuLimitHistory) SetKey() {
	now := time.Now().Format("2006-01-02")
	s.Key = fmt.Sprintf("customer_%d_sku_%s_item_code_%s_date_%s", s.CustomerID, s.Sku, s.ItemCode, now)
}

// SkuLimitHistoryDB ...
var SkuLimitHistoryDB = &db.Instance{
	ColName:        "sku_limit_history",
	TemplateObject: &SkuLimitHistory{},
}

// InitSkuLimitHistoryDBModel is func init model sku apply result
func InitSkuLimitHistoryDBModel(s *mongo.Database) {
	SkuLimitHistoryDB.ApplyDatabase(s)
}
