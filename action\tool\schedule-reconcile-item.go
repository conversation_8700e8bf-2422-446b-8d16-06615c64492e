package tool

import (
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ScheduleOrderItem struct {
	SellerCode string `json:"sellerCode"`
	OrderID    int64  `json:"orderID,omitempty"`
}

type ScheduleReconciliationItemReq struct {
	OrderItems    []ScheduleOrderItem `json:"orderItems"`
	CompletedTime *time.Time          `json:"completedTime"`
}

func ScheduleReconciliationItem(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *ScheduleReconciliationItemReq
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	if input.CompletedTime == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing CompletedTime",
			ErrorCode: "MISSING_COMPLETED_TIME",
		})
	}

	ProcessScheduleReconciliationItem(input)

	return res.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Trigger ScheduleReconciliationItem complete",
	})
}

func ProcessScheduleReconciliationItem(input *ScheduleReconciliationItemReq) {
	// Loop qua OrderItems
	// map lại theo key=seller_code, value=array of order_ids
	reconciliationData := make(map[string][]int64)
	for _, orderItem := range input.OrderItems {
		reconciliationData[orderItem.SellerCode] = append(reconciliationData[orderItem.SellerCode], orderItem.OrderID)
	}

	// build sellerMap từ sellerCodes
	sellerCodes := make([]string, 0, len(reconciliationData))
	for k := range reconciliationData {
		sellerCodes = append(sellerCodes, k)
	}
	sellerMap := client.Services.Seller.GetSellerMapFromCodes(sellerCodes)

	// fetch Data Objects
	categories := client.Services.Product.GetSellerCategoriesLevel()
	subCategories := client.Services.Product.GetSellerSubCategories()
	scheduleSettings := client.Services.Seller.GetReconciliationScheduleSetting()

	var feeConfigs []client.SellerReconciliationFeeConfig
	feeConfigRes := client.Services.Seller.GetSellerReconciliationFeeConfigs()
	if feeConfigRes.Status == common.APIStatus.Ok {
		feeConfigs = feeConfigRes.Data.([]client.SellerReconciliationFeeConfig)
	}

	wg := &sync.WaitGroup{}
	for sellerCode, orderIDs := range reconciliationData {
		// Lấy ra seller
		seller := sellerMap[sellerCode]

		// skip for nil seller or nil seller level
		if seller == nil || seller.Level == nil {
			continue
		}

		// Lấy [fromDate, toDate, scheduleIndex, scheduleTime] từ [seller.level + completedTime]
		fromDate, toDate, scheduleIndex, scheduleTime := getScheduleData(seller, input.CompletedTime, scheduleSettings)

		// Special Revenue
		total := model.ReconciliationSpecRevenueDB.Count(model.ReconciliationSpecRevenue{Schedule: scheduleIndex}).Total
		specRevenues := make([]model.ReconciliationSpecRevenue, 0, total)

		var lastID *primitive.ObjectID
		for {
			query := bson.M{
				"schedule": scheduleIndex,
			}

			if lastID != nil {
				query["_id"] = bson.M{"$gt": lastID}
			}

			res := model.ReconciliationSpecRevenueDB.Query(query, 0, 1000, &primitive.M{"_id": 1})

			if res.Status != common.APIStatus.Ok {
				break
			}

			data := res.Data.([]model.ReconciliationSpecRevenue)

			specRevenues = append(specRevenues, data...)

			if len(data) == 0 || len(data) < 1000 {
				break
			}

			lastID = data[len(data)-1].ID
		}

		// specRevRes := model.ReconciliationSpecRevenueDB.Query(model.ReconciliationSpecRevenue{Schedule: scheduleIndex}, 0, total, nil)
		// if specRevRes.Status == common.APIStatus.Ok {
		// 	specRevenues = specRevRes.Data.([]model.ReconciliationSpecRevenue)
		// }

		// For return
		// orderIDStrs := make([]string, 0, len(orderIDs))
		// for _, id := range orderIDs {
		// 	orderIDStrs = append(orderIDStrs, strconv.FormatInt(id, 10))
		// }
		// mapOrderArrSKUReturn := getReturnQuantityByTicket(orderIDStrs, time.Time{}, time.Time{})

		// // Remove orderID if it has return (so that it can be created as REVENUE_RETURN_OUT_TURN)
		// returnOrderIDs := make([]int64, 0)
		// for k, v := range mapOrderArrSKUReturn {
		// 	if len(v) > 0 {
		// 		returnOrderIDs = append(returnOrderIDs, k)
		// 	}
		// }
		// newOrderIDs := make([]int64, 0)
		// for _, orderID := range orderIDs {
		// 	if !utils.IsInt64Contains(returnOrderIDs, orderID) {
		// 		newOrderIDs = append(newOrderIDs, orderID)
		// 	}
		// }
		newOrderIDs := orderIDs
		mapOrderArrSKUReturn := make(map[int64][]*model.ReturnQuantityObject)

		wg.Add(1)
		go processScheduleForSeller(
			wg,
			newOrderIDs,
			*seller,
			fromDate,
			toDate,
			scheduleIndex,
			scheduleTime,
			categories,
			subCategories,
			specRevenues,
			&mapOrderArrSKUReturn,
			feeConfigs,
		)
	}
	wg.Wait()
}

func getScheduleData(seller *client.Seller, completedTime *time.Time, scheduleSettings []*client.ReconciliationScheduleSetting) (fromDate, toDate, scheduleIndex string, scheduleTime time.Time) {
	schedule := reconcile_action.GetSchedule(seller, *completedTime)
	from, scheduleTime, scheduleIndex, _, _ := reconcile_action.GetReconcileTime(schedule, *completedTime, scheduleSettings)
	fromDate = from.Format("2006-01-02")
	toDate = scheduleTime.AddDate(0, 0, -1).Format("2006-01-02")
	return
}
