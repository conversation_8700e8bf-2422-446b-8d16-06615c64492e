package client

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetPromoApplyByCart        = "/marketplace/promotion/v1/voucher/check-with-cart"
	pathGetPromoApplyVoucher       = "/marketplace/promotion/v1/voucher/used"
	pathGetPromoRefundVoucher      = "/marketplace/promotion/v1/voucher/refund"
	pathSyncGamification           = "/marketplace/promotion/v1/gamification-result/sync-from-order"
	pathSyncCartWithCampaign       = "/marketplace/promotion/v1/campaign/check"
	pathUpdateSoldQuantityCampaign = "/marketplace/promotion/v1/campaign/product/sold-quantity"
	pathGetListVoucherActive       = "/marketplace/promotion/v1/voucher/list-from-order"
	pathScoreMission               = "/marketplace/promotion/v1/gamification-score"

	pathCreateVoucherReuseOnOrderCancel = "/marketplace/promotion/v1/voucher/reuse-on-order-cancel"
)

type promotionClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewPromotionServiceClient ...
func NewPromotionServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *promotionClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	promotionClient := &promotionClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	promotionClient.svc.SetDBLog(session)
	return promotionClient
}

// GetVoucherActive is func ...
func (cli *promotionClient) GetListVoucherActive(payload *CheckVoucherRequest) (*VoucherActiveResponse, error) {
	params := map[string]string{}
	// s, _ := json.Marshal(payload)
	// fmt.Printf("%s\n", string(s))
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, &payload, pathGetListVoucherActive, nil)
	if err != nil {
		return nil, err
	}
	var result *VoucherActiveResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result, nil
}

// CheckVoucherCode is func ...
func (cli *promotionClient) CheckVoucherCode(payload *CheckVoucherRequest) ([]*model.PromoApply, error) {
	var mu sync.Mutex

	mu.Lock()
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, &payload, pathGetPromoApplyByCart, nil)
	mu.Unlock()

	if err != nil {
		return nil, err
	}

	var result *PromoResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

// UseVoucher is func ...
func (cli *promotionClient) UseVoucher(payload *UseVoucherRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, &payload, pathGetPromoApplyVoucher, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UseVoucher",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UseVoucher",
		}
	}

	return result
}

// RefundVoucher is func ...
func (cli *promotionClient) RefundVoucher(payload *UseVoucherRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, &payload, pathGetPromoRefundVoucher, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_RefundVoucher",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_RefundVoucher",
		}
	}

	return result
}

type CountOrderValue struct {
	CustomerId       int                             `json:"customerId" bson:"_id"`
	Orders           []int                           `json:"orders" bson:"orders"`
	OrderValues      []*model.GamificationOrderValue `json:"orderValues,omitempty" bson:"order_values,omitempty"`
	TotalPrice       int                             `json:"totalPrice" bson:"total_price"`
	TotalActualPrice int                             `json:"totalActualPrice" bson:"total_actual_price"`
}

// SyncGamification is func ...
func (cli *promotionClient) SyncGamification(values []*CountOrderValue, logSyncGamificationCode string, systemDisplay string) *common.APIResponse {
	params := map[string]string{}
	var body = struct {
		Values                  []*CountOrderValue `json:"values"`
		LogSyncGamificationCode string             `json:"logSyncGamificationCode"`
		SystemDisplay           string             `json:"systemDisplay"`
	}{
		Values:                  values,
		LogSyncGamificationCode: logSyncGamificationCode,
		SystemDisplay:           systemDisplay,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, &body, pathSyncGamification, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UseVoucher",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UseVoucher",
		}
	}

	return result
}

// SyncCampaign is func ...
func (cli *promotionClient) SyncCampaign(payload *model.Cart) ([]*SyncCartWithCampaign, error) {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, &payload, pathSyncCartWithCampaign, nil)
	if err != nil {
		return nil, err
	}
	var result *SyncCartWithCampaignResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

// UpdateSoldQuantityCampaign is func ...
func (cli *promotionClient) UpdateSoldQuantityCampaign(payload *UpdateSoldQuantityCampaign) error {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, &payload, pathUpdateSoldQuantityCampaign, nil)
	if err != nil {
		return err
	}
	var result *BaseAPIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return err
	}

	if result.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", result.Message)
	}

	return nil
}

// pathCreateVoucherReuseOnOrderCancel is func ...
func (cli *promotionClient) CreateVoucherReuseOnOrderCancel(payload *CreateVoucherReuseOnOrderCancelReq) (*common.APIResponse, error) {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, &payload, pathCreateVoucherReuseOnOrderCancel, nil)
	if err != nil {
		return nil, err
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result, nil
}

type orderScore struct {
	OrderId          int64      `json:"orderId"`
	Status           string     `json:"status"`
	CreatedTime      *time.Time `json:"createdTime"`
	TotalPrice       *int       `json:"totalPrice"`
	ActualTotalPrice *int       `json:"actualTotalPrice"`
	SystemDisplay    string     `json:"systemDisplay"`
	Items            []struct {
		ProductId         int      `json:"productId"`
		Quantity          int      `json:"quantity"`
		Sku               string   `json:"sku"`
		Tags              []string `json:"tags"`
		SellerCode        string   `json:"sellerCode"`
		TotalPrice        int      `json:"totalPrice"`
		ActualPrice       int      `json:"actualPrice"`
		CompletedQuantity int      `json:"completedQuantity"`
	} `json:"orderItems"`
	CompletedTime *time.Time `json:"completedTime"`
	ActualPrice   *int       `json:"actualPrice"`
}

func parseOrderToScoreMission(order model.Order, orderScore *orderScore) error {
	bytes, err := json.Marshal(order)
	if err != nil {
		return err
	}
	err = json.Unmarshal(bytes, orderScore)
	return err
}

// ScoreMission is func ...
func (cli *promotionClient) ScoreMission(action string, payload *model.Order) error {
	params := map[string]string{}
	scoreData := orderScore{}
	parseOrderToScoreMission(*payload, &scoreData)
	body := struct {
		AccountID     int64      `json:"accountId"`
		CustomerID    int64      `json:"customerId"`
		Action        string     `json:"action"`
		Key           string     `json:"key"`
		Type          string     `json:"type"`
		Types         []string   `json:"types"`
		Data          orderScore `json:"data"`
		SystemDisplay string     `json:"systemDisplay"`
	}{
		AccountID:     payload.AccountID,
		CustomerID:    payload.CustomerID,
		Action:        action,
		Key:           fmt.Sprintf("ACCOUNT_%d_ORDER_%d_STATUS_%s", payload.AccountID, payload.OrderID, payload.Status),
		Type:          "ORDER",
		Types:         []string{"ORDER", "PRODUCT", "TAG", "SELLER"},
		Data:          scoreData,
		SystemDisplay: payload.SystemDisplay,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, &body, pathScoreMission, nil)
	if err != nil {
		return err
	}
	var result *BaseAPIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return err
	}

	if result.Status != common.APIStatus.Ok {
		return fmt.Errorf("%v", result.Message)
	}

	return nil
}
