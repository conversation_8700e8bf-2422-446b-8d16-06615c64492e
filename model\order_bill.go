package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type OrderBill struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	OrderId       int64                `json:"orderId,omitempty" bson:"order_id,omitempty"`
	CustomerId    int64                `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	SaleOrderCode string               `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	BillCode      string               `json:"billCode,omitempty" bson:"bill_code,omitempty"`
	Status        enum.BillStatusValue `json:"status,omitempty" bson:"status,omitempty" validate:"required"`
}

var OrderBillDB = &db.Instance{
	ColName:        "order_bill",
	TemplateObject: &OrderBill{},
}

func InitOrderBillModel(s *mongo.Database) {
	OrderBillDB.ApplyDatabase(s)
}
