package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type OrderTagConfig struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Name                 string            `json:"name,omitempty" bson:"name,omitempty"`
	TagCode              string            `json:"tagCode,omitempty" bson:"tagCode,omitempty"`
	IsActive             *bool             `json:"isActive,omitempty" bson:"is_active,omitempty"`
	ProvinceAppliedCodes *[]string         `json:"provinceAppliedCodes,omitempty" bson:"province_applied_codes,omitempty"`
	ProvinceAppliedValue *int64            `json:"provinceAppliedValue,omitempty" bson:"province_applied_value,omitempty"` // hour
	ProvinceAppliedMap   map[string]string `json:"-" bson:"-"`
}

// OrderTagConfigDB ...
var OrderTagConfigDB = &db.Instance{
	ColName:        "order_tag_config",
	TemplateObject: &OrderTagConfig{},
}

func InitOrderTagConfigModel(s *mongo.Database) {
	OrderTagConfigDB.ApplyDatabase(s)

}
