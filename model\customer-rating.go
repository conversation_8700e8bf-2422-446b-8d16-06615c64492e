package model

import "gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"

type CustomerRating struct {
	RatingID     int64                   `json:"ratingID,omitempty" bson:"rating_id,omitempty"`
	Code         string                  `json:"code,omitempty" bson:"code,omitempty"`
	OrderID      int64                   `json:"orderID,omitempty" bson:"order_id,omitempty"`
	Status       string                  `json:"status,omitempty" bson:"status,omitempty"`
	CustomerID   int64                   `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	RegionCode   string                  `json:"regionCode,omitempty" bson:"region_code,omitempty"`
	ProvinceCode string                  `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	Point        *float64                `json:"point,omitempty" bson:"point,omitempty"`
	FeedBack     *string                 `json:"feedback,omitempty" bson:"feedback,omitempty"`
	Type         enum.CustomerRatingType `json:"type,omitempty" bson:"type,omitempty"`
}
