package tool

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

type RemoveReconcileReq struct {
	SellerCode       string   `json:"sellerCode"`
	ReconcileIndexes []string `json:"reconcileIndexes"`
}

func RemoveReconcile(req sdk.APIRequest, res sdk.APIResponder) error {
	var input RemoveReconcileReq
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	return res.Respond(removeReconciles(&input))
}

func removeReconciles(input *RemoveReconcileReq) *common.APIResponse {
	if input.SellerCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "SellerCode required",
		}
	}

	if len(input.ReconcileIndexes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "ReconcileIndexes required",
		}
	}

	total := len(input.ReconcileIndexes)
	errors := make([]string, 0)
	for _, reconcileIndex := range input.ReconcileIndexes {
		if err := removeReconcile(input.SellerCode, reconcileIndex); err != nil {
			errors = append(errors, err.Error())
		}
	}

	removeFailed := len(errors)
	removedCount := total - removeFailed

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Remove reconcile finished with data",
		Data: []interface{}{
			map[string]interface{}{
				"sellerCode":       input.SellerCode,
				"reconcileIndexes": input.ReconcileIndexes,
				"errors":           errors,
				"total":            total,
				"removedCount":     removedCount,
				"removeFailed":     removeFailed,
			},
		},
	}
}

func removeReconcile(sellerCode, reconcileIndex string) error {
	reconciliationRes := model.ReconciliationDB.QueryOne(&model.Reconciliation{
		SellerCode:                 sellerCode,
		ReconcileScheduleTimeIndex: reconcileIndex,
	})
	if reconciliationRes.Status != common.APIStatus.Ok {
		return fmt.Errorf(
			"reconciliation for seller %s with schedule index %s not found",
			sellerCode, reconcileIndex)
	}

	reconciliation := reconciliationRes.Data.([]*model.Reconciliation)[0]
	if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
		return fmt.Errorf(
			"reconciliation for seller %s with schedule index %s has status %s",
			sellerCode, reconcileIndex, reconciliation.ReconciliationStatus)
	}

	deleteRes := model.ReconciliationDB.Delete(&model.Reconciliation{
		SellerCode:                 sellerCode,
		ReconcileScheduleTimeIndex: reconcileIndex,
	})
	if deleteRes.Status != common.APIStatus.Ok {
		return fmt.Errorf(
			"delete reconciliation for seller %s with schedule index %s failed: %s",
			sellerCode, reconcileIndex, deleteRes.Message)
	}

	deleteRes = model.ReconciliationItemDB.Delete(&model.ReconciliationItem{
		SellerCode:                 sellerCode,
		ReconcileScheduleTimeIndex: reconcileIndex,
	})
	if deleteRes.Status != common.APIStatus.Ok {
		return fmt.Errorf(
			"delete reconciliation item for seller %s with schedule index %s failed: %s",
			sellerCode, reconcileIndex, deleteRes.Message)
	}

	return nil
}
