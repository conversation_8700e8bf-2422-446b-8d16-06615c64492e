package migrate

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateReconcileInvoiceOverdue() *common.APIResponse {
	var offset, limit int64 = 0, 100
	recItemCount := 0
	recItemDelCount := 0

	for {
		queryRes := model.ReconciliationItemDB.Query(&model.ReconciliationItem{
			OperationAnd: []bson.M{
				{"reconcile_schedule_time_index": bson.M{"$in": []string{"20230901", "20230901.20230823"}}},
				{"fee_type": "INVOICE_OVERDUE_FEE"},
			},
		}, offset, limit, &primitive.M{"_id": 1})
		if queryRes.Status != common.APIStatus.Ok {
			break
		}

		reconciliationItems := queryRes.Data.([]*model.ReconciliationItem)
		for _, reconciliationItem := range reconciliationItems {
			recItemCount++

			if !reconciliationItem.AutoPenaltyFee {
				continue
			}

			if reconciliationItem.InvoiceID == 0 {
				continue
			}

			invoiceRes := model.InvoiceDB.QueryOne(model.Invoice{
				InvoiceID:   reconciliationItem.InvoiceID,
				InvoiceCode: reconciliationItem.InvoiceCode,
			})
			if invoiceRes.Status != common.APIStatus.Ok {
				continue
			}

			invoice := invoiceRes.Data.([]*model.Invoice)[0]
			if invoice.Request != nil && *invoice.Request {
				continue
			}

			recItemDeletedRes := model.ReconciliationItemDB.Delete(&model.ReconciliationItem{
				ID: reconciliationItem.ID,
			})
			if recItemDeletedRes.Status == common.APIStatus.Ok {
				recItemDelCount++
			}
		}

		offset += limit
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate finish",
		Data: []interface{}{
			map[string]interface{}{
				"recItemCount":    recItemCount,
				"recItemDelCount": recItemDelCount,
			},
		},
	}
}

func MigrateInvoiceAutoChangeStatus() *common.APIResponse {

	now := time.Now()
	filter := model.Invoice{
		InvoiceStatus: model.InvoiceStatus.Waiting,
		OperationAnd: []*bson.M{
			{
				"invoice_deadline": bson.M{"$lte": now},
			},
		},
	}
	updater := model.Invoice{
		AutoChangeToOverdue: utils.ParseBoolToPointer(true),
	}

	fmt.Println("Migrate Invoice Auto Change Status Completed")

	return model.InvoiceDB.UpdateMany(filter, updater)
}
