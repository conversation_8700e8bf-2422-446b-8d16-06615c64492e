package migrate

import (
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func MigrateReconcile3DaysReconcile() *common.APIResponse {
	recIndex1 := "20231028.20231025"
	recIndex2 := "20231101.20231028"

	model.ReconciliationInboundPenaltyDB.Delete(model.ReconciliationInboundPenalty{
		ReconcileTimeIndex: recIndex1,
	})
	model.ReconciliationInboundPenaltyDB.Delete(model.ReconciliationInboundPenalty{
		ReconcileTimeIndex: recIndex2,
	})

	offset := 0
	limit := 1000
	_, total, errGetSellers := client.Services.Seller.GetSellerList("EXTERNAL", offset, 1)
	sellers := make([]*client.Seller, 0, total)
	var data []*client.Seller
	for errGetSellers == nil && offset < total {
		data, _, errGetSellers = client.Services.Seller.GetSellerList("EXTERNAL", offset, limit)
		offset += limit
		sellers = append(sellers, data...)
	}

	for _, seller := range sellers {
		model.ReconciliationDB.Delete(model.Reconciliation{
			SellerCode:                 seller.Code,
			ReconcileScheduleTimeIndex: recIndex1,
		})
		model.ReconciliationDB.Delete(model.Reconciliation{
			SellerCode:                 seller.Code,
			ReconcileScheduleTimeIndex: recIndex2,
		})
		model.ReconciliationItemDB.Delete(model.ReconciliationItem{
			SellerCode:                 seller.Code,
			ReconcileScheduleTimeIndex: recIndex1,
		})
		model.ReconciliationItemDB.Delete(model.ReconciliationItem{
			SellerCode:                 seller.Code,
			ReconcileScheduleTimeIndex: recIndex2,
		})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "MigrateReconcile3DaysReconcile finished " + strconv.Itoa(len(sellers)),
	}
}
