package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

// ActionSource ...
type ActionSource struct {
	Account *Account `json:"account"`
	Session *Session `json:"session"`
}

// Account is model define response from register account
type Account struct {
	AccountID int64       `json:"accountId"`
	Customer  interface{} `json:"customer"`
	Type      string      `json:"type"`
	FullName  string      `json:"fullname"`
	Username  string      `json:"username"`

	SourceDetail *OrderSourceDetail `json:"-"`
}

// Customer ...
type Customer struct {
	CustomerCode string                  `json:"code"`
	CustomerID   int64                   `json:"customerID"`
	AccountID    int64                   `json:"accountID"`
	Name         string                  `json:"name"`
	Username     string                  `json:"username"`
	Scope        string                  `json:"scope"`
	Level        string                  `json:"level"`
	Point        float64                 `json:"point"`
	RegionCode   string                  `json:"regionCode"`
	ProvinceCode string                  `json:"provinceCode"`
	DistrictCode string                  `json:"districtCode"`
	WardCode     string                  `json:"wardCode"`
	IsActive     int                     `json:"isActive"`
	Status       string                  `json:"status"`
	OrdersCount  *int                    `json:"ordersCount"`
	Phone        string                  `json:"phone,omitempty"`
	Email        string                  `json:"email"`
	BusinessName string                  `json:"businessName"`
	Address      string                  `json:"address,omitempty"`
	TaxCode      string                  `json:"taxCode,omitempty"`
	Color        enum.CustomerColorValue `json:"color"`
	Tags         []string                `json:"tags"`
	TagMap       map[string]bool         `json:"-"`

	Licenses                       *[]*DocumentImage                      `json:"licenses,omitempty"`                   // Giấy phép kinh doanh
	PharmacyEligibilityLicense     *[]*DocumentImage                      `json:"pharmacyEligibilityLicense,omitempty"` // giầy đủ điều kiện kinh doanh dược
	Gpp                            *[]*DocumentImage                      `json:"gpp,omitempty"`
	Gdp                            *[]*DocumentImage                      `json:"gdp,omitempty"`
	Gsp                            *[]*DocumentImage                      `json:"gsp,omitempty"`
	ExaminationAndTreatmentLicense *[]*DocumentImage                      `json:"examinationAndTreatmentLicense,omitempty"` // giấy phép hoạt động khám, chữa bệnh
	CertificateMap                 map[enum.CustomerCertificateValue]bool `json:"-"`

	BusinessType enum.BusinessTypeValue `json:"businessType,omitempty" bson:"business_type,omitempty"` // TRADING || NON-TRADING

	IsInvalidLicense bool `json:"isInvalidLicense,omitempty" bson:"is_invalid_license,omitempty"`
	IsBlockCheckout  bool `json:"isBlockCheckout,omitempty" bson:"is_block_checkout,omitempty"`
}

// canSendZns checks if the customer can receive ZNS messages
// Returns false if customer has CLINIC tag, true otherwise
func (c *Customer) CanReceiveZns() bool {
	if c.TagMap != nil {
		if c.TagMap[string(enum.CustomerTag.Clinic)] {
			return false
		}
	}

	// Also check Tags slice in case TagMap is not populated
	for _, tag := range c.Tags {
		if tag == string(enum.CustomerTag.Clinic) {
			return false
		}
	}

	return true
}

type SummationOrderInfo struct {
	CustomerID int64  `json:"customerId"`
	LastOrder  *Order `json:"lastOrder"`  // Đơn hàng gần nhất đã được confirm
	TotalOrder int64  `json:"totalOrder"` // Tổng đơn hàng đã được confirm
}

type Session struct {
	Token         string     `json:"token,omitempty" bson:"token,omitempty" `
	Username      string     `json:"username,omitempty" bson:"username,omitempty" `
	ExpiredTime   *time.Time `json:"expiredTime,omitempty" bson:"expired_time,omitempty" `
	Status        string     `json:"status,omitempty" bson:"status,omitempty" `
	Type          string     `json:"type,omitempty" bson:"type,omitempty"`
	RootAccountID int64      `json:"rootAccountID,omitempty" bson:"root_account_id,omitempty"`

	IP        string `json:"ip,omitempty" bson:"ip,omitempty" `
	UserAgent string `json:"userAgent,omitempty" bson:"user_agent,omitempty" `
	Note      string `json:"note,omitempty" bson:"note,omitempty" `

	EntityCode string `json:"entityCode,omitempty" bson:"entity_code,omitempty"`
	SalesType  string `json:"salesType,omitempty" bson:"sales_type,omitempty"`
}

type OnepayGetList struct {
	Url string `json:"url"`
}

type OnepayGetListResponse struct {
	Status    string           `json:"status,omitempty"`
	Message   string           `json:"message,omitempty"`
	ErrorCode string           `json:"errorCode,omitempty"`
	Data      []*OnepayGetList `json:"data,omitempty"`
}

type OnelinePaymentConfig struct {
	PaymentMethodOrderTransfer []string `json:"paymentMethodOrderTransfer"`
	PartnerPaymentMethod       []string `json:"partnerPaymentMethod"`
}
