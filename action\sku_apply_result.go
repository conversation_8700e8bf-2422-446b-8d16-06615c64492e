package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateSkuApplyResult(input *model.SkuApplyResult) *common.APIResponse {
	if input.SKU == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không tìm thấy mã sku",
			ErrorCode: "SKU_NOT_FOUND",
		}
	}
	return model.SkuApplyResultDB.Create(input)
}

func UpdateSkuApplyResult(itemCode string, updateData *model.SkuApplyResult) *common.APIResponse {
	qResult := model.SkuApplyResultDB.QueryOne(model.SkuApplyResult{SKU: updateData.SKU, ItemCode: updateData.ItemCode})
	if qResult.Status != common.APIStatus.Ok {
		return model.SkuApplyResultDB.Create(updateData)
	}
	updateData.ID = primitive.NilObjectID
	updateData.SKU = ""
	updateData.Type = ""
	if updateData.Quantity == nil {
		updateData.Quantity = utils.ParseIntToPointer(0)
	}
	return model.SkuApplyResultDB.UpdateOne(model.SkuApplyResult{ItemCode: itemCode}, updateData)
}
