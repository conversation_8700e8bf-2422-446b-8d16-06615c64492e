package action

import (
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/tool"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

func GenerateSellerClassForOrderItems(orderItems []*model.OrderItem) {
	for _, item := range orderItems {
		item.AutoSetSellerClass()
		if item.SubItems != nil {
			for _, subItem := range *item.SubItems {
				subItem.AutoSetSellerClass()
			}
		}

		if item.SellerClass != nil && *item.SellerClass == enum.SellerClass.EXTERNAL {
			sellerConfig, _ := client.Services.Seller.GetSellerConfig(item.SellerCode)
			receiveInvoiceInfoBy := tool.GetValidReceiveInvoiceInfoBy(sellerConfig)
			item.ReceiveInvoiceInfoBy = receiveInvoiceInfoBy
		}
	}
}
