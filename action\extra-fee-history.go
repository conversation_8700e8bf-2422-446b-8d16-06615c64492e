package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func GetExtraFeeHistoryByCustomerId(customerId int64) *common.APIResponse {
	return model.UserExtraFeeHistoryDB.QueryOne(&model.UserExtraFeeHistory{CustomerID: customerId})
}

func UpdateExtraFeeHistory(input *model.UserExtraFeeHistory) *common.APIResponse {
	query := model.UserExtraFeeHistoryDB.QueryOne(&model.UserExtraFeeHistory{CustomerID: input.CustomerID})

	if input.CustomerID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "ID_REQUIRED",
			Message:   "Customer is required",
		}
	}

	if query.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			ErrorCode: "ID_NOT_FOUND",
			Message:   "Not found extra fee history",
		}
	}

	return model.UserExtraFeeHistoryDB.UpdateOne(&model.UserExtraFeeHistory{
		CustomerID: input.CustomerID,
	}, input)
}
