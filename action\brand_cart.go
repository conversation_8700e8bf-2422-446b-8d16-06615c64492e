package action

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func getCustomerInfoBrandSales(acc *model.Account, customerID int64) (*model.Customer, *common.APIResponse) {

	if acc.Type == enum.AccountType.CUSTOMER {
		return getCustomerProfile(acc)
	} else if acc.Type == enum.AccountType.BRAND_SALES && customerID != 0 {
		return getCustomerProfileByCustomerID(customerID)
	}

	return nil, &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Invalid body request",
		ErrorCode: "INVALID_CUSTOMER_ID",
	}
}

func GetBrandCartInfo(acc *model.Account, session *model.Session, customerID int64, salesTypeCode string) *common.APIResponse {

	customer, errCustomerRes := getCustomerInfoBrandSales(acc, customerID)
	if errCustomerRes != nil {
		return errCustomerRes
	}
	customerID = customer.CustomerID

	// get cart key
	key, err := generateKeyBrandPortal(acc.Type, customerID, acc.AccountID, session)
	if err != nil {
		return err
	}
	cartRes := model.BrandCartDB.QueryOne(&model.Cart{
		Key: key,
	})
	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]
	cartItemRes := model.BrandCartItemDB.Query(&model.CartItem{CartID: cart.CartID}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		_ = model.BrandCartDB.Delete(&model.Cart{CartID: cart.CartID})
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Not found any matched cart",
		}
	}

	salesType, errSales := client.Services.BrandSales.GetSalesType(session.EntityCode)

	cart.Items = cartItemRes.Data.([]*model.CartItem)
	cart.ProvinceCode = customer.ProvinceCode
	cart.DistrictCode = customer.DistrictCode
	cart.WardCode = customer.WardCode
	cart.CustomerScope = customer.Scope
	cart.SourceDetail = acc.SourceDetail
	if errSales == nil && salesType != nil {
		cart.SalesType = salesType.Type
		cart.SalesTypeCode = salesType.Code
	}

	if customer.Scope == "DENTISTRY" {
		cart.SystemDisplay = "BUYDENTAL"
	} else {
		if acc.Type == enum.AccountType.CUSTOMER {
			cart.SystemDisplay = "CLINIC_PORTAL"
			if cart.Source == nil {
				cart.Source = &enum.Source.CLINIC_PORTAL
			}

		} else {
			cart.SystemDisplay = "BRAND_PORTAL"
			if cart.Source == nil {
				cart.Source = &enum.Source.BRAND_PORTAL
			}
		}
	}
	cart.AutoRemoveVoucherAutoInvalid = true
	cart.GetVoucherAutoApply = true
	resetCartPrice(cart)
	_ = handleProcessSelectItemByBrand(cart, salesTypeCode)
	_ = handleProcessContentItems(cart, customer, nil, true)
	// if source is branch portal call handleProcessPriceItems 2 times
	_ = handleProcessContentItems(cart, customer, nil, true)

	_ = handleProcessPriceItems(customer, cart)
	_, _ = handleProcessDeliveryAndPaymentMethod(cart, customer)
	_ = handleApplyVoucherCode(cart, customer, "GetCartInfo")
	skipReturnAutoApplyVoucher(cart)
	_ = handleDeliveryDate(cart, common.False)
	_ = handleExtraFee(customer, cart)
	_ = checkCartLimit(cart)
	_ = validateCartItem(customer, cart)

	debtResp, err1 := client.Services.AccountingDebt.CheckDebtContractByCustomerID(cart.CustomerID)
	if err1 == nil {
		debtContract := debtResp[0]
		if debtContract.IsValid {
			isAllow := client.Services.AccountingDebt.CheckoutOrder(&client.CheckoutOrderInput{
				IsVerify:    true,
				OrderCode:   cart.CartNo,
				CustomerID:  cart.CustomerID,
				OrderAmount: int64(cart.TotalPrice),
				RequestID:   time.Now().UnixNano(),
			})
			if isAllow.Status == common.APIStatus.Ok {
				cart.PaymentMethod = string(enum.PaymentMethod.CREDIT)
			}
		}
	}

	go func(cart *model.Cart) {

		cartUpdater := &model.Cart{
			RegionCodes:       cart.RegionCodes,
			TotalPrice:        cart.TotalPrice,
			Price:             cart.Price,
			SubPrice:          cart.SubPrice,
			TotalFee:          cart.TotalFee,
			Discount:          cart.Discount,
			TotalItem:         cart.TotalItem,
			TotalQuantity:     cart.TotalQuantity,
			RedeemCode:        cart.AllRedeemCode,
			RedeemApplyResult: cart.RedeemApplyResult,
			RedeemCodeMap:     cart.RedeemCodeMap,
			SystemDisplay:     cart.SystemDisplay,
			ProvinceCode:      customer.ProvinceCode,
			DistrictCode:      customer.DistrictCode,
			WardCode:          customer.WardCode,
			Status:            enum.CartState.DRAFT,
			PaymentMethod:     cart.PaymentMethod,
		}

		if cart.DeliveryMethod == "" {
			cartUpdater.DeliveryMethod = "DELIVERY_PLATFORM_NORMAL"
		}
		if cartUpdater.PaymentMethod == "" {
			cartUpdater.PaymentMethod = string(enum.PaymentMethod.COD)
		}

		cartUpdater.FlattenLocation = FlatLocationCart(*cartUpdater)

		model.BrandCartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, cartUpdater)
	}(cart)
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    []*model.Cart{cart},
		Message: "Get cart success",
	}
}

/*
BrandAddCartItem func process:
- 1 - get customer & validate customer info
- 2 - get current cart: handle new cart if current cart is not exist
- 3 - item existed -> update current item, not existed -> new item
- 4 - create or update cart
*/
func AddBrandCartItem(acc *model.Account, input *model.CartAddItem, eventTracking *client.AddToCartEvent, session *model.Session) *common.APIResponse {

	customer, errCustomerRes := getCustomerInfoBrandSales(acc, input.CustomerID)
	if errCustomerRes != nil {
		return errCustomerRes
	}
	input.CustomerID = customer.CustomerID
	var salesTypeCode = ""
	if acc.Type == enum.AccountType.BRAND_SALES && session != nil {
		salesTypeCode = session.EntityCode
	} else if acc.Type == enum.AccountType.CUSTOMER {
		salesTypeCode = input.SalesTypeCode
	}
	// get cart key
	key, err := generateKeyBrandPortal(acc.Type, input.CustomerID, acc.AccountID, session)
	if err != nil {
		return err
	}

	if customer.Scope == "DENTISTRY" && !utils.IsContains(conf.DENTAL_SELLERS, input.SellerCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Khách hàng Nha khoa không thể mua sản phẩm không thuộc Nha khoa",
			ErrorCode: "DENTISTRY_CAN_NOT_BUY_MEDICAL_PRODUCT",
		}
	} else if customer.Scope != "DENTISTRY" && utils.IsContains(conf.DENTAL_SELLERS, input.SellerCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể mua sản phẩm thuộc Nha khoa",
			ErrorCode: "CAN_NOT_BUY_DENISTRY_PRODUCT",
		}
	}

	product := client.Services.Product.GetProductInfo(input.Sku, 0)
	if product == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   fmt.Sprintf("Không tìm thấy sản phẩm từ sku: %s", input.Sku),
			ErrorCode: "PRODUCT_NOT_FOUND_BY_SKU",
		}
	}

	salesType, errSales := client.Services.BrandSales.GetSalesType(salesTypeCode)
	if errSales != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: errSales.Error(),
		}
	}
	if salesType == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   fmt.Sprintf("Không tìm thấy sales type: %s", salesTypeCode),
			ErrorCode: "SALES_TYPE_NOT_FOUND_BY_ENTITYCODE",
		}
	}

	if acc.Type == enum.AccountType.BRAND_SALES &&
		session != nil && salesTypeCode != salesType.Code {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   fmt.Sprintf("Mã không hợp lệ: %s", salesTypeCode),
			ErrorCode: "ENTITY_CODE_INVALID",
		}
	}

	cartItem := &model.CartItem{
		Sku:         input.Sku,
		Quantity:    input.Quantity,
		Type:        input.Type,
		IsSelected:  input.IsSelected,
		Page:        input.Page,
		SearchKey:   input.SearchKey,
		EventSource: input.EventSource,
		EventScreen: input.EventScreen,
	}

	if input.Source != nil {
		cartItem.Source = *input.Source
	} else {
		if acc.Type == enum.AccountType.CUSTOMER {
			input.Source = &enum.Source.CLINIC_PORTAL
		} else {
			input.Source = &enum.Source.BRAND_PORTAL
		}
	}
	if input.SystemDisplay == "" {
		if acc.Type == enum.AccountType.CUSTOMER {
			input.SystemDisplay = "CLINIC_PORTAL"
		} else {
			input.SystemDisplay = "BRAND_PORTAL"
		}
	}
	if input.Source == nil || *input.Source == "" {
		if acc.Type == enum.AccountType.CUSTOMER {
			input.Source = &enum.Source.CLINIC_PORTAL
		} else {
			input.Source = &enum.Source.BRAND_PORTAL
		}
	}
	cartRes := model.BrandCartDB.QueryOne(&model.Cart{
		Key: key,
	})

	// current cart no existed
	if cartRes.Status != common.APIStatus.Ok {
		t := time.Now()

		newCart := &model.Cart{
			CreatedByAccountID: acc.AccountID,
			CustomerID:         customer.CustomerID,
			AccountID:          customer.AccountID,
			CustomerCode:       customer.CustomerCode,
			Status:             enum.CartState.DRAFT,
			CreatedTime:        &t,
			RefCartItem:        t.UnixNano(),
			Source:             input.Source,
			Key:                key,
			SalesTypeCode:      salesType.Code,
			SalesType:          salesType.Type,
		}
		if acc.Type == enum.AccountType.BRAND_SALES && session != nil {
			newCart.BrandCode = session.EntityCode
		}

		newCart.CartID, newCart.CartNo = model.GetOrderID()
		newCart.LastActionTime = &t

		cartItem.CartID = newCart.CartID
		cartItem.CartNo = newCart.CartNo
		cartItem.RefCart = newCart.RefCartItem
		cartItem.IsFirstAdd = true
		cartItem.ManufacturerCode = product.ManufacturerCode

		newCart.Items = []*model.CartItem{cartItem}
		newCart.RegionCode = customer.RegionCode
		newCart.ProvinceCode = customer.ProvinceCode
		newCart.DistrictCode = customer.DistrictCode
		newCart.WardCode = customer.WardCode
		newCart.CustomerScope = customer.Scope
		// for PIC
		newCart.FlattenLocation = FlatLocationCart(*newCart)

		err := handleProcessContentItems(newCart, customer, nil, false)
		if err != nil {
			return err
		}
		_ = handleProcessPriceItems(customer, newCart)
		err = validateCartItem(customer, newCart)
		if err != nil {
			return err
		}
		err = checkBrandSkuLimit(newCart)
		if err != nil {
			return err
		}

		newCart.SystemDisplay = input.SystemDisplay
		createCartRes := model.BrandCartDB.Create(&newCart)
		if createCartRes.Status == common.APIStatus.Ok {
			if newCart.Items[0].IsCombo == nil || (newCart.Items[0].IsCombo != nil && !*newCart.Items[0].IsCombo) {
				newCart.Items[0].CurPrice = newCart.Items[0].Price
				newCart.Items[0].OldType = newCart.Items[0].Type
				newCart.Items[0].OldLevel = customer.Level
			}
			createCartItemRes := model.BrandCartItemDB.CreateMany(newCart.Items)
			go func() {
				for k, v := range input.Metadata {
					eventTracking.Metadata[k] = v
				}
				eventTracking.ResultErrorCode = createCartItemRes.ErrorCode
				eventTracking.ResultStatus = createCartItemRes.Status
				eventTracking.Metadata["product_id"] = fmt.Sprint(cartItem.ProductID)
				eventTracking.Metadata["seller_code"] = fmt.Sprint(cartItem.SellerCode)
				sellerResp := client.Services.Seller.GetSellerListWithParams(client.ReqSellerList{SellerCode: cartItem.SellerCode})
				if sellerResp.Status == common.APIStatus.Ok {
					eventTracking.Metadata["seller_id"] = fmt.Sprint(sellerResp.Data[0].SellerID)
				}
				client.Services.Collector.SendEventAddToCart(eventTracking)
			}()
			if createCartItemRes.Status != common.APIStatus.Ok {
				return createCartItemRes
			}
		}
		return createCartRes
	}

	// current cart existed
	curCart := cartRes.Data.([]*model.Cart)[0]
	cartItemRes := model.BrandCartItemDB.Query(&model.CartItem{CartID: curCart.CartID}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		_ = model.BrandCartDB.Delete(&model.Cart{
			CartID: curCart.CartID,
		})
		return cartItemRes
	}
	curItems := cartItemRes.Data.([]*model.CartItem)

	if len(curItems) >= 200 {
		return CartItemLimitError
	}
	isExisted := false
	cartItem.CartID = curCart.CartID
	cartItem.CartNo = curCart.CartNo
	cartItem.RefCart = curCart.RefCartItem
	cartItem.CreatedTime = utils.ParseTimeToPointer(time.Now())
	cartItem.ManufacturerCode = product.ManufacturerCode
	for _, item := range curItems {
		if item.Sku == input.Sku {
			isExisted = true
			cartItem.IsSelected = item.IsSelected
			cartItem.CurPrice = item.CurPrice
			if item.CreatedTime != nil {
				cartItem.CreatedTime = item.CreatedTime
			}
		}
	}
	curCart.Items = []*model.CartItem{cartItem}
	curCart.CustomerScope = customer.Scope
	curCart.SalesTypeCode = salesType.Code
	curCart.SalesType = salesType.Type
	resetCartPrice(curCart)

	err = handleProcessContentItems(curCart, customer, nil, false)
	if err != nil {
		return err
	}
	err = checkBrandSkuLimit(curCart)
	if err != nil {
		return err
	}

	_ = handleProcessPriceItems(customer, curCart)
	if !isExisted {
		cartItem.IsFirstAdd = true
		if cartItem.IsCombo == nil || (cartItem.IsCombo != nil && !*cartItem.IsCombo) {
			cartItem.CurPrice = cartItem.Price
			cartItem.OldType = cartItem.Type
			cartItem.OldLevel = customer.Level
		}
	}
	err = validateCartItem(customer, curCart)
	if err != nil {
		return err
	}
	cartItem = curCart.Items[0]

	cartItem.ManufacturerCode = product.ManufacturerCode
	returnOpt := options.After
	res := model.BrandCartItemDB.UpdateOne(&model.CartItem{CartID: curCart.CartID, Sku: cartItem.Sku}, cartItem, &options.FindOneAndUpdateOptions{
		Upsert:         utils.ParseBoolToPointer(true),
		ReturnDocument: &returnOpt,
	})

	// Send tracking event for ADD_TO_CART and UPDATE_CART_QUANTITY
	go func() {
		for k, v := range input.Metadata {
			eventTracking.Metadata[k] = v
		}
		eventTracking.ResultErrorCode = res.ErrorCode
		eventTracking.ResultStatus = res.Status
		eventTracking.Metadata["product_id"] = fmt.Sprint(cartItem.ProductID)
		eventTracking.Metadata["seller_code"] = fmt.Sprint(cartItem.SellerCode)
		sellerResp := client.Services.Seller.GetSellerListWithParams(client.ReqSellerList{SellerCode: cartItem.SellerCode})
		if sellerResp.Status == common.APIStatus.Ok {
			eventTracking.Metadata["seller_id"] = fmt.Sprint(sellerResp.Data[0].SellerID)
		}

		if !isExisted {
			client.Services.Collector.SendEventAddToCart(eventTracking)
		} else {
			client.Services.Collector.SendEventUpdateCartQuantity(eventTracking)
		}
	}()

	if res.Status == common.APIStatus.Ok {
		go func() {
			t := time.Now()
			model.BrandCartDB.UpdateOne(&model.Cart{CartID: cartItem.CartID}, &model.Cart{LastActionTime: &t})
		}()
	}

	return res
}

func BrandRemoveCartItem(acc *model.Account, input *model.CartRemoveItem, event *client.RemoveFromCartEvent, session *model.Session) *common.APIResponse {

	customer, errCustomerRes := getCustomerInfoBrandSales(acc, input.CustomerID)
	if errCustomerRes != nil {
		return errCustomerRes
	}
	input.CustomerID = customer.CustomerID

	if !input.IsDeleteAll && len(input.Skus) == 0 && input.Sku == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid body request",
			ErrorCode: "INVALID_DELETE_ITEM_REQUEST",
		}
	}

	cart, errGetCart := getBrandCart(acc, input.CustomerID, session)
	if errGetCart != nil {
		return errGetCart
	}
	query := &model.CartItem{CartID: cart.CartID}

	itemBySkuCode := make(map[string]*model.CartItem, len(cart.Items))
	sellerCodes := make([]string, 0, len(cart.Items))
	for _, item := range cart.Items {
		itemBySkuCode[item.Sku] = item
		sellerCodes = append(sellerCodes, item.SellerCode)
	}

	// Delete 1 item
	if input.Sku != "" {
		input.Skus = append(input.Skus, input.Sku)
	}

	// Delete all
	if input.IsDeleteAll {
		input.Skus = nil
		for _, item := range cart.Items {
			input.Skus = append(input.Skus, item.Sku)
		}
	}
	input.Skus = utils.UniqueStringSlice(input.Skus)
	if len(input.Skus) > 0 {
		query.ComplexQuery = []*bson.M{
			{
				"sku": bson.M{
					"$in": input.Skus,
				},
			},
		}
	}
	// TODO SKIP CHECK CART ITEM TYPE
	qResult := model.BrandCartItemDB.Query(query, 0, 0, nil)
	if qResult.Status != common.APIStatus.Ok {
		return qResult
	}

	qItemResult := qResult.Data.([]*model.CartItem)

	res := model.BrandCartItemDB.Delete(query)
	if res.Status == common.APIStatus.Ok {
		go func() {
			// send event
			sellerBySkuCode := client.Services.Seller.GetSellerMapFromCodes(sellerCodes)
			for _, sku := range input.Skus {
				if item, ok := itemBySkuCode[sku]; ok {
					for k, v := range input.Metadata {
						event.Metadata[k] = v
					}
					event.Metadata["sku"] = item.Sku
					event.Metadata["product_id"] = fmt.Sprint(item.ProductID)
					event.Metadata["seller_code"] = fmt.Sprint(item.SellerCode)
					if seller, ok := sellerBySkuCode[item.SellerCode]; ok {
						event.Metadata["seller_id"] = fmt.Sprint(seller.SellerID)
					}
					event.ResultStatus = res.Status
					event.ResultErrorCode = res.ErrorCode
					client.Services.Collector.SendEventRemoveFromCart(event)
				}
			}
		}()
		if len(cart.Items) == 0 || len(cart.Items)-len(qItemResult) <= 0 {
			return model.BrandCartDB.Delete(&model.Cart{CartID: cart.CartID})
		}
		go func() {
			t := time.Now()
			model.BrandCartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, &model.Cart{LastActionTime: &t})
		}()
	}
	return res
}

func getBrandCart(acc *model.Account, customerID int64, session *model.Session) (*model.Cart, *common.APIResponse) {

	customer, errCustomerRes := getCustomerInfoBrandSales(acc, customerID)
	if errCustomerRes != nil {
		return nil, errCustomerRes
	}
	customerID = customer.CustomerID

	// get cart key
	key, err := generateKeyBrandPortal(acc.Type, customerID, acc.AccountID, session)
	if err != nil {
		return nil, err
	}
	modelCart := &model.Cart{
		Key: key,
	}

	cartRes := model.BrandCartDB.QueryOne(modelCart)
	if cartRes.Status != common.APIStatus.Ok {
		return nil, cartRes
	}

	cart := cartRes.Data.([]*model.Cart)[0]
	cartItemRes := model.BrandCartItemDB.Query(&model.CartItem{CartID: cart.CartID}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		_ = model.BrandCartDB.Delete(modelCart)
		return nil, cartItemRes
	}

	cart.Items = cartItemRes.Data.([]*model.CartItem)
	return cartRes.Data.([]*model.Cart)[0], nil
}

func DeleteBrandCart(acc *model.Account, customerID int64, session *model.Session) *common.APIResponse {

	customer, errCustomerRes := getCustomerInfoBrandSales(acc, customerID)
	if errCustomerRes != nil {
		return errCustomerRes
	}
	customerID = customer.CustomerID

	cart, errGetCart := getBrandCart(acc, customerID, session)
	if errGetCart != nil {
		return errGetCart
	}

	delCartRes := model.BrandCartDB.Delete(&model.Cart{CartID: cart.CartID})
	if delCartRes.Status == common.APIStatus.Ok {
		delCartItem := model.BrandCartItemDB.Delete(&model.CartItem{CartID: cart.CartID})
		if delCartItem.Status != common.APIStatus.Ok {
			return delCartItem
		}
	}
	return delCartRes
}

func UpdateBrandCartInfo(acc *model.Account, input *model.CartUpdateInfo, session *model.Session) *common.APIResponse {
	cart, errCart := getBrandCart(acc, input.CustomerID, session)
	if errCart != nil {
		return errCart
	}
	t := time.Now()
	input.LastActionTime = &t
	return model.BrandCartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, input)
}

/*
RevertBrandCart func process:
- 1 - get customer & validate customer info
- 2 - get order & check order : type != normal, status != wait to confirm, created time > 30p -> can't edit order
- 3 - get cart backup from cart deleted (get latest cart deleted)
- 4 - delete order
- 5 - create cart
- 6 - complete process : move order -> order deleted, push notification, decrease order count for customer, check to return voucher for customer
*/
func RevertBrandCart(acc *model.Account, orderId int64, session *model.Session, customerID int64, isInternal ...bool) *common.APIResponse {
	if orderId <= 0 {
		return OrderIdNotFoundError
	}

	customer, errCustomerRes := getCustomerInfoBrandSales(acc, customerID)
	if errCustomerRes != nil {
		return errCustomerRes
	}
	customerID = customer.CustomerID

	// get cart key
	key, err := generateKeyBrandPortal(acc.Type, customerID, acc.AccountID, session)
	if err != nil {
		return err
	}

	// get order and check validate
	orderRes := model.OrderDB.QueryOne(&model.Order{
		CustomerID: customer.CustomerID,
		OrderID:    orderId,
	})
	if orderRes.Status != common.APIStatus.Ok {
		return OrderNotFoundError
	}
	order := orderRes.Data.([]*model.Order)[0]
	if acc.AccountID != order.CreatedByAccountID {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể sửa đơn hàng",
			ErrorCode: "CAN_NOT_REVERT_CART_ANOTHER_ACCOUNT_ID",
		}
	}
	if notBrandOrClinic(order.Source) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể sửa đơn hàng này",
			ErrorCode: "CAN_NOT_REVERT_CART_ANOTHER_PORTAL",
		}
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "RevertBrandCart")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	orderItemRes := orderItemPartitionDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
	if orderItemRes.Status != common.APIStatus.Ok {
		return OrderItemNotFoundError
	}
	order.Items = orderItemRes.Data.([]*model.OrderItem)
	if len(isInternal) == 0 {
		revertErrRes := checkOrderToRevertCart(order)
		if revertErrRes != nil {
			return revertErrRes
		}
	}

	orderUpdateStatus := model.OrderDB.UpdateOne(&model.Order{
		CustomerID: customer.CustomerID,
		OrderID:    orderId,
		Status:     enum.OrderState.WaitConfirm,
	}, model.Order{Status: enum.OrderState.Reverting})
	if orderUpdateStatus.Status != common.APIStatus.Ok {
		return orderUpdateStatus
	}
	var resp *common.APIResponse
	defer func() {
		if resp != nil && resp.Status != common.APIStatus.Ok {
			model.OrderDB.UpdateOne(&model.Order{
				CustomerID: customer.CustomerID,
				OrderID:    orderId,
				Status:     enum.OrderState.Reverting,
			}, model.Order{Status: enum.OrderState.WaitConfirm})
		}
	}()
	// get cart backup
	cartRes := model.BrandCartDeletedDB.Query(&model.Cart{CartID: order.OrderID}, 0, 1, &primitive.M{"ref_cart_item": -1})
	if cartRes.Status != common.APIStatus.Ok {
		resp = cartRes
		return cartRes
	}

	cart := cartRes.Data.([]*model.Cart)[0]

	cartItemRes := model.BrandCartItemDeletedDB.Query(&model.CartItem{CartID: cart.CartID, RefCart: cart.RefCartItem}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		resp = cartItemRes
		return cartItemRes
	}
	cart.Items = cartItemRes.Data.([]*model.CartItem)

	// get current cart to delete
	var curCart *model.Cart
	curCartRes := model.BrandCartDB.QueryOne(&model.Cart{
		CustomerID: customer.CustomerID,
		Source:     order.Source,
		Key:        key,
	})

	if curCartRes.Status == common.APIStatus.Ok {
		curCart = curCartRes.Data.([]*model.Cart)[0]
		qCartItem := model.BrandCartItemDB.Query(&model.CartItem{CartID: curCart.CartID, RefCart: curCart.RefCartItem}, 0, 0, nil)
		if qCartItem.Status == common.APIStatus.Ok {
			curCart.Items = qCartItem.Data.([]*model.CartItem)
		}

		delCurCartRes := DeleteBrandCart(acc, customerID, session)
		if delCurCartRes.Status != common.APIStatus.Ok {
			resp = delCurCartRes
			return delCurCartRes
		}
	}
	newCart := mixCart(curCart, cart, true, customer)

	newCart.SystemDisplay = order.SystemDisplay
	if newCart.SystemDisplay == "" {
		newCart.SystemDisplay = "BUYMED"
	}

	if utils.IsOrderTagContains(order.Tags, enum.Tag.REFUSE_SPLIT_ORDER) {
		TRUE := true
		newCart.IsRefuseSplitOrder = &TRUE
	}

	// create cart & cart item
	t := time.Now()
	newCart.RefCartItem = t.UnixNano()
	newCart.LastActionTime = &t
	newCart.FlattenLocation = FlatLocationCart(*newCart)
	//cart.ID = primitive.NilObjectID

	newCart.CreatedByAccountID = acc.AccountID
	newCart.BrandCode = session.EntityCode
	newCart.Source = cart.Source
	newCart.Key = key
	newCart.PaymentMethod = cart.PaymentMethod
	newCart.SalesTypeCode = cart.SalesTypeCode
	newCart.SalesType = cart.SalesType

	res := model.BrandCartDB.Create(newCart)

	if res.Status == common.APIStatus.Ok {
		for _, item := range newCart.Items {
			item.RefCart = newCart.RefCartItem
		}

		createCartItem := model.BrandCartItemDB.CreateMany(newCart.Items)
		if createCartItem.Status == common.APIStatus.Ok {
			go func() {
				completeRevertCartProcess(acc, customer, order)
			}()
		} else {
			resp = createCartItem
			return createCartItem
		}
	}
	resp = res
	return res
}

func checkBrandSkuLimit(cart *model.Cart) (errRes *common.APIResponse) {
	// Just apply to Clinic-Portal
	if isBrandPortal(cart.Source) {
		return
	}

	now := time.Now().In(model.VNTimeZone)

	// get sku brand contract
	skuLimitMap := make(map[string]*int)
	skusCodes := []string{}
	for _, item := range cart.Items {
		skusCodes = append(skusCodes, item.Sku)
	}

	skuLimitRes, _ := client.Services.Product.GetListSkuBrandContract(skusCodes, string(*cart.Source), nil)
	for _, limit := range skuLimitRes {
		if !limit.IsActive {
			continue
		}
		skuLimitMap[limit.SKU] = limit.LimitQuantityPerMonth
	}

	_, err := client.Services.Product.GetCustomerPurchaseConfiguration(cart.CustomerID)
	if err == nil {
		customerPurchaseConfigurationDetails, err := client.Services.Product.GetListCustomerPurchaseConfigurationDetail(cart.CustomerID)
		if err == nil {
			for _, item := range customerPurchaseConfigurationDetails {
				/*
					1. Apply just to IsActive = true
					2. Item Source have to mapping correct to source cart
				*/
				if _, exists := skuLimitMap[item.SKU]; exists {
					skuLimitMap[item.SKU] = item.LimitQuantityPerMonth
				}
			}
		}
	}

	// query brand sku limit history
	logMap := make(map[string]*model.BrandSkuLimitHistory)
	qLog := model.BrandSkuLimitHistoryDB.Query(
		model.BrandSkuLimitHistory{
			CustomerID: cart.CustomerID,
			Version:    now.Format("2006-01"),
		},
		0, 0, nil)
	if qLog.Status == common.APIStatus.Ok {
		logs := qLog.Data.([]*model.BrandSkuLimitHistory)
		for _, log := range logs {
			logMap[log.Sku] = log
		}
	}

	// do validate
	errData := make([]*model.CartItem, 0)
	for _, item := range cart.Items {
		if limitQuantityPerMonth, ok := skuLimitMap[item.Sku]; ok && isCartItemSelected(item) {
			if limitQuantityPerMonth == nil || *limitQuantityPerMonth == 0 {
				continue
			}

			item.LimitQuantityPerMonth = *limitQuantityPerMonth
			if log, ok := logMap[item.Sku]; ok && log != nil { // if existed quantity ordered
				if log.Quantity == nil {
					log.Quantity = utils.ParseIntToPointer(0)
				}

				item.QtyOrdered = *log.Quantity
				if *log.Quantity+item.Quantity > *limitQuantityPerMonth {
					errRes = buildMaxQuantityError(&maxQuantityErrorData{
						MaxQty: *limitQuantityPerMonth,
						SKU:    item.Sku,
						Note:   "checkout with cached SkuLimitHistory",
					})
					errData = append(errData, item)
				}
			} else {
				item.QtyOrdered = 0
				if item.Quantity > *limitQuantityPerMonth {
					errRes = buildMaxQuantityError(&maxQuantityErrorData{
						MaxQty: *limitQuantityPerMonth,
						SKU:    item.Sku,
						Note:   "checkout",
					})
					errData = append(errData, item)
				}

				model.BrandSkuLimitHistoryDB.Create(&model.BrandSkuLimitHistory{
					CustomerID:        cart.CustomerID,
					CustomerAccountID: cart.AccountID,
					Quantity:          utils.ParseIntToPointer(0),
					Sku:               item.Sku,
					Version:           now.Format("2006-01"),
				})
			}
		}
	}

	if errRes != nil {
		errRes.Data = errData
	}
	return errRes
}

func logBrandSkuLimit(order *model.Order) {
	// Just apply to Clinic-Portal
	if isBrandPortal(order.Source) {
		return
	}

	skuLimitMap := make(map[string]*model.BrandSkuLimitHistory)
	itemLimits := make([]*model.OrderItem, 0)
	for _, item := range order.Items {
		// Temp remove this logic
		// if item.IsSkuLimitExisted {
		itemLimits = append(itemLimits, item)
		// }
	}
	strNow := time.Now().In(model.VNTimeZone).Format("2006-01")
	queryLog := &model.BrandSkuLimitHistory{
		CustomerID: order.CustomerID,
		Version:    strNow,
	}
	qLogs := model.BrandSkuLimitHistoryDB.Query(queryLog, 0, 0, nil)
	if qLogs.Status == common.APIStatus.Ok {
		logs := qLogs.Data.([]*model.BrandSkuLimitHistory)
		for _, log := range logs {
			skuLimitMap[log.Sku] = log
		}
	}
	for _, item := range itemLimits {
		if item.Type == enum.ItemType.GIFT {
			continue
		}
		if log, ok := skuLimitMap[item.Sku]; log != nil && ok { // existed --> upsert
			model.BrandSkuLimitHistoryDB.IncreOne(&model.BrandSkuLimitHistory{
				Sku:        log.Sku,
				CustomerID: log.CustomerID,
				Version:    strNow,
			}, "quantity", item.Quantity)
		} else {
			newLog := &model.BrandSkuLimitHistory{
				CustomerID:        order.CustomerID,
				CustomerAccountID: order.AccountID,
				Quantity:          utils.ParseIntToPointer(item.Quantity),
				Sku:               item.Sku,
				Version:           strNow,
			}
			model.BrandSkuLimitHistoryDB.Create(newLog)
		}
	}
}

func BrandReOrder(acc *model.Account, orderId int64, customerID int64, session *model.Session) *common.APIResponse {
	if orderId <= 0 {
		return OrderIdNotFoundError
	}

	customer, errCustomerRes := getCustomerInfoBrandSales(acc, customerID)
	if errCustomerRes != nil {
		return errCustomerRes
	}
	customerID = customer.CustomerID

	// get cart key
	key, err := generateKeyBrandPortal(acc.Type, customerID, acc.AccountID, session)
	if err != nil {
		return err
	}

	// get order and check validate
	orderRes := model.OrderDB.QueryOne(&model.Order{
		CustomerID: customer.CustomerID,
		OrderID:    orderId,
	})
	if orderRes.Status != common.APIStatus.Ok {
		return OrderNotFoundError
	}
	order := orderRes.Data.([]*model.Order)[0]

	if customer.Scope == "DENTISTRY" && order.SystemDisplay != "BUYDENTAL" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể đặt lại đơn hàng này",
			ErrorCode: "CAN_NOT_REORDER_BUYDENTAL_ORDER",
		}
	}
	if acc.AccountID != order.CreatedByAccountID {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể đặt lại đơn hàng này",
			ErrorCode: "CAN_NOT_REORDER_ANOTHER_ACCOUNT_ID",
		}
	}
	if notBrandOrClinic(order.Source) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Không thể đặt lại đơn hàng này",
			ErrorCode: "CAN_NOT_REORDER_ANOTHER_PORTAL",
		}
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ReOrder")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	orderItemRes := orderItemPartitionDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
	if orderItemRes.Status != common.APIStatus.Ok {
		return OrderItemNotFoundError
	}
	order.Items = orderItemRes.Data.([]*model.OrderItem)

	// get current cart to delete
	var curCart *model.Cart
	curCartRes := model.BrandCartDB.QueryOne(&model.Cart{
		Key: key,
	})

	if curCartRes.Status == common.APIStatus.Ok {
		curCart = curCartRes.Data.([]*model.Cart)[0]
		qCartItem := model.BrandCartItemDB.Query(&model.CartItem{CartID: curCart.CartID, RefCart: curCart.RefCartItem}, 0, 0, nil)
		if qCartItem.Status == common.APIStatus.Ok {
			curCart.Items = qCartItem.Data.([]*model.CartItem)
		}

		delCurCartRes := DeleteBrandCart(acc, customerID, session)
		if delCurCartRes.Status != common.APIStatus.Ok {
			return delCurCartRes
		}
	}

	// get cart backup
	cartRes := model.BrandCartDeletedDB.Query(&model.Cart{CartID: order.OrderID}, 0, 1, &primitive.M{"ref_cart_item": -1})
	if cartRes.Status != common.APIStatus.Ok {
		return cartRes
	}
	cart := cartRes.Data.([]*model.Cart)[0]
	cartItemRes := model.BrandCartItemDeletedDB.Query(&model.CartItem{CartID: cart.CartID, RefCart: cart.RefCartItem}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		return cartItemRes
	}

	cart.Items = cartItemRes.Data.([]*model.CartItem)
	newCart := mixCart(curCart, cart, false, customer)
	newCart.SystemDisplay = order.SystemDisplay
	if newCart.SystemDisplay == "" {
		newCart.SystemDisplay = "BUYMED"
	}

	// Edge case: support source is empty
	if cart.Source == nil || *cart.Source == "" {
		if acc.Type == enum.AccountType.CUSTOMER {
			cart.Source = &enum.Source.CLINIC_PORTAL
		} else {
			cart.Source = &enum.Source.BRAND_PORTAL
		}
	}

	// create cart & cart item
	t := time.Now()
	newCart.RefCartItem = t.UnixNano()
	newCart.LastActionTime = &t
	emptyArr := make([]*string, 0)
	newCart.RedeemCode = &emptyArr
	newCart.FlattenLocation = FlatLocationCart(*newCart)
	newCart.CreatedByAccountID = acc.AccountID
	newCart.BrandCode = cart.BrandCode
	newCart.AccountID = customer.AccountID
	newCart.CustomerID = customerID
	newCart.Source = cart.Source
	newCart.SalesType = cart.SalesType
	newCart.SalesTypeCode = cart.SalesTypeCode
	newCart.Key = key

	res := model.BrandCartDB.Create(newCart)
	if res.Status == common.APIStatus.Ok {
		for _, item := range newCart.Items {
			item.RefCart = newCart.RefCartItem
		}
		createCartItem := model.BrandCartItemDB.CreateMany(newCart.Items)
		if createCartItem.Status == common.APIStatus.Ok {
		} else {
			return createCartItem
		}
	}
	return res
}

func generateKeyBrandPortal(accountType string, customerID int64, accountID int64, session *model.Session) (string, *common.APIResponse) {
	if accountType == enum.AccountType.CUSTOMER {
		return fmt.Sprintf("%v-%v", enum.Source.CLINIC_PORTAL, customerID), nil
	}
	if accountType == enum.AccountType.BRAND_SALES && session != nil && session.EntityCode != "" {
		return fmt.Sprintf("%v-%v-%v-%v", enum.Source.BRAND_PORTAL, accountID, customerID, session.EntityCode), nil
	}
	return "", &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Thông tin tài khoản không hợp lệ",
		ErrorCode: "CAN_NOT_GENERATE_KEY_BRAND_PORTAL",
	}
}

// RecountQuantityCustomerBoughtInThisMonth is a function that recounts the quantity of items bought by a customer in the current month.
// It takes the customerID as a parameter and returns the new quantity of items bought in this month.
func RecountQuantityCustomerBoughtInThisMonth(customerID int64) *common.APIResponse {
	type countData struct {
		Quantity int    `json:"quantity" bson:"quantity"`
		Sku      string `json:"sku" bson:"_id"`
		// CustomerID int    `json:"customerId" bson:"_id"`
	}

	var (
		t                = time.Now()
		timeStartOfMonth = time.Date(t.Year(), t.Month(), 0, 0, 0, 0, 0, t.Location())
		countDatas       []*countData
		orderCodes       []string
	)
	orderCodesRes := model.OrderDB.Query(&model.Order{
		CustomerID:   customerID,
		ComplexQuery: []*bson.M{{"created_time": bson.M{"$gte": timeStartOfMonth}}},
	}, 0, 1000, nil)

	if orderCodesRes.Status == common.APIStatus.Ok {
		ordersData := orderCodesRes.Data.([]*model.Order)
		for _, order := range ordersData {
			if order.Status == enum.OrderState.Canceled ||
				order.Status == enum.OrderState.Reverting ||
				order.Status == enum.OrderState.Returned {
				continue
			}
			orderCodes = append(orderCodes, order.OrderCode)
		}
	} else {
		return orderCodesRes
	}

	// get list order code of customer
	orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{CreatedTime: &t}, "RecountQuantityCustomerBoughtInThisMonth")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	query := bson.M{
		"type":         enum.ItemType.NORMAL,
		"customer_id":  customerID,
		"created_time": bson.M{"$gte": timeStartOfMonth},
		"source":       "clinic-portal",
		"order_code":   bson.M{"$in": orderCodes},
	}

	respDB := orderItemPartitionDB.Aggregate([]primitive.M{
		{
			"$match": query,
		},
		{
			"$group": primitive.M{
				"_id": "$sku",
				"quantity": primitive.M{
					"$sum": "$quantity",
				},
			},
		},
	}, &countDatas)

	if respDB.Status == common.APIStatus.Ok && len(countDatas) > 0 {
		mapCountData := make(map[string]*countData)
		for _, quantitySku := range countDatas {
			mapCountData[quantitySku.Sku] = quantitySku
			brandSkuOld := model.BrandSkuLimitHistoryDB.QueryOne(&model.BrandSkuLimitHistory{
				CustomerID: customerID,
				Sku:        quantitySku.Sku,
				Version:    t.Format("2006-01"),
			})

			if brandSkuOld.Status == common.APIStatus.Ok {
				oldQuantity := 0
				if brandSkuOld.Data.([]*model.BrandSkuLimitHistory)[0].Quantity != nil {
					oldQuantity = *brandSkuOld.Data.([]*model.BrandSkuLimitHistory)[0].Quantity
				}

				model.BrandSkuLimitHistoryDB.UpdateOne(&model.BrandSkuLimitHistory{
					CustomerID: customerID,
					Sku:        quantitySku.Sku,
					Version:    t.Format("2006-01"),
				}, &model.BrandSkuLimitHistory{
					Quantity:    utils.ParseIntToPointer(quantitySku.Quantity),
					OldQuantity: &oldQuantity,
				})
			} else {

				cus := &model.BrandSkuLimitHistory{
					CustomerID:  customerID,
					Sku:         quantitySku.Sku,
					Version:     t.Format("2006-01"),
					Quantity:    utils.ParseIntToPointer(quantitySku.Quantity),
					OldQuantity: utils.ParseIntToPointer(quantitySku.Quantity),
				}
				customer, _ := getCustomerProfileByCustomerID(customerID)
				if customer != nil {
					cus.CustomerAccountID = customer.AccountID
				}
				model.BrandSkuLimitHistoryDB.Create(cus)
			}
		}

		brandSkuResp := model.BrandSkuLimitHistoryDB.Query(&model.BrandSkuLimitHistory{
			CustomerID: customerID,
			Version:    t.Format("2006-01"),
		}, 0, 0, nil)

		if brandSkuResp.Status == common.APIStatus.Ok {
			brandSkus := brandSkuResp.Data.([]*model.BrandSkuLimitHistory)
			for _, brandSku := range brandSkus {
				if brandSku.OldQuantity == nil {
					model.BrandSkuLimitHistoryDB.Delete(&model.BrandSkuLimitHistory{ID: brandSku.ID})
				}
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cập nhật data thành công",
	}
}

// BrandRemoveVoucherCode remove voucher code
func BrandRemoveVoucherCode(acc *model.Account, input *model.RemoveBrandVoucherRequest, session *model.Session) *common.APIResponse {
	if input.RedeemCodes == nil || len(*input.RedeemCodes) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Redeem code invalid",
			ErrorCode: "REDEEM_CODE_INVALID",
		}
	}

	// get customer info
	customer, errCustomerRes := getCustomerInfoBrandSales(acc, input.CustomerID)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cart, errCart := getBrandCart(acc, input.CustomerID, session)
	if errCart != nil {
		return errCart
	}

	cart.SourceDetail = acc.SourceDetail

	if len(cart.ProvinceCode) == 0 {
		cart.ProvinceCode = customer.ProvinceCode
	}
	if len(cart.DistrictCode) == 0 {
		cart.DistrictCode = customer.DistrictCode
	}
	if len(cart.WardCode) == 0 {
		cart.WardCode = customer.WardCode
	}
	if len(cart.CustomerScope) == 0 {
		cart.CustomerScope = customer.Scope
	}
	resetCartPrice(cart)
	_ = handleProcessContentItems(cart, customer, nil, true)
	_ = handleProcessPriceItems(customer, cart)
	_ = handleApplyVoucherCode(cart, customer, "RemoveVoucherCode")
	t := time.Now()
	input.LastActionTime = &t
	newRedeemCodes := make([]*string, 0)
	newResults := make([]*model.PromoApplyResult, 0)
	if cart.RedeemCode != nil {
		for _, code := range *cart.RedeemCode {
			isExist := false
			for _, codeInput := range *input.RedeemCodes {
				if code != nil && codeInput != nil && *code == *codeInput {
					isExist = true
					break
				}
			}
			if !isExist {
				newRedeemCodes = append(newRedeemCodes, code)
			}
		}
	}
	if cart.RedeemApplyResult != nil {
		for _, result := range cart.RedeemApplyResult {
			isExist := false
			for _, codeInput := range *input.RedeemCodes {
				if codeInput != nil && result.Code == *codeInput {
					isExist = true
					break
				}
			}
			if !isExist {
				newResults = append(newResults, result)
			}
		}
	}
	input.RedeemCodes = &newRedeemCodes
	input.RedeemApplyResult = &newResults
	return model.BrandCartDB.UpdateOne(model.Cart{CartID: cart.CartID}, input)
}

// BrandAddVoucherCode remove voucher code
func BrandAddVoucherCode(acc *model.Account, input *model.RemoveBrandVoucherRequest, session *model.Session) *common.APIResponse {
	if input.RedeemCodes == nil || len(*input.RedeemCodes) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Redeem code invalid",
			ErrorCode: "REDEEM_CODE_INVALID",
		}
	}

	// get customer info
	customer, errCustomerRes := getCustomerInfoBrandSales(acc, input.CustomerID)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cart, errCart := getBrandCart(acc, input.CustomerID, session)
	if errCart != nil {
		return errCart
	}

	cart.SourceDetail = acc.SourceDetail

	if len(cart.ProvinceCode) == 0 {
		cart.ProvinceCode = customer.ProvinceCode
	}
	if len(cart.DistrictCode) == 0 {
		cart.DistrictCode = customer.DistrictCode
	}
	if len(cart.WardCode) == 0 {
		cart.WardCode = customer.WardCode
	}
	if len(cart.CustomerScope) == 0 {
		cart.CustomerScope = customer.Scope
	}
	resetCartPrice(cart)
	cart.GetVoucherAutoApply = false
	_ = handleProcessContentItems(cart, customer, nil, true)
	_ = handleProcessPriceItems(customer, cart)
	_ = handleApplyVoucherCode(cart, customer, "AddVoucherCode")
	t := time.Now()
	input.LastActionTime = &t
	cart.SubPrice = cart.Price
	//redeemApplyResult := make([]*model.PromoApplyResult, 0)
	//cart.RedeemApplyResult = redeemApplyResult
	tempCartItems := make([]*model.CartItem, 0)
	for _, item := range cart.Items {
		if item.IsSelected != nil && *item.IsSelected {
			tempCartItems = append(tempCartItems, item)
		}
	}
	currentRedeems := cart.RedeemCode
	currentRedeemApplyResult := cart.RedeemApplyResult
	tempCart := *cart
	tempCart.TotalPrice = cart.TotalPrice
	tempCart.TotalQuantity = cart.TotalQuantitySelected
	tempCart.TotalItem = cart.TotalItemSelected
	tempCart.Items = tempCartItems
	tempCart.RedeemCode = input.RedeemCodes
	promoApply, errCheck := client.Services.Promotion.CheckVoucherCode(&client.CheckVoucherRequest{
		Customer:            customer,
		Cart:                &tempCart,
		VoucherCode:         *tempCart.RedeemCode,
		AccountID:           cart.AccountID,
		GetVoucherAutoApply: cart.GetVoucherAutoApply,
		SystemDisplay:       cart.SystemDisplay,

		Source:       "AddVoucherCode",
		SourceDetail: cart.SourceDetail,
	})
	if errCheck != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errCheck.Error(),
			ErrorCode: "VOUCHER_CODE_INVALID",
		}
	}
	for _, redeem := range promoApply {
		if !redeem.CanUse {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   redeem.ErrorMessage,
				ErrorCode: "VOUCHER_CODE_INVALID",
			}
		}
		currentRedeemApplyResult = append(currentRedeemApplyResult, &model.PromoApplyResult{
			Code:          redeem.VoucherCode,
			CanUse:        redeem.CanUse,
			Gift:          redeem.Gifts,
			AutoApply:     redeem.AutoApply,
			MatchSeller:   redeem.MatchSeller,
			MatchProducts: redeem.MatchProducts,
			DiscountValue: redeem.DiscountValue,
			SellerCode:    redeem.SellerCode,
			SellerCodes:   redeem.SellerCodes,
		})
		input.RedeemApplyResult = &currentRedeemApplyResult
	}
	if currentRedeems != nil {
		for _, code := range *currentRedeems {
			if code != nil {
				*input.RedeemCodes = append(*input.RedeemCodes, code)
			}
		}
	}
	//if currentResult != nil {
	//	for _, result := range currentResult {
	//		if result != nil && result.Code {
	//			promoApply = append(promoApply, result)
	//		}
	//	}
	//}
	return model.BrandCartDB.UpdateOne(model.Cart{CartID: cart.CartID}, input)
}

func BrandUpdateCartApplyVoucher(acc *model.Account, input *model.BrandCartUpdateVoucher, session *model.Session) *common.APIResponse {

	// get customer info
	customer, errCustomerRes := getCustomerInfoBrandSales(acc, input.CustomerID)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cart, errCart := getBrandCart(acc, input.CustomerID, session)
	if errCart != nil {
		return errCart
	}

	cart.RedeemCode = input.RedeemCode
	cart.AccountID = customer.AccountID
	cart.CustomerCode = customer.CustomerCode
	cart.SourceDetail = acc.SourceDetail

	if len(cart.ProvinceCode) == 0 {
		cart.ProvinceCode = customer.ProvinceCode
	}
	if len(cart.DistrictCode) == 0 {
		cart.DistrictCode = customer.DistrictCode
	}
	if len(cart.WardCode) == 0 {
		cart.WardCode = customer.WardCode
	}
	if len(cart.CustomerScope) == 0 {
		cart.CustomerScope = customer.Scope
	}
	resetCartPrice(cart)
	_ = handleProcessContentItems(cart, customer, nil, true)
	_ = handleProcessPriceItems(customer, cart)
	//_ = handleProcessDeliveryAndPaymentMethod(cart)
	errHandleVoucherCode := handleApplyVoucherCode(cart, customer, "UpdateCartApplyVoucher")
	if errHandleVoucherCode != nil {
		return errHandleVoucherCode
	}
	t := time.Now()
	cart.LastActionTime = &t
	if cart.RedeemApplyResult == nil || len(cart.RedeemApplyResult) == 0 {
		empty := make([]*model.PromoApplyResult, 0)
		model.BrandCartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, model.RemoveVoucherRequest{
			RedeemApplyResult: &empty,
		})
	}
	return model.BrandCartDB.UpdateOne(&model.Cart{CartID: cart.CartID}, cart)
}

// BrandCartGetActiveVoucher ...
func BrandCartGetActiveVoucher(acc *model.Account, offset, limit int64, getTotal, getValidate bool, search, scope string,
	systemDisplay string, sourceDetail *model.OrderSourceDetail) *common.APIResponse {
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cartRes := model.BrandCartDB.QueryOne(&model.Cart{
		CustomerID: customer.CustomerID,
	})
	if cartRes.Status != common.APIStatus.Ok {
		// case cart not found
		cart := &model.Cart{
			AccountID:     customer.AccountID,
			CustomerCode:  customer.CustomerCode,
			CustomerID:    customer.CustomerID,
			ProvinceCode:  customer.ProvinceCode,
			DistrictCode:  customer.DistrictCode,
			WardCode:      customer.WardCode,
			CustomerScope: customer.Scope,
			Items:         make([]*model.CartItem, 0),
		}
		regions, er := client.Services.Location.GetRegionList(0, 100, []string{cart.ProvinceCode})
		if er == nil {
			for _, region := range regions {
				cart.RegionCodes = append(cart.RegionCodes, region.Code)
				if region.Scope == "SALE_REGION" {
					cart.SaleRegionCodes = append(cart.SaleRegionCodes, region.Code)
				}
			}
		}

		promoApply, err := client.Services.Promotion.GetListVoucherActive(&client.CheckVoucherRequest{
			Cart:        cart,
			AccountID:   cart.AccountID,
			Offset:      offset,
			Limit:       limit,
			GetTotal:    getTotal,
			GetValidate: getValidate,
			Search:      search,
			Scope:       scope,

			SystemDisplay: systemDisplay,
			SourceDetail:  sourceDetail,
		})

		msg := ""
		if err != nil {
			msg = err.Error()
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: msg,
			}
		} else {
			msg = promoApply.Message
		}
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: msg,
			Data:    promoApply.Data,
			Total:   promoApply.Total,
		}
	}
	cart := cartRes.Data.([]*model.Cart)[0]
	cartItemRes := model.CartItemDB.Query(&model.CartItem{CartID: cart.CartID}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		_ = model.BrandCartDB.Delete(&model.Cart{CartID: cart.CartID})
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Not found any matched cart",
		}
	}
	cart.Items = cartItemRes.Data.([]*model.CartItem)
	cart.ProvinceCode = customer.ProvinceCode
	cart.DistrictCode = customer.DistrictCode
	cart.WardCode = customer.WardCode
	cart.CustomerScope = customer.Scope

	resetCartPrice(cart)

	_ = handleProcessContentItems(cart, customer, nil, false)
	_ = handleProcessPriceItems(customer, cart)
	_, _ = handleProcessDeliveryAndPaymentMethod(cart, customer)

	tempCartItems := make([]*model.CartItem, 0)
	for _, item := range cart.Items {
		if item.IsSelected != nil && *item.IsSelected {
			tempCartItems = append(tempCartItems, item)
		}
	}
	tempCart := *cart
	tempCart.TotalPrice = cart.TotalPrice
	tempCart.TotalQuantity = cart.TotalQuantitySelected
	tempCart.TotalItem = cart.TotalItemSelected
	tempCart.Items = tempCartItems

	promoApply, err := client.Services.Promotion.GetListVoucherActive(&client.CheckVoucherRequest{
		Cart:        tempCart,
		AccountID:   cart.AccountID,
		Offset:      offset,
		Limit:       limit,
		GetTotal:    getTotal,
		GetValidate: getValidate,
		Search:      search,
		Scope:       scope,

		SystemDisplay: systemDisplay,
		SourceDetail:  sourceDetail,
	})

	msg := ""
	if err != nil {
		msg = err.Error()
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: msg,
		}
	} else {
		msg = promoApply.Message
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: msg,
		Data:    promoApply.Data,
		Total:   promoApply.Total,
	}
}

func handleProcessSelectItemByBrand(cart *model.Cart, salesTypeCode string) *common.APIResponse {

	// get sales type
	salesType, err := client.Services.BrandSales.GetSalesType(salesTypeCode)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}
	if salesType.SystemDisplay != "CLINIC_PORTAL" {
		return nil
	}
	manufactureCodes := salesType.ManufactureCodes
	if len(manufactureCodes) == 0 {
		return nil
	}
	var selectedSkus []string
	var unSelectedSkus []string
	for _, item := range cart.Items {
		if item.ManufacturerCode != "" && utils.IsContains(manufactureCodes, item.ManufacturerCode) {
			item.IsSelected = utils.ParseBoolToPointer(true)
			selectedSkus = append(selectedSkus, item.Sku)
		} else {
			item.IsSelected = utils.ParseBoolToPointer(false)
			unSelectedSkus = append(unSelectedSkus, item.Sku)
		}

	}
	if len(selectedSkus) > 0 {
		model.BrandCartItemDB.UpdateMany(&model.CartItem{CartID: cart.CartID, ComplexQuery: []*primitive.M{
			{"sku": primitive.M{
				"$in": selectedSkus,
			}},
		}}, &model.CartItem{IsSelected: utils.ParseBoolToPointer(true)})
	}
	if len(unSelectedSkus) > 0 {
		model.BrandCartItemDB.UpdateMany(&model.CartItem{CartID: cart.CartID, ComplexQuery: []*primitive.M{
			{"sku": primitive.M{
				"$in": unSelectedSkus,
			}},
		}}, &model.CartItem{IsSelected: utils.ParseBoolToPointer(false)})
	}

	return nil
}

func validateCartItem(customer *model.Customer, cart *model.Cart) (errRes *common.APIResponse) {
	// validate item
	if cart.SkuMap == nil {
		return errRes
	}

	dealLimitMap := make(map[string]int)
	if len(cart.DealLimitPerCustomerCodes) > 0 {
		qDealLimit := model.DealLimitDB.Query(bson.M{"key": bson.M{"$in": cart.DealLimitPerCustomerCodes}, "customer_id": cart.CustomerID}, 0, 0, &primitive.M{"_id": -1})
		if qDealLimit.Status == common.APIStatus.Ok {
			for _, limit := range qDealLimit.Data.([]*model.DealLimit) {
				dealLimitMap[limit.Key] = limit.Quantity
			}
		}
		if cart.DealLimitMap == nil {
			cart.DealLimitMap = make(map[string]int)
		}
		cart.DealLimitMap = dealLimitMap
	}

	skuMap := cart.SkuMap.(map[string]*client.ProductData)
	for _, item := range cart.Items {
		sku := skuMap[item.Sku]
		if item.CurPrice == 0 && !item.IsFirstAdd {
			item.CurPrice = item.Price
			item.OldLevel = customer.Level
			item.OldType = item.Type
			go model.CartItemDB.UpdateOne(model.CartItem{CartID: cart.CartID, Sku: item.Sku}, model.CartItem{CurPrice: item.Price, OldLevel: customer.Level, OldType: item.Type})
		}
		if !isCartItemSelected(item) || sku == nil {
			continue
		}

		limitPerOrder := sku.LimitPerOrder
		// if isBrandOrClinic(cart.Source) {
		// 	limitPerOrder = 1000000 // no limit
		// }

		switch item.Type {
		case enum.ItemType.NORMAL:
			item.MaxQuantity = limitPerOrder

		case enum.ItemType.DEAL:
			item.MaxQuantity = sku.Deal.MaxQuantity // reset max quantity with item type
			if int64(limitPerOrder) < int64(item.Quantity) {
				errRes = buildMaxQuantityError(&maxQuantityErrorData{
					MaxQty: limitPerOrder,
					SKU:    item.Sku,
					Note:   "for type=DEAL limitPerOrder",
				})
				makeCartItemError(errRes, item)

			} else if sku.Deal.MaxQuantity-sku.Deal.CurrentQuantity < item.Quantity {
				errRes = buildMaxQuantityError(&maxQuantityErrorData{
					MaxQty: sku.Deal.MaxQuantity - sku.Deal.CurrentQuantity,
					SKU:    item.Sku,
					Note:   "for type=DEAL MaxQuantity-CurrentQuantity",
				})
				makeCartItemError(errRes, item)
			}
		case enum.ItemType.CAMPAIGN:
			item.MaxQuantity = int(sku.Campaign.Quantity)
		}

		if item.MaxQuantity < item.Quantity && item.Type != enum.ItemType.GIFT {
			errRes = buildMaxQuantityError(&maxQuantityErrorData{
				MaxQty: item.MaxQuantity,
				SKU:    item.Sku,
				Note:   "for non-GIFT MaxQuantity",
			})
			makeCartItemError(errRes, item)
		}

		dealCode := item.CampaignProductCode
		if item.DealCode != nil && *item.DealCode != "" {
			dealCode = *item.DealCode
		}
		limitPerCustomer := item.DealMaxQuantityPerCustomer - dealLimitMap[dealCode]

		limitSku := limitPerOrder
		if item.SkuStatus != nil && *item.SkuStatus == enum.SkuStatus.LIMIT &&
			item.SkuStatusData != nil && item.SkuStatusData.Quantity != nil {
			limitSku = *item.SkuStatusData.Quantity
		}
		minLimit := limitSku
		if limitPerOrder < minLimit {
			minLimit = limitPerOrder
		}
		if item.DealMaxQuantityPerCustomer > 0 && limitPerCustomer < minLimit {
			minLimit = limitPerCustomer
		}
		if minLimit < item.Quantity && item.Type != enum.ItemType.GIFT {
			errRes = buildMaxQuantityError(&maxQuantityErrorData{
				MaxQty: minLimit,
				SKU:    item.Sku,
				Note:   "for non-GIFT minLimit",
			})
			makeCartItemError(errRes, item)
		}

		if !item.IsAvailable {
			errRes = NotAvailableSkuError
			makeCartItemError(errRes, item)
		}
	}

	return errRes
}
