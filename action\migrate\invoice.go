package migrate

import (
	"context"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateInvoiceDeliveredTime() *common.APIResponse {
	fmt.Println("MigrateInvoiceDeliveredTime START")
	defer fmt.Println("MigrateInvoiceDeliveredTime FINISH")

	var limit int64 = 1000
	var updatedCount int64

	cursor := primitive.NewObjectIDFromTimestamp(time.Date(2000, 01, 01, 0, 0, 0, 0, utils.VNTimeZone))

	for {
		invoicesRes := model.InvoiceDB.Query(bson.M{
			"_id": bson.M{
				"$gt": cursor,
			},
		}, 0, limit, &primitive.M{"_id": 1})

		if invoicesRes.Status != common.APIStatus.Ok {
			break
		}

		invoices := invoicesRes.Data.([]*model.Invoice)
		cursor = *invoices[len(invoices)-1].ID

		for _, invoice := range invoices {
			if invoice.DeliveredTime != nil || invoice.InvoiceDeadline == nil {
				continue
			}

			deliveredTime := invoice.InvoiceDeadline.AddDate(0, 0, -10)

			model.InvoiceDB.UpdateOne(&model.Invoice{
				ID: invoice.ID,
			}, &model.Invoice{
				DeliveredTime: &deliveredTime,
			})

			time.Sleep(time.Millisecond * 10)
		}

		updatedCount += limit
		if updatedCount%10000 == 0 {
			fmt.Println("MigrateOrders", invoices[len(invoices)-1].CreatedTime, invoices[len(invoices)-1].ID)
			fmt.Println("MigrateOrders updated", updatedCount)
		}

		if len(invoices) < int(limit) {
			break
		}
		time.Sleep(time.Millisecond * 100)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate Invoice Delivered Time finish",
	}
}

func MigrateCreateInvoiceDO(status enum.OrderStateValue) *common.APIResponse {
	fmt.Println("MigrateCreateInvoiceDO START")
	defer fmt.Println("MigrateCreateInvoiceDO FINISH")

	var limit int64 = 1000
	var updatedCount int64

	cursor := primitive.NewObjectIDFromTimestamp(time.Date(2000, 01, 01, 0, 0, 0, 0, utils.VNTimeZone))

	for {
		ordersRes := model.OrderDB.Query(&model.Order{
			ComplexQuery: []*bson.M{
				{
					"_id": bson.M{
						"$gt": cursor,
					},
				},
				{
					"status": bson.M{
						"$eq": status,
					},
				},
			},
		}, 0, limit, &primitive.M{"_id": 1})
		if ordersRes.Status != common.APIStatus.Ok {
			break
		}

		orders := ordersRes.Data.([]*model.Order)
		cursor = *orders[len(orders)-1].ID

		for _, order := range orders {
			orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "UpdateSyncAuditOrderLine")
			if orderItemPartitionDB == nil {
				continue
			}

			filter := bson.M{
				"seller_class": enum.SellerClass.EXTERNAL,
				"order_id":     order.OrderID,
			}
			updater := bson.M{
				"$set": bson.M{
					"receive_invoice_info_by": enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO,
				},
			}

			orderItemPartitionDB.
				GetClient().
				Database(orderItemPartitionDB.DBName).
				Collection(orderItemPartitionDB.ColName).
				UpdateMany(context.TODO(), filter, updater)
		}

		updatedCount += limit
		if updatedCount%10000 == 0 {
			fmt.Println("MigrateCreateInvoiceDO", orders[len(orders)-1].ID)
			fmt.Println("MigrateCreateInvoiceDO updated", updatedCount)
		}

		if len(orders) < int(limit) {
			break
		}
		time.Sleep(time.Millisecond * 100)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate Create Invoice DO finish",
	}
}
