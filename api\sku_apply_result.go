package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

// SkuApplyResultUpdate ...
func SkuApplyResultUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.SkuApplyResult
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateSkuApplyResult(input.ItemCode, &input))
}

// SkuApplyResultCreate ...
func SkuApplyResultCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.SkuApplyResult
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateSkuApplyResult(&input))
}
