package action

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetListAuditOrderBill(query interface{}, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.OrderBillAuditDB.Query(query, offset, limit, &primitive.M{"ordered_at": 1})
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "NOT_FOUND"
		return result
	}
	if getTotal {
		result.Total = model.OrderBillAuditDB.Count(query).Total
	}
	return result
}

func GetListAuditOrder(query interface{}, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.OrderAuditDB.Query(query, offset, limit, &primitive.M{"created_time": 1})
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "NOT_FOUND"
		return result
	}
	if getTotal {
		result.Total = model.OrderAuditDB.Count(query).Total
	}
	return result
}

func GetListAuditOrderLine(query interface{}, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.OrderLineAuditDB.Query(query, offset, limit, &primitive.M{"created_time": 1})
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "NOT_FOUND"
		return result
	}
	if getTotal {
		result.Total = model.OrderLineAuditDB.Count(query).Total
	}
	return result
}

func UpdateSyncAllAudit(query *model.OrderAudit) *common.APIResponse {
	fmt.Println("UpdateSyncAllAudit")
	result := updateSyncAllAudit(query)
	return result
}
func updateSyncAllAudit(query *model.OrderAudit) *common.APIResponse {
	offset := int64(0)
	limit := int64(500)

	for true {
		q := model.OrderAuditDB.Query(query, offset, limit, &primitive.M{"_id": 1})
		if q.Status != common.APIStatus.Ok {
			debugStr, _ := json.Marshal(query)
			fmt.Printf("\nBreak %s, offset=%d, limit=%d, %v\n", q.Message, offset, limit, string(debugStr))
			return q
		}
		data := q.Data.([]*model.OrderAudit)
		for _, au := range data {
			UpdateSyncAuditOrderLine(au.OrderID, false)
		}
		offset += limit
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Done",
	}
}

func UpdateSyncAuditOrderLine(orderID int64, checkAudit bool) *common.APIResponse {
	q := model.OrderDB.QueryOne(&model.Order{
		OrderID: orderID,
	})
	if q.Status != common.APIStatus.Ok {
		return model.OrderAuditDB.Delete(&model.OrderAudit{
			OrderID: orderID,
			Status:  "PENDING",
		})
	}
	order := q.Data.([]*model.Order)[0]

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "UpdateSyncAuditOrderLine")
	if orderItemPartitionDB == nil {
		return model.PARTITION_NOT_FOUND_RESPONSE
	}

	if checkAudit {
		queryOrderAudit := model.OrderAuditDB.Query(&model.OrderAudit{
			OrderID: orderID,
			Status:  "PENDING",
		}, 0, 0, nil)
		if queryOrderAudit.Status != common.APIStatus.Ok {
			return queryOrderAudit
		}
	}

	orderItemResult := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID: orderID,
	}, 0, 0, nil)

	if orderItemResult.Status != common.APIStatus.Ok {
		return orderItemResult
	}

	orderItems := orderItemResult.Data.([]*model.OrderItem)
	orderWarehouse, err := client.Services.Warehouse.GetSaleOrder(orderID, order.SaleOrderCode)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "WMS_ORDER_ERROR",
		}
	}

	lst := make(map[string]*model.OrderLineWarehouse)
	lst2 := make(map[int64]*model.OrderLineWarehouse)

	for _, item := range orderWarehouse.OrderLines {
		lst[item.Sku] = item
		lst2[item.AdminProductID] = item
	}

	msg := ""
	for _, item := range orderItems {
		if orderLine, ok := lst[item.Sku]; ok {
			flag := false
			updater := &model.OrderItem{}
			if (item.ReservedQuantity == nil && orderLine.ReservedQuantity > 0) || (item.ReservedQuantity != nil && orderLine.ReservedQuantity != *item.ReservedQuantity) {
				updater.ReservedQuantity = toInt(orderLine.ReservedQuantity)
				if item.ReservedQuantity == nil {
					msg += fmt.Sprintf("%s ReservedQuantity nil -> %d;", item.Sku, orderLine.ReservedQuantity)
				} else {
					msg += fmt.Sprintf("%s ReservedQuantity %d -> %d;", item.Sku, *item.ReservedQuantity, orderLine.ReservedQuantity)
				}

				flag = true
			}

			if (item.OutboundQuantity == nil && orderLine.OutboundQuantity > 0) || (item.OutboundQuantity != nil && orderLine.OutboundQuantity != *item.OutboundQuantity) {
				updater.OutboundQuantity = toInt(orderLine.OutboundQuantity)
				if item.OutboundQuantity == nil {
					msg += fmt.Sprintf("%s OutboundQuantity nil -> %d;", item.Sku, orderLine.OutboundQuantity)
				} else {
					msg += fmt.Sprintf("%s OutboundQuantity %d -> %d;", item.Sku, *item.OutboundQuantity, orderLine.OutboundQuantity)
				}
				flag = true
			}

			if (item.DeliveredQuantity == nil && orderLine.DeliveredQuantity > 0) || (item.DeliveredQuantity != nil && orderLine.DeliveredQuantity != *item.DeliveredQuantity) {
				updater.DeliveredQuantity = toInt(orderLine.DeliveredQuantity)
				if item.DeliveredQuantity == nil {
					msg += fmt.Sprintf("%s DeliveredQuantity nil -> %d;", item.Sku, orderLine.DeliveredQuantity)
				} else {
					msg += fmt.Sprintf("%s DeliveredQuantity %d -> %d;", item.Sku, *item.DeliveredQuantity, orderLine.DeliveredQuantity)
				}
				flag = true
			}

			if (item.ReturnedQuantity == nil && orderLine.ReturnedQuantity > 0) || (item.ReturnedQuantity != nil && orderLine.ReturnedQuantity != *item.ReturnedQuantity) {
				updater.ReturnedQuantity = toInt(orderLine.ReturnedQuantity)
				if item.ReturnedQuantity == nil {
					msg += fmt.Sprintf("%s ReturnedQuantity nil -> %d;", item.Sku, orderLine.ReturnedQuantity)
				} else {
					msg += fmt.Sprintf("%s ReturnedQuantity %d -> %d;", item.Sku, *item.ReturnedQuantity, orderLine.ReturnedQuantity)
				}
				flag = true
			}

			if flag {
				_ = orderItemPartitionDB.UpdateOne(&model.OrderItem{
					Sku:     item.Sku,
					OrderID: orderID,
				}, updater)
			}
		} else if orderLine, ok := lst2[item.ProductID]; ok {
			flag := false
			updater := &model.OrderItem{}
			if (item.ReservedQuantity == nil && orderLine.ReservedQuantity > 0) || (item.ReservedQuantity != nil && orderLine.ReservedQuantity != *item.ReservedQuantity) {
				updater.ReservedQuantity = toInt(orderLine.ReservedQuantity)
				if item.ReservedQuantity == nil {
					msg += fmt.Sprintf("%s ReservedQuantity nil -> %d;", item.Sku, orderLine.ReservedQuantity)
				} else {
					msg += fmt.Sprintf("%s ReservedQuantity %d -> %d;", item.Sku, *item.ReservedQuantity, orderLine.ReservedQuantity)
				}
				flag = true
			}

			if (item.OutboundQuantity == nil && orderLine.OutboundQuantity > 0) || (item.OutboundQuantity != nil && orderLine.OutboundQuantity != *item.OutboundQuantity) {
				updater.OutboundQuantity = toInt(orderLine.OutboundQuantity)
				if item.OutboundQuantity == nil {
					msg += fmt.Sprintf("%s OutboundQuantity nil -> %d;", item.Sku, orderLine.OutboundQuantity)
				} else {
					msg += fmt.Sprintf("%s OutboundQuantity %d -> %d;", item.Sku, *item.OutboundQuantity, orderLine.OutboundQuantity)
				}
				flag = true
			}

			if (item.DeliveredQuantity == nil && orderLine.DeliveredQuantity > 0) || (item.DeliveredQuantity != nil && orderLine.DeliveredQuantity != *item.DeliveredQuantity) {
				updater.DeliveredQuantity = toInt(orderLine.DeliveredQuantity)
				if item.DeliveredQuantity == nil {
					msg += fmt.Sprintf("%s DeliveredQuantity nil -> %d;", item.Sku, orderLine.DeliveredQuantity)
				} else {
					msg += fmt.Sprintf("%s DeliveredQuantity %d -> %d;", item.Sku, *item.DeliveredQuantity, orderLine.DeliveredQuantity)
				}
				flag = true
			}

			if (item.ReturnedQuantity == nil && orderLine.ReturnedQuantity > 0) || (item.ReturnedQuantity != nil && orderLine.ReturnedQuantity != *item.ReturnedQuantity) {
				updater.ReturnedQuantity = toInt(orderLine.ReturnedQuantity)
				if item.ReturnedQuantity == nil {
					msg += fmt.Sprintf("%s ReturnedQuantity nil -> %d;", item.Sku, orderLine.ReturnedQuantity)
				} else {
					msg += fmt.Sprintf("%s ReturnedQuantity %d -> %d;", item.Sku, *item.ReturnedQuantity, orderLine.ReturnedQuantity)
				}
				flag = true
			}

			if flag {
				orderItemPartitionDB.UpdateOne(&model.OrderItem{
					ProductID: item.ProductID,
					OrderID:   orderID,
				}, updater)
			}
		} else {

		}
	}

	_ = model.OrderAuditDB.Delete(&model.OrderAudit{
		OrderID: orderID,
		Status:  "PENDING",
	})

	// if removeOrderAudit.Status != common.APIStatus.Ok {
	// 	return removeOrderAudit
	// }

	_ = model.OrderLineAuditDB.Delete(&model.OrderLineAudit{
		OrderID: orderID,
	})

	// if removeOrderLineAudit.Status != common.APIStatus.Ok {
	// 	return removeOrderLineAudit
	// }

	queryAudit := &model.Order{
		OrderID: orderID,
	}

	auditOrderWarehouse(queryAudit, msg)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: msg,
	}
}

func AuditOrderPickWarehouse() {
	if conf.Config.Env == "uat" {
		return
	}

	t1 := time.Now().Add(-3 * 24 * time.Hour)
	t2 := time.Now()
	query := &model.Order{
		ComplexQuery: []*bson.M{
			&bson.M{
				"created_time": bson.M{
					"$gte": t1,
				},
			},
			&bson.M{
				"created_time": bson.M{
					"$lte": t2,
				},
			},
			&bson.M{
				"status": bson.M{
					"$in": []string{"CONFIRMED", "PROCESSING", "WAIT_TO_DELIVER"},
				},
			},
		},
	}
	auditOrderPickWarehouse(query, "")
}

func TrigerAuditOrderBill() {
	t1 := time.Now().Add(-210 * 24 * time.Hour)

	query := &model.Order{
		ComplexQuery: []*bson.M{
			&bson.M{
				"status": bson.M{
					"$in": []string{"DELIVERING", "DELIVERED", "RETURNED", "COMPLETED", "CANCEL"},
				},
			},
			&bson.M{
				"created_time": bson.M{
					"$gte": t1,
				},
			},
		},
	}
	auditOrderBill(query, "", false)
}

func AuditOrderBill() {
	if conf.Config.Env == "uat" {
		return
	}
	t1 := time.Now().Add(-1 * 24 * time.Hour)
	// t2 := time.Now().Add(-1 * 12 * time.Hour)

	query := &model.Order{
		ComplexQuery: []*bson.M{
			&bson.M{
				"status": bson.M{
					"$in": []string{"DELIVERING", "DELIVERED", "RETURNED", "COMPLETED", "CANCEL"},
				},
			},
			&bson.M{
				"last_updated_time": bson.M{
					"$gte": t1,
				},
			},
			// &bson.M{
			// 	"created_time": bson.M{
			// 		"$lt": t2,
			// 	},
			// },
			// &bson.M{
			// 	"order_id": 510044,
			// },
		},
	}
	auditOrderBill(query, "", false)
}

func AuditOrderWarehouse() {
	if conf.Config.Env == "uat" {
		return
	}

	t1 := time.Now().Add(-7 * 24 * time.Hour)
	// t2 := time.Now().Add(-1 * 12 * time.Hour)

	query := &model.Order{
		ComplexQuery: []*bson.M{
			&bson.M{
				"created_time": bson.M{
					"$gte": t1,
				},
			},
			// &bson.M{
			// 	"created_time": bson.M{
			// 		"$lt": t2,
			// 	},
			// },
			// &bson.M{
			// 	"order_id": 510044,
			// },
		},
	}
	auditOrderWarehouse(query, "")
}

func auditOrderWarehouse(query *model.Order, msg string) {
	offset := int64(0)
	limit := int64(100)

	for true {
		q := model.OrderDB.Query(query, offset, limit, &primitive.M{"_id": 1})
		if q.Status != common.APIStatus.Ok {
			return
		}

		lstOrders := q.Data.([]*model.Order)
		for _, item := range lstOrders {
			orderItemPartitionDB := model.GetOrderItemPartitionDB(item, "auditOrderWarehouse")
			if orderItemPartitionDB == nil {
				continue
			}

			queryOrderAudit := model.OrderAuditDB.QueryOne(&model.OrderAudit{
				OrderID:     item.OrderID,
				OrderStatus: string(item.Status),
			})

			if queryOrderAudit.Status == common.APIStatus.Ok {
				continue
			}

			// remove prev status, not check
			_ = model.OrderAuditDB.Delete(&model.OrderAudit{
				OrderID: item.OrderID,
				Status:  "PENDING",
			})

			orderItemResult := orderItemPartitionDB.Query(&model.OrderItem{
				OrderID: item.OrderID,
			}, 0, 0, nil)

			if orderItemResult.Status != common.APIStatus.Ok {
				continue
			}

			orderItems := orderItemResult.Data.([]*model.OrderItem)

			orderWarehouse, err := client.Services.Warehouse.GetSaleOrder(item.OrderID, item.SaleOrderCode)
			if err != nil {
				au := &model.OrderAudit{
					OrderID:   item.OrderID,
					OrderCode: item.OrderCode,
					ActionAt:  time.Now().Unix(),
				}

				au.CustomerID = item.CustomerID
				au.CustomerCode = item.CustomerCode

				if item.Status == enum.OrderState.Canceled {
					// MARKETPLACE CANCEL 1 WAY, WMS NOT FOUND
					au.Status = "DONE"
				} else {
					au.Status = "PENDING"
					au.Note = "Order " + string(item.Status) + ", wms" + err.Error()
				}
				_ = model.OrderAuditDB.Create(au)
				continue
			}

			// compare
			au, fl := compareOrder(item, orderWarehouse)
			auLines, existOut := compareOrderLine(au, orderItems, orderWarehouse)

			// audit filter
			if orderWarehouse.PaymentMethod == "COD" {
				fee := int64(0)
				if item.PaymentMethodFee != nil {
					fee = fee - *item.PaymentMethodFee
				}
				if item.DeliveryMethodFee != nil {
					fee = fee + *item.DeliveryMethodFee
				}
				if item.ExtraFee != nil {
					fee = fee + *item.ExtraFee
				}
				if item.TotalDiscount != nil {
					fee = fee - int64(*item.TotalDiscount)
				}

				if existOut {
					actualPrice := au.OrderAuditDetail.TotalPrice + int(fee)
					if actualPrice != int(orderWarehouse.CodAmount) {
						fl = true
						au.OrderAuditDetail.TotalPrice = actualPrice
						au.OrderAuditDetail.WmsTotalPrice = int(orderWarehouse.CodAmount)
						au.Note = fmt.Sprintf("%v;%s", au.Note, "TotalPrice/CodAmount not match, "+item.PaymentMethod+"/"+orderWarehouse.PaymentMethod)
					} else {
						au.OrderAuditDetail.TotalPrice = 0
						au.OrderAuditDetail.WmsTotalPrice = 0
					}
				}

			} else {
				fee := -au.OrderAuditDetail.PaymentMethodFee + au.OrderAuditDetail.DeliveryMethodFee + au.OrderAuditDetail.ExtraFee - au.OrderAuditDetail.TotalDiscount
				feeWare := -au.OrderAuditDetail.WmsPaymentMethodFee + au.OrderAuditDetail.WmsDeliveryMethodFee + au.OrderAuditDetail.WmsExtraFee - au.OrderAuditDetail.WmsTotalDiscount

				// support old version
				t1 := time.Date(2021, time.November, 1, 0, 0, 0, 0, time.UTC)
				t2 := t1.Add(-7 * time.Hour)
				if item.CreatedTime.Before(t2) {
					feeWare = au.OrderAuditDetail.WmsDeliveryMethodFee + au.OrderAuditDetail.WmsExtraFee - au.OrderAuditDetail.WmsTotalDiscount
				}
				deliveryAmount := int(orderWarehouse.DeliveryAmount) + int(feeWare)

				actualPrice := au.OrderAuditDetail.TotalPrice + int(fee)
				// ho tro case wms lam tron so
				if actualPrice == deliveryAmount || (item.CreatedTime.Before(t2) && actualPrice == deliveryAmount+1) {
					au.OrderAuditDetail.TotalPrice = 0
					au.OrderAuditDetail.WmsTotalPrice = 0
				} else {
					fl = true
					subNote := fmt.Sprintf("PaymentMethodFee: %d\n, DeliveryMethodFee: %d\n, ExtraFee: %d\n, TotalDiscount: %d\n", au.OrderAuditDetail.PaymentMethodFee, au.OrderAuditDetail.DeliveryMethodFee, au.OrderAuditDetail.ExtraFee, au.OrderAuditDetail.TotalDiscount)
					subWmsNote := fmt.Sprintf("WmsPaymentMethodFee: %d\n, WmsDeliveryMethodFee: %d\n, WmsExtraFee: %d\n, WmsTotalDiscount: %d\n", au.OrderAuditDetail.WmsPaymentMethodFee, au.OrderAuditDetail.WmsDeliveryMethodFee, au.OrderAuditDetail.WmsExtraFee, au.OrderAuditDetail.WmsTotalDiscount)
					au.OrderAuditDetail.TotalPrice = actualPrice
					au.OrderAuditDetail.WmsTotalPrice = deliveryAmount
					au.Note = fmt.Sprintf("%v;%s", au.Note, "TotalPrice/DeliveryAmount not match, "+item.PaymentMethod+"/"+orderWarehouse.PaymentMethod)
					au.Note = fmt.Sprintf("%v;%s", au.Note, subNote)
					au.Note = fmt.Sprintf("%v;%s", au.Note, subWmsNote)
				}
			}

			if au.OrderAuditDetail.TotalDiscount == au.OrderAuditDetail.WmsTotalDiscount {
				au.OrderAuditDetail.TotalDiscount = 0
				au.OrderAuditDetail.WmsTotalDiscount = 0
			} else {
				fl = true
			}

			if au.OrderAuditDetail.ExtraFee == au.OrderAuditDetail.WmsExtraFee {
				au.OrderAuditDetail.ExtraFee = 0
				au.OrderAuditDetail.WmsExtraFee = 0
			} else {
				fl = true
			}

			if au.OrderAuditDetail.PaymentMethodFee == au.OrderAuditDetail.WmsPaymentMethodFee {
				au.OrderAuditDetail.PaymentMethodFee = 0
				au.OrderAuditDetail.WmsPaymentMethodFee = 0
			} else {
				fl = true
			}

			if au.OrderAuditDetail.DeliveryMethodFee == au.OrderAuditDetail.WmsDeliveryMethodFee {
				au.OrderAuditDetail.DeliveryMethodFee = 0
				au.OrderAuditDetail.WmsDeliveryMethodFee = 0
			} else {
				fl = true
			}

			au.CustomerID = item.CustomerID
			au.CustomerCode = item.CustomerCode

			if len(auLines) > 0 {
				au.Status = "PENDING"
				au.Note = fmt.Sprintf("%v;%s", au.Note, "Order line have issue")
				au.ActionAt = time.Now().Unix()
				rs := model.OrderAuditDB.Create(au)
				if rs.Status != common.APIStatus.Ok {
					continue
				}
				rs = model.OrderLineAuditDB.CreateMany(auLines)
				if rs.Status != common.APIStatus.Ok {
					continue
				}
			} else if fl {
				au.Status = "PENDING"
				au.ActionAt = time.Now().Unix()
				rs := model.OrderAuditDB.Create(au)
				if rs.Status != common.APIStatus.Ok {
					continue
				}
			} else {
				au.Status = "DONE"
				if len(lstOrders) == 1 {
					au.Note = "Audit success;" + msg
				}
				au.ActionAt = time.Now().Unix()
				_ = model.OrderAuditDB.Create(au)
			}
		}
		offset += limit
	}
}

func auditOrderPickWarehouse(query *model.Order, msg string) {
	offset := int64(0)
	limit := int64(100)

	for true {
		q := model.OrderDB.Query(query, offset, limit, &primitive.M{"_id": 1})
		if q.Status != common.APIStatus.Ok {
			return
		}

		lstOrders := q.Data.([]*model.Order)
		for _, item := range lstOrders {

			orderItemPartitionDB := model.GetOrderItemPartitionDB(item, "auditOrderPickWarehouse")
			if orderItemPartitionDB == nil {
				continue
			}

			queryOrderAudit := model.OrderAuditDB.QueryOne(&model.OrderAudit{
				OrderID:     item.OrderID,
				OrderStatus: string(item.Status),
			})

			if queryOrderAudit.Status == common.APIStatus.Ok {
				// continue
			}

			orderItemResult := orderItemPartitionDB.Query(&model.OrderItem{
				OrderID: item.OrderID,
			}, 0, 0, nil)

			if orderItemResult.Status != common.APIStatus.Ok {
				continue
			}

			orderItems := orderItemResult.Data.([]*model.OrderItem)

			orderWarehouse, err := client.Services.Warehouse.GetSaleOrder(item.OrderID, item.SaleOrderCode)
			if err != nil {
				continue
			}

			// compare
			au, _ := compareOrder(item, orderWarehouse)
			auLines, _ := compareOrderLine(au, orderItems, orderWarehouse)

			if len(auLines) > 0 {
				UpdateSyncAuditOrderLine(item.OrderID, false)
			}
		}
		offset += limit
	}
}

func UpdateSyncAuditOrderBill(orderID int64, checkAudit bool) *common.APIResponse {
	query := &model.Order{
		ComplexQuery: []*bson.M{
			&bson.M{
				"status": bson.M{
					"$in": []string{"DELIVERING", "DELIVERED", "RETURNED", "COMPLETED", "CANCEL"},
				},
			},
			&bson.M{
				"order_id": orderID,
			},
		},
	}

	auditOrderBill(query, fmt.Sprintf("Check sync at %s", time.Now().String()), true)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
	}
}

var (
	mapOrderStatusWms = map[string][]string{
		"CONFIRMED":       []string{"CONFIRMED", "RESERVING"},
		"PROCESSING":      []string{"RESERVING", "WAIT_TO_PICK", "PICKING", "WAIT_TO_CHECK", "CHECKING", "WAIT_TO_PACK", "PACKING"},
		"WAIT_TO_DELIVER": []string{"WAIT_TO_DELIVERY"},
		"DELIVERING":      []string{"DELIVERING", "RETURN", "RETURNING"},
		"DELIVERED":       []string{"DELIVERED"},
		"RETURNED":        []string{"RETURNED"},
		"CANCEL":          []string{"CANCEL", "RETURNED"},
	}
)

func compareOrder(order *model.Order, orderWarehouse *model.OrderWarehouse) (*model.OrderAudit, bool) {
	flag := false
	au := &model.OrderAudit{
		OrderID:          order.OrderID,
		OrderCode:        order.OrderCode,
		SaleOrderCode:    order.SaleOrderCode,
		OrderedAt:        order.CreatedTime,
		OrderStatus:      string(order.Status),
		OrderAuditDetail: &model.OrderAuditDetail{},
	}

	auDetail := &model.OrderAuditDetail{}
	if checkStatus, ok := mapOrderStatusWms[string(order.Status)]; ok && len(checkStatus) > 0 {
		match := false
		for _, status := range checkStatus {
			if orderWarehouse.Status == status {
				match = true
				break
			}
		}
		if !match {
			flag = true
			au.Note = fmt.Sprintf("%v; [Urgent] Status not match %s/%s", au.Note, string(order.Status), orderWarehouse.Status)
		}
	}

	if order.SaleOrderCode != orderWarehouse.SaleOrderCode {
		flag = true
		auDetail.SO = order.SaleOrderCode
		auDetail.WmsSO = orderWarehouse.SaleOrderCode
		au.Note = fmt.Sprintf("%v;%s", au.Note, "SO not match")
	}

	// check payment method
	if order.PaymentMethod == "PAYMENT_METHOD_NORMAL" && orderWarehouse.PaymentMethod != "COD" {
		flag = true
		auDetail.PaymentMethod = order.PaymentMethod
		auDetail.WmsPaymentMethod = orderWarehouse.PaymentMethod
		au.Note = fmt.Sprintf("%v;%s", au.Note, "PaymentMethod not match")
	}

	if orderWarehouse.DiscountAmount != nil {
		if orderWarehouse.DiscountAmount.PaymentMethod != nil {
			fe := *orderWarehouse.DiscountAmount.PaymentMethod
			if fe < 0 {
				fe = -fe
			}
			auDetail.WmsPaymentMethodFee = int64(fe)
		}
		if orderWarehouse.DiscountAmount.VoucherAmount != nil {
			fe := *orderWarehouse.DiscountAmount.VoucherAmount
			if fe < 0 {
				fe = -fe
			}
			auDetail.WmsTotalDiscount = int64(fe)
		}
	}

	if orderWarehouse.Fee != nil {
		if orderWarehouse.Fee.DeliveryFee != nil {
			fe := int64(*orderWarehouse.Fee.DeliveryFee)
			if fe < 0 {
				fe = -fe
			}
			auDetail.WmsDeliveryMethodFee = fe
		}
		if orderWarehouse.Fee.ExtraFee != nil {
			fe := int64(*orderWarehouse.Fee.ExtraFee)
			if fe < 0 {
				fe = -fe
			}
			auDetail.WmsExtraFee = fe
		}
	}

	if order.TotalDiscount != nil {
		fe := *order.TotalDiscount
		if fe < 0 {
			fe = -fe
		}
		auDetail.TotalDiscount = int64(fe)
	}

	if order.DeliveryMethodFee != nil {
		fe := *order.DeliveryMethodFee
		if fe < 0 {
			fe = -fe
		}
		auDetail.DeliveryMethodFee = int64(fe)
	}

	if order.ExtraFee != nil {
		fe := *order.ExtraFee
		if fe < 0 {
			fe = -fe
		}
		auDetail.ExtraFee = fe
	}

	if order.PaymentMethodFee != nil {
		fe := *order.PaymentMethodFee
		if fe < 0 {
			fe = -fe
		}
		auDetail.PaymentMethodFee = fe
	}

	au.OrderAuditDetail = auDetail
	return au, flag
}

func compareOrderLine(order *model.OrderAudit, orderItems []*model.OrderItem, orderWarehouse *model.OrderWarehouse) ([]*model.OrderLineAudit, bool) {

	totalPrice := 0
	existOut := false
	for _, item := range orderItems {
		if item.OutboundQuantity != nil && *item.OutboundQuantity > 0 {
			totalPrice = totalPrice + *item.OutboundQuantity*int(item.Price)
		}
	}

	order.OrderAuditDetail.TotalPrice = totalPrice

	auLines := make([]*model.OrderLineAudit, 0)

	// CASE 1
	if len(orderItems) > len(orderWarehouse.OrderLines) {
		lst := make(map[string]*model.OrderLineWarehouse)
		lst2 := make(map[int64]*model.OrderLineWarehouse)

		for _, item := range orderWarehouse.OrderLines {
			if item.OutboundQuantity > 0 {
				existOut = true
			}
			lst[item.Sku] = item
			lst2[item.AdminProductID] = item
		}

		for _, item := range orderItems {
			flag := false
			line := &model.OrderLineAudit{
				OrderID:     order.OrderID,
				OrderStatus: order.Status,
			}
			if orderLine, ok := lst[item.Sku]; ok {
				// found
				line.ProductID = item.ProductID
				line.SKU = item.Sku
				if int64((orderLine.UnitPrice)) != int64(item.Price) {
					line.Price = toInt64(int64(item.Price))
					line.WmsPrice = toInt64(int64((orderLine.UnitPrice)))
					line.Note = fmt.Sprintf("%v%s", line.Note, "UnitPrice not match")
					flag = true
				}

				if (item.ReservedQuantity == nil && orderLine.ReservedQuantity > 0) || (item.ReservedQuantity != nil && orderLine.ReservedQuantity != *item.ReservedQuantity) {
					line.ReservedQuantity = item.ReservedQuantity
					line.WmsReservedQuantity = toInt(orderLine.ReservedQuantity)
					line.Note = fmt.Sprintf("%v;%s", line.Note, "ReservedQuantity not match")
					flag = true
				}

				if (item.OutboundQuantity == nil && orderLine.OutboundQuantity > 0) || (item.OutboundQuantity != nil && orderLine.OutboundQuantity != *item.OutboundQuantity) {
					line.OutboundQuantity = item.OutboundQuantity
					line.WmsOutboundQuantity = toInt(orderLine.OutboundQuantity)
					line.Note = fmt.Sprintf("%v;%s", line.Note, "OutboundQuantity not match")
					flag = true
				}

				if (item.DeliveredQuantity == nil && orderLine.DeliveredQuantity > 0) || (item.DeliveredQuantity != nil && orderLine.DeliveredQuantity != *item.DeliveredQuantity) {
					line.DeliveredQuantity = item.DeliveredQuantity
					line.WmsDeliveredQuantity = toInt(orderLine.DeliveredQuantity)
					line.Note = fmt.Sprintf("%v;%s", line.Note, "DeliveredQuantity not match")
					flag = true
				}

				if (item.ReturnedQuantity == nil && orderLine.ReturnedQuantity > 0) || (item.ReturnedQuantity != nil && orderLine.ReturnedQuantity != *item.ReturnedQuantity) {
					line.ReturnQuantity = item.ReturnedQuantity
					line.WmsReturnQuantity = toInt(orderLine.ReturnedQuantity)
					line.Note = fmt.Sprintf("%v;%s", line.Note, "ReturnQuantity not match")
					flag = true
				}

			} else if orderLine, ok := lst2[item.ProductID]; ok {
				// found
				line.ProductID = item.ProductID
				line.SKU = item.Sku
				if int64((orderLine.UnitPrice)) != int64(item.Price) {
					line.Price = toInt64(int64(item.Price))
					line.WmsPrice = toInt64(int64((orderLine.UnitPrice)))
					line.Note = fmt.Sprintf("%v%s", line.Note, "UnitPrice not match")
					flag = true
				}

				if (item.ReservedQuantity == nil && orderLine.ReservedQuantity > 0) || (item.ReservedQuantity != nil && orderLine.ReservedQuantity != *item.ReservedQuantity) {
					line.ReservedQuantity = item.ReservedQuantity
					line.WmsReservedQuantity = toInt(orderLine.ReservedQuantity)
					line.Note = fmt.Sprintf("%v;%s", line.Note, "ReservedQuantity not match")
					flag = true
				}

				if (item.OutboundQuantity == nil && orderLine.OutboundQuantity > 0) || (item.OutboundQuantity != nil && orderLine.OutboundQuantity != *item.OutboundQuantity) {
					line.OutboundQuantity = item.OutboundQuantity
					line.WmsOutboundQuantity = toInt(orderLine.OutboundQuantity)
					line.Note = fmt.Sprintf("%v;%s", line.Note, "OutboundQuantity not match")
					flag = true
				}

				if (item.DeliveredQuantity == nil && orderLine.DeliveredQuantity > 0) || (item.DeliveredQuantity != nil && orderLine.DeliveredQuantity != *item.DeliveredQuantity) {
					line.DeliveredQuantity = item.DeliveredQuantity
					line.WmsDeliveredQuantity = toInt(orderLine.DeliveredQuantity)
					line.Note = fmt.Sprintf("%v;%s", line.Note, "DeliveredQuantity not match")
					flag = true
				}

				if (item.ReturnedQuantity == nil && orderLine.ReturnedQuantity > 0) || (item.ReturnedQuantity != nil && orderLine.ReturnedQuantity != *item.ReturnedQuantity) {
					line.ReturnQuantity = item.ReturnedQuantity
					line.WmsReturnQuantity = toInt(orderLine.ReturnedQuantity)
					line.Note = fmt.Sprintf("%v;%s", line.Note, "ReturnQuantity not match")
					flag = true
				}
			} else {
				flag = true
				line.ProductID = item.ProductID
				line.SKU = item.Sku
				line.Note = fmt.Sprintf("%v;%sproduct_id=%d, sku=%s unit_price=%v", line.Note, "WMS missing line ", item.ProductID, item.Sku, item.Price)
			}
			if flag {
				auLines = append(auLines, line)
			}
		}

		return auLines, existOut
	}

	lst := make(map[string]*model.OrderItem)
	lst2 := make(map[int64]*model.OrderItem)
	for _, item := range orderItems {
		lst[item.Sku] = item
		lst2[item.ProductID] = item
	}

	for _, orderLine := range orderWarehouse.OrderLines {
		if orderLine.OutboundQuantity > 0 {
			existOut = true
		}
		flag := false
		line := &model.OrderLineAudit{
			OrderID:     order.OrderID,
			OrderStatus: order.Status,
		}

		if item, ok := lst[orderLine.Sku]; ok {
			// found
			line.ProductID = orderLine.AdminProductID
			line.SKU = item.Sku
			if int64((orderLine.UnitPrice)) != int64(item.Price) {
				line.Price = toInt64(int64(item.Price))
				line.WmsPrice = toInt64(int64((orderLine.UnitPrice)))
				line.Note = fmt.Sprintf("%v;%s", line.Note, "UnitPrice not match")
				flag = true
			}

			if (item.ReservedQuantity == nil && orderLine.ReservedQuantity > 0) || (item.ReservedQuantity != nil && orderLine.ReservedQuantity != *item.ReservedQuantity) {
				line.ReservedQuantity = item.ReservedQuantity
				line.WmsReservedQuantity = toInt(orderLine.ReservedQuantity)
				line.Note = fmt.Sprintf("%v;%s", line.Note, "ReservedQuantity not match")
				flag = true
			}

			if (item.OutboundQuantity == nil && orderLine.OutboundQuantity > 0) || (item.OutboundQuantity != nil && orderLine.OutboundQuantity != *item.OutboundQuantity) {
				line.OutboundQuantity = item.OutboundQuantity
				line.WmsOutboundQuantity = toInt(orderLine.OutboundQuantity)
				line.Note = fmt.Sprintf("%v;%s", line.Note, "OutboundQuantity not match")
				flag = true
			}

			if (item.DeliveredQuantity == nil && orderLine.DeliveredQuantity > 0) || (item.DeliveredQuantity != nil && orderLine.DeliveredQuantity != *item.DeliveredQuantity) {
				line.DeliveredQuantity = item.DeliveredQuantity
				line.WmsDeliveredQuantity = toInt(orderLine.DeliveredQuantity)
				line.Note = fmt.Sprintf("%v;%s", line.Note, "DeliveredQuantity not match")
				flag = true
			}

			if (item.ReturnedQuantity == nil && orderLine.ReturnedQuantity > 0) || (item.ReturnedQuantity != nil && orderLine.ReturnedQuantity != *item.ReturnedQuantity) {
				line.ReturnQuantity = item.ReturnedQuantity
				line.WmsReturnQuantity = toInt(orderLine.ReturnedQuantity)
				line.Note = fmt.Sprintf("%v;%s", line.Note, "ReturnQuantity not match")
				flag = true
			}

		} else if item, ok := lst2[orderLine.AdminProductID]; ok {
			// found
			line.ProductID = orderLine.AdminProductID
			line.SKU = item.Sku
			if int64((orderLine.UnitPrice)) != int64(item.Price) {
				line.Price = toInt64(int64(item.Price))
				line.WmsPrice = toInt64(int64((orderLine.UnitPrice)))
				line.Note = fmt.Sprintf("%v;%s", line.Note, "UnitPrice not match")
				flag = true
			}

			if (item.ReservedQuantity == nil && orderLine.ReservedQuantity > 0) || (item.ReservedQuantity != nil && orderLine.ReservedQuantity != *item.ReservedQuantity) {
				line.ReservedQuantity = item.ReservedQuantity
				line.WmsReservedQuantity = toInt(orderLine.ReservedQuantity)
				line.Note = fmt.Sprintf("%v;%s", line.Note, "ReservedQuantity not match")
				flag = true
			}

			if (item.OutboundQuantity == nil && orderLine.OutboundQuantity > 0) || (item.OutboundQuantity != nil && orderLine.OutboundQuantity != *item.OutboundQuantity) {
				line.OutboundQuantity = item.OutboundQuantity
				line.WmsOutboundQuantity = toInt(orderLine.OutboundQuantity)
				line.Note = fmt.Sprintf("%v;%s", line.Note, "OutboundQuantity not match")
				flag = true
			}

			if (item.DeliveredQuantity == nil && orderLine.DeliveredQuantity > 0) || (item.DeliveredQuantity != nil && orderLine.DeliveredQuantity != *item.DeliveredQuantity) {
				line.DeliveredQuantity = item.DeliveredQuantity
				line.WmsDeliveredQuantity = toInt(orderLine.DeliveredQuantity)
				line.Note = fmt.Sprintf("%v;%s", line.Note, "DeliveredQuantity not match")
				flag = true
			}

			if (item.ReturnedQuantity == nil && orderLine.ReturnedQuantity > 0) || (item.ReturnedQuantity != nil && orderLine.ReturnedQuantity != *item.ReturnedQuantity) {
				line.ReturnQuantity = item.ReturnedQuantity
				line.WmsReturnQuantity = toInt(orderLine.ReturnedQuantity)
				line.Note = fmt.Sprintf("%v;%s", line.Note, "ReturnQuantity not match")
				flag = true
			}
		} else {
			// payment fee wms version old
			if orderLine.AdminProductID == 10180 && order.OrderAuditDetail != nil {
				if orderLine.UnitPrice < 0 {
					order.OrderAuditDetail.WmsPaymentMethodFee = -int64(orderLine.UnitPrice)
					continue
				}
				order.OrderAuditDetail.WmsPaymentMethodFee = int64(orderLine.UnitPrice)
				continue
			}
			flag = true
			line.ProductID = orderLine.AdminProductID
			line.SKU = orderLine.Sku
			line.Note = fmt.Sprintf("%v;%sproduct_id=%d, sku=%s unit_price=%v, time=%s", line.Note, "Marketplace missing line ", orderLine.AdminProductID, orderLine.Sku, orderLine.UnitPrice, time.Now().String())
		}
		if flag {
			auLines = append(auLines, line)
		}
	}

	return auLines, existOut
}

func auditOrderBill(query *model.Order, msg string, skip bool) {
	offset := int64(0)
	limit := int64(100)
	for true {
		q := model.OrderDB.Query(query, offset, limit, &primitive.M{"_id": 1})
		if q.Status != common.APIStatus.Ok {
			return
		}

		lstOrders := q.Data.([]*model.Order)
		for _, item := range lstOrders {

			orderItemPartitionDB := model.GetOrderItemPartitionDB(item, "auditOrderBill")
			if orderItemPartitionDB == nil {
				continue
			}

			queryOrderAudit := model.OrderBillAuditDB.QueryOne(&model.OrderBillAudit{
				OrderID:     item.OrderID,
				OrderStatus: string(item.Status),
			})

			if !skip && queryOrderAudit.Status == common.APIStatus.Ok {
				continue
			}

			// remove prev status, not check
			_ = model.OrderBillAuditDB.Delete(&model.OrderBillAudit{
				OrderID: item.OrderID,
				Status:  "PENDING",
			})

			orderBill := &model.OrderBillAudit{
				OrderID:       item.OrderID,
				OrderCode:     item.OrderCode,
				Status:        "PENDING",
				OrderStatus:   string(item.Status),
				CustomerID:    item.CustomerID,
				CustomerCode:  item.CustomerCode,
				SaleOrderCode: item.SaleOrderCode,
				OrderedAt:     item.CreatedTime,
			}

			if item.Status == enum.OrderState.Canceled && len(item.SaleOrderCode) == 0 {
				continue
			}

			if len(item.SaleOrderCode) == 0 && item.Status != enum.OrderState.Canceled {
				orderBill.Note = "Missing so"
				_ = model.OrderBillAuditDB.Create(orderBill)
				continue
			}

			orderWarehouse, err := client.Services.Warehouse.GetSaleOrder(item.OrderID, item.SaleOrderCode)
			if err != nil {
				orderBill.Note = fmt.Sprintf("SO %s not found, err=%s ", item.SaleOrderCode, err.Error())
				_ = model.OrderBillAuditDB.Create(orderBill)
				continue
			}
			orderBill.SoStatus = orderWarehouse.Status

			// skip if order and so cancelled
			if item.Status == enum.OrderState.Canceled && orderWarehouse.Status == "CANCEL" {
				continue
			}

			existOut := false
			for _, item := range orderWarehouse.OrderLines {
				if item.OutboundQuantity > 0 {
					existOut = true
					break
				}
			}

			if !existOut {
				orderBill.Note = "Not outbound"
				orderBill.Status = "DONE"
				_ = model.OrderBillAuditDB.Create(orderBill)
				continue
			}

			bills, err := client.Services.Bill.GetBill(item.OrderID)
			if err != nil {
				orderBill.Note = "Bill not found, " + err.Error()
				_ = model.OrderBillAuditDB.Create(orderBill)
				continue
			}
			if len(bills) > 1 {
				orderBill.Note = "Bill dup, " + err.Error()
				orderBill.BillId = bills[0].BillId
				orderBill.BillCode = bills[0].BillCode
				_ = model.OrderBillAuditDB.Create(orderBill)
				continue
			}
			bill := bills[0]
			orderBill.BillId = bill.BillId
			orderBill.BillCode = bill.BillCode
			orderBill.BillStatus = bill.Status
			orderItemResult := orderItemPartitionDB.Query(&model.OrderItem{
				OrderID: item.OrderID,
			}, 0, 0, nil)

			if orderItemResult.Status != common.APIStatus.Ok {
				orderBill.Note = "Order item len zero"
				orderBill.Status = "DONE"
				_ = model.OrderBillAuditDB.Create(orderBill)
				continue
			}

			orderItems := orderItemResult.Data.([]*model.OrderItem)

			totalPrice := 0
			for _, item := range orderItems {
				if item.OutboundQuantity != nil && *item.OutboundQuantity > 0 {
					totalPrice = totalPrice + *item.OutboundQuantity*int(item.Price)
				}
			}

			fee := int64(0)
			if item.PaymentMethodFee != nil {
				fee = fee + *item.PaymentMethodFee
			}
			if item.DeliveryMethodFee != nil {
				fee = fee + *item.DeliveryMethodFee
			}
			if item.ExtraFee != nil {
				fee = fee + *item.ExtraFee
			}
			if item.TotalDiscount != nil {
				fee = fee - int64(*item.TotalDiscount)
			}

			wmsPaymentMethodFee := int64(0)
			wmsTotalDiscount := int64(0)
			wmsDeliveryMethodFee := int64(0)
			wmsExtraFee := int64(0)

			if orderWarehouse.DiscountAmount != nil {
				if orderWarehouse.DiscountAmount.PaymentMethod != nil {
					fe := *orderWarehouse.DiscountAmount.PaymentMethod
					if fe < 0 {
						fe = -fe
					}
					wmsPaymentMethodFee = int64(fe)
				}
				if orderWarehouse.DiscountAmount.VoucherAmount != nil {
					fe := *orderWarehouse.DiscountAmount.VoucherAmount
					if fe < 0 {
						fe = -fe
					}
					wmsTotalDiscount = int64(fe)
				}
			}

			if orderWarehouse.Fee != nil {
				if orderWarehouse.Fee.DeliveryFee != nil {
					fe := int64(*orderWarehouse.Fee.DeliveryFee)
					if fe < 0 {
						fe = -fe
					}
					wmsDeliveryMethodFee = fe
				}
				if orderWarehouse.Fee.ExtraFee != nil {
					fe := int64(*orderWarehouse.Fee.ExtraFee)
					if fe < 0 {
						fe = -fe
					}
					wmsExtraFee = fe
				}
			}

			// audit filter
			if orderWarehouse.PaymentMethod == "COD" {

				actualPrice := totalPrice + int(fee)
				if actualPrice != int(orderWarehouse.CodAmount) {
					orderBill.TotalOrder = actualPrice
					orderBill.TotalOrderWms = int(orderWarehouse.CodAmount)
					orderBill.TotalOrderBill = bill.TotalOrderAmount
					if orderBill.TotalOrderBill != orderBill.TotalOrderWms || orderBill.TotalOrderBill != orderBill.TotalOrder {
						orderBill.Note = fmt.Sprintf("[COD] Bill total not match %d - %d - %d (BILL/WMS/CRM)", orderBill.TotalOrderBill, orderBill.TotalOrderWms, orderBill.TotalOrder)
						_ = model.OrderBillAuditDB.Create(orderBill)
						continue
					}
				}
				orderBill.Status = "DONE"
				_ = model.OrderBillAuditDB.Create(orderBill)

			} else {
				feeWare := -wmsPaymentMethodFee + wmsDeliveryMethodFee + wmsExtraFee - wmsTotalDiscount

				// support old version
				t1 := time.Date(2021, time.November, 1, 0, 0, 0, 0, time.UTC)
				t2 := t1.Add(-7 * time.Hour)
				if item.CreatedTime.Before(t2) {
					feeWare = wmsDeliveryMethodFee + wmsExtraFee - wmsTotalDiscount
				}
				deliveryAmount := int(orderWarehouse.DeliveryAmount) + int(feeWare)

				actualPrice := totalPrice + int(fee)

				orderBill.TotalOrder = actualPrice
				orderBill.TotalOrderWms = deliveryAmount
				orderBill.TotalOrderBill = bill.TotalOrderAmount
				if orderBill.TotalOrderBill != orderBill.TotalOrderWms || orderBill.TotalOrderBill != orderBill.TotalOrder {
					orderBill.Note = fmt.Sprintf("[BANK] Bill total not match %d - %d - %d (BILL/WMS/CRM)", orderBill.TotalOrderBill, orderBill.TotalOrderWms, orderBill.TotalOrder)
					_ = model.OrderBillAuditDB.Create(orderBill)
					continue
				}

				orderBill.Status = "DONE"
				_ = model.OrderBillAuditDB.Create(orderBill)
			}
		}
		offset += limit
	}
}

func AuditOrderPoint() {
	if conf.Config.Env == "uat" {
		return
	}

	t1 := time.Now().Add(-7 * 24 * time.Hour)

	query := &model.Order{
		ComplexQuery: []*bson.M{
			&bson.M{
				"created_time": bson.M{
					"$gte": t1,
				},
			},
			&bson.M{
				"point": 0,
			},
			&bson.M{
				"status": "COMPLETED",
			},
		},
	}
	auditOrderPoint(query, "")
}

func auditOrderPoint(query *model.Order, msg string) {
	offset := int64(0)
	limit := int64(100)

	for true {
		q := model.OrderDB.Query(query, offset, limit, &primitive.M{"_id": 1})
		if q.Status != common.APIStatus.Ok {
			return
		}

		lstOrders := q.Data.([]*model.Order)
		for _, item := range lstOrders {
			processOrderPoint(item.OrderID)
		}
		offset += limit
	}
}

func toInt(val int) *int {
	return &val
}

func toInt64(val int64) *int64 {
	return &val
}
