package action

import (
	"bytes"
	"fmt"
	"html/template"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/tool"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetInvoicesBySeller(input *model.ReqGetInvoiceList) *common.APIResponse {
	filter := model.Invoice{
		OrderID:       input.OrderID,
		InvoiceCode:   input.InvoiceCode,
		InvoiceID:     input.InvoiceID,
		SellerCode:    input.SellerCode,
		InvoiceStatus: input.InvoiceStatus,
	}

	if input.IsFined {
		filter.IsFined = &input.IsFined
	}

	if !input.GetAll {
		filter.Request = &input.Request
	}
	if input.GetDownloadFile {
		filter.DownloadedFile = &input.DownloadedFile
	}
	if len(input.ReturnStatus) > 0 {
		filter.ReturnStatus = &input.ReturnStatus
	}

	complexQuery := []*bson.M{}

	if !input.SkipTimeRange {
		t := time.Now()
		if input.FromDate == nil {
			tFromDate := t.AddDate(0, 0, -60) // default from is last 30 days
			input.FromDate = &tFromDate
		}

		if input.ToDate == nil {
			input.ToDate = &t
		}
	}

	if input.FromDate != nil {
		complexQuery = append(complexQuery, &bson.M{
			"created_time": bson.M{"$gte": input.FromDate},
		})
	}
	if input.ToDate != nil {
		complexQuery = append(complexQuery, &bson.M{
			"created_time": bson.M{"$lte": input.ToDate},
		})
	}

	if input.OrderCompletedFrom != nil && input.OrderCompletedTo != nil {
		timeQuery := bson.M{}
		if input.OrderCompletedFrom != nil {
			timeQuery["$gte"] = input.OrderCompletedFrom
		}
		if input.OrderCompletedTo != nil {
			timeQuery["$lte"] = input.OrderCompletedTo
		}

		orderDistinctRes := model.OrderDB.Distinct(
			&bson.M{
				"status":         string(enum.OrderState.Completed),
				"completed_time": timeQuery,
			},
			"order_id",
		)
		if orderDistinctRes.Status == common.APIStatus.Ok {
			complexQuery = append(complexQuery, &bson.M{"order_id": bson.M{"$in": orderDistinctRes.Data}})
		}
	}

	if input.OrderDeliveredFrom != nil || input.OrderDeliveredTo != nil {
		timeQuery := bson.M{}
		if input.OrderDeliveredFrom != nil {
			timeQuery["$gte"] = input.OrderDeliveredFrom
		}
		if input.OrderDeliveredTo != nil {
			timeQuery["$lte"] = input.OrderDeliveredTo
		}

		// orderDistinctRes := model.OrderDB.Distinct(
		// 	&bson.M{
		// 		"status": bson.M{
		// 			"$in": []string{string(enum.OrderState.Delivered), string(enum.OrderState.Completed)},
		// 		},
		// 		"delivered_time": timeQuery,
		// 	},
		// 	"order_id",
		// )

		complexQuery = append(complexQuery, &bson.M{
			"delivered_time": timeQuery,
		})
	}

	if input.InvoicePartnerCode != "" {
		complexQuery = append(complexQuery, &bson.M{"config.partner_code": input.InvoicePartnerCode})
	}
	if input.SignedBy != "" {
		complexQuery = append(complexQuery, &bson.M{"config.signed_by": input.SignedBy})
	}
	if len(input.ExcludeTags) > 0 {
		complexQuery = append(complexQuery, &bson.M{
			"exclude_tags": bson.M{
				"$in": input.ExcludeTags,
			},
		})
	}

	if len(complexQuery) > 0 {
		filter.OperationAnd = complexQuery
	}

	result := model.InvoiceDBReplica.Query(filter, input.Offset, input.Limit, &primitive.M{"created_time": -1})
	if input.GetTotal {
		result.Total = model.InvoiceDBReplica.Count(filter).Total
	}

	if result.Status == common.APIStatus.Ok && input.GetInvoiceItems {
		var (
			invoices  = result.Data.([]*model.Invoice)
			semaphore = utils.NewSemaphore(20)
		)
		for index := range invoices {
			semaphore.Acquire()
			go func(i int) {
				defer semaphore.Release()

				orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{
					OrderID: invoices[i].OrderID,
				}, "GetInvoicesBySeller")
				if orderItemPartitionDB == nil {
					return
				}

				orderItemsRes := orderItemPartitionDB.Query(model.OrderItem{
					SellerCode: invoices[i].SellerCode,
					OrderID:    invoices[i].OrderID,
				}, 0, 0, nil)
				if orderItemsRes.Status != common.APIStatus.Ok {
					fmt.Printf("Error querying orderItem [sellerCode=%s, orderID=%d] %#v\n", input.SellerCode, invoices[i].OrderID, orderItemsRes)
					return
				}
				orderItems := orderItemsRes.Data.([]*model.OrderItem)
				tool.CalculateInvoiceReport(invoices[i], orderItems)
			}(index)
		}
		semaphore.Wait()
	}

	return result
}

func GetSellerInvoiceByMailProcessor(input *model.Invoice) *common.APIResponse {
	result := model.InvoiceDB.QueryOne(input)
	return result
}

func UploadInvoiceDocumentByMailProcessor(input *model.Invoice) *common.APIResponse {
	if input.SellerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "SellerCode is required",
			ErrorCode: "SELLER_CODE_REQUIRED",
		}
	}

	if input.OrderID == 0 && input.DeliveryOrderCode == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "OrderID or DeliveryOrderCode is required",
			ErrorCode: "ORDER_ID_REQUIRED",
		}
	}

	if input.IssuedTime == nil || input.IssuedTime.IsZero() {
		now := time.Now()
		input.IssuedTime = &now
	}

	filter := model.Invoice{
		SellerCode: input.SellerCode,
	}

	if input.OrderID != 0 {
		filter.OrderID = input.OrderID
	}

	if input.DeliveryOrderCode != nil {
		filter.DeliveryOrderCode = input.DeliveryOrderCode
	}

	result := model.InvoiceDB.QueryOne(filter)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	invoice := result.Data.([]*model.Invoice)[0]
	t := true
	updater := model.Invoice{
		SellerSentMail:     &t,
		IssuedTime:         input.IssuedTime,
		InvoiceDocumentURL: input.InvoiceDocumentURL,

		MailFrom:             input.MailFrom,
		Helo:                 input.Helo,
		MailProcessingStatus: input.MailProcessingStatus,
	}

	if input.InvoiceDocumentURL != nil && *input.InvoiceDocumentURL != "" {
		updater.InvoiceStatus = model.InvoiceStatus.Completed
	}

	if input.IssuedTime.After(*invoice.InvoiceDeadline) {
		updater.IsLate = &t
	}
	if updater.IsLate != nil && *updater.IsLate && invoice.AutoChangeToOverdue != nil && *invoice.AutoChangeToOverdue {
		updater.InvoiceStatus = model.InvoiceStatus.LateDeadline
	}
	if invoice.ReturnStatus != nil && *invoice.ReturnStatus == model.InvoiceReturnStatusState.NEW {
		updater.ReturnStatus = &model.InvoiceReturnStatusState.COMPLETED
	}

	updater.Logs = append(updater.Logs, "Upload invoice document by mail processor")

	riFilter := model.ReconciliationItem{
		OrderID:    input.OrderID,
		SellerCode: input.SellerCode,
	}
	riUpdater := model.ReconciliationItem{
		HasSentInvoice: &t,
	}
	model.ReconciliationItemDB.UpdateMany(riFilter, riUpdater)

	result = model.InvoiceDB.UpdateOne(filter, updater)
	return result
}

func InvoiceCallbackFromMailProcessor(input *model.Invoice) *common.APIResponse {
	if input.SellerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "SellerCode is required",
			ErrorCode: "SELLER_CODE_REQUIRED",
		}
	}

	if input.OrderID == 0 && input.DeliveryOrderCode == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "OrderID or DeliveryOrderCode is required",
			ErrorCode: "ORDER_ID_REQUIRED",
		}
	}

	filter := model.Invoice{
		SellerCode: input.SellerCode,
	}

	if input.OrderID != 0 {
		filter.OrderID = input.OrderID
	}
	if input.DeliveryOrderCode != nil {
		filter.DeliveryOrderCode = input.DeliveryOrderCode
	}

	result := model.InvoiceDB.QueryOne(filter)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	t := true
	updater := model.Invoice{
		SellerSentMail:       &t,
		MailProcessingStatus: input.MailProcessingStatus,
		MailFrom:             input.MailFrom,
		Helo:                 input.Helo,
		RejectedReason:       input.RejectedReason,
	}

	old := result.Data.([]*model.Invoice)[0]
	updater.Logs = append(old.Logs, "Invoice callback from mail processor")

	result = model.InvoiceDB.UpdateOne(filter, updater)
	return result
}

func DownloadFile(input *model.ReqGetInvoiceList) *common.APIResponse {
	filter := model.Invoice{
		OperationAnd: []*bson.M{
			{
				"invoice_id": bson.M{
					"$exists": true,
					"$in":     input.InvoiceIDs,
				},
			},
		},
	}

	downloadedFile := true
	result := model.InvoiceDB.UpdateMany(filter, model.Invoice{
		DownloadedFile: &downloadedFile,
	})
	return result
}

func SellerDownloadFile(input *model.ReqGetInvoiceList) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Input is required",
		}
	}

	// Validate input
	invoiceIDs := make([]int, 0, len(input.InvoiceIDs))
	for _, invoiceID := range input.InvoiceIDs {
		if invoiceID == nil {
			continue
		}

		invoiceIDs = append(invoiceIDs, *invoiceID)
	}
	if len(invoiceIDs) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "InvoiceIDs is empty",
		}
	}

	filter := model.Invoice{
		OperationAnd: []*bson.M{
			{
				"invoice_id": bson.M{
					"$exists": true,
					"$in":     invoiceIDs,
				},
			},
		},
	}

	sellerDownloadedFile := true
	result := model.InvoiceDB.UpdateMany(filter, model.Invoice{
		SellerDownloadedFile: &sellerDownloadedFile,
	})
	return result
}

func UpdateInvoiceStatus() {
	// {
	// 	f := false
	// 	filter := model.Invoice{
	// 		InvoiceStatus: model.InvoiceStatus.Waiting,
	// 		OperationAnd: []*bson.M{
	// 			{
	// 				"created_time": bson.M{"$lte": time.Date(2021, time.October, 1, 0, 0, 0, 0, model.VNTimeZone)},
	// 			},
	// 		},
	// 	}
	// 	updater := model.Invoice{
	// 		InvoiceStatus: model.InvoiceStatus.Completed,
	// 		IsLate:        &f,
	// 	}
	// 	model.InvoiceDB.UpdateMany(filter, updater)
	// }

	{
		now := time.Now()
		filter := model.Invoice{
			InvoiceStatus:       model.InvoiceStatus.Waiting,
			AutoChangeToOverdue: utils.ParseBoolToPointer(true),
			OperationAnd: []*bson.M{
				{
					"invoice_deadline": bson.M{"$lte": now},
				},
			},
		}
		updater := model.Invoice{
			InvoiceStatus: model.InvoiceStatus.LateDeadline,
		}
		model.InvoiceDB.UpdateMany(filter, updater)
	}
}

func UpdateInvoiceToCompleted(invoiceID int64) *common.APIResponse {
	return model.InvoiceDB.UpdateOne(
		model.Invoice{InvoiceID: invoiceID},
		model.Invoice{InvoiceStatus: model.InvoiceStatus.Completed},
	)
}

func DeleteInvoice(invoiceID int64) *common.APIResponse {
	return model.InvoiceDB.Delete(model.Invoice{InvoiceID: invoiceID})
}

func UpdateInvoiceReturnTickets(input *model.InvoiceReturnTicket) *common.APIResponse {
	if input.OrderSellerID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "OrderSellerID is required",
			ErrorCode: "ORDER_SELLER_ID_REQUIRED",
		}
	}

	if len(input.ReturnTicketIDs) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "ReturnTicketIDs is required",
			ErrorCode: "RETURN_TICKET_IDS_REQUIRED",
		}
	}

	invoiceResp := model.InvoiceDB.Query(&model.Invoice{
		OrderSellerID: input.OrderSellerID,
	}, 0, 0, nil)
	if invoiceResp.Status != common.APIStatus.Ok {
		return invoiceResp
	}

	invoices := invoiceResp.Data.([]*model.Invoice)

	// If there is new ticketID in ReturnTickets, compared to invoice.ReturnTickets
	// -> It means there is a new return, waiting to be sent email
	hasNewReturn := false

LOOP:
	for _, invoice := range invoices {
		for _, tID := range input.ReturnTicketIDs {
			if !utils.IsInt64Contains(invoice.ReturnTicketIDs, tID) {
				hasNewReturn = true
				break LOOP
			}
		}
	}

	if !hasNewReturn {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "No new return ticket",
			ErrorCode: "NO_NEW_RETURN_TICKET",
		}
	}

	invoice := invoices[0]

	result := model.InvoiceDB.UpdateMany(
		model.Invoice{
			OrderID:       invoice.OrderID,
			OrderSellerID: invoice.OrderSellerID,
		},
		model.Invoice{
			ReturnStatus:    &model.InvoiceReturnStatusState.NEW,
			ReturnTicketIDs: input.ReturnTicketIDs,
		},
	)

	result.Data = []*bson.M{
		{
			"invoice_order_seller_id": input.OrderSellerID,
			"return_status":           model.InvoiceReturnStatusState.NEW,
			"return_ticket_ids":       input.ReturnTicketIDs,
		},
	}

	go ProcessNotifyInvoiceReturn(invoice)

	go ProcessUpdateReturnedInvoiceForDOSellerByReturnTickets(invoice, input)
	return result
}

func ProcessUpdateReturnedInvoiceForDOSellerByReturnTickets(invoice *model.Invoice, input *model.InvoiceReturnTicket) {
	if input == nil || invoice == nil || invoice.ReceiveInvoiceInfoBy == nil || *invoice.ReceiveInvoiceInfoBy != enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
		return
	}

	order, errRes := getOrder(invoice.OrderID)
	if errRes != nil {
		return
	}

	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ProcessUpdateReturnedInvoiceForDOSeller")
	if orderItemPartitionDB == nil {
		return
	}

	orderItemResult := orderItemPartitionDB.Query(&model.OrderItem{
		OrderID:     order.OrderID,
		SellerClass: &enum.SellerClass.EXTERNAL,
		SellerCode:  invoice.SellerCode,
	}, 0, 0, nil)

	if orderItemResult.Status != common.APIStatus.Ok {
		return
	}

	orderItems := orderItemResult.Data.([]*model.OrderItem)
	returnedQtyInfosByDO := make(map[string][]*model.ReturnedQuantityInfoByDO)
	for _, orderItem := range orderItems {
		if orderItem.ReturnedQuantity == nil {
			continue
		}

		remainingQty := *orderItem.ReturnedQuantity
		for _, outboundInfo := range orderItem.OutboundInfos {
			quantity := outboundInfo.Quantity
			if remainingQty < quantity {
				quantity = remainingQty
			}

			returnedQtyInfosByDO[orderItem.Sku] = append(returnedQtyInfosByDO[orderItem.Sku], &model.ReturnedQuantityInfoByDO{
				SKU:      orderItem.Sku,
				DoCode:   outboundInfo.DoCode,
				Quantity: quantity,
			})

			remainingQty -= quantity
			if remainingQty == 0 {
				break
			}
		}

		filter := &model.OrderItem{
			Sku:         orderItem.Sku,
			SellerClass: &enum.SellerClass.EXTERNAL,
			SellerCode:  orderItem.SellerCode,
			OrderID:     orderItem.OrderID,
		}
		updater := &model.OrderItem{
			ReturnedQuantityInfosByDO: returnedQtyInfosByDO[orderItem.Sku],
		}
		orderItemPartitionDB.UpdateOne(filter, updater)
	}
}

func FineLateInvoice(input *model.Invoice, as *model.ActionSource) *common.APIResponse {
	if input.InvoiceID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "InvoiceID is required",
			ErrorCode: "INVOICE_ID_REQUIRED",
		}
	}

	LogActionRequest(as.Account, "PUT", "/seller/invoice/late-insurance-fined", &input)

	res := model.InvoiceDB.QueryOne(model.Invoice{InvoiceID: input.InvoiceID})

	if res.Status != common.APIStatus.Ok {
		return res
	}

	invoice := res.Data.([]*model.Invoice)[0]

	// turn off
	if IsMarkedAsFine(invoice) && !IsMarkedAsFine(input) {
		updater := bson.M{
			"is_fined":                      false,
			"reconcile_schedule_time_index": nil,
			"reconcile_item_id":             nil,
		}

		if invoice.ReconcileItemID == nil {
			res = model.InvoiceDB.UpdateOne(model.Invoice{InvoiceID: input.InvoiceID}, updater)

			return res
		}

		res = model.ReconciliationItemDB.QueryOne(model.ReconciliationItem{
			ID:         invoice.ReconcileItemID,
			SellerCode: invoice.SellerCode,
		})

		if res.Status != common.APIStatus.Ok {
			return res
		}

		reconciliationItem := res.Data.([]*model.ReconciliationItem)[0]
		res = reconcile_action.RemoveReconciliationItem(reconciliationItem.ID, as, tool.SyncRIDeleteCustom)

		return res
	}

	// turn on fined
	res = model.InvoiceDB.UpdateOne(model.Invoice{InvoiceID: input.InvoiceID}, model.Invoice{
		IsFined: input.IsFined,
	})

	return res
}

func IsMarkedAsFine(invoice *model.Invoice) bool {
	if invoice.IsFined != nil && *invoice.IsFined {
		return true
	}

	return false
}

func ForceUpdateOrderInvoice(input *model.UpdateInvoiceRequest) *common.APIResponse {
	return model.OrderDB.UpdateOne(
		model.Order{OrderID: input.OrderID},
		model.Order{Invoice: &input.InvoiceRequest},
	)
}

// TODO: turn into job queue
func CallbackInvoiceV2(input model.InvoiceV2Callback) *common.APIResponse {
	query := model.Invoice{
		SellerCode: input.SellerCode,
		OrderID:    input.OrderID,
	}

	if input.DoCode != "" {
		query.DeliveryOrderCode = &input.DoCode
	}

	invoiceRes := model.InvoiceDB.QueryOne(query)

	if invoiceRes.Status != common.APIStatus.Ok {
		return invoiceRes
	}

	invoice := invoiceRes.Data.([]*model.Invoice)[0]

	var res *common.APIResponse

	if input.ResponseCallback != nil && input.ResponseCallback.Status != common.APIStatus.Ok {
		res = model.InvoiceDB.UpdateOne(model.Invoice{InvoiceID: invoice.InvoiceID}, model.Invoice{PartnerStatus: model.InvoicePartnerStatus.Error})
		client.Services.SellerPurchasing.AlertInvoiceSyncFailed(
			invoice.InvoiceID,
			invoice.InvoiceCode,
			invoice.SellerCode,
			fmt.Sprintf("Message: %s (%s)", input.ResponseCallback.Message, input.ResponseCallback.ErrorCode),
		)
	}

	if invoice.Config != nil && input.ResponseCallback != nil && input.ResponseCallback.Status == common.APIStatus.Ok {
		switch invoice.Config.SignedBy {
		case enum.SignedBy.SELLER:
			res = model.InvoiceDB.UpdateOne(model.Invoice{InvoiceID: invoice.InvoiceID}, model.Invoice{
				PartnerStatus: model.InvoicePartnerStatus.Draft,
				InvoiceV2Code: input.Code,
			})

			if res.Status != common.APIStatus.Ok {
				return res
			}

			mailDataItem := model.InvoiceEmailItem{
				OrderID:         invoice.OrderID,
				InvoiceCode:     input.Code,
				DeliveredTime:   invoice.DeliveredTime,
				InvoiceDeadline: invoice.InvoiceDeadline,
			}

			mailData := model.InvoiceEmail{
				Invoices: []model.InvoiceEmailItem{mailDataItem},
			}

			emailTmpl, err := template.ParseFiles("template/hilo-invoice-draft.html")
			if err != nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Can not parse email template." + err.Error(),
					ErrorCode: "PARSE_EMAIL_TEMPLATE_FAILED",
				}
			}

			var contentBytes bytes.Buffer
			if err := emailTmpl.Execute(&contentBytes, mailData); err != nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Can not execute email template." + err.Error(),
					ErrorCode: "EXECUTE_EMAIL_TEMPLATE_FAILED",
				}
			}

			content := []model.Content{
				{
					Type:  "text/html",
					Value: contentBytes.String(),
				},
			}

			subject := "[Buymed] Yêu cầu kiểm tra và ký phát hành hóa đơn điện tử trên Hilo Portal"

			to := []model.Email{
				{
					Email: invoice.Config.SellerEmail,
					Name:  invoice.SellerCode,
				},
			}

			client.Services.MailService.SendMailHilo(to, subject, content, &[]string{
				"SEND_DRAFT_HILO_INVOICE",
				fmt.Sprintf("%d", invoice.InvoiceID),
			})
		case enum.SignedBy.MEDX:
			res = model.InvoiceDB.UpdateOne(model.Invoice{InvoiceID: invoice.InvoiceID}, model.Invoice{
				PartnerStatus: model.InvoicePartnerStatus.Issuing,
				InvoiceV2Code: input.Code,
			})
		}

	}

	if input.InvoiceData != nil {
		var pdfUrl string
		for _, data := range *input.InvoiceData {
			if data.PdfUrl != "" {
				pdfUrl = data.PdfUrl
			}
		}

		if pdfUrl != "" {
			now := time.Now()
			res = model.InvoiceDB.UpdateOne(model.Invoice{InvoiceID: invoice.InvoiceID}, model.Invoice{
				InvoiceDocumentURL:   &pdfUrl,
				MailFrom:             "Hilo",
				IssuedTime:           &now,
				MailProcessingStatus: enum.MailProcessingStatus.SENT,
				SellerSentMail:       utils.ParseBoolToPointer(true),
				InvoiceStatus:        model.InvoiceStatus.Completed,
				PartnerStatus:        model.InvoicePartnerStatus.Completed,
			})

			if res.Status != common.APIStatus.Ok {
				return res
			}

			// sent invoice v1 file
			client.Services.Invoice.SendSellerExportedInvoice(model.SellerSendInvoice{
				OrderID:           invoice.OrderID,
				SellerCode:        invoice.SellerCode,
				InvoiceNo:         invoice.InvoiceV2Code,
				DeliveryOrderCode: invoice.DeliveryOrderCode,
				Filename:          fmt.Sprintf("invoice_%v_%v_%d", invoice.SellerCode, input.Code, invoice.OrderID),
				DocUrl:            pdfUrl,
			})

		}
	}

	if res == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "OK",
		}
	}

	return res
}

func ProcessNotifyInvoiceReturn(invoice *model.Invoice) {
	invoiceRes := model.InvoiceDB.Query(model.Invoice{
		OrderID:       invoice.OrderID,
		OrderSellerID: invoice.OrderSellerID,
	}, 0, 0, nil)

	if invoiceRes.Status != common.APIStatus.Ok {
		return
	}

	invoices := invoiceRes.Data.([]*model.Invoice)

	for _, invoice := range invoices {
		if invoice.ReturnStatus == nil || *invoice.ReturnStatus != model.InvoiceReturnStatusState.NEW {
			continue
		}

		if invoice.InvoiceStatus != model.InvoiceStatus.Completed {
			continue
		}

		if invoice.InvoiceDocumentURL == nil || *invoice.InvoiceDocumentURL == "" {
			continue
		}

		if invoice.Config == nil {
			continue
		}

		now := time.Now()
		mailDataItem := model.InvoiceEmailItem{
			OrderID:         invoice.OrderID,
			InvoiceCode:     invoice.InvoiceV2Code,
			DeliveredTime:   invoice.DeliveredTime,
			InvoiceDeadline: invoice.InvoiceDeadline,
			ReturnedTime:    &now,
		}

		mailData := model.InvoiceEmail{
			Invoices: []model.InvoiceEmailItem{mailDataItem},
		}

		emailTmpl, err := template.ParseFiles("template/hilo-invoice-returned.html")
		if err != nil {
			return
		}

		var contentBytes bytes.Buffer
		if err := emailTmpl.Execute(&contentBytes, mailData); err != nil {
			return
		}

		content := []model.Content{
			{
				Type:  "text/html",
				Value: contentBytes.String(),
			},
		}

		subject := "[Buymed] Thông báo phát sinh trả hàng – Yêu cầu kiểm tra và điều chỉnh hóa đơn"

		to := []model.Email{
			{
				Email: invoice.Config.SellerEmail,
				Name:  invoice.SellerCode,
			},
		}

		client.Services.MailService.SendMailHilo(to, subject, content, &[]string{
			"SEND_RETURNED_HILO_INVOICE",
			fmt.Sprintf("%d", invoice.InvoiceID),
		})
	}
}
