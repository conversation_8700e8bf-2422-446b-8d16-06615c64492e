package action

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetOrderList is func get list order have pagination
func GetOrderListByPic(account *model.Account, query *model.Order, skuCode string, offset, limit int64, getTotal, hasVoucher bool, sort string) *common.APIResponse {
	if account.AccountID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_ACCOUNT",
			Message:   "account ID is required",
		}
	}

	// get pic
	paramQ, _ := json.Marshal(
		model.Pic{
			AccountID: account.AccountID,
		})

	getPicParam := map[string]string{
		"q": string(paramQ),
	}

	getPicResp := client.Services.Customer.GetPicList(getPicParam)
	if getPicResp.Status != common.APIStatus.Ok {
		return getPicResp
	}

	pic := getPicResp.Data.([]*model.Pic)[0]

	//  build a tree to flatten the location
	tree := BuildTreeLocation(*pic.Provinces)
	leafCodes := DFSTree(tree)
	if len(leafCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Customer not found",
		}
	}

	var complexQuery = []*bson.M{}
	complexQuery = append(complexQuery, &bson.M{"flatten_location": bson.M{"$in": leafCodes}})
	if len(pic.Scopes) > 0 {
		complexQuery = append(complexQuery, &bson.M{"customer_scope": bson.M{"$in": pic.Scopes}})
	}

	if query.DateFrom != nil {
		complexQuery = append(complexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.DateFrom,
			},
		})
	}

	if query.DateTo != nil {
		complexQuery = append(complexQuery, &bson.M{
			"created_time": bson.M{
				"$lte": query.DateTo,
			},
		})
	}

	if query.PriceFrom != nil || query.PriceTo != nil {
		if query.PriceFrom == nil {
			t := 0
			query.PriceFrom = &t
		} else if query.PriceTo == nil {
			t := 10000000000 // 10 billions is too much
			query.PriceTo = &t
		}
		complexQuery = append(complexQuery, &bson.M{
			"total_price": bson.M{
				"$gte": query.PriceFrom,
				"$lte": query.PriceTo,
			},
		})
	}

	if len(query.StatusIn) > 0 {
		complexQuery = append(
			complexQuery,
			&bson.M{
				"status": bson.M{
					"$in": query.StatusIn,
				},
			},
		)
	} else {
		complexQuery = append(
			complexQuery,
			&bson.M{
				"status": bson.M{
					"$ne": nil,
				},
			},
		)
	}
	if len(query.SaleOrderCodeIn) > 0 {
		complexQuery = append(
			complexQuery,
			&bson.M{
				"sale_order_code": bson.M{
					"$in": query.SaleOrderCodeIn,
				},
			},
		)
	}

	if len(skuCode) > 0 {
		qResult := model.OrderDetailDB.Query(&bson.M{
			"skus": bson.M{"$in": []string{skuCode}},
		}, 0, 0, nil)
		if qResult.Status == common.APIStatus.Ok {
			orderDetails := qResult.Data.([]*model.OrderDetail)
			orderIds := make([]int64, 0, len(orderDetails))
			for _, od := range orderDetails {
				orderIds = append(orderIds, od.OrderID)
			}

			complexQuery = append(complexQuery, &bson.M{
				"order_id": bson.M{"$in": orderIds},
			})
		} else if qResult.Status == common.APIStatus.NotFound {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "NOT_FOUND",
				Message:   "Not found suitable order",
			}
		}
	}

	if query.RedeemCode != nil && len(*query.RedeemCode) > 0 {
		complexQuery = append(
			complexQuery,
			&bson.M{
				"redeem_code": bson.M{
					"$all": *query.RedeemCode,
				},
			})
		query.RedeemCode = nil
	}

	if hasVoucher {
		complexQuery = append(complexQuery, &bson.M{
			"redeem_code.0": bson.M{
				"$exists": true,
			},
		})
	}

	if len(query.Tags) > 0 {
		complexQuery = append(complexQuery, &bson.M{
			"tags": bson.M{"$in": query.Tags},
		})
		query.Tags = nil
	}

	if len(complexQuery) > 0 {
		query.ComplexQuery = complexQuery
	}

	mSort := primitive.M{"created_time": -1} // uu tien
	if sort == "-created_time" {
		mSort = primitive.M{"created_time": -1}
	} else if sort == "created_time" {
		mSort = primitive.M{"created_time": 1}
	}
	if query.SourceDetail != nil {
		// create query from source detail, then append to complex query
		query.ComplexQuery = append(query.ComplexQuery, createQueryFromSourceDetail(query.SourceDetail))

		// remove source detail to avoid error
		query.SourceDetail = nil
	}
	result := model.OrderDB.Query(query, offset, limit, &mSort)
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "ORDER_NOT_FOUND"
		return result
	}

	if getTotal {
		result.Total = model.OrderDB.Count(query).Total
	}
	return result
}

func GetCartListByPic(query *model.Cart, skuCode string, offset, limit int64, getTotal, hasVoucher bool, account model.Account) *common.APIResponse {
	if account.AccountID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			ErrorCode: "INVALID_ACCOUNT",
			Message:   "account ID is required",
		}
	}

	// get pic
	paramQ, _ := json.Marshal(
		model.Pic{
			AccountID: account.AccountID,
		})

	getPicParam := map[string]string{
		"q": string(paramQ),
	}

	// get pic
	getPicResp := client.Services.Customer.GetPicList(getPicParam)
	if getPicResp.Status != common.APIStatus.Ok {
		return getPicResp
	}

	pic := getPicResp.Data.([]*model.Pic)[0]

	//  build a tree to flatten the location
	tree := BuildTreeLocation(*pic.Provinces)
	leafCodes := DFSTree(tree)
	if len(leafCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Customer not found",
		}
	}
	query.ComplexQuery = append(
		query.ComplexQuery,
		&bson.M{"flatten_location": bson.M{"$in": leafCodes}},
	)

	if len(pic.Scopes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"customer_scope": bson.M{"$in": pic.Scopes}})
	}

	if len(skuCode) > 0 {
		qResult := model.CartItemDB.Query(&model.CartItem{Sku: skuCode}, 0, 0, nil)
		if qResult.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "NOT_FOUND",
				Message:   "Không tìm thấy giỏ hàng phù hợp",
			}
		}
		cartIds := make([]int64, 0)
		for _, item := range qResult.Data.([]*model.CartItem) {
			cartIds = append(cartIds, item.CartID)
		}
		if len(cartIds) == 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "NOT_FOUND",
				Message:   "Không tìm thấy giỏ hàng phù hợp",
			}
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"cart_id": bson.M{
				"$in": cartIds,
			},
		})
	}
	sortFields := &bson.M{
		"last_action_time": -1,
	}

	if query.PriceFrom != nil || query.PriceTo != nil {
		if query.PriceFrom == nil {
			t := 0
			query.PriceFrom = &t
		} else if query.PriceTo == nil {
			t := 10000000000 // 10 billions is too much
			query.PriceTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"total_price": bson.M{
				"$gte": query.PriceFrom,
				"$lte": query.PriceTo,
			},
		})
	}

	if query.TotalQuantityFrom != nil || query.TotalQuantityTo != nil {
		if query.TotalQuantityFrom == nil {
			t := 0
			query.TotalQuantityFrom = &t
		} else if query.TotalQuantityTo == nil {
			t := 100000
			query.TotalQuantityTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"total_quantity": bson.M{
				"$gte": query.TotalQuantityFrom,
				"$lte": query.TotalQuantityTo,
			},
		})
	}

	if query.TotalItemFrom != nil || query.TotalItemTo != nil {
		if query.TotalItemFrom == nil {
			t := 0
			query.TotalItemFrom = &t
		} else if query.TotalItemTo == nil {
			t := 1000
			query.TotalItemTo = &t
		}
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"total_item": bson.M{
				"$gte": query.TotalItemFrom,
				"$lte": query.TotalItemTo,
			},
		})
	}

	if query.RedeemCode != nil && len(*query.RedeemCode) > 0 {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"redeem_code": bson.M{
					"$all": *query.RedeemCode,
				},
			})
		query.RedeemCode = nil
	}

	if hasVoucher {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"redeem_code.0": bson.M{
				"$exists": true,
			},
		})
	}

	result := model.CartDB.Query(query, offset, limit, sortFields)
	// if notfound, try fallback with phone number and search customer do not have district
	if result.Status == common.APIStatus.NotFound && len(query.CustomerPhone) > 0 {
		// filter flatten location condition
		query.ComplexQuery = unsetfilterComplexQuery(query.ComplexQuery, []string{"flatten_location"})
		provinceCodes := DFSTree(tree, province)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"province_code": bson.M{
				"$in": provinceCodes,
			},
		})
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": []*bson.M{
				{
					"district_code": "",
				},
				{
					"district_code": nil,
				},
			},
		})

		result = model.CartDB.Query(query, offset, limit, sortFields)
	}

	if result.Status != common.APIStatus.Ok {
		return result
	}
	if getTotal {
		countResult := model.CartDB.Count(query)
		result.Total = countResult.Total
	}

	return result
}

func FlatLocationOrder(order model.Order) []string {
	flatten := []string{}
	if order.ProvinceCode != "" {
		flatten = append(flatten, order.ProvinceCode)
	}

	if order.DistrictCode != "" {
		flatten = append(flatten, order.DistrictCode)
	}
	if order.WardCode != "" {
		flatten = append(flatten, order.WardCode)
	}

	return flatten
}

func FlatLocationCart(cart model.Cart) []string {
	flatten := []string{}
	if cart.ProvinceCode != "" {
		flatten = append(flatten, cart.ProvinceCode)
	}

	if cart.DistrictCode != "" {
		flatten = append(flatten, cart.DistrictCode)
	}
	if cart.WardCode != "" {
		flatten = append(flatten, cart.WardCode)
	}

	return flatten
}
