package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"time"
)

// UserExtraFeeHistory is model to define extra fee history
type UserExtraFeeHistory struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	ExpireTime      *time.Time          `json:"expireTime,omitempty" bson:"expire_time,omitempty"`

	CustomerID  int64                  `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	ExtraFeeUse map[string]ExtraFeeUse `json:"extraFeeUse,omitempty" bson:"extra_fee_use,omitempty"` // key is level code, value is ExtraFeeUse
}

type ExtraFeeUse struct {
	Max  int64 `json:"max,omitempty" bson:"max,omitempty"`   // times of use
	Used int64 `json:"used,omitempty" bson:"used,omitempty"` // times of used
}

// UserExtraFeeHistoryDB is db instance of UserExtraFeeHistory
var UserExtraFeeHistoryDB = &db.Instance{
	ColName:        "user_extra_fee_history",
	TemplateObject: &UserExtraFeeHistory{},
}

// InitUserExtraFeeHistoryDB is to apply database to UserExtraFeeHistoryDB
func InitUserExtraFeeHistoryDB(s *mongo.Database) {
	UserExtraFeeHistoryDB.ApplyDatabase(s)

	// index
	t := true
	_ = UserExtraFeeHistoryDB.CreateIndex(bson.D{
		bson.E{Key: "customer_id", Value: 1},
	}, &options.IndexOptions{
		Unique: &t,
	})

	// ttl index in expireTime, 1 second after expireTime, remove document
	ttlExpireTime := int32(1)
	_ = UserExtraFeeHistoryDB.CreateIndex(bson.D{
		bson.E{Key: "expire_time", Value: 1},
	}, &options.IndexOptions{
		ExpireAfterSeconds: &ttlExpireTime,
	})
}
