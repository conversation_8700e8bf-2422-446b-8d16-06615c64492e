package api

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func AuditOrderTrigger(req sdk.APIRequest, resp sdk.APIResponder) error {
	go action.AuditOrderWarehouse()
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
	})
}

func AuditAllOrderTrigger(req sdk.APIRequest, resp sdk.APIResponder) error {
	var query = model.OrderAudit{}

	t1 := time.Date(2021, time.November, 1, 0, 0, 0, 0, time.UTC)
	t := t1.Add(-7 * time.Hour)
	query.ComplexQuery = []*bson.M{
		&bson.M{
			"ordered_at": bson.M{
				"$gte": t,
			},
		},
		&bson.M{
			"status": "PENDING",
		},
	}
	if req.GetParam("latest") == "false" {
		query.ComplexQuery = []*bson.M{
			&bson.M{
				"ordered_at": bson.M{
					"$lte": t,
				},
			},
			&bson.M{
				"status": "PENDING",
			},
		}
	}

	go action.UpdateSyncAllAudit(&query)

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "",
	})
}

func AuditOrderGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		prev     = req.GetParam("latest") == "false"
	)
	if limit < 0 {
		limit = 20
	}
	if limit > 1000 {
		limit = 1000
	}
	if offset < 0 {
		offset = 0
	}
	var query = model.OrderAudit{
		Status: "PENDING",
	}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}

	if !prev {
		t1 := time.Date(2021, time.November, 1, 0, 0, 0, 0, time.UTC)
		t := t1.Add(-7 * time.Hour)
		query.ComplexQuery = []*bson.M{
			&bson.M{
				"ordered_at": bson.M{
					"$gte": t,
				},
			},
		}
	}

	return resp.Respond(action.GetListAuditOrder(&query, offset, limit, getTotal))
}

func AuditOrderLineGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
	)
	if limit < 0 {
		limit = 20
	}
	if limit > 1000 {
		limit = 1000
	}
	if offset < 0 {
		offset = 0
	}
	var query = model.OrderLineAudit{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}
	return resp.Respond(action.GetListAuditOrderLine(&query, offset, limit, getTotal))
}

func AuditOrderLineUpdateSync(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		orderID = sdk.ParseInt64(req.GetParam("orderID"), 0)
	)

	if orderID <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Order ID invalid",
			ErrorCode: "ID_INVALID",
		})
	}
	return resp.Respond(action.UpdateSyncAuditOrderLine(orderID, true))
}

func AuditOrderBillGetList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		prev     = req.GetParam("latest") == "false"
	)
	if limit < 0 {
		limit = 20
	}
	if limit > 1000 {
		limit = 1000
	}
	if offset < 0 {
		offset = 0
	}
	var query = model.OrderBillAudit{
		Status: "PENDING",
	}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}

	if !prev {
		t1 := time.Date(2021, time.November, 20, 0, 0, 0, 0, time.UTC)
		t := t1.Add(-7 * time.Hour)
		query.ComplexQuery = []*bson.M{
			&bson.M{
				"ordered_at": bson.M{
					"$gte": t,
				},
			},
		}
	}

	return resp.Respond(action.GetListAuditOrderBill(&query, offset, limit, getTotal))
}

// func AuditAllOrderBillTrigger(req sdk.APIRequest, resp sdk.APIResponder) error {
// 	go action.TrigerAuditOrderBill()

// 	return resp.Respond(&common.APIResponse{
// 		Status:  common.APIStatus.Ok,
// 		Message: "",
// 	})
// }

func AuditOrderBillUpdateSync(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		orderID = sdk.ParseInt64(req.GetParam("orderID"), 0)
	)

	if orderID <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Order ID invalid",
			ErrorCode: "ID_INVALID",
		})
	}
	return resp.Respond(action.UpdateSyncAuditOrderBill(orderID, true))
}
