# Project customer order service.
## Indexes


db.getCollection("order").ensureIndex({order_code:1},{background:true, unique:true})
db.getCollection("order").ensureIndex({sale_order_code:1},{background:true})
db.getCollection("order").ensureIndex({status:1, created_time:1},{background:true})
db.getCollection("order").ensureIndex({status:1, delivered_time:1},{background:true})
db.getCollection("order").ensureIndex({status:1, completed_time:1, last_updated_time:1},{background:true})
db.getCollection("order").ensureIndex({customer_code:1, created_time:1},{background:true, unique:true})
db.getCollection("order").ensureIndex({created_time:1},{background:true})
db.getCollection("order").ensureIndex({created_time:1, customer_id:1},{background:true})
db.getCollection("order").ensureIndex({created_time:1, payment_method:1},{background:true})
db.getCollection("order").ensureIndex({status:1, customer_id:1},{background:true})
db.getCollection("order").ensureIndex({status:1, is_split_delivery_order:1},{background:true})

db.getCollection("order").ensureIndex({last_updated_time:1},{background:true})
db.getCollection("order").ensureIndex({customer_order_index:1},{background:true})

<!-- reporting -->

// db.getCollection("order").ensureIndex({
// "last_updated_time" : 1,
// },{"background":true})

db.getCollection("order_update_log").ensureIndex({order_id:1},{background:true})
db.getCollection("order_update_log").ensureIndex({code:1},{background:true})
db.getCollection("order_update_log").ensureIndex({order_id:1, action:1},{background:true})
db.getCollection("order_update_log").ensureIndex({order_id:1, status:1, action:1},{background:true})

db.getCollection("order_calc_log").ensureIndex({order_id:1},{background:true})
db.getCollection("order_calc_log").ensureIndex({order_id:1, action:1},{background:true})

db.getCollection("cart").ensureIndex({customer_id:1},{background:true, unique:true})
db.getCollection("cart_item").ensureIndex({cart_no:1, cart_id:1, sku:1, type:1},{background:true, unique:true})
db.getCollection("cart_item").ensureIndex({cart_id:1},{background:true})

db.getCollection("order_item").ensureIndex({order_code:1, order_id:1, sku:1, type:1},{background:true, unique:true})
db.getCollection("order_item").ensureIndex({sku:1, created_time},{background:true})
db.getCollection("order_item").ensureIndex({order_id:1, last_updated_time:1},{background:true})
db.getCollection("order_item").ensureIndex({order_id:1, last_updated_time:1, completed_quantity:1},{background:true})
db.getCollection("order_item").ensureIndex({order_id:1, none_vat:1, seller_code:1, outbound_quantity:1},{background:true})
db.getCollection("order_item").ensureIndex({product_code:1},{background:true})
db.getCollection("order_item").ensureIndex({order_id:1, seller_code:1, outbound_quantity:1, sub_items:1},{background:true})
db.getCollection("order_item").ensureIndex({deal_code:1},{background:true})
db.getCollection("order_item").ensureIndex({order_id:1, sku:1},{background:true})
db.getCollection("order_item").ensureIndex({product_id:1, seller_code:1},{background:true})
db.getCollection("order_item").ensureIndex({sub_items.sku:1, completed_quantity:1, created_time:1},{background:true})
db.getCollection("order_item").ensureIndex({seller_code:1},{background:true})
db.getCollection("order_item").ensureIndex({"skus.sku":1, "order_id":1},{background:true})
db.getCollection("order_item").ensureIndex({"skus.product_id":1, "order_id":1},{background:true})

<!-- reporting -->

db.getCollection("order_item").ensureIndex({
"created_time" : 1,
"sku" : 1,
},{"background":true})
db.getCollection("order_item").ensureIndex({
"created_time" : 1,
"skus.sku" : 1,
},{"background":true})
db.getCollection("order_item").ensureIndex({
"last_updated_time" : 1,
"sku" : 1,
},{"background":true})

db.getCollection("deal_apply_result").ensureIndex({sku:1},{background:true})
db.getCollection("deal_apply_result").ensureIndex({deal_code:1},{background:true})
db.getCollection("sku_apply_result").ensureIndex({sku:1},{background:true, unique:true})

db.getCollection("order-seller").ensureIndex({order_seller_code:1},{background:true, unique:true})
db.getCollection("order-seller").ensureIndex({order_seller_id:1},{background:true, unique:true})
db.getCollection("order-seller").ensureIndex({seller_code:1, created_time:1},{background:true})
db.getCollection("order-seller").ensureIndex({status:1, created_time:1},{background:true})
db.getCollection("order-seller").ensureIndex({reconciliation_status:1, created_time:1},{background:true})
db.getCollection("order-seller").ensureIndex({seller_code:1, reconciliation_status:1, created_time:1},{background:true})
db.getCollection("order-seller").ensureIndex({seller_code:1, created_time:1, customer_phone:1},{background:true})
db.getCollection("order-seller").ensureIndex({seller_code:1, customer_phone:1},{background:true})
db.getCollection("order-seller").ensureIndex({seller_code:1, customer_email:1},{background:true})
db.getCollection("order-seller").ensureIndex({seller_code:1, parent_order_id:1},{background:true})
db.getCollection("order-seller").createIndex({seller_code:1, required_license:1},{background:true})
db.getCollection("order-seller").ensureIndex({sale_order_code:1},{background:true})

db.getCollection("order-seller-item").ensureIndex({order_seller_code:1, order_seller_id:1, sku:1, type:1},{background:true, unique:true})
db.getCollection("order-seller-item").ensureIndex({seller_code:1, order_seller_id:1},{background:true})

db.getCollection("invoice").ensureIndex({invoice_code:1},{background:true, unique:true})
db.getCollection("invoice").ensureIndex({invoice_id:1},{background:true, unique:true})
db.getCollection("invoice").ensureIndex({order_seller_id:1},{background:true, unique:true})
db.getCollection("invoice").ensureIndex({seller_code:1, order_id:1},{background:true})
db.getCollection("invoice").ensureIndex({seller_code:1, order_code:1},{background:true, unique:true})
db.getCollection("invoice").ensureIndex({seller_code:1, order_id:1},{background:true, unique:true})
db.getCollection("invoice").ensureIndex({seller_code:1, invoice_status:1, created_time:true},{background:true})
db.getCollection("invoice").ensureIndex({invoice_status:1, created_time:1},{background:true})
db.getCollection("invoice").createIndex({invoice_status:1, invoice_deadline:1},{background:true})
db.getCollection("invoice").ensureIndex({return_status: 1},{background:true})
db.getCollection("invoice_item").ensureIndex({invoice_code:1},{background:true})
db.getCollection("invoice_item").ensureIndex({seller_code:1, sku: 1},{background:true})

db.getCollection("reconciliation").ensureIndex({seller_code:1, reconcile_schedule_time_index:true},{background:true, unique:true})
db.getCollection("reconciliation").ensureIndex({seller_code:1, reconciliation_status:true},{background:true})

db.getCollection("reconciliation_error").ensureIndex({seller_code:1, index:true},{background:true})
db.getCollection("reconciliation_spec_revenue").ensureIndex({order_id:1, product_id:true, schedule:1},{background:true})
db.getCollection("reconciliation_spec_revenue").ensureIndex({order_id:1, sku:true, schedule:1},{background:true})
db.getCollection("cart_limit").ensureIndex({code:1},{background:true, unique:true})

db.getCollection("sku_limit_history").ensureIndex({code:1},{background:true, unique:true})
db.getCollection("sku_limit_history").ensureIndex({sku_limit_code:1},{background:true})
db.getCollection("sku_limit_history").ensureIndex({account_id:1},{background:true})
db.getCollection("sku_limit_history").ensureIndex({customer_id:1},{background:true})
db.getCollection("sku_limit_history").ensureIndex({is_active:1},{background:true})
db.getCollection("sku_limit_history").ensureIndex({sku:1},{background:true})
db.getCollection("sku_limit_history").ensureIndex({start_time:1},{background:true})
db.getCollection("sku_limit_history").ensureIndex({end_time:1},{background:true})
db.getCollection("sku_limit_history").ensureIndex({customer_id: 1, sku_limit_code:1, start_time: 1, end_time: 1},{background:true})
db.getCollection("sku_limit_history").ensureIndex({customer_id: 1, sku:1, start_time: 1, end_time: 1},{background:true})
db.getCollection("sku_limit_history").ensureIndex({account_id: 1, sku: 1, start_time: 1, end_time: 1, is_active: 1},{background:true})

db.getCollection("cart_deleted").ensureIndex({ref_cart_item:1},{background:true})
db.getCollection("cart_deleted").ensureIndex({cart_id:1},{background:true})
db.getCollection("cart_deleted").ensureIndex({cart_no:1},{background:true})
db.getCollection("cart_deleted").ensureIndex({account_id:1},{background:true})
db.getCollection("cart_deleted").ensureIndex({customer_id:1},{background:true})

db.getCollection("cart_item_deleted").ensureIndex({ref_cart_item:1},{background:true})
db.getCollection("cart_item_deleted").ensureIndex({cart_id:1},{background:true})
db.getCollection("cart_item_deleted").ensureIndex({cart_no:1},{background:true})
db.getCollection("cart_item_deleted").ensureIndex({sku:1},{background:true})

db.invoice.ensureIndex({"config.partner_code":1, "config.signed_by":1},{background:true})

#new 08Jun2023
db.getCollection("order").ensureIndex({tags:1, status:1},{background:true})

#new 02Aug2023
db.getCollection("run_one").ensureIndex({"key":1},{background:true, unique:true})
db.getCollection("run_one").ensureIndex({created_time: 1}, {background: true, expireAfterSeconds: 2592000 })

## Author

- tamth

## Contents

- [Project customer order service.](#project-customer-order-service)
  - [Indexes](#indexes)
  - [Author](#author)
  - [Contents](#contents)
  - [Supported Versions](#supported-versions)
  - [Descriptions](#descriptions)
  - [How to build and install](#how-to-build-and-install)
  - [How to deploy](#how-to-deploy)
- [Postman link](#postman-link)

## Supported Versions

- Go 1.15.2

## Descriptions

Service order

## How to build and install

    go run main.go

## How to deploy

You can remote to your server, then clone git source to that local.
Next build with command

    docker run -d image:latest

# Postman link

    https://www.getpostman.com/collections/b51d8612f4a7a5286128
