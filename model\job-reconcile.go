package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"go.mongodb.org/mongo-driver/mongo"
)

var SendInvoiceJob = job.Executor{ColName: "send_invoice_job"}

func InitSendInvoiceJob(database *mongo.Database, consumer job.ExecutionFn) {
	SendInvoiceJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
		ChannelCount:     10,
	})

	SendInvoiceJob.SetConsumer(consumer)
}

var ChargeSellerIncreasePriceJob = job.Executor{ColName: "charge_seller_increase_price_job"}

func InitChargeSellerIncreasePriceJob(database *mongo.Database, consumer job.ExecutionFn) {
	ChargeSellerIncreasePriceJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:    true,
		FailThreshold:       10,
		ChannelCount:        10,
		MaximumWaitToRetryS: 30,
	})

	ChargeSellerIncreasePriceJob.SetConsumer(consumer)
}

var ChargePoorQualityProductJob = job.Executor{ColName: "charge_poor_quality_product_job"}

func InitChargePoorQualityProductJob(database *mongo.Database, consumer job.ExecutionFn) {
	ChargePoorQualityProductJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:    true,
		FailThreshold:       10,
		ChannelCount:        10,
		MaximumWaitToRetryS: 30,
	})

	ChargePoorQualityProductJob.SetConsumer(consumer)
}

var CalculateBizHouseholdTax = job.Executor{ColName: "calculate_biz_household_tax"}

func InitCalculateBizHouseholdTax(database *mongo.Database, consumer job.ExecutionFn) {
	CalculateBizHouseholdTax.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:    true,
		FailThreshold:       2,
		ChannelCount:        10,
		MaximumWaitToRetryS: 30,
	})

	CalculateBizHouseholdTax.SetConsumer(consumer)
}
