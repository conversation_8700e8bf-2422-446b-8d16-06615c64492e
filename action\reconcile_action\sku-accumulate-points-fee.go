package reconcile_action

import (
	"fmt"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
)

const accumulateProductPointsRate = 0.04

func ProcessSkuAccumulatePointsFee(
	reconciliation *model.Reconciliation,
	reconciliationItem *model.ReconciliationItem,
	skuAccumulatePoints *client.SkuAccumulatePoints,
) {
	if reconciliation == nil ||
		reconciliationItem == nil ||
		skuAccumulatePoints == nil {
		return
	}

	if skuAccumulatePoints.ExpiredTime != nil &&
		skuAccumulatePoints.ExpiredTime.Before(time.Now()) {
		return
	}

	riFilter := model.ReconciliationItem{
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		SellerCode:                 reconciliationItem.SellerCode,
		FeeType:                    enum.FeeType.ACCUMULATE_POINTS_FEE,
		AutoPenaltyFee:             true,

		OrderID:       reconciliationItem.OrderID,
		OrderCode:     reconciliationItem.OrderCode,
		SaleOrderCode: reconciliationItem.SaleOrderCode,
		Sku:           reconciliationItem.Sku,
	}

	penaltyDescription := fmt.Sprintf(
		"Phí hàng điểm đơn hàng #%d, sản phẩm #%d đợt %s ~ %s",
		reconciliationItem.OrderID,
		reconciliationItem.ProductId,
		utils.FormatTimeString(reconciliation.FromTime),
		utils.FormatTimeString(reconciliation.ToTime),
	)

	model.ReconciliationItemDB.Upsert(
		riFilter,
		bson.M{
			"penalty_description": penaltyDescription,
			"penalty_bm_fee":      getSkuAccumulatePointsFee(reconciliationItem),
		},
	)
}

func getSkuAccumulatePointsFee(reconciliationItem *model.ReconciliationItem) int {
	if reconciliationItem == nil ||
		reconciliationItem.Price == nil ||
		reconciliationItem.DeliveryQuantity == nil {
		return 0
	}

	totalRevenue := *reconciliationItem.Price * *reconciliationItem.DeliveryQuantity
	return int(float64(totalRevenue) * accumulateProductPointsRate)
}
