package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// DeliveryLimitation ...
type DeliveryLimitation struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	IsLimitAll    *bool     `json:"isLimitAll,omitempty" bson:"is_limit_all,omitempty"`
	ProvinceCodes *[]string `json:"provinceCodes,omitempty" bson:"province_codes,omitempty"`
	DistrictCodes *[]string `json:"districtCodes,omitempty" bson:"district_codes,omitempty"`
	WardCodes     *[]string `json:"wardCodes,omitempty" bson:"ward_codes,omitempty"`
	Message       string    `json:"message,omitempty" bson:"message,omitempty"`
}

// DeliveryLimitationDB ...
var DeliveryLimitationDB = &db.Instance{
	ColName:        "delivery_limitation",
	TemplateObject: &DeliveryLimitation{},
}

var LstDeliveryLimitation map[string]string

// InitDeliveryLimitationDBModel is func init model delivery limitation
func InitDeliveryLimitationDBModel(s *mongo.Database) {
	LstDeliveryLimitation = make(map[string]string)
	DeliveryLimitationDB.ApplyDatabase(s)
}
