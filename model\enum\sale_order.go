package enum

type SaleOrderStateValue string

type saleOrderStatus struct {
	Draft          SaleOrderStateValue
	Confirmed      SaleOrderStateValue
	Reserving      SaleOrderStateValue
	WaitToPick     SaleOrderStateValue
	Picking        SaleOrderStateValue
	WaitToCheck    SaleOrderStateValue
	Checking       SaleOrderStateValue
	WaitToPack     SaleOrderStateValue
	Packing        SaleOrderStateValue
	WaitToDelivery SaleOrderStateValue
	Transporting   SaleOrderStateValue
	Delivering     SaleOrderStateValue
	Delivered      SaleOrderStateValue
	Completed      SaleOrderStateValue
	Return         SaleOrderStateValue
	Returning      SaleOrderStateValue
	Returned       SaleOrderStateValue
	Damage         SaleOrderStateValue
	Lost           SaleOrderStateValue
	Cancel         SaleOrderStateValue
}

var SaleOrderStatus = &saleOrderStatus{
	"DRAFT",
	"CONFIRMED",
	"RESERVING",
	"WAIT_TO_PICK",
	"PICKING",
	"WAIT_TO_CHECK",
	"CHECKING",
	"WAIT_TO_PACK",
	"PACKING",
	"WAIT_TO_DELIVERY",
	"TRANSPORTING",
	"DELIVERING",
	"DELIVERED",
	"COMPLETED",
	"RETURN",
	"RETURNING",
	"RETURNED",
	"DAMAGE",
	"LOST",
	"CANCEL",
}

type MethodValue string

type paymentMethodType struct {
	COD  MethodValue
	BANK MethodValue
}

var PaymentMethodType = &paymentMethodType{
	"COD",
	"BANK",
}
