package conf

import (
	"fmt"
	"os"
	"time"

	"gitlab.buymed.tech/sdk/golang/configuration"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

type config struct {
	Env      string
	Protocol string
	Version  string

	// OrderDBName             string
	// OrderAuthDB             string
	// CartDBName              string
	// CartAuthDB              string
	// LogDBName               string
	// LogAuthDB               string
	// CacheDBName             string
	// CacheAuthDB             string
	// JobDBName               string
	// JobAuthDB               string

	CartDBConf           configuration.Database
	MainDBConf           configuration.Database
	LogDBConf            configuration.Database
	JobDBConf            configuration.Database
	CacheDBConf          configuration.Database
	CustomerCacheDBConf  configuration.Database
	PromotionCacheDBConf configuration.Database

	APIHost                  string
	APIKey                   string
	AutoConfirmOrderTime     int
	NextSchedule             time.Duration
	IsSendSMSOrderConfirmed  bool
	BotTeleToken             string
	BotTeleChannelID         int64
	BotMonitorPriceToken     string
	BotMonitorPriceChannelID int64
	InternalURL              string

	BillingHost  string
	BillingToken string
	SSOHost      string
	SSOToken     string

	BuymedAPIHost string
	BuymedAPIKey  string

	ZnsDevMode bool

	AllowConfirmReconciliationAfter int // in days
	FromSellerCenterEmail           model.Email
	BuymedCsMail                    string

	HiloBccNotify []model.Email

	FeeInvoice time.Duration

	ForceSplitOrder string

	CustomerIDSkipValidateAmount []int64
	CustomerIDAutoConfirm        []int64
	BrandOrderTags               []string

	MaxOrderIDSkip int
	FrontendURL    string

	OnepayForwardFromKey string
	WarmupCustomerSKUs   int // minutes

	KiotvietAPIHost string
	KiotvietAPIKey  string

	ReconciliationTime int

	// please update on product-v2 service as well
	ProductFeeStartTime time.Time
}

// Config main config object
var (
	Config                  *config
	FixedLocation                    = time.FixedZone("Asia/VN", 7*3600)
	FirstDate2022                    = time.Date(2022, 1, 1, 0, 0, 0, 0, FixedLocation)
	TimeDiff                         = -7 * time.Hour
	DENX_SELLER             string   = "DENX"
	DENTAL_SELLERS          []string = []string{"DENX"}
	SELLERS_NOT_SHOW_BUYMED []string = []string{}
)

func init() {
	env := os.Getenv("env")
	protocol := os.Getenv("protocol")
	version := os.Getenv("version")
	switch env {

	// config for dev
	case "dev":
		Config = &config{
			Env:      "dev",
			Protocol: protocol,
			Version:  version,
			// OrderDBName:             "marketplace_dev_order_v2",
			// OrderAuthDB:             "admin",
			// CartDBName:              "marketplace_dev_cart_v2",
			// CartAuthDB:              "admin",
			// CacheDBName:             "marketplace_dev_product-v2",
			// CacheAuthDB:             "admin",
			// LogDBName:               "marketplace_dev_order-v2_log",
			// LogAuthDB:               "admin",
			AutoConfirmOrderTime: 2,
			NextSchedule:         2 * time.Hour,
			// JobDBName:               "marketplace_dev_order-v2_job",
			// JobAuthDB:               "admin",
			IsSendSMSOrderConfirmed: false,
			BotTeleToken:            "5388007145:AAE2KkkLC2HUcKxk5LV1x2unnCaToEW5hew",
			BotTeleChannelID:        -1001591346241,
			InternalURL:             "https://internal.v2-dev.thuocsi.vn/crm/order",

			AllowConfirmReconciliationAfter: 1,
			FromSellerCenterEmail: model.Email{
				Email: "<EMAIL>",
				Name:  "Admin SC test",
			},
			BuymedCsMail: "<EMAIL>",

			HiloBccNotify: []model.Email{
				{
					Email: "<EMAIL>",
				},
			},

			FeeInvoice: 5 * time.Minute,

			ForceSplitOrder: "FORCE_SPLIT_ORDER",
			BrandOrderTags:  []string{"OPV"},

			MaxOrderIDSkip:     1000000,
			FrontendURL:        "https://web.v2-dev.thuocsi.vn",
			WarmupCustomerSKUs: 1 * 60,

			ReconciliationTime: 16,

			ProductFeeStartTime: time.Date(2100, 10, 10, 0, 0, 0, 0, utils.VNTimeZone),
		}
		SELLERS_NOT_SHOW_BUYMED = []string{"MEDX_E"}

	case "stg":
		Config = &config{
			Env:      "stg",
			Protocol: protocol,
			Version:  version,
			// OrderDBName: "marketplace_stg_order_v2",
			// OrderAuthDB: "admin",
			// CartDBName:  "marketplace_stg_cart_v2",
			// CartAuthDB:  "admin",
			// CacheDBName: "marketplace_stg_product-v2",
			// CacheAuthDB: "admin",
			// LogDBName:   "marketplace_stg_order-v2_log",
			// LogAuthDB:   "admin",
			// APIHost:     "http://proxy-service.frontend-stg",
			AutoConfirmOrderTime: 5,
			NextSchedule:         2 * time.Hour,
			// JobDBName:               "marketplace_stg_order-v2_job",
			// JobAuthDB:               "admin",
			IsSendSMSOrderConfirmed:  false,
			BotTeleToken:             "5388007145:AAE2KkkLC2HUcKxk5LV1x2unnCaToEW5hew",
			BotTeleChannelID:         -1001548696654,
			BotMonitorPriceToken:     "7153514560:AAEmHYN0XGTDa8OUXd-FsTXD9LHPWx17LVA",
			BotMonitorPriceChannelID: -4130216075,
			InternalURL:              "https://internal.v2-stg.thuocsi.vn/crm/order",

			AllowConfirmReconciliationAfter: 1,
			FromSellerCenterEmail: model.Email{
				Email: "<EMAIL>",
				Name:  "Admin SC test",
			},
			BuymedCsMail: "<EMAIL>",
			HiloBccNotify: []model.Email{
				{
					Email: "<EMAIL>",
					Name:  "Chị Linh",
				},
				{
					Email: "<EMAIL>",
				},
			},

			FeeInvoice: 5 * time.Minute,

			ForceSplitOrder: "FORCE_SPLIT_ORDER",

			CustomerIDAutoConfirm: []int64{},
			BrandOrderTags:        []string{"OPV"},

			MaxOrderIDSkip: 1000000,
			FrontendURL:    "https://web.v2-stg.thuocsi.vn",

			ZnsDevMode:         true,
			WarmupCustomerSKUs: 1 * 60,

			ReconciliationTime: 16,

			ProductFeeStartTime: time.Date(2025, 04, 18, 0, 0, 0, 0, utils.VNTimeZone),
		}

		DENTAL_SELLERS = append(DENTAL_SELLERS, "TJRRRRRRRR")
		SELLERS_NOT_SHOW_BUYMED = []string{"MEDX_E"}

	case "uat":
		Config = &config{
			Env:      "uat",
			Protocol: protocol,
			Version:  version,
			// OrderDBName:             "marketplace_prd_order-v2",
			// OrderAuthDB:             "marketplace_prd_order-v2",
			// CartDBName:              "marketplace_prd_cart",
			// CartAuthDB:              "marketplace_prd_cart",
			// LogDBName:               "marketplace_uat_order-v2_log",
			// LogAuthDB:               "admin",
			// CacheDBName:             "marketplace_prd_product-v2",
			// CacheAuthDB:             "admin",
			AutoConfirmOrderTime: 30,
			// JobDBName:               "marketplace_uat_order-v2_job",
			// JobAuthDB:               "admin",
			IsSendSMSOrderConfirmed: true,

			AllowConfirmReconciliationAfter: 1,
			FromSellerCenterEmail: model.Email{
				Email: "<EMAIL>",
				Name:  "Admin Seller Center",
			},
			BuymedCsMail: "<EMAIL>",

			HiloBccNotify: []model.Email{
				{
					Email: "<EMAIL>",
					Name:  "Chị Linh",
				},
				{
					Email: "<EMAIL>",
				},
			},

			FeeInvoice: 6 * time.Hour,

			ForceSplitOrder: "FORCE_SPLIT_ORDER",

			CustomerIDAutoConfirm: []int64{225556},
			BrandOrderTags:        []string{"OPV"},

			MaxOrderIDSkip: 10000000,
			FrontendURL:    "https://web.v2-uat.thuocsi.vn",

			ZnsDevMode: true,

			BotMonitorPriceToken:     "7153514560:AAEmHYN0XGTDa8OUXd-FsTXD9LHPWx17LVA",
			BotMonitorPriceChannelID: -4187635289,
			WarmupCustomerSKUs:       3 * 60,

			ReconciliationTime: 2,

			// please update on product-v2 service as well
			ProductFeeStartTime: time.Date(2025, 5, 5, 0, 0, 0, 0, utils.VNTimeZone),
		}

		DENTAL_SELLERS = append(DENTAL_SELLERS, "FD1YACBVI4")
		SELLERS_NOT_SHOW_BUYMED = []string{"MEDX_E"}

	case "prd":
		Config = &config{
			Env:      "prd",
			Protocol: protocol,
			Version:  version,
			// OrderDBName:             "marketplace_prd_order-v2",
			// OrderAuthDB:             "marketplace_prd_order-v2",
			// CartDBName:              "marketplace_prd_cart",
			// CartAuthDB:              "marketplace_prd_cart",
			// LogDBName:               "marketplace_prd_order-v2_log",
			// LogAuthDB:               "admin",
			// CacheDBName:             "marketplace_prd_product-v2",
			// CacheAuthDB:             "admin",
			AutoConfirmOrderTime: 30,
			// JobDBName:               "marketplace_prd_order-v2_job",
			// JobAuthDB:               "admin",
			IsSendSMSOrderConfirmed: true,
			BotTeleToken:            "5388007145:AAE2KkkLC2HUcKxk5LV1x2unnCaToEW5hew",
			BotTeleChannelID:        -1001760828817,
			InternalURL:             "https://internal.thuocsi.vn/crm/order",

			AllowConfirmReconciliationAfter: 1,
			FromSellerCenterEmail: model.Email{
				Email: "<EMAIL>",
				Name:  "Admin Seller Center",
			},
			BuymedCsMail: "<EMAIL>",

			HiloBccNotify: []model.Email{
				// {
				// 	Email: "<EMAIL>",
				// },
				{
					Email: "<EMAIL>",
				},
			},
			FeeInvoice: 6 * time.Hour,

			ForceSplitOrder: "FORCE_SPLIT_ORDER",

			CustomerIDAutoConfirm: []int64{225556},
			BrandOrderTags:        []string{"OPV"},

			MaxOrderIDSkip: 10000000,
			FrontendURL:    "https://thuocsi.vn",

			ZnsDevMode: false,

			BotMonitorPriceToken:     "7153514560:AAEmHYN0XGTDa8OUXd-FsTXD9LHPWx17LVA",
			BotMonitorPriceChannelID: -4187635289,
			WarmupCustomerSKUs:       3 * 60,

			ReconciliationTime: 2,

			// please update on product-v2 service as well
			ProductFeeStartTime: time.Date(2025, 5, 5, 0, 0, 0, 0, utils.VNTimeZone),
		}

		DENTAL_SELLERS = append(DENTAL_SELLERS, "FD1YACBVI4")
		SELLERS_NOT_SHOW_BUYMED = []string{"MEDX_E", "MEDC"}

	}

	if protocol == "" {
		protocol = "THRIFT"
	}

	// DB Config
	{
		dbFormat := "marketplace_%s_order_v2"
		dbFormatPRD := "marketplace_%s_order-v2"

		dbCartFormat := "marketplace_%s_cart_v2"
		dbCartFormatPRD := "marketplace_%s_cart"

		dbCacheFormat := "marketplace_%s_product-v2"
		if env == "uat" {
			dbCacheFormat = "marketplace_prd_product-v2"
		}

		dbCustomerCacheFormat := "marketplace_%s_customer"
		if env == "uat" {
			dbCustomerCacheFormat = "marketplace_prd_customer"
		}

		dbPromotionCacheFormat := "marketplace_%s_promotion"
		if env == "uat" {
			dbPromotionCacheFormat = "marketplace_prd_promotion"
		}

		dbLogFormat := dbFormatPRD + "_log"
		dbJobFormat := dbFormatPRD + "_job"

		// _mapDB := getDatabaseConfig()
		if env == "stg" || env == "dev" {
			Config.MainDBConf = fineMainDBConfig(configuration.Get("db").ToDatabaseConfig(), dbFormat, env)
			Config.CartDBConf = fineCartDBConfig(configuration.Get("marketplaceCartDB").ToDatabaseConfig(), dbCartFormat, env)

		} else {
			Config.MainDBConf = fineMainDBConfig(configuration.Get("db").ToDatabaseConfig(), dbFormatPRD, env)
			Config.CartDBConf = fineCartDBConfig(configuration.Get("marketplaceCartDB").ToDatabaseConfig(), dbCartFormatPRD, env)
		}

		Config.LogDBConf = fineLogDBConfig(configuration.Get("logDB").ToDatabaseConfig(), dbLogFormat, env)
		Config.JobDBConf = fineQueueDBConfig(configuration.Get("jobDB").ToDatabaseConfig(), dbJobFormat, env)
		Config.CacheDBConf = fineCacheDBConfig(configuration.Get("cacheDB").ToDatabaseConfig(), dbCacheFormat, env)
		Config.CustomerCacheDBConf = fineCacheDBConfig(configuration.Get("customerCacheDB").ToDatabaseConfig(), dbCustomerCacheFormat, env)
		Config.PromotionCacheDBConf = fineCacheDBConfig(configuration.Get("promotionCacheDB").ToDatabaseConfig(), dbPromotionCacheFormat, env)
	}

	// Service config
	{
		buymedVNClient := configuration.Get("buymed-vn-client").ToServiceConfig()
		Config.APIHost = buymedVNClient.Host
		Config.APIKey = buymedVNClient.Authorization

		buymedComClient := configuration.Get("buymed-com-client").ToServiceConfig()
		Config.BuymedAPIHost = buymedComClient.Host
		Config.BuymedAPIKey = buymedComClient.Authorization

		billingClient := configuration.Get("billing-client").ToServiceConfig()
		Config.BillingHost = billingClient.Host
		Config.BillingToken = billingClient.Authorization

		ssoClient := configuration.Get("sso-client").ToServiceConfig()
		Config.SSOHost = ssoClient.Host
		Config.SSOToken = ssoClient.Authorization

		forwardfromConfig := configuration.Get("onepay-forward-from").ToServiceConfig()
		Config.OnepayForwardFromKey = forwardfromConfig.Key

		kiotvietClient := configuration.Get("kiotviet-client").ToServiceConfig()
		Config.KiotvietAPIHost = kiotvietClient.Host
		Config.KiotvietAPIKey = kiotvietClient.Key
	}
}

func fineMainDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	// UAT và PRD dùng chung db main của prd
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineCartDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	// UAT và PRD dùng chung db main của prd
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineQueueDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineLogDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineCacheDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {
	if env != "uat" {
		_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)
	} else {
		_config.DatabaseName = dbNameFormat
	}

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func (c *config) IsApplyProductFee(t *time.Time) bool {

	if t == nil {
		return false
	}

	if t.Before(c.ProductFeeStartTime) {
		return false
	}
	return true
}
