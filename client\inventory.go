package client

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	getSkuLocation  = "/warehouse/inventory/v1/sku-locations"
	getSkuInventory = "/warehouse/inventory/v1/sku"
)

type inventoryClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewProductServiceClient ...
func NewInventoryServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *inventoryClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	inventoryClient := &inventoryClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	inventoryClient.svc.SetDBLog(session)
	return inventoryClient
}

func (cli *inventoryClient) GetSkuLocationMapping(skuCode, warehouseCode string) *SKULocation {
	q := map[string]string{
		"sku":           skuCode,
		"warehouseCode": warehouseCode,
		"type":          "MAPPING",
	}

	qBytes, err := json.Marshal(q)
	if err != nil {
		return nil
	}

	params := map[string]string{
		"q": string(qBytes),
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, getSkuLocation, nil)
	if err != nil {
		fmt.Println("GetSkuLocation failed: " + err.Error())
		return nil
	}

	var locRes *SKULocationRes
	err = json.Unmarshal([]byte(result.Body), &locRes)
	if err != nil {
		fmt.Println("GetSkuLocation Unmarshal failed: " + err.Error())
		return nil
	}
	if locRes == nil {
		return nil
	}
	if locRes.Status != common.APIStatus.Ok {
		if locRes.Status != common.APIStatus.NotFound {
			log.Printf("GetSkuLocation [%s] failed with status: %#v\n", skuCode, locRes)
		}
		return nil
	}
	if len(locRes.Data) == 0 {
		return nil
	}

	return locRes.Data[0]
}

func (cli *inventoryClient) GetSkuInventory(sellerCode string, offset, limit int) []*SKUInventory {
	q := map[string]string{
		"sellerCode": sellerCode,
	}

	qBytes, err := json.Marshal(q)
	if err != nil {
		return nil
	}

	params := map[string]string{
		"q":      string(qBytes),
		"offset": strconv.Itoa(offset),
		"limit":  strconv.Itoa(limit),
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, getSkuInventory, nil)
	if err != nil {
		fmt.Println("GetSkuInventory failed: " + err.Error())
		return nil
	}

	var invRes *InventorySKURes
	err = json.Unmarshal([]byte(result.Body), &invRes)
	if err != nil {
		fmt.Println("GetSkuInventory failed: " + err.Error())
		return nil
	}

	if invRes == nil || invRes.Status != common.APIStatus.Ok {
		return nil
	}

	if len(invRes.Data) == 0 {
		return nil
	}

	return invRes.Data
}
