package reconcile_action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ProcessChargeIncreasePrice(input *model.IncreaseSkuPriceRequest) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "input is required",
			ErrorCode: "INVALID_INPUT",
		}
	}

	if input.Sku == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "sku is required",
			ErrorCode: "INVALID_INPUT",
		}
	}

	skuSplit := strings.Split(input.Sku, ".")
	sellerCode := skuSplit[0]

	var reconciliation *model.Reconciliation
	var err error

	recRes := model.ReconciliationDB.Query(model.Reconciliation{
		SellerCode:           sellerCode,
		ReconciliationStatus: model.ReconciliationStatus.Waiting,
	}, 0, 1, &primitive.M{"_id": -1})
	if recRes.Status == common.APIStatus.Ok {
		reconciliation = recRes.Data.([]*model.Reconciliation)[0]
	} else if recRes.Status == common.APIStatus.NotFound {
		reconciliation, err = CreateReconciliationFromTime(sellerCode, time.Now())
		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				Message:   err.Error(),
				ErrorCode: "CREATE_RECONCILIATION_ERROR",
			}
		}
	} else {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   recRes.Message,
			ErrorCode: "RECONCILIATION_ERROR",
		}
	}

	sku := client.Services.Product.GetSkuInfo(input.Sku)
	if sku == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "sku not found",
			ErrorCode: "SKU_NOT_FOUND",
		}
	}

	penaltyDescription := fmt.Sprintf(
		"Phí tăng giá sản phẩm #%d đợt %s ~ %s",
		sku.ProductID,
		utils.FormatTimeString(reconciliation.FromTime),
		utils.FormatTimeString(reconciliation.ToTime),
	)

	createRes := model.ReconciliationItemDB.Create(model.ReconciliationItem{
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		SellerCode:                 reconciliation.SellerCode,
		FeeType:                    enum.FeeType.INCREASE_PRICE_FEE,
		AutoPenaltyFee:             true,

		Sku:                input.Sku,
		ProductId:          sku.ProductID,
		PenaltyDescription: penaltyDescription,
		PenaltyBMFee:       100000, // 100k VND
	})
	if createRes.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   createRes.Message,
			ErrorCode: "CREATE_RECONCILIATION_ITEM_ERROR",
		}
	}

	value, err := CalculateReconciliation(
		reconciliation.SellerCode,
		reconciliation.ReconcileScheduleTimeIndex,
	)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "CALCULATE_RECONCILIATION_ERROR",
		}
	}

	reconciliationF := model.Reconciliation{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		ReconciliationStatus:       model.ReconciliationStatus.Waiting,
	}
	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}
	updateRes := model.ReconciliationDB.UpdateOne(reconciliationF, updater)
	if updateRes.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   updateRes.Message,
			ErrorCode: "UPDATE_RECONCILIATION_ERROR",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "ProcessChargeIncreasePrice success",
	}
}
