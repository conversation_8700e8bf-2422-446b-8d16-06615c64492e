package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

const (
	pathCreateSms = "/integration/messaging/v1/sms"
)

// Client is model define SMS client
type smsClient struct {
	SMS     *client.RestClient
	headers map[string]string
}

// NewClient is func define new Notification client
func NewSMSClient(apiHost, apiKey, logName string) *smsClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	return &smsClient{
		SMS: client.NewRESTClient(
			apiHost,
			logName,
			3*time.Second,
			1,
			3*time.Second,
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
}

// CreateSMS is func create new SMS
func (cli *smsClient) CreateSMS(in *SMS) (*SMS, error) {

	params := map[string]string{}
	res, err := cli.SMS.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathCreateSms, nil)
	if err != nil {
		return nil, err
	}

	var result *SMSResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}
