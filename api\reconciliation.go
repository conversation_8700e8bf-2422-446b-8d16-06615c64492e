package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/tool"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func RevertReconciliationStatus(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	var input *model.Reconciliation
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	response := reconcile_action.RevertReconciliationStatus(input, as)
	// TODO: turn off sync reconciliation to billing
	// go tool.SyncReconciliation(model.Reconciliation{ID: input.ID})
	return res.Respond(response)
}

func ConfirmReconciliation(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	var input *model.Reconciliation
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	response := reconcile_action.ConfirmReconciliation(input.ID, as)
	// TODO: turn off sync reconciliation to billing
	// go tool.SyncRecon(model.Reconciliation{ID: input.ID}, nil)
	return res.Respond(response)
}

func CompleteReconciliation(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	var input *model.Reconciliation
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	response := reconcile_action.CompleteReconciliation(input, as)
	// go tool.SyncReconciliation(model.Reconciliation{ID: input.ID})
	return res.Respond(response)
}

func CreateReconciliationItem(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	var input *model.ReconciliationItem
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	response := reconcile_action.CreateReconciliationItem(input, as, tool.SyncRICreateCustom)
	return res.Respond(response)
}

func UpdateReconciliationItem(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}
	var input *model.ReconciliationItem
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	response := reconcile_action.UpdateReconciliationItem(input, as)
	return res.Respond(response)
}

func RemoveReconciliationItem(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	id, err := primitive.ObjectIDFromHex(req.GetParam("id"))
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	if id.IsZero() {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				req.GetParam("id"),
			},
			Message:   "Invalid id",
			ErrorCode: "INVALID_ID",
		})
	}

	response := reconcile_action.RemoveReconciliationItem(&id, as, tool.SyncRIDeleteCustom)
	return res.Respond(response)
}

func RemoveMultipleReconciliationItem(req sdk.APIRequest, res sdk.APIResponder) error {

	type Input struct {
		IDs []string `json:"ids"`
	}

	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	var input Input
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	var ids []*primitive.ObjectID
	for _, id := range input.IDs {
		oid, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return res.Respond(&common.APIResponse{
				Status: common.APIStatus.Invalid,
				Data: []string{
					err.Error(),
				},
				Message:   "Invalid input",
				ErrorCode: "INVALID_INPUT",
			})
		}

		if oid.IsZero() {
			return res.Respond(&common.APIResponse{
				Status: common.APIStatus.Invalid,
				Data: []string{
					req.GetParam("id"),
				},
				Message:   "Invalid id",
				ErrorCode: "INVALID_ID",
			})

		}
		ids = append(ids, &oid)
	}

	response := reconcile_action.RemoveMultipleReconciliationItem(ids, as, tool.SyncRIDeleteCustom)
	return res.Respond(response)
}

func GetReconciliationInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ReconciliationItem
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "PAYLOAD_IVALID",
		})
	}

	return resp.Respond(reconcile_action.GetReconciliationInfo(&input))
}

func ResyncCompletedReconciliation(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ResyncCompletedReconciliationReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	return resp.Respond(reconcile_action.ResyncCompletedReconciliation(&input))
}

func ApproveReconcileAutoPenaltyInventory(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	var input model.ReconciliationItem
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "payload invalid",
			ErrorCode: "PAYLOAD_IVALID",
			Data: []string{
				err.Error(),
			},
		})
	}

	response := reconcile_action.ApproveReconcileAutoPenaltyInventory(&input)
	return res.Respond(response)
}

func ChargeSellerIncreaseProductPrice(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	var input model.IncreaseSkuPriceRequest
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "payload invalid",
			ErrorCode: "PAYLOAD_IVALID",
			Data: []string{
				err.Error(),
			},
		})
	}

	response := reconcile_action.PushChargeSellerIncreaseProductPriceToQueue(&input)
	return res.Respond(response)
}

func ChargePoorQualityProduct(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	var input model.PoorQualityProductRequest
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "payload invalid",
			ErrorCode: "PAYLOAD_IVALID",
			Data: []string{
				err.Error(),
			},
		})
	}

	response := reconcile_action.PushChargePoorQualityProductToQueue(&input)
	return res.Respond(response)
}

func ProcessCalculateBizHouseholdTax(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	type Input struct {
		Data []model.BizHouseholdTaxItem `json:"data"`
	}

	var input Input
	if err := req.GetContent(&input); err != nil {

		return res.Respond(&common.APIResponse{
			Status: common.APIStatus.Invalid,
			Data: []string{
				err.Error(),
			},
			Message:   "Invalid input",
			ErrorCode: "INVALID_INPUT",
		})
	}

	response := reconcile_action.CreateBizHouseHoldTaxItem(input.Data)
	return res.Respond(response)
}

func GetSellerCurrentReconciliation(req sdk.APIRequest, res sdk.APIResponder) error {
	type Input struct {
		SellerCode string `json:"sellerCode,omitempty"`
	}

	var input Input
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	if input.SellerCode == "" {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "sellerCode is required",
		})
	}

	response := reconcile_action.GetSellerCurrentReconciliation(input.SellerCode)
	return res.Respond(response)
}

func UpdateReconciliation(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}
	var input model.Reconciliation
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	response := reconcile_action.UpdateReconciliation(&input)
	return res.Respond(response)
}

func CallbackTransferRequest(req sdk.APIRequest, res sdk.APIResponder) error {
	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return res.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}
	var input model.TransferRequest
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data",
		})
	}

	response := reconcile_action.CallbackTransferRequestHandler(&input)
	return res.Respond(response)
}
