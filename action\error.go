package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

var (
	// Cart item error
	ImportantItemError           = buildError("IMPORTANT_ITEM", common.APIStatus.Invalid, "Số lượng sản phẩm quan trọng không hợp lệ, với mỗi 5 sản phẩm khác nhau bạn được chọn 1 sản phẩm quan trọng")
	NotActiveSkuError            = buildError("NOT_ACTIVE_SKU", common.APIStatus.Invalid, "Sản phẩm đã ngưng bán")
	SuspendedSkuError            = buildError("SUSPENDED_SKU", common.APIStatus.Invalid, "Sản phẩm đã ngưng bán")
	StopProducingSkuError        = buildError("STOP_PRODUCING_SKU", common.APIStatus.Invalid, "Sản phẩm đã ngưng sản xuất")
	OutOfStockSkuError           = buildError("OUT_OF_STOCK_SKU", common.APIStatus.Invalid, "Sản phẩm hết hàng")
	CartItemError                = buildError("CART_ITEM_INVALID", common.APIStatus.Invalid, "Sản phẩm trong giỏ hàng không hợp lệ")
	NotAvailableSkuError         = buildError("NOT_AVAILABLE_SKU", common.APIStatus.Invalid, "Sản phẩm chưa hỗ trợ giao vùng này")
	NotFoundSkuError             = buildError("NOT_FOUND_SKU", common.APIStatus.Invalid, "Không tìm thấy sản phẩm")
	NotFoundPriceSkuError        = buildError("NOT_FOUND_PRICE_SKU", common.APIStatus.Invalid, "Không tìm thấy thông tin giá sản phẩm")
	NotFoundPriceSkuInComboError = buildError("NOT_FOUND_PRICE_SKU_IN_COMBO", common.APIStatus.Invalid, "Không tìm thấy thông tin giá của sản phẩm con trong combo")
	ComboError                   = buildError("COMBO_INVALID", common.APIStatus.Invalid, "Khuyến mãi này đã hết hạn")
	ChangePriceError             = buildError("CHANGED_PRICE", common.APIStatus.Invalid, "Sản phẩm có sự thay đổi giá")
	NormalToDealError            = buildError("NORMAL_TO_DEAL", common.APIStatus.Invalid, "Sản phẩm đang có khuyến mãi")
	DealToNormalError            = buildError("DEAL_TO_NORMAL", common.APIStatus.Invalid, "Khuyến mãi của sản phẩm đã hết, chuyển về sản phẩm thường")
	RequiredCertificateError     = buildError("REQUIRED_CERTIFICATE", common.APIStatus.Invalid, "Bạn chưa đủ giấy phép để mua SP này")
	PriceZeroError               = buildError("PRICE_ZERO", common.APIStatus.Invalid, "Giá sản phẩm không hợp lệ")

	// Cart error
	DealMaxQuantityError                         = buildError("DEAL_MAX_QUANTITY", common.APIStatus.Invalid, "Sản phẩm không đủ hoặc đã hết hàng")
	SkuMaxQuantityError                          = buildError("SKU_MAX_QUANTITY", common.APIStatus.Invalid, "Sản phẩm không đủ hoặc đã hết hàng")
	DeliveryAndPaymentError                      = buildError("DELIVERY_AND_PAYMENT_METHOD_INVALID", common.APIStatus.Invalid, "Không tìm thấy thông tin hình thức vận chuyển và hình thức thanh toán")
	DeliveryMethodError                          = buildError("DELIVERY_METHOD_NOT_FOUND", common.APIStatus.Invalid, "Không tìm thấy thông tin hình thức giao hàng")
	PaymentMethodError                           = buildError("PAYMENT_METHOD_NOT_FOUND", common.APIStatus.Invalid, "Không tìm thấy thông tin hình thức thanh toán")
	NotSupportPaymentMethodError                 = buildError("CUSTOMER_ORANGE_NOT_SUPPORT", common.APIStatus.Invalid, "Tài khoản của bạn không hỗ trợ thanh toán bằng tiền mặt khi nhận hàng")
	NotSupportPaymentMethodBankError             = buildError("PAYMENT_METHOD_BANK_NOT_SUPPORT", common.APIStatus.Invalid, "Tài khoản của bạn không hỗ trợ thanh toán chuyển khoản")
	OrderHasDealError                            = buildError("ORDER_HAS_DEAL", common.APIStatus.Invalid, "Không thể chỉnh sửa đơn hàng có khuyến mãi")
	OrderHasCampaignError                        = buildError("ORDER_HAS_CAMPAIGN", common.APIStatus.Invalid, "Không thể chỉnh sửa đơn hàng có sản phẩm mua trong chương trình khuyến mãi")
	OrderStatusError                             = buildError("ORDER_STATUS", common.APIStatus.Invalid, "Chỉ có thể chỉnh sửa đơn hàng đang trong trạng thái chờ xác nhận")
	OrderCreatedTimeError                        = buildError("ORDER_TIME_EXCEED_30", common.APIStatus.Invalid, "Đơn hàng không thể sửa sau 30 phút")
	OrderIdNotFoundError                         = buildError("ORDER_ID_NOT_FOUND", common.APIStatus.Invalid, "Không tìm thấy mã đơn hàng")
	OrderNotFoundError                           = buildError("ORDER_NOT_FOUND", common.APIStatus.Invalid, "Không tìm thấy đơn hàng")
	OrderItemNotFoundError                       = buildError("ORDER_ITEM_NOT_FOUND", common.APIStatus.Invalid, "Không tìm thấy sản phẩm trong đơn hàng")
	AccountError                                 = buildError("ACCOUNT_INVALID", common.APIStatus.Invalid, "Thông tin tài khoản không hợp lệ")
	CustomerError                                = buildError("CUSTOMER_ERROR", common.APIStatus.Invalid, "Không thể lấy thông tin khách hàng")
	CustomerNotFoundError                        = buildError("CUSTOMER_NOT_FOUND", common.APIStatus.Invalid, "Không tìm thấy thông tin khách hàng")
	CustomerProvinceError                        = buildError("PROVINCE_NOT_FOUND", common.APIStatus.Invalid, "Vui lòng cập nhật thông tin cá nhân địa chỉ, tỉnh/thành")
	CustomerLevelError                           = buildError("LEVEL_NOT_FOUND", common.APIStatus.Invalid, "Khách hàng có cấp độ không hợp lệ")
	OrderMinPrice1MError                         = buildError("ORDER_MIN_1MILION", common.APIStatus.Invalid, "Bạn chưa đạt được đơn hàng tối thiểu 1.000.000 VNĐ. Vui lòng quay lại ĐẶT HÀNG NHANH để tiếp tục đặt hàng.")
	OrderMinPrice500Error                        = buildError("ORDER_MIN_500", common.APIStatus.Invalid, "Bạn chưa đạt được đơn hàng tối thiểu 500.000 VNĐ. Vui lòng quay lại ĐẶT HÀNG NHANH để tiếp tục đặt hàng.")
	BrandOrderMinPrice500Error                   = buildError("BRAND_ORDER_MIN_500", common.APIStatus.Invalid, "Bạn chưa đạt điều kiện đơn hàng tối thiểu 50.000 VNĐ. Vui lòng quay lại trang Sản phẩm hoặc thực hiện Tìm kiếm sản phẩm để tiếp tục đặt hàng.")
	ClinicOrderMinPrice500Error                  = buildError("CLINIC_ORDER_MIN_500", common.APIStatus.Invalid, "Bạn chưa đạt điều kiện đơn hàng tối thiểu 500.000 VNĐ. Vui lòng quay lại trang Sản phẩm hoặc thực hiện Tìm kiếm sản phẩm để tiếp tục đặt hàng.")
	ProvinceCodeError                            = buildError("PROVINCE_CODE_NOT_FOUND", common.APIStatus.Invalid, "Thông tin tỉnh/thành phố không thể để trống")
	CartItemChangePrice                          = buildError("CART_ITEM_CHANGED_PRICE", common.APIStatus.Invalid, "Sản phẩm trong giỏ hàng có sự thay đổi giá")
	CartItemLimitError                           = buildError("CART_ITEM_LIMIT", common.APIStatus.Invalid, "Giỏ hàng bị giới hạn 200 sản phẩm khác nhau")
	CartLimitWeightError                         = buildError("CART_WEIGHT_LIMIT", common.APIStatus.Invalid, "Đơn hàng cho phép đặt tối đa %.1f kg")
	CartLimitVolumeError                         = buildError("CART_VOLUME_LIMIT", common.APIStatus.Invalid, "Đơn hàng cho phép đặt tối đa %.1f centimet khối")
	CustomerBusinessAddressError                 = buildError("CUSTOMER_BUSINESS_ADDRESS_ERROR", common.APIStatus.Invalid, "Vui lòng liên hệ Thuocsi để hỗ trợ cập nhập lại địa chỉ doanh nghiệp.")
	CartProvinceInvalid                          = buildError("CART_WARD_AND_PROVINCE_INVALID", common.APIStatus.Invalid, "Vui lòng cập nhật tỉnh/thành")
	CartDistrictInvalid                          = buildError("CART_WARD_AND_DISTRICT_INVALID", common.APIStatus.Invalid, "Vui lòng cập nhật quận/huyện")
	CartWardNotFound                             = buildError("CART_WARD_NOT_FOUND", common.APIStatus.Invalid, "Vui lòng cập nhật phường/xã")
	PaymentMethodCredit_CustomerColorError       = buildError("CUSTOMER_COLOR_INVALID", common.APIStatus.Invalid, "Cấp bậc khách hàng không thể sử dụng công nợ")
	PaymentMethodCredit_DebtContractNotFound     = buildError("DEBT_CONTRACT_NOTFOUND", common.APIStatus.Invalid, "Không tìm thấy hợp đồng công nợ")
	PaymentMethodCredit_OverLimit                = buildError("DEBT_CONTRACT_OVERLIMIT", common.APIStatus.Invalid, "Quá hạn mức công nợ")
	PaymentMethodCredit_ContractStartTimeInvalid = buildError("DEBT_CONTRACT_STARTTIME_INVALID", common.APIStatus.Invalid, "Thời gian bắt đầu hợp đồng công nợ không hợp lệ")
	PaymentMethodCredit_ContractInactive         = buildError("DEBT_CONTRACT_INACTIVE", common.APIStatus.Invalid, "Hợp đồng công nợ đang bị tắt")
	PaymentMethodCredit_BalanceRemain            = buildError("DEBT_BALANCE_REMAIN", common.APIStatus.Invalid, "Vui lòng thanh toán dư nợ để đặt hàng")
	PaymentMethodCredit_NotAllowAnotherMethod    = buildError("DEBT_NOT_ALLOW_ANOTHER_METHOD", common.APIStatus.Invalid, "Vui lòng chọn hình thức Thanh Toán Hợp Đồng")
	PaymentMethodCredit_InvalidStatusContract    = buildError("DEBT_STATUS_INVALID", common.APIStatus.Invalid, "Trạng thái hợp đồng không hợp lệ")
	PaymentMethodCredit_InvalidContract          = buildError("DEBT_INVALID", common.APIStatus.Invalid, "Không thể thanh toán với hình thức thanh toán hợp đồng")
	// FEE_INVALID, VOUCHER_CODE_INVALID
)

func buildError(errorCode, status, message string, data ...interface{}) *common.APIResponse {
	return &common.APIResponse{
		Status:    status,
		Message:   message,
		ErrorCode: errorCode,
		Data:      data,
	}
}

// Max QuantityError
const Err_MAX_QUANTITY = "MAX_QUANTITY"

type maxQuantityErrorData struct {
	MaxQty int    `json:"quantity,omitempty"`
	SKU    string `json:"sku,omitempty"`
	Note   string `json:"note,omitempty"`
}

func buildMaxQuantityError(errorData *maxQuantityErrorData) *common.APIResponse {
	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		ErrorCode: Err_MAX_QUANTITY,
		Message:   "Vượt quá số lượng đặt hàng cho phép",
		Data:      []*maxQuantityErrorData{errorData},
	}
}

func OrderMinPriceError(price int) *common.APIResponse {
	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		ErrorCode: "ORDER_MIN_PRICE",
		Message:   fmt.Sprintf("Bạn chưa đạt được đơn hàng tối thiểu %s VNĐ. Vui lòng quay lại ĐẶT HÀNG NHANH để tiếp tục đặt hàng.", utils.FormatInt(price)),
	}
}
