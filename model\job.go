package model

import (
	"context"
	"time"

	"github.com/labstack/gommon/log"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Job ...
type Job struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
}

// JobDB ...
var JobExecutor = &job.Executor{
	ColName: "job",
}

// InitJobModel is func init model job
func InitJobModel(s *mongo.Database, dbName string) {
	JobExecutor.InitWithConfig(s, dbName, &job.ExecutorConfiguration{
		WaitForReadyTime: true,
	})
}

// PushTopic ...
func PushTopic(ctx context.Context, data interface{}, itemMetaData *job.JobItemMetadata) {
	defer func() {
		select {
		case <-time.After(2 * time.Microsecond):
			// comment this log to reduce log
			// {
			// 	log.Info("Request context processed")
			// }
		case <-ctx.Done():
			{
				log.Error("Request context cancelled")
				log.Error("Context error: " + ctx.Err().Error())
				return
			}
		}

		if r := recover(); r != nil {
			log.Error("Recover rollback, ", r)
		}
	}()

	err := JobExecutor.Push(data, itemMetaData)
	if err != nil {
		log.Error(err)
	}
}

var ConfigSchedule *schedule.ConfigDB

func InitSchedule(database *mongo.Database, mapProcess map[string]schedule.Process) {
	ConfigSchedule = schedule.NewConfigDB("worker", mapProcess)
	ConfigSchedule.Init(database)
}

var CompleteOrderJobExecutor = &job.Executor{ColName: "complete_order_job"}

func InitCompleteOrderJob(database *mongo.Database, consumer job.ExecutionFn) {
	CompleteOrderJobExecutor.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
		ChannelCount:     100,
	})

	CompleteOrderJobExecutor.SetConsumer(consumer)
}

var CreateSellerInvoiceJob = job.Executor{ColName: "create_seller_invoice_job"}

func InitCreateSellerInvoiceJob(database *mongo.Database, consumer job.ExecutionFn) {
	CreateSellerInvoiceJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
		ChannelCount:     100,
	})

	CreateSellerInvoiceJob.SetConsumer(consumer)
}

var CreateMissingInvoiceJob = job.Executor{ColName: "create_missing_invoice_job"}

func InitCreateMissingInvoiceJob(database *mongo.Database, consumer job.ExecutionFn) {
	CreateMissingInvoiceJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
		ChannelCount:     2,
	})

	CreateMissingInvoiceJob.SetConsumer(consumer)
}

type UpdateOrderSellerReturnJobParam struct {
	TimeFrom *time.Time `json:"timeFrom,omitempty" bson:"time_from,omitempty"`
}

var UpdateDeliveryJob = &job.Executor{ColName: "update_delivery_job"}

func InitUpdateDeliveryJob(database *mongo.Database, consumer job.ExecutionFn) {
	UpdateDeliveryJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:    false,
		SortedItem:          true,
		FailThreshold:       10,
		ChannelCount:        100,
		MaximumWaitToRetryS: 30,
	})
	UpdateDeliveryJob.SetConsumer(consumer)
}

var CreateInvoiceDraftJobExecutor = &job.Executor{ColName: "create_invoice_draft_job"}
var ExportInvoiceJobExecutor = &job.Executor{ColName: "export_invoice_job"}
var ReplaceInvoiceJobExecutor = &job.Executor{ColName: "replace_invoice_job"}
var UpdateInvoiceInfoJobExecutor = &job.Executor{ColName: "update_invoice_info_job"}

func InitCreateInvoiceDraftJob(database *mongo.Database, consumer job.ExecutionFn) {
	CreateInvoiceDraftJobExecutor.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{WaitForReadyTime: false, FailThreshold: 50})
	CreateInvoiceDraftJobExecutor.SetConsumer(consumer)
}

func InitExportInvoiceJob(database *mongo.Database, consumer job.ExecutionFn) {
	ExportInvoiceJobExecutor.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{WaitForReadyTime: false, FailThreshold: 50})
	ExportInvoiceJobExecutor.SetConsumer(consumer)
}

func InitReplaceInvoiceJob(database *mongo.Database, consumer job.ExecutionFn) {
	ReplaceInvoiceJobExecutor.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{WaitForReadyTime: false, FailThreshold: 50})
	ReplaceInvoiceJobExecutor.SetConsumer(consumer)
}

func InitUpdateInvoiceInfoJob(database *mongo.Database, consumer job.ExecutionFn) {
	UpdateInvoiceInfoJobExecutor.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{WaitForReadyTime: false, FailThreshold: 50})
	UpdateInvoiceInfoJobExecutor.SetConsumer(consumer)
}

var OrderedSKUJob = &job.Executor{ColName: "ordered_sku"}

func InitOrderedSKUJob(database *mongo.Database) {
	OrderedSKUJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		UniqueItem: true,
	})
}

var SyncSkuLimitHistoryJob = &job.Executor{
	ColName: "sync_sku_limit_history",
}

func InitSyncSkuLimitHistoryJob(database *mongo.Database, consumer job.ExecutionFn) {
	SyncSkuLimitHistoryJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
		ChannelCount:     1,
	})

	SyncSkuLimitHistoryJob.SetConsumer(consumer)
}

var CreateTicketCSHoldOrderJob = &job.Executor{
	ColName: "create_ticket_cs_job",
}

func InitCreateTicketCSHoldOrderJob(database *mongo.Database, consumer job.ExecutionFn) {
	CreateTicketCSHoldOrderJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:        true,
		FailThreshold:           10,
		ChannelCount:            5,
		ParallelTopicProcessing: true,
	})

	CreateTicketCSHoldOrderJob.SetConsumer(consumer)
}

var CalcTransferingDifferenceJob = &job.Executor{
	ColName: "calc_transfering_difference_job",
}

func InitCalcTransferingDifferenceJob(database *mongo.Database, consumer job.ExecutionFn) {
	CalcTransferingDifferenceJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
		FailThreshold:    10,
		ChannelCount:     1,
	})

	CalcTransferingDifferenceJob.SetConsumer(consumer)
}

var CreateSaleOrderJob = &job.Executor{
	ColName: "create_sale_order_job",
}

func InitCreateSaleOrderJob(database *mongo.Database, consumer job.ExecutionFn) {
	CreateSaleOrderJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:        true,
		ParallelTopicProcessing: true,
		FailThreshold:           10,
		SortedItem:              true,
		UniqueItem:              true,
	})

	CreateSaleOrderJob.SetConsumer(consumer)
}

var CreateSaleOrderFailJob = &job.Executor{
	ColName: "create_sale_order_fail_job",
}

func InitCreateSaleOrderFailJob(database *mongo.Database, consumer job.ExecutionFn) {
	CreateSaleOrderFailJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:        true,
		ParallelTopicProcessing: true,
		FailThreshold:           60,
		SortedItem:              true,
		UniqueItem:              true,
	})

	CreateSaleOrderFailJob.SetTopicConsumer("create_sale_order_fail", consumer)
}

var ReCheckCompletedOrderJob = &job.Executor{
	ColName: "retry_check_completed_order",
}

func InitReCheckCompletedOrderJob(database *mongo.Database, consumer job.ExecutionFn) {
	ReCheckCompletedOrderJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime: true,
	})

	ReCheckCompletedOrderJob.SetConsumer(consumer)
}

var ProcessPaymentMethodCreditJob = &job.Executor{
	ColName: "process_payment_method_credit",
}

func InitProcessPaymentMethodCreditJob(database *mongo.Database, consumer job.ExecutionFn) {
	ProcessPaymentMethodCreditJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		ParallelTopicProcessing: false,
		FailThreshold:           50,
		ChannelCount:            2,
		SortedItem:              false,
		LogSize:                 5,
		SelectorDelayMS:         100,
		WaitForReadyTime:        false,
		UniqueItem:              false,
		ConsumedExipredTime:     time.Duration(7) * time.Hour,
		CurVersionTimeoutS:      60,
		OldVersionTimeoutS:      30,
		MaximumWaitToRetryS:     15,
	})
	ProcessPaymentMethodCreditJob.SetTopicConsumer("checkout", consumer)
	ProcessPaymentMethodCreditJob.SetTopicConsumer("refund", consumer)
}

var AutoCancelPaymentMethodBankJob = &job.Executor{
	ColName: "auto_cancel_payment_method_bank",
}

func InitAutoCancelPaymentMethodBankJob(database *mongo.Database, consumer job.ExecutionFn) {
	AutoCancelPaymentMethodBankJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		ParallelTopicProcessing: false,
		ChannelCount:            1,
		WaitForReadyTime:        true,
		UniqueItem:              false,
		FailThreshold:           10,
	})
	AutoCancelPaymentMethodBankJob.SetConsumer(consumer)
}

var NotifyInvoiceCompleteOrderJob = &job.Executor{
	ColName: "notify_complete_order_job",
}

func InitNotifyInvoiceCompleteOrderJob(database *mongo.Database, consumer job.ExecutionFn) {
	NotifyInvoiceCompleteOrderJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		ParallelTopicProcessing: false,
		ChannelCount:            1,
		UniqueItem:              false,
		FailThreshold:           10,
	})
	NotifyInvoiceCompleteOrderJob.SetConsumer(consumer)
}

var CreateOrderRatingJob = &job.Executor{
	ColName: "create_order_rating_job",
}

func InitCreateOrderRatingJob(database *mongo.Database, consumer job.ExecutionFn) {
	CreateOrderRatingJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		ParallelTopicProcessing: false,
		ChannelCount:            1,
		UniqueItem:              false,
		FailThreshold:           10,
	})
	CreateOrderRatingJob.SetConsumer(consumer)
}

var AutoSendPaymentRemindJob = &job.Executor{
	ColName: "auto_send_payment_remind",
}

func InitAutoSenPaymentRemindJob(database *mongo.Database, consumer job.ExecutionFn) {
	AutoSendPaymentRemindJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		ParallelTopicProcessing: false,
		ChannelCount:            1,
		WaitForReadyTime:        true,
		UniqueItem:              false,
		FailThreshold:           10,
	})
	AutoSendPaymentRemindJob.SetConsumer(consumer)
}

var FillOrderProductFeeJob = &job.Executor{
	ColName: "fill_order_product_fee_job",
}

func InitFillOrderProductFeeJob(database *mongo.Database, consumer job.ExecutionFn) {
	FillOrderProductFeeJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		ParallelTopicProcessing: false,
		ChannelCount:            1,
		UniqueItem:              false,
		FailThreshold:           10,
	})
	FillOrderProductFeeJob.SetConsumer(consumer)
}
