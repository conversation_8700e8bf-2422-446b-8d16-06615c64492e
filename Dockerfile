FROM asia-southeast1-docker.pkg.dev/buymed-testing/docker-image/buymed-alpine:latest
RUN mkdir -p /app
COPY --chown=$USER_NAME:$GROUP_NAME ./app-exe /app/
COPY --chown=$USER_NAME:$GROUP_NAME ./template/ /app/template/
WORKDIR /app
ARG env
ARG config
ARG version
ARG service
ENV env=${env}
ENV version=${version}
ENV config=${config}
ENV service=${service}
RUN setcap CAP_NET_BIND_SERVICE=+eip /app/app-exe
USER $USER_NAME
CMD ["/app/app-exe"]
