package tool

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

func RemoveReconcileAutoInboundPenalty(req sdk.APIRequest, res sdk.APIResponder) error {
	var input model.ReconciliationItem
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	return res.Respond(RemoveReconcileInboundPenalty(&input))
}

func RemoveReconcileInboundPenalty(item *model.ReconciliationItem) *common.APIResponse {
	if item == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "item is nil",
			ErrorCode: "NIL_ITEM",
		}
	}

	if item.SellerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "SellerCode is empty",
			ErrorCode: "EMPTY_SELLER_CODE",
		}
	}

	if item.ReconcileScheduleTimeIndex == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "TimeIndex is empty",
			ErrorCode: "EMPTY_RECONCILE_SCHEDULE_TIME_INDEX",
		}
	}

	if item.Sku == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Sku is empty",
			ErrorCode: "EMPTY_SKU",
		}
	}

	if item.WarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCode is empty",
			ErrorCode: "EMPTY_WAREHOUSE_CODE",
		}
	}

	rFilter := model.Reconciliation{
		SellerCode:                 item.SellerCode,
		ReconcileScheduleTimeIndex: item.ReconcileScheduleTimeIndex,
		ReconciliationStatus:       model.ReconciliationStatus.Waiting,
	}
	reconciliationRes := model.ReconciliationDB.QueryOne(rFilter)
	if reconciliationRes.Status != common.APIStatus.Ok {
		return reconciliationRes
	}

	riFilter := model.ReconciliationItem{
		SellerCode:                 item.SellerCode,
		ReconcileScheduleTimeIndex: item.ReconcileScheduleTimeIndex,
		Sku:                        item.Sku,
		WarehouseCode:              item.WarehouseCode,
		FeeType:                    enum.FeeType.INBOUND_OVERDUE_PENALTY,
		AutoPenaltyFee:             true,
	}

	result := model.ReconciliationItemDB.Delete(riFilter)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	value, err := reconcile_action.CalculateReconciliation(item.SellerCode, item.ReconcileScheduleTimeIndex)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "CALCULATE_RECONCILIATION_ERROR",
		}
	}

	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}

	result = model.ReconciliationDB.UpdateOne(rFilter, updater)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Remove auto reconcile inbound penalty success",
		Data: []interface{}{
			map[string]string{
				"sellerCode":                 item.SellerCode,
				"reconcileScheduleTimeIndex": item.ReconcileScheduleTimeIndex,
				"sku":                        item.Sku,
				"warehouseCode":              item.WarehouseCode,
			},
		},
	}
}
