package api

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/tool"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func ToolUpdatePoint(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ToolUpdatePointRequest

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if acc, _ := wrapActionSourceAllInfo(req); acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "<PERSON><PERSON><PERSON> kho<PERSON>n của bạn không thể thực hiện thao tác này",
			ErrorCode: "ACTION_NOT_FOUND",
		})
	}

	return resp.Respond(action.ToolUpdatePoint(&input))
}

func ToolTriggerSyncInvoiceV2(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		InvoiceID int64 `json:"invoice_id,omitempty"`
	}
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	if input.InvoiceID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "invoice_id is required",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	return resp.Respond(tool.TriggerSyncInvoice(input.InvoiceID))
}

func ToolRecheckCompletedOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input struct {
		OrderIDs []int64 `json:"orderIds,omitempty"`
		OrderID  int64   `json:"orderId,omitempty"`
		Force    bool    `json:"force,omitempty"`
	}

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data",
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	// Collect order IDs to process
	var orderIDs []int64
	if input.OrderID > 0 {
		orderIDs = append(orderIDs, input.OrderID)
	}
	if len(input.OrderIDs) > 0 {
		orderIDs = append(orderIDs, input.OrderIDs...)
	}

	if len(orderIDs) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "orderId or orderIds is required",
			ErrorCode: "MISSING_ORDER_ID",
		})
	}

	// Execute recheck directly for each order
	for _, orderID := range orderIDs {
		err := action.RecheckCompletedOrder(orderID, input.Force)
		if err != nil {
			fmt.Println("ToolRecheckCompletedOrder", orderID, err)
		}
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: fmt.Sprintf("Recheck completed orders executed successfully for %d orders", len(orderIDs)),
	})
}
