package client

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathSendReconcileCode = "/accounting/core/v1/reconcile-session/approve"
)

type reconcileClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewReconcileServiceClient ...
func NewReconcileServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *reconcileClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	recClient := &reconcileClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	recClient.svc.SetDBLog(session)
	return recClient
}

// SendReconcileBankMessage is func...
func (cli *reconcileClient) SendReconcileBankMessage(recCode string, amount int) *common.APIResponse {
	payload := struct {
		ReconcileShortCode string `json:"reconcileShortCode"`
		BankAmount         int    `json:"bankAmount"`
		TransactionCode    string `json:"transactionCode"`
	}{
		ReconcileShortCode: recCode,
		BankAmount:         amount,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, payload, pathSendReconcileCode, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_SEND_RECONCILE_FAIL",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_SEND_RECONCILE_FAIL",
		}
	}

	return result
}
