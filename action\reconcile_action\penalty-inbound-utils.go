package reconcile_action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func getWarehouseMap() map[string]int64 {
	warehouseMap := map[string]int64{}

	warehouses := client.Services.Warehouse.GetWarehouses()
	for _, warehouse := range warehouses {
		if warehouse.Status != client.WarehouseStatusValue.ACTIVE ||
			warehouse.Code == "HCM" {
			continue
		}
		warehouseMap[warehouse.Code] = warehouse.WarehouseID
	}

	return warehouseMap
}

func getSkuRetailPrice(skuCode string) int64 {
	skuInfo := client.Services.Product.GetSkuInfo(skuCode)
	if skuInfo == nil {
		return 0
	}

	return skuInfo.RetailPriceValue
}

func getOrderInTimeRange(skuCode string, from, to time.Time) *model.OrderDetail {
	filter := bson.M{
		"$and": []bson.M{
			{"created_time": bson.M{"$gte": from}},
			{"created_time": bson.M{"$lte": to}},
			{"$or": []bson.M{
				{"skus": bson.M{"$in": []string{skuCode}}},
				{"sub_skus": bson.M{"$in": []string{skuCode}}},
			}},
		},
	}
	res := model.OrderDetailDB.QueryOne(filter)
	if res.Status != common.APIStatus.Ok {
		return nil
	}

	return res.Data.([]*model.OrderDetail)[0]
}

func getDaysInbound(inboundTime *time.Time) int {
	if inboundTime == nil {
		return 0
	}

	days := time.Since(*inboundTime).Hours() / 24
	return int(days)
}

func getInboundTime(skuInventory *client.SKUInventory, warehouseId int64) (*client.InboundTicket, *time.Time) {
	inboundTickets := client.Services.SellerWis.
		GetLatestInbounds(skuInventory.SellerCode, skuInventory.Sku, warehouseId)
	if len(inboundTickets) == 0 {
		return nil, getInboundTimeFromInventory(skuInventory)
	}

INBOUNDS_LOOP:
	for _, inboundTicket := range inboundTickets {
		if inboundTicket == nil ||
			inboundTicket.CheckInTime == nil {
			continue
		}

	INBOUND_PRODUCTS_LOOP:
		for _, product := range inboundTicket.Products {
			if product.Sku != skuInventory.Sku {
				continue INBOUND_PRODUCTS_LOOP
			}

			if product.ActualQuantity == 0 &&
				product.CreatedBy == "WAREHOUSE" {
				continue INBOUNDS_LOOP
			}

			if product.ActualQuantity == 0 &&
				product.CreatedBy == "" {
				continue INBOUND_PRODUCTS_LOOP
			}

			break INBOUND_PRODUCTS_LOOP
		}

		return inboundTicket, getInboundTimeFromTicket(inboundTicket)
	}

	return nil, getInboundTimeFromInventory(skuInventory)
}

func getInboundTimeByPutTicket(skuInventory *client.SKUInventory) (*client.LatestPutInfo, *time.Time) {
	latestPutTicket, err := client.Services.Warehouse.GetLatestPutTicket(&client.GetLatestPutTicketReq{
		Sku:           skuInventory.Sku,
		WarehouseCode: skuInventory.WarehouseCode,
	})
	if err != nil {
		return nil, getInboundTimeFromInventory(skuInventory)
	}

	return latestPutTicket, latestPutTicket.TransferFinishTime

}

func getInboundTimeFromTicket(inboundTicket *client.InboundTicket) *time.Time {
	if inboundTicket == nil {
		return nil
	}

	if inboundTicket.CheckInTime != nil {
		return inboundTicket.CheckInTime
	}

	if inboundTicket.LastUpdatedTime != nil {
		return inboundTicket.LastUpdatedTime
	}

	return nil
}

func getInboundTimeFromInventory(skuInventory *client.SKUInventory) *time.Time {
	if skuInventory == nil {
		return nil
	}

	return skuInventory.LastUpdatedTime
}
