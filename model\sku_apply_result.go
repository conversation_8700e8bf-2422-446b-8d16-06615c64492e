package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// SkuApplyResult ...
type SkuApplyResult struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SKU         string      `json:"sku,omitempty" bson:"sku,omitempty"`
	ItemCode    string      `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	Quantity    *int        `json:"quantity,omitempty" bson:"quantity,omitempty"`
	MaxQuantity *int        `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`
	Type        string      `json:"type,omitempty" bson:"type,omitempty"`
	Data        interface{} `json:"data,omitempty" bson:"data,omitempty"`
}

// SkuApplyResultDB ...
var SkuApplyResultDB = &db.Instance{
	ColName:        "sku_apply_result",
	TemplateObject: &SkuApplyResult{},
}

// InitSkuApplyResultModel is func init model sku apply result
func InitSkuApplyResultModel(s *mongo.Database) {
	SkuApplyResultDB.ApplyDatabase(s)
	// t := true
	// _ = SkuApplyResultDB.CreateIndex(bson.D{
	// 	bson.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	// Unique:     &t,
	// })

	// _ = SkuApplyResultDB.CreateIndex(bson.D{
	// 	bson.E{Key: "item_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
}
