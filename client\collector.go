package client

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathSendEvent = "/monitoring/collector/v1/event"
)

const (
	ADD_TO_CART          = "ADD_TO_CART"
	UPDATE_CART_QUANTITY = "UPDATE_CART_QUANTITY"
	REMOVE_FROM_CART     = "REMOVE_FROM_CART"
	CREATE_ORDER         = "CREATE_ORDER"
)

type collectorClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewCollectorServiceClient ...
func NewCollectorServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *collectorClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	collectorClient := &collectorClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 0, 0),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	collectorClient.svc.SetDBLog(session)
	return collectorClient
}

func (cli *collectorClient) SendEventAddToCart(body *AddToCartEvent) *common.APIResponse {
	body.Event = ADD_TO_CART
	if body.CreatedTime == nil {
		now := time.Now()
		body.CreatedTime = &now
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathSendEvent, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_SEND_ADD_TO_CART",
		}
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_ADD_TO_CART",
		}
	}
	return result
}

func (cli *collectorClient) SendEventUpdateCartQuantity(body *AddToCartEvent) *common.APIResponse {
	body.Event = UPDATE_CART_QUANTITY
	if body.CreatedTime == nil {
		now := time.Now()
		body.CreatedTime = &now
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathSendEvent, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_SEND_UPDATE_CART_QUANTITY",
		}
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UPDATE_CART_QUANTITY",
		}
	}
	return result
}

func (cli *collectorClient) SendEventRemoveFromCart(body *RemoveFromCartEvent) *common.APIResponse {
	body.Event = REMOVE_FROM_CART
	if body.CreatedTime == nil {
		now := time.Now()
		body.CreatedTime = &now
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathSendEvent, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_SEND_REMOVE_FROM_CART",
		}
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_REMOVE_FROM_CART",
		}
	}
	return result
}

type CreateOrderEvent struct {
	Event           string            `json:"event,omitempty"`    // will be overwritten, can be blank
	Metadata        map[string]string `json:"metadata,omitempty"` // required
	ResultStatus    string            `json:"resultStatus,omitempty"`
	ResultErrorCode string            `json:"resultErrorCode,omitempty"`
	UserAgent       string            `json:"userAgent,omitempty"` // required
	CreatedTime     *time.Time        `json:"createdTime,omitempty"`
	IP              string            `json:"ip,omitempty"` // required
	AccountType     string            `json:"accountType,omitempty"`
	AccountID       int64             `json:"accountID,omitempty"`
}

func (cli *collectorClient) SendEventCreateOrder(body CreateOrderEvent) {
	body.Event = CREATE_ORDER
	_, _ = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathSendEvent, &[]string{
		CREATE_ORDER,
	})
}
