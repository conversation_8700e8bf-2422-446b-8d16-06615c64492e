package tool

import (
	"math"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

func CalculateInvoiceReport(invoice *model.Invoice, orderItems []*model.OrderItem) {
	if len(orderItems) == 0 {
		return
	}

	invoice.InvoiceItems = make([]*model.InvoiceItem, 0, len(orderItems))
	invoice.TotalActualPrice = utils.ParseIntToPointer(0)
	invoice.TotalCompleteQuantity = utils.ParseIntToPointer(0)
	invoice.NumberOfItems = utils.ParseIntToPointer(0)

	orderRes := model.OrderDB.Query(model.Order{OrderID: invoice.OrderID}, 0, 0, nil)
	if orderRes.Status != common.APIStatus.Ok {
		return
	}
	order := orderRes.Data.([]*model.Order)[0]

	sellerApplyVouchers := make(map[string]bool, 0)
	for _, applyResult := range order.RedeemApplyResult {
		if applyResult.ChargeFee != "SELLER_CENTER" {
			continue
		}

		if invoice.SellerCode != applyResult.SellerCode {
			continue
		}

		sellerApplyVouchers[applyResult.Code] = true
	}

	acceptedDOs, _, doFinalStatus := getAcceptedDOs(order.SaleOrderCode)
	processInvoiceItems(order, invoice, orderItems, doFinalStatus)

	discountDetailBySKUlMap := map[string]*model.DiscountDetail{}
	for _, orderItem := range orderItems {

		if orderItem.DiscountDetail != nil {
			discountDetailBySKUlMap[orderItem.Sku] = orderItem.DiscountDetail
		}
	}

	deliveryOrderStatusesMap := make(map[string]*model.DeliveryOrderStatus)
	if len(order.DeliveryOrderStatuses) > 0 {
		for _, deliveryOrderStatus := range order.DeliveryOrderStatuses {
			if deliveryOrderStatus == nil || (deliveryOrderStatus.Status != enum.OrderState.Completed && deliveryOrderStatus.Status != enum.OrderState.Delivered) {
				continue
			}

			deliveryOrderStatusesMap[deliveryOrderStatus.Code] = deliveryOrderStatus
		}
	}

	sellerConfig, _ := client.Services.Seller.GetSellerConfig(invoice.SellerCode)
	invoiceMethodForVoucher := GetValidInvoiceMethodForVoucher(sellerConfig)

	var invoicePriceAfterIssuance, tradeDiscountAmount int64
	var totalActualVoucherReturned int
	for _, invoiceItem := range invoice.InvoiceItems {
		invoiceItem.CompletedQuantity = getInvoiceItemQty(invoice, order, invoiceItem)
		if invoiceItem.CompletedQuantity != nil && *invoiceItem.CompletedQuantity < 0 {
			invoiceItem.CompletedQuantity = utils.ParseIntToPointer(0)
		}
		if invoiceItem.ReturnedQuantity == nil {
			invoiceItem.ReturnedQuantity = utils.ParseIntToPointer(0)
		}

		CalTotalFeeValue(&invoiceItem.OrderItem, order.Status)
		CalSellerRevenueDisplay(&invoiceItem.OrderItem)
		CalTotalSellerRevenueDisplay(&invoiceItem.OrderItem, order.Status)

		*invoice.TotalActualPrice += *invoiceItem.ActualPrice
		*invoice.TotalCompleteQuantity += *invoiceItem.CompletedQuantity
		if *invoiceItem.CompletedQuantity > 0 {
			*invoice.NumberOfItems += 1
		}

		if invoiceItem.NoneVat != nil && !*invoiceItem.NoneVat && invoiceItem.VAT != nil {

			var vat, unitPrice float64
			var actualInvoiceVoucherBySKU int64
			vat = 1.0 + *invoiceItem.VAT/100.0
			if _, exists := discountDetailBySKUlMap[invoiceItem.Sku]; exists {
				if *invoiceMethodForVoucher == enum.InvoiceMethodForVoucher.TRADE_DISCOUNT {
					if invoiceItem.ReceiveInvoiceInfoBy != nil && *invoiceItem.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
						for _, returnedQuantity := range invoiceItem.ReturnedQuantityInfosByDO {
							if returnedQuantity == nil {
								continue
							}
							if invoice.DeliveryOrderCode != nil && *invoice.DeliveryOrderCode == returnedQuantity.DoCode && returnedQuantity.SKU == invoiceItem.Sku {
								totalActualVoucherReturned += invoiceItem.VoucherAmountBySKUUnit * returnedQuantity.Quantity
							}
						}
					} else {
						totalActualVoucherReturned += TotalActualVoucherReturned(invoiceItem, invoice)
					}

				}
			}

			switch *invoiceMethodForVoucher {
			case enum.InvoiceMethodForVoucher.ALLOCATING_TO_UNIT_PRICE:
				unitPrice = float64(int64(invoiceItem.SellerPrice)-invoiceItem.ActualInvoiceVoucherAmountBySKU) / vat
			case enum.InvoiceMethodForVoucher.PRODUCT_DISCOUNT:
				invoiceItem.DiscountIncludedVAT = actualInvoiceVoucherBySKU
				unitPrice = float64(invoiceItem.SellerPrice) / vat
			default:
				unitPrice = float64(invoiceItem.SellerPrice) / vat
			}

			subTotal := *invoiceItem.ActualPrice - int(invoiceItem.DiscountIncludedVAT)
			invoiceItem.ActualPrice = &subTotal
			invoiceItem.UnitPrice = int64(math.Round(unitPrice))
			invoicePriceAfterIssuance += int64(*invoiceItem.ActualPrice)
		}
	}

	tradeDiscountAmount = TotalActualVoucherApplied(invoice, order.DeliveryOrderStatuses, acceptedDOs, sellerApplyVouchers) - int64(totalActualVoucherReturned)
	if tradeDiscountAmount < 0 {
		tradeDiscountAmount = 0
	}

	invoice.InvoicePriceAfterIssuance = invoicePriceAfterIssuance - tradeDiscountAmount
	if invoice.InvoicePriceAfterIssuance < 0 {
		invoice.InvoicePriceAfterIssuance = 0
	}
	invoice.TradeDiscountAmount = tradeDiscountAmount
}

func processInvoiceItems(order *model.Order, invoice *model.Invoice, orderItems []*model.OrderItem, doFinalStatus map[string]bool) {
	skuItemsDoCodeMap := make(map[string]*model.OrderItem)
	isInvoiceByDO := invoice.DeliveryOrderCode != nil && *invoice.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO
	if isInvoiceByDO {
		for _, orderItem := range orderItems {
			for _, outbound := range orderItem.OutboundInfos {
				if outbound == nil || invoice.DeliveryOrderCode == nil || *invoice.DeliveryOrderCode != outbound.DoCode {
					continue
				}

				skuItemsDoCodeMap[orderItem.Sku] = orderItem
			}
			if orderItem.SubItems != nil {
				for _, subItem := range *orderItem.SubItems {
					for _, outbound := range subItem.OutboundInfos {
						if outbound == nil || invoice.DeliveryOrderCode == nil || *invoice.DeliveryOrderCode != outbound.DoCode {
							continue
						}

						skuItemsDoCodeMap[subItem.Sku] = subItem
					}
				}
			}
		}
	}

ITEM_LOOP:
	for _, orderItem := range orderItems {
		if isInvoiceByDO {
			if orderItem.SubItems != nil {
				for _, subItem := range *orderItem.SubItems {
					if skuItemsDoCodeMap[subItem.Sku] == nil {
						continue ITEM_LOOP
					}
				}
			} else {
				if skuItemsDoCodeMap[orderItem.Sku] == nil {
					continue
				}
			}
		}

		if getChargeDealType(orderItem) == "SELLER" {
			if orderItem.SkuPriceType != nil && *orderItem.SkuPriceType == enum.PriceType.FIXED_REVENUE {

				if orderItem.DealPricingType != nil && *orderItem.DealPricingType == "ABSOLUTE" {
					orderItem.SellerPrice = orderItem.SellerRevenue
				} else {
					totalFee := 0.0
					if orderItem.Fee != nil {
						for _, fee := range orderItem.Fee.Result {
							if fee == nil {
								continue
							}

							if fee.FeeType == "CHARGE_BUYER" {
								totalFee += fee.FeeValue
							}
						}
					}
					orderItem.SellerPrice = orderItem.Price - int(totalFee)
				}
			} else {
				orderItem.SellerPrice = orderItem.Price
			}
		}

		var isOrderDOFinallyStatus bool
		if invoice.DeliveryOrderCode != nil {
			isOrderDOFinallyStatus = doFinalStatus[*invoice.DeliveryOrderCode]
		}

		if orderItem.SubItems != nil {

			// HACK: correct data: tags, noneVAT, vat is missing
			// TODO: find root cause
			for _, subItem := range *orderItem.SubItems {
				if subItem == nil {
					continue
				}

				// Check if tags, noneVAT, or vat fields are missing or zero value
				if len(subItem.Tags) == 0 || subItem.NoneVat == nil || subItem.VAT == nil {
					// Query SKU info for this subItem by SKU
					skuInfo := client.Services.Product.GetSkuInfo(subItem.Sku)
					if skuInfo != nil {
						if len(subItem.Tags) == 0 && len(skuInfo.Tags) > 0 {
							subItem.Tags = skuInfo.Tags
						}
						if subItem.NoneVat == nil && skuInfo.NoneVat != nil {
							subItem.NoneVat = skuInfo.NoneVat
						}
						if subItem.VAT == nil && skuInfo.VAT != nil {
							subItem.VAT = skuInfo.VAT
						}
					}
				}
			}

			// is combo
			totalPriceItem := 0
			for _, subItem := range *orderItem.SubItems {
				if CanExportInvoice(subItem) {
					totalPriceItem += subItem.SellerPrice * subItem.Quantity
				}
			}

			priceCombo := orderItem.SellerPrice
			totalPrice := utils.MinInt(priceCombo, totalPriceItem)
			rate := float64(totalPrice) / float64(totalPriceItem)

			for _, subItem := range *orderItem.SubItems {
				if !CanExportInvoice(subItem) {
					continue
				}

				priceItem := float64(subItem.SellerPrice) * rate
				var odrItmCompletedQty int
				if orderItem.CompletedQuantity != nil {
					odrItmCompletedQty = *orderItem.CompletedQuantity
				} else {
					if orderItem.DeliveredQuantity != nil {
						odrItmCompletedQty = *orderItem.DeliveredQuantity
					}
				}

				var odrItmDeliveredQty int
				if orderItem.DeliveredQuantity != nil {
					odrItmDeliveredQty = *orderItem.DeliveredQuantity
				}

				subItem.Type = orderItem.Type
				subItem.Price = int(priceItem)
				subItem.CompletedQuantity = utils.ParseIntToPointer(odrItmCompletedQty * subItem.Quantity)

				newSubItemQty := subItem.Quantity
				subItem.Quantity = orderItem.Quantity * subItem.Quantity
				if isInvoiceByDO {
					tmpOutboundQty := 0
					for _, subItemOutboundInfo := range subItem.OutboundInfos {
						if subItemOutboundInfo == nil || invoice.DeliveryOrderCode == nil || *invoice.DeliveryOrderCode != subItemOutboundInfo.DoCode {
							continue
						}

						tmpOutboundQty += subItemOutboundInfo.Quantity
					}
					subItem.OutboundQuantity = &tmpOutboundQty

					returnedQuantity := 0
					if subItem.ReturnedQuantity != nil && *subItem.ReturnedQuantity > 0 {
						returnedQuantity = *subItem.ReturnedQuantity
					}

					invoiceQty := (*subItem.OutboundQuantity - returnedQuantity)
					subItem.ActualPrice = utils.ParseIntToPointer(invoiceQty * subItem.Price)

					if isOrderDOFinallyStatus {
						subItem.DeliveredQuantity = utils.ParseIntToPointer(odrItmDeliveredQty * newSubItemQty)
					} else {
						subItem.DeliveredQuantity = utils.ParseIntToPointer(0)
					}

					if odrItmDeliveredQty > 0 {
						if subItem.ReturnedQuantity != nil && *subItem.ReturnedQuantity > 0 {
							subItem.InvoiceQuantity = *subItem.DeliveredQuantity - *subItem.ReturnedQuantity
						} else {
							subItem.InvoiceQuantity = *subItem.DeliveredQuantity
						}
					}
					if subItem.ActualPrice != nil && *subItem.ActualPrice < 0 {
						subItem.ActualPrice = utils.ParseIntToPointer(0)
					}
					if subItem.InvoiceQuantity < 0 {
						subItem.InvoiceQuantity = 0
					}
				} else {
					tmpOutboundQty := 0
					subItem.DeliveredQuantity = utils.ParseIntToPointer(odrItmDeliveredQty * newSubItemQty)
					for _, subItemOutboundInfo := range subItem.OutboundInfos {
						if subItemOutboundInfo == nil {
							continue
						}

						tmpOutboundQty += subItemOutboundInfo.Quantity
					}
					subItem.OutboundQuantity = &tmpOutboundQty

					returnedQuantity := 0
					if subItem.ReturnedQuantity != nil && *subItem.ReturnedQuantity > 0 {
						returnedQuantity = *subItem.ReturnedQuantity
					}
					invoiceQty := (*subItem.OutboundQuantity - returnedQuantity)
					subItem.ActualPrice = utils.ParseIntToPointer(invoiceQty * subItem.Price)

					if odrItmDeliveredQty > 0 {
						if subItem.ReturnedQuantity != nil && *subItem.ReturnedQuantity > 0 {
							subItem.InvoiceQuantity = *subItem.DeliveredQuantity - *subItem.ReturnedQuantity
						} else {
							subItem.InvoiceQuantity = *subItem.DeliveredQuantity
						}
					}
					if subItem.ActualPrice != nil && *subItem.ActualPrice < 0 {
						subItem.ActualPrice = utils.ParseIntToPointer(0)
					}
					if subItem.InvoiceQuantity < 0 {
						subItem.InvoiceQuantity = 0
					}
				}

				subItem.OrderID = orderItem.OrderID
				subItem.SellerPrice = subItem.Price
				setFeeForComboSubItem(orderItem, subItem)

				invoice.InvoiceItems = append(invoice.InvoiceItems, &model.InvoiceItem{OrderItem: *subItem})
			}
		} else {
			if CanExportInvoice(orderItem) {
				if orderItem.ReceiveInvoiceInfoBy == nil || *orderItem.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_SO {
					outboundInfoQty := 0
					for _, outboundInfo := range orderItem.OutboundInfos {
						if outboundInfo == nil {
							continue
						}

						outboundInfoQty += outboundInfo.Quantity
					}
					orderItem.OutboundQuantity = &outboundInfoQty

					if order.Status == enum.OrderState.Completed {
						if orderItem.CompletedQuantity == nil {
							orderItem.CompletedQuantity = utils.ParseIntToPointer(0)
						}
						orderItem.ActualPrice = utils.ParseIntToPointer(*orderItem.CompletedQuantity * orderItem.SellerPrice)
					} else {
						if orderItem.DeliveredQuantity == nil {
							orderItem.ActualPrice = utils.ParseIntToPointer(0)
						} else {
							orderItem.CompletedQuantity = orderItem.DeliveredQuantity
							orderItem.ActualPrice = utils.ParseIntToPointer(*orderItem.DeliveredQuantity * orderItem.SellerPrice)
						}
					}

					if orderItem.CompletedQuantity != nil {
						returnedQuantity := 0
						if orderItem.ReturnedQuantity != nil && *orderItem.ReturnedQuantity > 0 {
							returnedQuantity = *orderItem.ReturnedQuantity
						}

						invoiceQty := (*orderItem.CompletedQuantity - returnedQuantity)
						orderItem.ActualPrice = utils.ParseIntToPointer(invoiceQty * orderItem.SellerPrice)
					} else {
						if order.Status == enum.OrderState.Delivered {
							orderItem.CompletedQuantity = orderItem.DeliveredQuantity
						}
						orderItem.ActualPrice = utils.ParseIntToPointer(0)
					}

					if orderItem.DeliveredQuantity != nil && *orderItem.DeliveredQuantity > 0 {
						if orderItem.ReturnedQuantity != nil && *orderItem.ReturnedQuantity > 0 {
							orderItem.InvoiceQuantity = *orderItem.DeliveredQuantity - *orderItem.ReturnedQuantity
						} else {
							orderItem.InvoiceQuantity = *orderItem.DeliveredQuantity
						}
					}
					if orderItem.ActualPrice != nil && *orderItem.ActualPrice < 0 {
						orderItem.ActualPrice = utils.ParseIntToPointer(0)
					}
					if orderItem.InvoiceQuantity < 0 {
						orderItem.InvoiceQuantity = 0
					}
				} else {

					tmpOutboundQty := 0
					tmpDeliveredQty := 0
					for _, outbound := range orderItem.OutboundInfos {
						if outbound == nil || invoice.DeliveryOrderCode == nil || *invoice.DeliveryOrderCode != outbound.DoCode {
							continue
						}

						tmpOutboundQty += outbound.Quantity
						if isOrderDOFinallyStatus {
							tmpDeliveredQty += outbound.Quantity
						} else {
							orderItem.DeliveredQuantity = utils.ParseIntToPointer(0)
						}
					}
					orderItem.OutboundQuantity = &tmpOutboundQty
					orderItem.DeliveredQuantity = &tmpDeliveredQty

					for _, returnedQuantity := range orderItem.ReturnedQuantityInfosByDO {
						if invoice.DeliveryOrderCode != nil && *invoice.DeliveryOrderCode == returnedQuantity.DoCode {
							orderItem.ReturnedQuantity = utils.ParseIntToPointer(returnedQuantity.Quantity)
							break
						} else {
							orderItem.ReturnedQuantity = utils.ParseIntToPointer(0)
						}
					}

					if orderItem.ReturnedQuantity == nil {
						orderItem.ReturnedQuantity = utils.ParseIntToPointer(0)
					}

					invoiceQty := (*orderItem.OutboundQuantity - *orderItem.ReturnedQuantity)
					orderItem.ActualPrice = utils.ParseIntToPointer(invoiceQty * orderItem.SellerPrice)
				}
				invoice.InvoiceItems = append(invoice.InvoiceItems, &model.InvoiceItem{OrderItem: *orderItem})
			}
		}
	}
}

func setFeeForComboSubItem(orderItem, subItem *model.OrderItem) {
	if orderItem == nil || orderItem.Fee == nil {
		return
	}
	if subItem == nil || subItem.Fee == nil {
		return
	}

	// loop backward to remove multiple values
	for i := len(subItem.Fee.Result) - 1; i >= 0; i-- {
		foundFee := false
		for _, feeCombo := range orderItem.Fee.Result {
			if subItem.Fee.Result[i].FeeCode == feeCombo.FeeCode {
				subItem.Fee.Result[i].FeeValue = feeCombo.FeeValue * float64(subItem.Price) / float64(orderItem.SellerPrice)
				foundFee = true
			}
		}
		if !foundFee {
			// remove fee from slice
			subItem.Fee.Result = append(subItem.Fee.Result[:i], subItem.Fee.Result[i+1:]...)
		}
	}
}

func getInvoiceItemQty(
	invoice *model.Invoice,
	order *model.Order,
	invoiceItem *model.InvoiceItem,
) *int {
	switch order.Status {
	case enum.OrderState.Completed:
		if invoice.ReturnStatus == nil {
			if invoiceItem.CompletedQuantity == nil {
				return utils.ParseIntToPointer(0)
			}
			return invoiceItem.CompletedQuantity
		}

		if invoiceItem.CompletedQuantity != nil && invoiceItem.ReturnedQuantity != nil {
			return utils.ParseIntToPointer(*invoiceItem.CompletedQuantity - *invoiceItem.ReturnedQuantity)
		}
	case enum.OrderState.Delivered:
		if invoiceItem.DeliveredQuantity == nil {
			return utils.ParseIntToPointer(0)
		}

		return invoiceItem.DeliveredQuantity
	}

	return utils.ParseIntToPointer(0)
}

func TotalActualVoucherApplied(invoice *model.Invoice, deliveryOrderStatuses []*model.DeliveryOrderStatus, acceptedDOs map[string]bool, sellerApplyVouchers map[string]bool) (totalActualVoucherApplied int64) {
	if invoice == nil {
		return
	}

	for _, deliveryOrderStatus := range deliveryOrderStatuses {
		if deliveryOrderStatus == nil || !acceptedDOs[deliveryOrderStatus.Code] {
			continue
		}
		if invoice.ReceiveInvoiceInfoBy != nil && *invoice.ReceiveInvoiceInfoBy == enum.ReceiveInvoiceInfoBy.INVOICE_BY_DO {
			if invoice.DeliveryOrderCode != nil && *invoice.DeliveryOrderCode != deliveryOrderStatus.Code {
				continue
			}
		}

		for _, voucherAmountDetail := range deliveryOrderStatus.VoucherAmountDetails {
			if voucherAmountDetail == nil {
				continue
			}

			if ok := sellerApplyVouchers[voucherAmountDetail.Code]; !ok {
				continue
			}

			if utils.Contains(invoice.SellerCode, voucherAmountDetail.SellerCodes) {
				totalActualVoucherApplied += voucherAmountDetail.DiscountAmount
			}
		}
	}
	return
}

func TotalActualVoucherReturned(invoiceItem *model.InvoiceItem, invoice *model.Invoice) (totalActualVoucherReturned int) {
	if invoiceItem == nil || invoice == nil || invoiceItem.DiscountDetail == nil ||
		len(invoiceItem.DiscountDetail.VoucherDetails) == 0 ||
		invoiceItem.DeliveredQuantity == nil || *invoiceItem.DeliveredQuantity < 1 ||
		invoiceItem.ReturnedQuantity == nil || *invoiceItem.ReturnedQuantity < 1 {
		return
	}

	for _, voucherDetail := range invoiceItem.DiscountDetail.VoucherDetails {
		if !voucherDetail.IsApply {
			continue
		}
		if utils.Contains(invoice.SellerCode, voucherDetail.SellerCodes) {
			if *invoiceItem.ReturnedQuantity == *invoiceItem.DeliveredQuantity {
				totalActualVoucherReturned += voucherDetail.DiscountValue
			} else {
				cal := float64((voucherDetail.DiscountValue / *invoiceItem.DeliveredQuantity) * *invoiceItem.ReturnedQuantity)
				totalActualVoucherReturned += int(math.Round(cal))
			}
		}
	}
	return totalActualVoucherReturned
}

func getAcceptedDOs(saleOrderCode string) (map[string]bool, []*model.DeliveryOrder, map[string]bool) {
	m, x := map[string]bool{}, map[string]bool{}
	dosRes, err := client.Services.Warehouse.GetDeliveryOrders(&model.DeliveryOrderReq{
		SaleOrderCode: saleOrderCode,
	})
	if err != nil {
		return m, nil, nil
	}

	for _, do := range dosRes {
		if do.Status == nil {
			continue
		}

		if *do.Status == enum.SaleOrderStatus.Delivering ||
			*do.Status == enum.SaleOrderStatus.Delivered ||
			*do.Status == enum.SaleOrderStatus.Completed {
			m[do.DeliveryOrderCode] = true
		}
		if *do.Status == enum.SaleOrderStatus.Delivered ||
			*do.Status == enum.SaleOrderStatus.Completed {
			x[do.DeliveryOrderCode] = true
		}
	}

	return m, dosRes, x
}
