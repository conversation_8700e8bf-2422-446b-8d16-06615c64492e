package action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

// DEPRECATED:
// Before: update skuLimit in product-v2 will call `PUT /sku-limit/history` to update in order-v2
// After: these 2 collections are separated, we don't need to sync between them. SkuLimitHistory should only store info about the quantity purchased today of an sku, by a customer
func UpdateSkuLimitHistory(input *model.SkuLimitUpdateRequest) *common.APIResponse {
	now := time.Now().Add(time.Hour * 7)
	start := time.Date(now.Year(), now.Month(), now.Day(), 17, 0, 0, 0, now.Location())
	start = start.AddDate(0, 0, -1)
	end := start.AddDate(0, 0, input.NumberOfDays)

	return model.SkuLimitHistoryDB.UpdateMany(
		&model.SkuLimitHistory{
			SkuLimitCode: input.Code,
			ComplexQuery: []*bson.M{
				{"start_time": bson.M{"$lte": start}},
				{"end_time": bson.M{"$gte": end}},
			},
		},
		&model.SkuLimitHistory{
			LimitQuantity: input.Quantity,
			NumberOfDays:  input.NumberOfDays,
			IsActive:      input.IsActive,
		})
}

func GetSkuLimitHistoryToday(acc *model.Account, input *model.SkuLimitHistoryListRequest) *common.APIResponse {
	now := time.Now()
	var filter = &model.SkuLimitHistory{
		ComplexQuery: []*bson.M{
			{"start_time": bson.M{"$lte": now}},
			{"end_time": bson.M{"$gte": now}},
		},
	}

	if input.CustomerID != nil {
		filter.CustomerID = *input.CustomerID
	} else {
		filter.AccountID = acc.AccountID
	}

	if len(input.SkuCodes) > 0 {
		filter.ComplexQuery = append(filter.ComplexQuery, &bson.M{
			"sku": bson.M{"$in": input.SkuCodes},
		})
	}

	if len(input.ItemCodes) > 0 {
		filter.ComplexQuery = append(filter.ComplexQuery, &bson.M{
			"item_code": bson.M{"$in": input.ItemCodes},
		})
	}

	res := model.SkuLimitHistoryDB.Query(filter, 0, 0, nil)

	if res.Status == common.APIStatus.Ok {
		var activeSkuLimitHistory []*model.SkuLimitHistory
		for _, skuLimitHistory := range res.Data.([]*model.SkuLimitHistory) {
			// Filter data that is not active
			if skuLimitHistory.IsActive != nil && !*skuLimitHistory.IsActive {
				continue
			}

			activeSkuLimitHistory = append(activeSkuLimitHistory, skuLimitHistory)
		}
		res.Data = activeSkuLimitHistory
	}

	return res
}

func getMapSkuLimitsBySkuItemCodeLive(itemCodes []string, customerID int64) (skuLimitMap map[string]*model.SkuLimit) {
	skuLimitMap = make(map[string]*model.SkuLimit, 0)

	sl, _ := client.Services.Product.GetSKULimitList(&client.SkuLimitListQuery{
		ItemCodes:     itemCodes,
		ForCustomerID: &customerID,
	})
	for _, skuLimit := range sl {
		skuLimitMap[skuLimit.ItemCode] = skuLimit
	}

	return
}
