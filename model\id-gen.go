package model

import (
	"errors"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"math"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

// ORDER is key to gen id order collection
const (
	ORDER               = "ORDER"
	ORDER_SELLER        = "ORDER_SELLER"
	INVOICE             = "INVOICE"
	RECONCILIATION      = "RECONCILIATION"
	RECONCILIATION_ITEM = "RECONCILIATION_ITEM"
)

// IDGen DB entity for gen code
type IDGen struct {
	ID    string `json:"id,omitempty" bson:"_id,omitempty"`
	Value int64  `json:"value,omitempty" bson:"value,omitempty"`
}

// IDGenDB DB model for gen code
var IDGenDB = &db.Instance{
	ColName:        "_id_gen",
	TemplateObject: &IDGen{},
}

// InitIDGenModel init model
func InitIDGenModel(s *mongo.Database) {

	IDGenDB.ApplyDatabase(s)

	genOrder := IDGen{
		ID:    ORDER,
		Value: 505000, // default value
	}
	IDGenDB.Create(genOrder)

	genOrderSeller := IDGen{
		ID:    ORDER_SELLER,
		Value: 1000000, // default value
	}
	IDGenDB.Create(genOrderSeller)

	genInvoice := IDGen{
		ID:    INVOICE,
		Value: 1000000, // default value
	}
	IDGenDB.Create(genInvoice)
}

// convertToCode convert id from int to string
func convertToCode(number int64, length int64, template string) string {
	var result = ""
	var i = int64(0)
	var ln = int64(len(template))
	var capacity = int64(math.Pow(float64(ln), float64(length)))
	number = number % capacity
	for i < length {
		var cur = number % ln
		if i > 0 {
			cur = (cur + int64(result[i-1])) % ln
		}
		result = result + string(template[cur])
		number = number / ln
		i++
	}
	return result
}

// GetOrderID is func gen code for product id
func GetOrderID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: ORDER,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetOrderIdAndErr() (int64, string, error) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: ORDER,
	}, "value", 1)
	if increResult.Status != common.APIStatus.Ok {
		return 0, "", errors.New(increResult.ErrorCode)
	}
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG"), nil
}

// GetSellerOrderID is func gen code for seller order id
func GetSellerOrderID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: ORDER_SELLER,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetInvoiceID is func gen code for invoice
func GetInvoiceID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{ID: INVOICE}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetReconciliationID is func gen code for reconciliation
func GetReconciliationID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: RECONCILIATION,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetReconciliationItemID is func gen code for reconciliation item
func GetReconciliationItemID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: RECONCILIATION_ITEM,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GenCodeWithTime() string {
	now := time.Now()
	return convertToCode(now.UnixNano(), 8, "1246789HJKLXCVBQWERTYUPADFG")
}
