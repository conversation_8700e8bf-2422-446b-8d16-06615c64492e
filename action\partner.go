package action

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	PartnerCodeBuymed   = "BUYMED"
	PartnerNameKiotviet = "kiotviet"
	ActionCreateOrder   = "create_order"
	ActionUpdateOrder   = "update_order"
)

type Params struct {
	Action         string
	BMID           string
	OrderID        string
	OrderStatus    enum.OrderStateValue
	DeliveryStatus enum.OrderStateValue
}

type param struct {
	key     string
	value   interface{}
	enabled bool
}

func GenerateParams(p Params) string {
	// Prepare data in a fixed order to generate signature hash256 correct
	orderedParams := []param{
		{key: "partner_code", value: PartnerCodeBuymed, enabled: true},
		{key: "action", value: p.Action, enabled: p.Action != ""},
		{key: "order_id", value: p.OrderID, enabled: p.OrderID != ""},
		{key: "bm_id", value: p.BMID, enabled: p.BMID != ""},
		{key: "order_status", value: p.OrderStatus, enabled: p.OrderStatus != ""},
		{key: "delivery_status", value: p.DeliveryStatus, enabled: p.DeliveryStatus != ""},
	}

	// Append parameters in the defined order
	params := []string{}
	for _, paramItem := range orderedParams {
		if paramItem.enabled {
			params = append(params, fmt.Sprintf("%s=%s", paramItem.key, paramItem.value))
		}
	}

	queryString := strings.Join(params, "&")

	return queryString
}

func CreateOrderPayload(order *model.Order) *model.SyncOrderCreatePayload {
	if order == nil {
		return nil
	}

	// Lấy thông tin đối tác kiotviet
	partnerInfo := client.Services.Customer.GetPartnerKiotvietInfo(order.CustomerID, PartnerNameKiotviet)
	if partnerInfo.Status != common.APIStatus.Ok {
		return nil
	}

	// Chuyển partnerInfo thành kiểu dữ liệu dễ sử dụng
	dataBytes, err := json.Marshal(partnerInfo.Data)
	if err != nil {
		return nil
	}

	var partnerData []map[string]interface{}
	err = json.Unmarshal(dataBytes, &partnerData)
	if err != nil {
		return nil
	}

	// Lấy identityId từ thông tin partner
	identityId := partnerData[0]["identityId"]

	customer, errGetCustomer := client.Services.Customer.GetCustomerByAccountID(order.AccountID, "")
	if errGetCustomer != nil {
		return nil
	}
	wardCodes := []string{order.WardCode, customer.WardCode, order.CustomerWardCode}
	wardMap := make(map[string]*client.Ward)
	if wards, errGetWard := client.Services.Location.GetWardList(wardCodes, 0, 0); errGetWard == nil {
		for _, ward := range wards {
			wardMap[ward.Code] = ward
		}
	}

	strCustomerId := fmt.Sprintf("%d", order.CustomerID)
	strOrderId := fmt.Sprintf("%d", order.OrderID)

	hashSHA256String := GenerateParams(Params{
		Action:  ActionCreateOrder,
		OrderID: strOrderId,
		BMID:    strCustomerId + GetKiotvietConfigs().ScrectKeySH256ForKiotViet,
	})
	// gen sha256
	hashSHA256 := utils.HashSHA256(hashSHA256String)

	payload := &model.SyncOrderCreatePayload{
		KvID:        identityId.(string),
		PartnerCode: PartnerCodeBuymed,
		Action:      ActionCreateOrder,
		Data: model.OrderCreateData{
			Signature:     hashSHA256,
			OrderID:       strconv.FormatInt(order.OrderID, 10),
			BmID:          strconv.FormatInt(order.CustomerID, 10),
			Note:          getNoteFromOrder(order),
			TotalPrice:    *order.TotalPrice - (int(*order.ExtraFee) + int(*order.PaymentMethodFee) + int(*order.DeliveryMethodFee)),
			TotalDiscount: *order.TotalDiscount,
			RawTotal:      *order.Price,
			CreatedTime:   *order.CreatedTime,
			Province:      getProvinceName(order.CustomerWardCode, wardMap),
			District:      getDistrictName(order.CustomerWardCode, wardMap),
		},
		Time: time.Now().Format("2006-01-02T15:04:05Z"),
	}

	// Lập danh sách các Item từ order.Items
	var items []model.OrderItemCreate
	for _, item := range order.Items {
		if item == nil {
			continue
		}

		// Lập danh sách Combo (các sản phẩm con)
		combos := []model.Combo{}
		if item.SubItems != nil && len(*item.SubItems) > 0 {
			for _, c := range *item.SubItems {
				if c == nil {
					continue
				}
				combos = append(combos, model.Combo{
					SubProductSKU:      c.Sku,
					SubProductCode:     c.ProductCode,
					SubProductName:     getSkuNameFromCode(c.Sku),
					SubProductUnit:     c.Unit,
					SubProductQuantity: c.Quantity,
					SubProductPrice:    c.Price,
				})
			}
		}

		// Tính giá sau khuyến mãi và discount cho mỗi item
		itemPrice := item.Price
		discountPrice := 0
		if item.PriceAfterDiscount != nil && *item.PriceAfterDiscount > 0 {
			itemPrice = *item.PriceAfterDiscount
			discountPrice = item.Price - *item.PriceAfterDiscount
		}

		// Tạo một item mới
		newItem := model.OrderItemCreate{
			ProductSKU:   item.Sku,
			ProductCode:  item.ProductCode,
			ProductName:  getSkuNameFromCode(item.Sku),
			Quantity:     item.Quantity,
			Unit:         item.Unit,
			RawItemPrice: item.Price,
			ItemPrice:    itemPrice,
			Discount:     discountPrice,
			Combo:        combos,
		}

		items = append(items, newItem)
	}

	payload.Data.Items = items

	return payload
}

func UpdateOrderPayload(order *model.Order, returnTicketInfo *model.ReturnTicket) *model.SyncOrderUpdatePayload {
	if order == nil {
		return nil
	}

	// Lấy thông tin đối tác kiotviet
	partnerInfo := client.Services.Customer.GetPartnerKiotvietInfo(order.CustomerID, PartnerNameKiotviet)
	if partnerInfo.Status != common.APIStatus.Ok {
		return nil
	}

	// Chuyển partnerInfo thành kiểu dữ liệu dễ sử dụng
	dataBytes, err := json.Marshal(partnerInfo.Data)
	if err != nil {
		return nil
	}

	var partnerData []map[string]interface{}
	err = json.Unmarshal(dataBytes, &partnerData)
	if err != nil {
		return nil
	}

	// Lấy identityId từ thông tin đối tác
	identityId := partnerData[0]["identityId"]

	deliveryStatus := order.SaleOrderStatus
	// if has return ticket info, set delivery status to returned
	if returnTicketInfo != nil {
		deliveryStatus = enum.SaleOrderStatus.Returned
	}

	orderStatus := order.Status

	lastUpdatedTime := time.Now().Format("2006-01-02T15:04:05Z") // ISO format

	strCustomerId := fmt.Sprintf("%d", order.CustomerID)
	strOrderId := fmt.Sprintf("%d", order.OrderID)

	hashSHA256String := GenerateParams(Params{
		Action:         ActionUpdateOrder,
		OrderID:        strOrderId,
		BMID:           strCustomerId,
		OrderStatus:    orderStatus,
		DeliveryStatus: enum.OrderStateValue(string(deliveryStatus) + GetKiotvietConfigs().ScrectKeySH256ForKiotViet),
	})
	// gen sha256
	hashSHA256 := utils.HashSHA256(hashSHA256String)

	payload := &model.SyncOrderUpdatePayload{
		KvID:        identityId.(string),
		PartnerCode: PartnerCodeBuymed,
		Action:      ActionUpdateOrder,
		Data: model.OrderUpdateData{
			Signature:       hashSHA256,
			OrderID:         strconv.FormatInt(order.OrderID, 10),
			BmID:            strconv.FormatInt(order.CustomerID, 10),
			DeliveryStatus:  string(deliveryStatus), // COMPLETE
			OrderStatus:     orderStatus,
			LastUpdatedTime: lastUpdatedTime,
		},
		Time: time.Now().Format("2006-01-02T15:04:05Z"),
	}

	// Lập danh sách các Item từ order.Items
	var items []model.OrderItemUpdate
	for _, item := range order.Items {
		if item == nil {
			continue
		}

		// Lập danh sách Combo (các sản phẩm con)
		combos := []model.Combo{}
		if item.SubItems != nil && len(*item.SubItems) > 0 {
			for _, c := range *item.SubItems {
				if c == nil {
					continue
				}

				comboItem := model.Combo{
					SubProductSKU:          c.Sku,
					SubProductCode:         c.ProductCode,
					SubProductLotDate:      getLotDatesFromOutboundInfos(c.OutboundInfos),
					SubProductDeliveredQty: getDeliveredQuantityFromOutboundInfos(c.OutboundInfos),
					SubProductReturnedQty:  utils.ParseIntToPointer(0),
				}

				if returnTicketInfo != nil && returnTicketInfo.Items != nil {
					for _, returnTicketItem := range returnTicketInfo.Items {
						if returnTicketItem == nil {
							continue
						}
						for _, returnTicketSubItem := range returnTicketItem.SubItems {
							if returnTicketSubItem == nil {
								continue
							}
							if returnTicketSubItem.SKU == c.Sku {
								if len(returnTicketSubItem.ReturnInfos) > 0 {
									comboItem.SubProductReturnedQty = returnTicketSubItem.ReturnInfos[0].ConfirmedQuantity
								}
							}
						}
					}
				}

				combos = append(combos, comboItem)
			}
		}

		// Tạo một item mới
		newItem := model.OrderItemUpdate{
			ProductSKU:   item.Sku,
			ProductCode:  item.ProductCode,
			DeliveredQty: utils.ParseIntToPointer(0),
			ReturnedQty:  utils.ParseIntToPointer(0),
			LotDate:      getLotDatesFromOutboundInfos(item.OutboundInfos),
			Combo:        combos,
		}

		if item.DeliveredQuantity != nil {
			newItem.DeliveredQty = item.DeliveredQuantity
		}

		if returnTicketInfo != nil && returnTicketInfo.Items != nil {
			for _, returnTicketItem := range returnTicketInfo.Items {
				if returnTicketItem == nil {
					continue
				}
				if returnTicketItem.SKU == item.Sku {
					if len(returnTicketItem.ReturnInfos) > 0 {
						newItem.ReturnedQty = returnTicketItem.ReturnInfos[0].ConfirmedQuantity
					} else {
						if len(returnTicketItem.SubItems) > 0 {
							// If item is a combo, returned quantity is 1.
							newItem.ReturnedQty = utils.ParseIntToPointer(1)
						}
					}
				}
			}
		}

		items = append(items, newItem)
	}

	payload.Data.Items = items

	return payload
}

func getSkuNameFromCode(skuCode string) string {
	product := client.Services.Product.GetProductInfo(skuCode, 0)
	if product == nil {
		return ""
	}

	return product.Name
}

func getNoteFromOrder(order *model.Order) string {
	if order != nil && order.Note != nil {
		return *order.Note
	}
	return ""
}

func getLotDatesFromOutboundInfos(outboundInfos []*model.OutboundInfo) string {
	if len(outboundInfos) == 0 {
		return ""
	}
	var lotDates []string
	for _, outboundInfo := range outboundInfos {
		if outboundInfo == nil {
			continue
		}
		if outboundInfo.Lot != "" && outboundInfo.ExpDate != "" {
			lotDates = append(lotDates, fmt.Sprintf("%s/%s", outboundInfo.Lot, outboundInfo.ExpDate))
		}
	}
	return strings.Join(lotDates, ",")
}

func getDeliveredQuantityFromOutboundInfos(outboundInfos []*model.OutboundInfo) *int {
	var deliveredQuantity int
	for _, outboundInfo := range outboundInfos {
		if outboundInfo == nil {
			continue
		}
		if outboundInfo.Quantity > 0 {
			deliveredQuantity += outboundInfo.Quantity
		}
	}
	return utils.ParseIntToPointer(deliveredQuantity)
}

func LatestCreateOrderInfo(createData *model.OrderCreateData) *model.LatestOrderData {
	if createData == nil {
		return nil
	}

	if createData.Items == nil {
		createData.Items = []model.OrderItemCreate{}
	}

	items := make([]model.LatestOrderItemInfo, len(createData.Items))
	for i, item := range createData.Items {
		items[i] = model.LatestOrderItemInfo{
			ProductSKU:   item.ProductSKU,
			ProductCode:  item.ProductCode,
			ProductName:  item.ProductName,
			Quantity:     item.Quantity,
			Unit:         item.Unit,
			RawItemPrice: item.RawItemPrice,
			ItemPrice:    item.ItemPrice,
			Discount:     item.Discount,
			DeliveredQty: new(int),
			ReturnedQty:  new(int),
			LotDate:      "",
			Combo:        item.Combo,
		}
	}

	return &model.LatestOrderData{
		OrderID:         createData.OrderID,
		BmID:            createData.BmID,
		Note:            createData.Note,
		TotalPrice:      createData.TotalPrice,
		TotalDiscount:   createData.TotalDiscount,
		RawTotal:        createData.RawTotal,
		CreatedTime:     createData.CreatedTime,
		Province:        createData.Province,
		District:        createData.District,
		DeliveryStatus:  "",
		OrderStatus:     "",
		LastUpdatedTime: "",
		Items:           items,
		Signature:       createData.Signature,
	}
}

func LatestUpdateOrderInfo(updateData *model.OrderUpdateData, latestResult *model.LatestOrderData) *model.LatestOrderData {
	if updateData == nil || latestResult == nil {
		return nil
	}

	if updateData.Items == nil {
		updateData.Items = []model.OrderItemUpdate{}
	}

	if latestResult.Items == nil {
		latestResult.Items = []model.LatestOrderItemInfo{}
	}

	// Map items by product code
	itemsMap := make(map[string]model.LatestOrderItemInfo)
	for _, item := range latestResult.Items {
		itemsMap[item.ProductCode] = item
	}

	items := make([]model.LatestOrderItemInfo, len(updateData.Items))
	for i, item := range updateData.Items {
		if latestItem, exists := itemsMap[item.ProductCode]; exists {
			items[i] = model.LatestOrderItemInfo{
				ProductSKU:   item.ProductSKU,
				ProductCode:  item.ProductCode,
				ProductName:  latestItem.ProductName,
				Quantity:     latestItem.Quantity,
				Unit:         latestItem.Unit,
				RawItemPrice: latestItem.RawItemPrice,
				ItemPrice:    latestItem.ItemPrice,
				Discount:     latestItem.Discount,
				DeliveredQty: item.DeliveredQty,
				ReturnedQty:  item.ReturnedQty,
				LotDate:      item.LotDate,
				Combo:        item.Combo,
			}
		}
	}
	return &model.LatestOrderData{
		OrderID:         updateData.OrderID,
		BmID:            updateData.BmID,
		Note:            latestResult.Note,
		TotalPrice:      latestResult.TotalPrice,
		TotalDiscount:   latestResult.TotalDiscount,
		RawTotal:        latestResult.RawTotal,
		CreatedTime:     latestResult.CreatedTime,
		Province:        latestResult.Province,
		District:        latestResult.District,
		DeliveryStatus:  updateData.DeliveryStatus,
		OrderStatus:     updateData.OrderStatus,
		LastUpdatedTime: updateData.LastUpdatedTime,
		Items:           items,
		Signature:       updateData.Signature,
	}
}

func SyncCreateOrderInfoToKiotvietPartner(order *model.Order) {
	syncData := CreateOrderPayload(order)
	if syncData != nil {
		resp := client.Services.Kiotviet.SyncCreateOrderInfoToKiotvietPartner(syncData)

		logResult := &model.SyncOrderCreateInfoLog{
			CustomerID:  order.CustomerID,
			OrderID:     order.OrderID,
			RequestData: syncData,
			Action:      ActionCreateOrder,
		}

		if resp != nil {
			logResult.RequestID = resp.RequestID
			logResult.Success = resp.Success
			logResult.ErrorCode = resp.ErrorCode
		}

		model.SyncOrderPartnerKiotvietDB.Create(logResult)

		if resp.Success {
			createData := LatestCreateOrderInfo(&syncData.Data)
			if createData != nil {
				lastestResult := &model.LatestOrderStatusPartnerKiotviet{
					CustomerID: order.CustomerID,
					KvID:       sdk.ParseInt64(syncData.KvID, 0),
					OrderID:    order.OrderID,
					Data:       createData,
				}

				model.LatestOrderStatusKiotvietDB.Create(lastestResult)
			}
		}

	}
}

func SyncUpdateOrderStatusToKiotvietPartner(orderUpdated *model.Order, returnTicketInfo *model.ReturnTicket) {
	syncData := UpdateOrderPayload(orderUpdated, returnTicketInfo)
	if syncData != nil {
		resp := client.Services.Kiotviet.SyncUpdateOrderInfoToKiotvietPartner(syncData)
		logResult := &model.SyncOrderUpdateInfoLog{
			CustomerID:  orderUpdated.CustomerID,
			OrderID:     orderUpdated.OrderID,
			RequestData: syncData,
			Action:      ActionUpdateOrder,
		}

		if resp != nil {
			logResult.RequestID = resp.RequestID
			logResult.Success = resp.Success
			logResult.ErrorCode = resp.ErrorCode
		}

		model.SyncOrderPartnerKiotvietDB.Create(logResult)

		if resp != nil && resp.Success {
			filter := model.LatestOrderStatusPartnerKiotviet{
				OrderID: orderUpdated.OrderID,
			}

			latestResultByOrderId := model.LatestOrderStatusKiotvietDB.QueryOne(filter)
			if latestResultByOrderId.Status == common.APIStatus.Ok {
				updateData := LatestUpdateOrderInfo(&syncData.Data, latestResultByOrderId.Data.([]*model.LatestOrderStatusPartnerKiotviet)[0].Data)

				if updateData != nil {
					lastestUpdater := &model.LatestOrderStatusPartnerKiotviet{
						CustomerID: orderUpdated.CustomerID,
						KvID:       sdk.ParseInt64(syncData.KvID, 0),
						OrderID:    orderUpdated.OrderID,
						Data:       updateData,
					}
					// Overwrite lastest result by orderID to reconciliation
					model.LatestOrderStatusKiotvietDB.Upsert(filter, lastestUpdater)
				}

			}
		}
	}
}

func GetReturnTicketInfo(returnTicketInfo *model.ReturnTicket) *common.APIResponse {
	if returnTicketInfo == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Missing return ticket info",
		}
	}

	if returnTicketInfo.Status == model.ReturnTicketStatus.COMPLETED {
		order, errRes := getOrder(returnTicketInfo.OrderID)
		if errRes != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Missing order info",
			}
		}

		order.SaleOrderStatus = enum.SaleOrderStatus.Returned

		if GetKiotvietConfigs().AllowSyncUpdateOrderKiotviet {
			if utils.IsContains(order.CustomerTags, "KIOTVIET") {
				go sdk.Execute(func() {
					SyncUpdateOrderStatusToKiotvietPartner(order, returnTicketInfo)
				})
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
	}
}

func ReconciliationSyncOrderInfoKiotvietPartner(query *model.ReconciliationOrderKiotviet, limit int64, getTotal bool) *common.APIResponse {
	if query.StartTime != "" && query.EndTime != "" {
		startParsed, startFormat, errStartTime := utils.ParseTimeWithFormats(query.StartTime)
		if errStartTime != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid start time",
			}
		}

		endParsed, endFormat, errEndTime := utils.ParseTimeWithFormats(query.EndTime)
		if errEndTime != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid end time",
			}
		}

		if startFormat != endFormat {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Start time and end time must be in the same format",
			}
		}

		if !startParsed.IsZero() {
			query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
				"created_time": bson.M{
					"$gte": startParsed,
				},
			})
		}

		if !endParsed.IsZero() {
			query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
				"created_time": bson.M{
					"$lte": endParsed,
				},
			})
		}
	}

	if query.OrderID != 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"order_id": query.OrderID,
		})
	}

	if query.CustomerID != 0 {
		query.ComplexQuery = append(query.ComplexQuery, &primitive.M{
			"customer_id": query.CustomerID,
		})
	}

	if query.LastID != primitive.NilObjectID {
		comparisonOperator := "$lt"
		if query.SortType == "ASC" {
			comparisonOperator = "$gt"
		}
		query.ComplexQuery = append(
			query.ComplexQuery,
			&primitive.M{
				"_id": bson.M{
					comparisonOperator: query.LastID,
				},
			},
		)
	}

	sortValue := -1
	if query.SortType == "ASC" {
		sortValue = 1
	}

	qResult := model.LatestOrderStatusKiotvietDB.Query(query, 0, limit, &primitive.M{"_id": sortValue})
	if qResult.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    qResult.Status,
			Message:   qResult.Message,
			ErrorCode: "QUERY_FAILED",
		}
	}

	if getTotal {
		countResult := model.LatestOrderStatusKiotvietDB.Count(query)
		qResult.Total = countResult.Total
	}

	return qResult
}
