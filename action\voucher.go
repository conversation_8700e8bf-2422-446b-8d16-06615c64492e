package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

// CartGetActiveVoucher ...
func CartGetActiveVoucher(acc *model.Account, offset, limit int64, getTotal, getValidate bool, search, scope string,
	systemDisplay string, sourceDetail *model.OrderSourceDetail) *common.APIResponse {
	customer, errCustomerRes := getCustomerProfile(acc)
	if errCustomerRes != nil {
		return errCustomerRes
	}

	cartRes := model.CartDB.QueryOne(&model.Cart{
		CustomerID: customer.CustomerID,
	})
	if cartRes.Status != common.APIStatus.Ok {
		// case cart not found
		cart := &model.Cart{
			AccountID:     customer.AccountID,
			CustomerCode:  customer.CustomerCode,
			CustomerID:    customer.CustomerID,
			ProvinceCode:  customer.ProvinceCode,
			DistrictCode:  customer.DistrictCode,
			WardCode:      customer.WardCode,
			CustomerScope: customer.Scope,
			Items:         make([]*model.CartItem, 0),
		}
		regions, er := client.Services.Location.GetRegionList(0, 100, []string{cart.ProvinceCode})
		if er == nil {
			for _, region := range regions {
				cart.RegionCodes = append(cart.RegionCodes, region.Code)
				if region.Scope == "SALE_REGION" {
					cart.SaleRegionCodes = append(cart.SaleRegionCodes, region.Code)
				}
			}
		}

		promoApply, err := client.Services.Promotion.GetListVoucherActive(&client.CheckVoucherRequest{
			Cart:        cart,
			AccountID:   cart.AccountID,
			Offset:      offset,
			Limit:       limit,
			GetTotal:    getTotal,
			GetValidate: getValidate,
			Search:      search,
			Scope:       scope,

			GetVoucherAuto: true,
			SystemDisplay:  systemDisplay,
			SourceDetail:   sourceDetail,
		})

		msg := ""
		if err != nil {
			msg = err.Error()
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: msg,
			}
		} else {
			msg = promoApply.Message
		}
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: msg,
			Data:    promoApply.Data,
			Total:   promoApply.Total,
		}
	}
	cart := cartRes.Data.([]*model.Cart)[0]
	cartItemRes := model.CartItemDB.Query(&model.CartItem{CartID: cart.CartID}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		_ = model.CartDB.Delete(&model.Cart{CustomerID: customer.CustomerID})
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Not found any matched cart",
		}
	}
	cart.Items = cartItemRes.Data.([]*model.CartItem)
	cart.ProvinceCode = customer.ProvinceCode
	cart.DistrictCode = customer.DistrictCode
	cart.WardCode = customer.WardCode
	cart.CustomerScope = customer.Scope

	resetCartPrice(cart)
	_ = handleProcessContentItems(cart, customer, nil, false)
	_ = handleProcessPriceItems(customer, cart)
	_, _ = handleProcessDeliveryAndPaymentMethod(cart, customer)

	tempCartItems := make([]*model.CartItem, 0)
	for _, item := range cart.Items {
		if item.IsSelected != nil && *item.IsSelected {
			tempCartItems = append(tempCartItems, item)
		}
	}
	tempCart := *cart
	tempCart.TotalPrice = cart.TotalPrice
	tempCart.TotalQuantity = cart.TotalQuantitySelected
	tempCart.TotalItem = cart.TotalItemSelected
	tempCart.Items = tempCartItems

	promoApply, err := client.Services.Promotion.GetListVoucherActive(&client.CheckVoucherRequest{
		Cart:           tempCart,
		AccountID:      cart.AccountID,
		Offset:         offset,
		Limit:          limit,
		GetTotal:       getTotal,
		GetValidate:    getValidate,
		Search:         search,
		Scope:          scope,
		GetVoucherAuto: true,

		SystemDisplay: systemDisplay,
		SourceDetail:  sourceDetail,
	})

	msg := ""
	if err != nil {
		msg = err.Error()
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: msg,
		}
	} else {
		msg = promoApply.Message
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: msg,
		Data:    promoApply.Data,
		Total:   promoApply.Total,
	}
}
