package reconcile_action

import (
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ConfirmReconciliation(id *primitive.ObjectID, as *model.ActionSource) *common.APIResponse {
	reconciliationF := model.Reconciliation{ID: id}
	result := model.ReconciliationDB.QueryOne(reconciliationF)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	reconciliation := result.Data.([]*model.Reconciliation)[0]

	model.ReconcileLogReqDb.Create(&model.ReconcileLogReq{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		ReqType:                    model.ReconcileLogReqType.Confirmed,
		ActionSource:               as.Account,
	})

	if reconciliation.ReconciliationStatus == model.ReconciliationStatus.Completed {
		return result
	}

	if !reconciliation.AllowConfirmTime.Before(time.Now()) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      result.Data,
			Message:   "Chưa đến thời điểm thực hiện xác nhận",
			ErrorCode: "UNREACHABLE_CONFIRM_TIME",
		}
	}

	reconciliationItemF := model.ReconciliationItem{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		OperationAnd: []bson.M{
			{
				"order_id": bson.M{
					"$exists": true,
				},
			},
		},
	}
	distinctRes := model.ReconciliationItemDB.Distinct(reconciliationItemF, "order_id")
	if distinctRes.Status != common.APIStatus.Ok {
		return distinctRes
	}

	orderIds := distinctRes.Data.([]interface{})
	triggerRes := triggerConfirmReconcileOrderSeller(
		reconciliation.SellerCode,
		orderIds,
	)
	if triggerRes.Status != common.APIStatus.Ok {
		log.Printf("trigger CompleteReconciliation failed: %#v\n", triggerRes)
	}

	updater := model.Reconciliation{
		ReconciliationStatus: model.ReconciliationStatus.Confirmed,
	}
	result = model.ReconciliationDB.UpdateOne(reconciliationF, updater)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	go sdk.Execute(func() {
		sendInvoiceReadyTime := time.Now().Add(time.Hour)
		model.SendInvoiceJob.Push(reconciliation, &job.JobItemMetadata{
			Keys: []string{
				reconciliation.SellerCode,
				reconciliation.ReconcileScheduleTimeIndex,
			},
			ReadyTime: &sendInvoiceReadyTime,
		})

		ScheduleSendFeeInvoice(reconciliation)
	})

	return result
}

func triggerConfirmReconcileOrderSeller(
	sellerCode string,
	orderIds []interface{},
) *common.APIResponse {
	orderIDs := make([]int64, len(orderIds))
	for i, v := range orderIds {
		orderIDs[i] = v.(int64)
	}

	result := client.Services.SellerMis.
		ConfirmReconcileOrderSeller(sellerCode, orderIDs)
	return result
}
