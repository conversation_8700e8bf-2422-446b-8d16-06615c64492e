package tool

import (
	"fmt"
	"log"
	"runtime/debug"
	"strconv"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Analyze struct {
	OrgUid     int64  `bson:"org_uid,omitempty" json:"orgUid,omitempty"`
	EntityUid  int64  `bson:"entity_uid,omitempty" json:"entityUid,omitempty"`
	ServiceUid int64  `bson:"service_uid,omitempty" json:"serviceUid,omitempty"`
	SkuUid     string `bson:"sku_uid" json:"skuUid,omitempty"`
	SkuName    string `bson:"sku_name,omitempty" json:"skuName,omitempty"`

	ProductId  int64  `bson:"product_id,omitempty" json:"productId,omitempty"`
	SellerName string `bson:"seller_name,omitempty" json:"sellerName,omitempty"`

	ReconcileScheduleTimeIndex string `bson:"reconcile_schedule_time_index" json:"reconcileScheduleTimeIndex"`

	Info []*model.ReconciliationItem `bson:"info,omitempty" json:"info,omitempty"`

	TotalRevenue float64 `bson:"total_revenue,omitempty" json:"totalRevenue,omitempty"`
	TotalPayment float64 `bson:"total_payment,omitempty" json:"totalPayment,omitempty"`

	Cost        float64   `bson:"cost,omitempty" json:"cost,omitempty"`
	Tax         float64   `bson:"tax,omitempty" json:"tax,omitempty"`
	CollectTime time.Time `bson:"collectTime" json:"collectTime,omitempty"`
}

type ReconciliationRun struct {
	Org       int64     `bson:"org,omitempty" json:"org,omitempty"`
	Services  int64     `bson:"services,omitempty" json:"services,omitempty"`
	Entity    int64     `bson:"entity,omitempty" json:"entity,omitempty"`
	TimeIndex string    `bson:"timeIndex,omitempty" json:"timeIndex,omitempty"`
	Items     []Analyze `bson:"items,omitempty" json:"items,omitempty"`
}

func BuildBillingData(data []*model.ReconciliationItem, seller client.Seller, entity client.EntityTf, timeIndex string) ReconciliationRun {
	reconInfo := ReconciliationRun{
		Org:       entity.OrgID,
		Services:  1,
		Entity:    entity.EntityID,
		TimeIndex: timeIndex,
	}
	var analyzes []Analyze
	analyzesM := make(map[string]Analyze)
	for _, item := range data {
		var key string
		var collectionTime time.Time
		if item.Sku != "" && (item.CompletedTime != nil || item.CompletedDebtTime != nil) {
			var date time.Time
			if item.CompletedTime != nil {
				key = fmt.Sprintf("%s-%s", item.Sku, item.CompletedTime.Format("2006-02-01"))
				date = time.Date(item.CompletedTime.Year(), item.CompletedTime.Month(), item.CompletedTime.Day(), 0, 0, 0, 0, time.UTC)
			} else {
				key = fmt.Sprintf("%s-%s", item.Sku, item.CompletedDebtTime.Format("2006-02-01"))
				date = time.Date(item.CompletedDebtTime.Year(), item.CompletedDebtTime.Month(), item.CompletedDebtTime.Day(), 0, 0, 0, 0, time.UTC)
			}
			d := &date
			collectionTime = d.In(model.VNTimeZone)
		} else {
			item.Sku = string(item.FeeType)
			key = fmt.Sprintf("%s-%s", item.FeeType, item.CreatedTime.Format("2006-02-01"))
			date := time.Date(item.CreatedTime.Year(), item.CreatedTime.Month(), item.CreatedTime.Day(), 0, 0, 0, 0, time.UTC)
			d := &date
			collectionTime = d.In(model.VNTimeZone)
		}
		if a, ok := analyzesM[key]; ok {
			items := a.Info
			items = append(items, item)
			value := reconcile_action.CalculateViaItems(items)
			a.TotalRevenue = float64(value.TotalRevenue)
			a.TotalPayment = float64(value.TotalPayment)
			a.Cost = float64(value.FulfillmentFee + value.ListingFee + value.TotalBuyerFee)
			a.Info = items
			analyzesM[key] = a
		} else {
			sellerName := seller.DisplayName
			if seller.DisplayName == "" {
				sellerName = seller.Name
			}
			value := reconcile_action.CalculateViaItems([]*model.ReconciliationItem{item})
			analyze := Analyze{
				SellerName:                 sellerName,
				OrgUid:                     entity.OrgID,
				EntityUid:                  entity.EntityID,
				ServiceUid:                 1,
				SkuUid:                     item.Sku,
				ProductId:                  item.ProductId,
				ReconcileScheduleTimeIndex: timeIndex,
				TotalRevenue:               float64(value.TotalRevenue),
				TotalPayment:               float64(value.TotalPayment),
				Cost:                       float64(value.FulfillmentFee + value.ListingFee + value.TotalBuyerFee),
				CollectTime:                collectionTime,
				Info:                       []*model.ReconciliationItem{item},
			}
			analyzesM[key] = analyze
		}
	}
	for _, v := range analyzesM {
		analyzes = append(analyzes, v)
	}
	reconInfo.Items = analyzes
	return reconInfo
}

func recoverFunc() {
	if r := recover(); r != nil {
		log.Printf("panic: %s - %s", r, string(debug.Stack()))
	}
}

func SyncReconciliation(reconciliationF model.Reconciliation) {
	defer recoverFunc()
	reconciliationRes := model.ReconciliationDB.QueryOne(reconciliationF)
	if reconciliationRes.Status != common.APIStatus.Ok {
		panic(reconciliationRes.Message)
	}
	recon := reconciliationRes.Data.([]*model.Reconciliation)[0]
	err := client.Services.Billing.PutReconciliation(recon)
	if err != nil {
		panic(err)
	}
}

const (
	CustomSyncCreate = "CustomSyncCreate"
	CustomSyncUpdate = "CustomSyncUpdate"
	CustomSyncDelete = "CustomSyncDelete"
)

type CustomRISync struct {
	Item Analyze `bson:"item,omitempty" json:"item,omitempty"`
	Type string  `bson:"type,omitempty" json:"type,omitempty"`
}

func SyncRICreateCustom(ri *model.ReconciliationItem) {
	defer recoverFunc()
	seller, err := client.Services.Seller.GetSellerByCode(ri.SellerCode)
	if err != nil {
		panic(err)
	}
	if seller.EntityID == 0 {
		seller.EntityID = 2588
	}
	entity, err := client.Services.Billing.GetEntity(seller.EntityID)
	if err != nil {
		panic(err)
	}
	sellerName := seller.DisplayName
	if seller.DisplayName == "" {
		sellerName = seller.Name
	}
	var collectionTime time.Time
	if ri.Sku != "" && ri.CompletedTime != nil {
		date := time.Date(ri.CompletedTime.Year(), ri.CompletedTime.Month(), ri.CompletedTime.Day(), 0, 0, 0, 0, time.UTC)
		d := &date
		collectionTime = d.In(model.VNTimeZone)
	} else {
		ri.Sku = string(ri.FeeType)
		date := time.Date(ri.CreatedTime.Year(), ri.CreatedTime.Month(), ri.CreatedTime.Day(), 0, 0, 0, 0, time.UTC)
		d := &date
		collectionTime = d.In(model.VNTimeZone)
	}
	value := reconcile_action.CalculateViaItems([]*model.ReconciliationItem{ri})
	analyze := Analyze{
		SellerName:                 sellerName,
		OrgUid:                     entity.OrgID,
		EntityUid:                  entity.EntityID,
		ServiceUid:                 1,
		SkuUid:                     ri.Sku,
		ProductId:                  ri.ProductId,
		ReconcileScheduleTimeIndex: ri.ReconcileScheduleTimeIndex,
		TotalRevenue:               float64(value.TotalRevenue),
		TotalPayment:               float64(value.TotalPayment),
		Cost:                       float64(value.FulfillmentFee + value.ListingFee + value.TotalBuyerFee),
		CollectTime:                collectionTime,
		Info:                       []*model.ReconciliationItem{ri},
	}
	err = client.Services.Billing.PutRICustomRealTime(CustomRISync{
		Type: CustomSyncCreate,
		Item: analyze,
	})
	if err != nil {
		log.Print(err)
	}
}

func SyncRIDeleteCustom(ri *model.ReconciliationItem) {
	defer recoverFunc()
	seller, err := client.Services.Seller.GetSellerByCode(ri.SellerCode)
	if err != nil {
		panic(err)
	}
	if seller.EntityID == 0 {
		seller.EntityID = 2588
	}
	entity, err := client.Services.Billing.GetEntity(seller.EntityID)
	if err != nil {
		panic(err)
	}
	sellerName := seller.DisplayName
	if seller.DisplayName == "" {
		sellerName = seller.Name
	}
	var collectionTime time.Time
	if ri.Sku != "" && ri.CompletedTime != nil {
		date := time.Date(ri.CompletedTime.Year(), ri.CompletedTime.Month(), ri.CompletedTime.Day(), 0, 0, 0, 0, time.UTC)
		d := &date
		collectionTime = d.In(model.VNTimeZone)
	} else {
		ri.Sku = string(ri.FeeType)
		date := time.Date(ri.CreatedTime.Year(), ri.CreatedTime.Month(), ri.CreatedTime.Day(), 0, 0, 0, 0, time.UTC)
		d := &date
		collectionTime = d.In(model.VNTimeZone)
	}
	value := reconcile_action.CalculateViaItems([]*model.ReconciliationItem{ri})
	analyze := Analyze{
		SellerName:                 sellerName,
		OrgUid:                     entity.OrgID,
		EntityUid:                  entity.EntityID,
		ServiceUid:                 1,
		SkuUid:                     ri.Sku,
		ProductId:                  ri.ProductId,
		ReconcileScheduleTimeIndex: ri.ReconcileScheduleTimeIndex,
		TotalRevenue:               float64(value.TotalRevenue),
		TotalPayment:               float64(value.TotalPayment),
		Cost:                       float64(value.FulfillmentFee + value.ListingFee + value.TotalBuyerFee),
		CollectTime:                collectionTime,
		Info:                       []*model.ReconciliationItem{ri},
	}
	err = client.Services.Billing.PutRICustomRealTime(CustomRISync{
		Type: CustomSyncUpdate,
		Item: analyze,
	})
	if err != nil {
		log.Print(err)
	}
}

func SyncRecon(reconciliationF model.Reconciliation, seller *client.Seller) {
	defer recoverFunc()
	reconciliationRes := model.ReconciliationDB.QueryOne(reconciliationF)
	if reconciliationRes.Status != common.APIStatus.Ok {
		log.Println(reconciliationRes.Message)
		return
	}
	recon := reconciliationRes.Data.([]*model.Reconciliation)[0]
	err := client.Services.Billing.PutReconciliation(recon)
	if err != nil {
		log.Println(err)
		return
	}
	filter := model.ReconciliationItem{
		SellerCode:                 recon.SellerCode,
		ReconcileScheduleTimeIndex: recon.ReconcileScheduleTimeIndex,
	}
	total := model.ReconciliationItemDB.Count(filter).Total
	rs := model.ReconciliationItemDB.Query(filter, 0, total, nil)
	if rs.Status != common.APIStatus.Ok {
		log.Println(reconciliationRes.Message)
		return
	}
	reconciliationItems := rs.Data.([]*model.ReconciliationItem)
	if seller == nil {
		s, err := client.Services.Seller.GetSellerByCode(recon.SellerCode)
		if err != nil {
			log.Println(err)
			return
		}
		seller = s
	}
	if seller.EntityID == 0 {
		seller.EntityID = 2588
	}
	entity, err := client.Services.Billing.GetEntity(seller.EntityID)
	if err != nil {
		log.Println(err)
		return
	}
	analyzes := BuildBillingData(reconciliationItems, *seller, entity, recon.ReconcileScheduleTimeIndex)
	err = client.Services.Billing.PutReconciliationItems(analyzes)
	if err != nil {
		log.Println(err)
		return
	}
}

func ProcessOrderToReconciliationItem(orderID int64) {
	defer recoverFunc()
	fOrder := model.Order{
		OrderID: orderID,
	}
	orderRes := model.OrderDB.QueryOne(fOrder)
	if orderRes.Status != common.APIStatus.Ok {
		log.Printf("%d: %s\n", orderID, orderRes.Message)
		return
	}
	order := orderRes.Data.([]*model.Order)[0]

	// Nếu đơn hàng chưa được fill phí sản phẩm thì fill phí sản phẩm
	if conf.Config.IsApplyProductFee(order.CreatedTime) && (order.FillProductFee == nil || !*order.FillProductFee) {
		fmt.Println("FillOrderProductFee ", orderID)
		FillOrderProductFee(orderID)
	}

	if order.Status == enum.OrderState.Completed &&
		(order.CompletedTime != nil || order.CompletedDebtTime != nil) {
		orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "ProcessOrderToReconciliationItem")
		if orderItemPartitionDB == nil {
			log.Println("partition not found")
			return
		}
		fOrderItem := model.OrderItem{
			OrderID: orderID,
		}
		result := orderItemPartitionDB.Query(fOrderItem, 0, 0, nil)
		if result.Status == common.APIStatus.NotFound {
			log.Println("order items not found")
			return
		} else if result.Status != common.APIStatus.Ok {
			log.Printf("%d: %s\n", orderID, result.Message)
			return
		}
		orderItems := result.Data.([]*model.OrderItem)

		sellerOrderItemMap := make(map[string][]*model.OrderItem)
		var sellerCodes []string
		for _, item := range orderItems {
			if !utils.IsContains(sellerCodes, item.SellerCode) && item.SellerClass != nil && *item.SellerClass == enum.SellerClass.EXTERNAL {
				sellerCodes = append(sellerCodes, item.SellerCode)
			}
			if m, ok := sellerOrderItemMap[item.SellerCode]; ok {
				m = append(m, item)
				sellerOrderItemMap[item.SellerCode] = m
			} else {
				sellerOrderItemMap[item.SellerCode] = []*model.OrderItem{item}
			}
		}
		var sellerMap map[string]*client.Seller
		var categories []client.Category
		var subCategories []client.SellerSubCategory
		var scheduleSettings []*client.ReconciliationScheduleSetting
		var feeConfigs []client.SellerReconciliationFeeConfig

		// fetch Data Objects
		wg := new(sync.WaitGroup)
		wg.Add(5)
		go func() {
			defer wg.Done()
			sellerMap = client.Services.Seller.GetSellerMapFromCodes(sellerCodes)
		}()
		go func() {
			defer wg.Done()
			categories = client.Services.Product.GetSellerCategoriesLevel()
		}()
		go func() {
			defer wg.Done()
			subCategories = client.Services.Product.GetSellerSubCategories()
		}()
		go func() {
			defer wg.Done()
			scheduleSettings = client.Services.Seller.GetReconciliationScheduleSetting()
		}()
		go func() {
			defer wg.Done()
			feeConfigRes := client.Services.Seller.GetSellerReconciliationFeeConfigs()
			if feeConfigRes.Status == common.APIStatus.Ok {
				feeConfigs = feeConfigRes.Data.([]client.SellerReconciliationFeeConfig)
			}
		}()
		wg.Wait()

		for _, sellerCode := range sellerCodes {
			seller := sellerMap[sellerCode]
			if seller == nil || seller.Level == nil {
				continue
			}
			var completedTime time.Time
			if order.CompletedTime != nil {
				completedTime = *order.CompletedTime
			} else {
				completedTime = *order.CompletedDebtTime
			}
			schedule := reconcile_action.GetSchedule(seller, completedTime)
			_, scheduleTime, index, _, err := reconcile_action.GetReconcileTime(schedule, completedTime, scheduleSettings)
			if err != nil {
				log.Printf("Trigger order completed getTime error: %v\n", err)
				continue
			}
			// Special Revenue
			var specRevenues []model.ReconciliationSpecRevenue

			var lastID *primitive.ObjectID
			for {
				query := bson.M{
					"schedule": index,
				}
				if lastID != nil {
					query["_id"] = bson.M{"$gt": lastID}
				}

				res := model.ReconciliationSpecRevenueDB.Query(query, 0, 1000, &primitive.M{"_id": 1})

				if res.Status != common.APIStatus.Ok {
					break
				}

				data := res.Data.([]model.ReconciliationSpecRevenue)

				specRevenues = append(specRevenues, data...)

				if len(data) == 0 || len(data) < 1000 {
					break
				}

				lastID = data[len(data)-1].ID
			}

			// total := model.ReconciliationSpecRevenueDB.Count(model.ReconciliationSpecRevenue{Schedule: index}).Total
			// specRevRes := model.ReconciliationSpecRevenueDB.Query(model.ReconciliationSpecRevenue{Schedule: index}, 0, total, nil)
			// if specRevRes.Status == common.APIStatus.Ok {
			// 	specRevenues = specRevRes.Data.([]model.ReconciliationSpecRevenue)
			// }
			mapOrderArrSKUReturn := getReturnQuantityByOrderIds([]string{strconv.Itoa(int(orderID))}, scheduleTime)

			// ======================== in turn ========================
			if orderSItems, ok := sellerOrderItemMap[sellerCode]; ok {
				var recons []*model.ReconciliationItem
				for _, item := range orderSItems {
					reconItem := OrderItemToRI(
						item,
						*seller,
						&mapOrderArrSKUReturn,
						specRevenues,
						feeConfigs,
						order,
						categories,
						subCategories,
						index,
					)
					recons = append(recons, &reconItem)
				}
				if seller.EntityID == 0 {
					seller.EntityID = 2588
				}
				entity, err := client.Services.Billing.GetEntity(seller.EntityID)
				if err != nil {
					log.Printf("Trigger order completed GetEntity error: %v\n", err)
					continue
				}
				analyzes := BuildBillingData(recons, *seller, entity, index)
				err = client.Services.Billing.PutRIsRealTime(analyzes.Items)
				if err != nil {
					log.Printf("Trigger order completed PutRIsRealTime error: %v\n", err)
					continue
				}
			}
		}
	}
}
