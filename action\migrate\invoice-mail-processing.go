package migrate

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateInvoiceMailProcessing(startTime, stopTime time.Time) {
	if startTime.IsZero() {
		startTime = time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)
	}

	if stopTime.IsZero() {
		fmt.Println("MigrateInvoiceMailProcessing: stopTime required")
		return
	}

	fmt.Println("MigrateInvoiceMailProcessing START")
	defer fmt.Println("MigrateInvoiceMailProcessing FINISH")

	var limit int64 = 100
	cursor := primitive.NewObjectIDFromTimestamp(startTime)

	updatedCount := 0
	updatedFailCount := 0

MigrateLoop:
	for {
		invoicesRes := model.InvoiceDB.Query(primitive.M{
			"_id": bson.M{
				"$gt": cursor,
			},
		}, 0, limit, &primitive.M{"_id": 1})
		if invoicesRes.Status != common.APIStatus.Ok {
			break MigrateLoop
		}

		invoices := invoicesRes.Data.([]*model.Invoice)
		for _, invoice := range invoices {
			if invoice.ID.Timestamp().After(stopTime) {
				break MigrateLoop
			}

			if invoice.SellerSentMail != nil {
				continue
			}

			if invoice.InvoiceDocumentURL == nil {
				continue
			}

			if err := invoiceProcessCompleted(invoice); err != nil {
				updatedFailCount++
				continue
			}

			updatedCount++
		}

		if len(invoices) < int(limit) {
			break MigrateLoop
		}

		cursor = *invoices[len(invoices)-1].ID
	}
}

func invoiceProcessCompleted(invoice *model.Invoice) error {
	if invoice == nil || invoice.ID == nil {
		return fmt.Errorf("invoice is nil")
	}

	updateRes := model.InvoiceDB.UpdateOne(&model.Invoice{
		ID: invoice.ID,
	}, &model.Invoice{
		SellerSentMail:       &[]bool{true}[0],
		MailProcessingStatus: enum.MailProcessingStatus.SENT,
	})
	if updateRes.Status != common.APIStatus.Ok {
		return fmt.Errorf("update invoice error: %s", updateRes.Message)
	}

	return nil
}
