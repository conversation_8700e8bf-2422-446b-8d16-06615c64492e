package enum

// HoldOrderValue ...
type HoldOrderValue string

type holdOrderValue struct {
	Hold                  HoldOrderValue // Cho them
	Active                HoldOrderValue // Di don luon
	RequestCancel         HoldOrderValue // Yeu cau huy don
	AutoCancelBankOrder   HoldOrderValue // tu dong huy don
	AutoSendPaymentRemind HoldOrderValue // tu dong gui nhac thanh toan
}

// HoldOrder ...
var HoldOrder = &holdOrderValue{
	"HOLD",
	"ACTIVE",
	"REQUEST_CANCEL",
	"AUTO_CANCEL_BANK_ORDER",
	"AUTO_SEND_PAYMENT_REMIND",
}
