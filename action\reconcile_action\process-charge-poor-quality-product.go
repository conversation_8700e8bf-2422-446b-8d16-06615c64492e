package reconcile_action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ProcessChargePoorQualityProduct(input *model.PoorQualityProductRequest) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "input is required",
			ErrorCode: "INVALID_INPUT",
		}
	}

	if input.OrderId == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "orderId is required",
			ErrorCode: "INVALID_INPUT",
		}
	}

	if len(input.SkuInfos) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "skus are required",
			ErrorCode: "INVALID_INPUT",
		}
	}

	mapSellerSkus := make(map[string][]*model.PoorQualitySku)
	for _, skuInfo := range input.SkuInfos {
		skuSplit := strings.Split(skuInfo.Sku, ".")
		mapSellerSkus[skuSplit[0]] = append(mapSellerSkus[skuSplit[0]], skuInfo)
	}

	errs := make([]error, 0)
	for sellerCode, skuInfos := range mapSellerSkus {
		err := processChargePoorQualityProductForSeller(input, sellerCode, skuInfos)
		if err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		entry := map[string]interface{}{
			"order_id": input.OrderId,
			"skuInfos": input.SkuInfos,
			"err":      errs,
		}
		fmt.Println("Errors in ProcessChargePoorQualityProduct: ", errs)
		model.ReconciliationErrorDB.Create(entry)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "ProcessChargePoorQualityProduct finished",
	}
}

func processChargePoorQualityProductForSeller(
	input *model.PoorQualityProductRequest,
	sellerCode string,
	skuInfos []*model.PoorQualitySku,
) error {
	var reconciliation *model.Reconciliation
	var err error

	recRes := model.ReconciliationDB.Query(model.Reconciliation{
		SellerCode:           sellerCode,
		ReconciliationStatus: model.ReconciliationStatus.Waiting,
	}, 0, 1, &primitive.M{"_id": -1})
	if recRes.Status == common.APIStatus.Ok {
		reconciliation = recRes.Data.([]*model.Reconciliation)[0]
	} else if recRes.Status == common.APIStatus.NotFound {
		reconciliation, err = CreateReconciliationFromTime(sellerCode, time.Now())
	}

	if err != nil {
		return err
	}

	if reconciliation == nil {
		return fmt.Errorf("reconciliation is nil")
	}

	for _, skuInfo := range skuInfos {
		if skuInfo == nil ||
			skuInfo.Sku == "" ||
			skuInfo.Price == nil ||
			*skuInfo.Price == 0 {
			continue
		}

		existRecItemRes := model.ReconciliationItemDB.QueryOne(model.ReconciliationItem{
			OrderID: input.OrderId,
			Sku:     skuInfo.Sku,
			FeeType: enum.FeeType.POOR_QUALITY_PRODUCT_FEE,
		})
		if existRecItemRes.Status == common.APIStatus.Ok {
			existRecItem := existRecItemRes.Data.([]*model.ReconciliationItem)[0]
			// skip if already charged in another reconciliation
			if existRecItem.ReconcileScheduleTimeIndex != reconciliation.ReconcileScheduleTimeIndex {
				continue
			}
		}

		sku := client.Services.Product.GetSkuInfo(skuInfo.Sku)
		if sku == nil {
			continue
		}

		penaltyDescription := fmt.Sprintf(
			"Phí hoàn hàng chất lượng kém sản phẩm #%d, đơn hàng #%d đợt %s ~ %s",
			sku.ProductID,
			input.OrderId,
			utils.FormatTimeString(reconciliation.FromTime),
			utils.FormatTimeString(reconciliation.ToTime),
		)

		model.ReconciliationItemDB.Upsert(model.ReconciliationItem{
			SellerCode:                 reconciliation.SellerCode,
			ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
			FeeType:                    enum.FeeType.POOR_QUALITY_PRODUCT_FEE,
			AutoPenaltyFee:             true,

			OrderID: input.OrderId,
			Sku:     skuInfo.Sku,
		}, model.ReconciliationItem{
			OrderCode:     input.OrderCode,
			SaleOrderCode: input.SaleOrderCode,
			TicketID:      input.TicketId,
			TicketCode:    input.TicketCode,

			ProductId:          sku.ProductID,
			PenaltyDescription: penaltyDescription,
			PenaltyBMLFee:      int(*skuInfo.Price),
		})
	}

	value, err := CalculateReconciliation(
		reconciliation.SellerCode,
		reconciliation.ReconcileScheduleTimeIndex,
	)
	if err != nil {
		return fmt.Errorf("calculate reconciliation error: %s, %s, %s",
			reconciliation.SellerCode, reconciliation.ReconcileScheduleTimeIndex, err.Error())
	}

	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}
	updateRes := model.ReconciliationDB.UpdateOne(model.Reconciliation{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		ReconciliationStatus:       model.ReconciliationStatus.Waiting,
	}, updater)
	if updateRes.Status != common.APIStatus.Ok {
		return fmt.Errorf("update reconciliation error: %s, %s, %s",
			reconciliation.SellerCode, reconciliation.ReconcileScheduleTimeIndex, updateRes.Message)
	}

	return nil
}
