package client

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

type InboundTicketRes struct {
	common.APIResponse `json:",inline"`
	Data               []*InboundTicket `json:"data"`
}

type StockQuantityRes struct {
	common.APIResponse `json:",inline"`
	Data               []*StockQuantity `json:"data"`
}

type InboundTicket struct {
	CreatedTime           *time.Time           `json:"createdTime,omitempty"`
	LastUpdatedTime       *time.Time           `json:"lastUpdatedTime,omitempty"`
	ExpectedDate          *string              `json:"expectedDate,omitempty"`
	ReceivedDate          *string              `json:"receivedDate,omitempty"`
	Status                InboundStatus        `json:"status,omitempty"`
	SellerCode            string               `json:"sellerCode,omitempty"`
	SellerName            string               `json:"sellerName,omitempty"`
	WarehouseID           int64                `json:"warehouseId"`
	InboundCode           string               `json:"inboundCode,omitempty"`
	TotalExpectedQuantity int                  `json:"totalExpectedQuantity,omitempty"`
	TotalActualQuantity   int                  `json:"totalActualQuantity,omitempty"`
	CheckInTime           *time.Time           `json:"checkInTime,omitempty"`
	Products              []ProductExpectedQty `json:"products,omitempty"`

	// client
	StatusIn        []InboundStatus `json:"statusIn,omitempty"`
	CheckInTimeFrom *time.Time      `json:"checkInTimeFrom,omitempty" bson:"-"`
	CheckInTimeTo   *time.Time      `json:"checkInTimeTo,omitempty" bson:"-"`
}

type ProductExpectedQty struct {
	Sku              string `json:"sku" bson:"sku,omitempty"`
	Lot              string `json:"lot" bson:"lot,omitempty"`
	ExpDate          string `json:"expDate" bson:"exp_date,omitempty"`
	ExpectedQuantity int64  `json:"expectedQuantity" bson:"expected_quantity,omitempty"`
	ActualQuantity   int64  `json:"actualQuantity" bson:"actual_quantity,omitempty"`

	CreatedBy string `json:"createdBy,omitempty" bson:"created_by,omitempty"`
}

type InboundStatus string

type inboundTicketStatus struct {
	Draft         InboundStatus
	WaitToConfirm InboundStatus
	Processing    InboundStatus
	Deleted       InboundStatus
	Finished      InboundStatus
}

var InboundTicketStatus = &inboundTicketStatus{
	Draft:         "DRAFT",
	WaitToConfirm: "WAIT_TO_CONFIRM",
	Processing:    "PROCESSING",
	Deleted:       "DELETED",
	Finished:      "FINISHED",
}

type StockQuantity struct {
	WarehouseCode       string           `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	SkuDemand           int64            `json:"skuDemand,omitempty" bson:"sku_demand,omitempty"`
	MinStock            int64            `json:"minStock,omitempty" bson:"min_stock,omitempty"`
	MaxStock            int64            `json:"maxStock,omitempty" bson:"max_stock,omitempty"`
	AvgDemand           float64          `json:"avgDemand,omitempty" bson:"avg_demand,omitempty"`
	Leadtime            int64            `json:"leadtime,omitempty" bson:"leadtime,omitempty"`
	MinGeneralBufferDay int64            `json:"minGeneralBufferDay,omitempty" bson:"min_general_buffer_day,omitempty"`
	MinSellerBufferDay  int64            `json:"minSellerBufferDay,omitempty" bson:"min_seller_buffer_day,omitempty"`
	MaxGeneralBufferDay int64            `json:"maxGeneralBufferDay,omitempty" bson:"max_general_buffer_day,omitempty"`
	MaxSellerBufferDay  int64            `json:"maxSellerBufferDay,omitempty" bson:"max_seller_buffer_day,omitempty"`
	DemandMap           map[string]int64 `json:"demandMap,omitempty" bson:"demand_map,omitempty"`
}
