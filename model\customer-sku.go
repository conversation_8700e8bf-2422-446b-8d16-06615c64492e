package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CustomerSKUs struct {
	ID          primitive.ObjectID          `json:"id,omitempty" bson:"_id,omitempty" `
	Sku         string                      `json:"sku,omitempty" bson:"sku,omitempty"`
	CustomerIds []int64                     `json:"customerIds,omitempty" bson:"customer_ids,omitempty"`
	RuleType    *enum.CustomerSkusRuleValue `json:"ruleType,omitempty" bson:"rule_type,omitempty"`
	StartTime   *time.Time                  `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime     *time.Time                  `json:"endTime,omitempty" bson:"end_time,omitempty"`
}

type CustomerSKUItem struct {
	CustomerIds []int64                     `json:"customerIds,omitempty" bson:"customer_ids,omitempty"`
	RuleType    *enum.CustomerSkusRuleValue `json:"ruleType,omitempty" bson:"rule_type,omitempty"`
}

var CustomerSKUsCacheDB = &db.Instance{
	ColName:        "customer_sku",
	TemplateObject: &CustomerSKUs{},
}

func InitCustomerSKUsCacheModel(s *mongo.Database) {
	CustomerSKUsCacheDB.ApplyDatabase(s)
}
