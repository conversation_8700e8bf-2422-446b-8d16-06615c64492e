package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Order ...
type Order struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// reference data
	OrderID        int64           `json:"orderId,omitempty" bson:"order_id,omitempty"`              //
	OrderCode      string          `json:"orderCode" bson:"order_code,omitempty"`                    //
	SaleOrderCode  string          `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"` // so
	Tags           []enum.TagValue `json:"tags,omitempty" bson:"tags,omitempty"`
	CustomerTags   []string        `json:"customerTags,omitempty" bson:"customer_tags,omitempty"`
	AdditionalTime int64           `json:"additionalTime,omitempty" bson:"additional_time,omitempty"`

	// shipping info
	AccountID               int64   `json:"accountId,omitempty" bson:"account_id,omitempty"`                              // mã tài khoản
	CustomerID              int64   `json:"customerId,omitempty" bson:"customer_id,omitempty"`                            // mã khách hàng
	CustomerCode            string  `json:"customerCode,omitempty" bson:"customer_code,omitempty"`                        // mã khách hàng
	CustomerName            string  `json:"customerName,omitempty" bson:"customer_name,omitempty"`                        // tên người nhận
	CustomerPhone           string  `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`                      // điện thoại người nhận
	CustomerEmail           *string `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`                      // email người nhận
	CustomerShippingAddress string  `json:"customerShippingAddress,omitempty" bson:"customer_shipping_address,omitempty"` // địa chỉ người nhận
	CustomerDistrictCode    string  `json:"customerDistrictCode,omitempty" bson:"customer_district_code,omitempty"`       // khu vực nhận
	CustomerWardCode        string  `json:"customerWardCode,omitempty" bson:"customer_ward_code,omitempty"`               //
	CustomerProvinceCode    string  `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`       //
	CustomerRegionCode      string  `json:"customerRegionCode,omitempty" bson:"customer_region_code,omitempty"`           //
	CustomerOrderIndex      int     `json:"customerOrderIndex,omitempty" bson:"customer_order_index,omitempty"`           //
	CustomerScope           string  `json:"customerScope,omitempty" bson:"customer_scope,omitempty"`                      //
	CustomerLevel           string  `json:"customerLevel,omitempty" bson:"customer_level,omitempty"`                      //

	// shipping method
	IsDropOffAtWarehouse *bool `json:"isDropOffAtWarehouse,omitempty" bson:"is_drop_off_at_warehouse,omitempty"` // nhận hàng tại kho

	// customer info
	WardCode        string   `json:"wardCode" bson:"ward_code,omitempty"`                   //
	DistrictCode    string   `json:"districtCode" bson:"district_code,omitempty"`           //
	ProvinceCode    string   `json:"provinceCode,omitempty" bson:"province_code,omitempty"` //
	RegionCode      string   `json:"regionCode,omitempty" bson:"region_code,omitempty"`     //
	FlattenLocation []string `json:"flattenLocation,omitempty" bson:"flatten_location,omitempty"`

	// payment & delivery
	PaymentMethod             string   `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`                      // phương thức thanh toán cod/chuyển khoản
	PaymentMethodPercentage   *float64 `json:"paymentMethodPercentage,omitempty" bson:"payment_method_percentage,omitempty"` // phần trăm giảm giá cho hình thức thanh toán
	DeliveryMethod            string   `json:"deliveryMethod,omitempty" bson:"delivery_method,omitempty"`                    // hình thức giao hàng
	DeliveryStatus            string   `json:"deliveryStatus,omitempty" bson:"delivery_status,omitempty"`                    // trạng thái nhà vận chuyển: đang lấy,...
	DeliveryTrackingNumber    string   `json:"deliveryTrackingNumber,omitempty" bson:"delivery_tracking_number,omitempty"`   // mã tracking
	DeliveryCarrier           string   `json:"deliveryCarrier,omitempty" bson:"delivery_carrier,omitempty"`                  // đơn vị vận chuyển
	DeliveryCarrierCode       string   `json:"deliveryCarrierCode,omitempty" bson:"delivery_carrier_code,omitempty"`         // code - đơn vị vận chuyển
	WaitForTransfer           *bool    `json:"waitForTransfer,omitempty" bson:"wait_for_transfer,omitempty"`                 // đánh dấu không được huỷ khi là đơn chuyển khoản
	WaitForTransferTime       int64    `json:"waitForTransferTime,omitempty" bson:"wait_for_transfer_time,omitempty"`        // thời gian chờ chuyển khoản (phút) lưu lại vì cài đặt có thể thay đổi
	AutoCancelOverdueTransfer *bool    `json:"autoCancelOverdueTransfer,omitempty" bson:"auto_cancel_overdue_transfer,omitempty"`

	// price
	PaymentMethodFee                  *int64 `json:"paymentMethodFee,omitempty" bson:"payment_method_fee,omitempty"`                                      // phí phương thức thanh toán cod/chuyển khoản
	DeliveryMethodFee                 *int64 `json:"deliveryMethodFee,omitempty" bson:"delivery_method_fee,omitempty"`                                    // phí hình thức giao hàng
	ExtraFee                          *int64 `json:"extraFee,omitempty" bson:"extra_fee,omitempty"`                                                       // phụ phí
	ActualTotalPrice                  *int   `json:"actualTotalPrice,omitempty" bson:"actual_total_price,omitempty"`                                      // tổng tiền cuối cùngs
	ActualPrice                       *int   `json:"actualPrice,omitempty" bson:"actual_price,omitempty"`                                                 // tổng tiền hàng thực tế
	TotalPrice                        *int   `json:"totalPrice,omitempty" bson:"total_price,omitempty"`                                                   // tổng tiền đơn hàng sau cùng
	Price                             *int   `json:"price,omitempty" bson:"price,omitempty"`                                                              // tổng tiển chưa trừ các khoản khác
	TotalDiscount                     *int   `json:"totalDiscount,omitempty" bson:"total_discount,omitempty"`                                             // tổng số tiền được giảm
	TotalFee                          *int   `json:"totalFee,omitempty" bson:"total_fee,omitempty"`                                                       // tổng phí
	TotalPriceBeforePartnerPaymentFee *int   `json:"totalPriceBeforePartnerPaymentFee,omitempty" bson:"total_price_before_partner_payment_fee,omitempty"` // tổng tiền trước khi trừ phí thanh toán online
	TotalDelivered                    *int   `json:"totalDelivered,omitempty" bson:"-"`                                                                   // tổng số sản phẩm đã được giao

	NotionalPrice      *int       `json:"notionalPrice,omitempty" bson:"notional_price,omitempty"`            // tổng giá TẠM TÍNH
	TotalNotionalPrice *int       `json:"totalNotionalPrice,omitempty" bson:"total_notional_price,omitempty"` // tổng giá TẠM TÍNH CUỐI CÙNG
	NotionalPriceTime  *time.Time `json:"notionalPriceTime,omitempty" bson:"notional_price_time,omitempty"`   // thời điểm tính cuối cùng
	Cod                *int       `json:"cod,omitempty" bson:"cod,omitempty"`
	TransferAmount     *int       `json:"-" bson:"transfer_amount,omitempty"`

	// estime
	DeliveryDate         *time.Time `json:"deliveryDate,omitempty" bson:"delivery_date,omitempty"`                   // ngày giao mong muốn
	ConfirmationDate     *time.Time `json:"confirmationDate,omitempty" bson:"confirmation_date,omitempty"`           // ngày xác nhận -- field cũ
	CompletedTime        *time.Time `json:"completedTime,omitempty" bson:"completed_time,omitempty"`                 // thời gian hoàn tất đơn hàng -- status = completed
	CompletedDebtTime    *time.Time `json:"completedDebtTime,omitempty" bson:"completed_debt_time,omitempty"`        // thời gian hoàn tất công nợ
	OutboundDate         *time.Time `json:"outboundDate,omitempty" bson:"outbound_date,omitempty"`                   // ngày xuất kho
	ProcessStartTime     *time.Time `json:"processStartTime,omitempty" bson:"process_start_time,omitempty"`          // ngày bắt đầu xử lý
	DeliveredTime        *time.Time `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`                 // ngày giao hàng
	CancelTime           *time.Time `json:"cancelTime,omitempty" bson:"cancel_time,omitempty"`                       // ngày huỷ
	SaleOrderCreatedTime *time.Time `json:"saleOrderCreatedTime,omitempty" bson:"sale_order_created_time,omitempty"` // thời gian tạo đơn hàng warehouse

	// state
	Status           enum.OrderStateValue     `json:"status,omitempty" bson:"status,omitempty"`                      // trạng thái đơn hàng
	ProcessingStatus enum.OrderStateValue     `json:"processingStatus,omitempty" bson:"processing_status,omitempty"` // trạng thái xử lý đơn hàng
	SaleOrderStatus  enum.SaleOrderStateValue `json:"saleOrderStatus,omitempty" bson:"sale_order_status,omitempty"`  // trạng thái đơn hàng dưới kho

	// order info
	Source              *enum.SourceValue           `json:"source,omitempty" bson:"source,omitempty"`                              // nguồn đơn hàng (web/mobile)
	SourceDetail        *OrderSourceDetail          `json:"sourceDetail,omitempty" bson:"source_detail,omitempty"`                 // chi tiết nguồn đơn hàng
	Note                *string                     `json:"note,omitempty" bson:"note,omitempty"`                                  // ghi chú đơn hàng
	AutoConfirmNote     *string                     `json:"autoConfirmNote,omitempty" bson:"auto_confirm_note,omitempty"`          // ghi chú auto xác nhân đơn
	CreateSaleOrderNote *string                     `json:"createSaleOrderNote,omitempty" bson:"create_sale_order_note,omitempty"` // ghi chú tạo đơn warehouse
	PrivateNote         string                      `json:"privateNote,omitempty" bson:"private_note,omitempty"`                   // ghi chú nội bộ đơn hàng
	ConfirmType         *enum.OrderConfirmTypeValue `json:"confirmType,omitempty" bson:"confirm_type,omitempty"`                   // xác nhận đơn hàng
	EmployeeID          int64                       `json:"employeeID,omitempty" bson:"employee_id,omitempty"`                     // mã nhân viên đặt hàng dùm khách

	// Skus        []string `json:"-" bson:"skus,omitempty"`         // skus exist in order
	// SellerCodes []string `json:"_" bson:"seller_codes,omitempty"` // sellers participate in order
	// ItemCodes   []string `json:"-" bson:"item_codes,omitempty"`   // itemCodes exist in order

	// promotion
	RedeemCode        *[]*string          `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"`                // mã giảm giá
	RedeemApplyResult []*PromoApplyResult `json:"redeemApplyResult,omitempty" bson:"redeem_apply_result,omitempty"` //

	//statistic
	TotalItem                 *int     `json:"totalItem,omitempty" bson:"total_item,omitempty"`
	TotalQuantity             *int     `json:"totalQuantity,omitempty" bson:"total_quantity,omitempty"`
	Point                     *float64 `json:"point,omitempty" bson:"point,omitempty"`
	AccumulateProductCount    *int     `json:"accumulateProductCount,omitempty" bson:"accumulate_product_count,omitempty"` // số sản phẩm tích lũy
	HasDeal                   bool     `json:"hasDeal,omitempty" bson:"has_deal,omitempty"`
	HasCampaign               bool     `json:"hasCampaign,omitempty" bson:"has_campaign,omitempty"`
	CanExportInvoice          *bool    `json:"canExportInvoice,omitempty" bson:"can_export_invoice,omitempty"`
	UpdatedProcessingQuantity *bool    `json:"-" bson:"updated_processing_quantity,omitempty"` // tracking if missing processing-status
	IsDelayDelivery           *bool    `json:"isDelayDelivery,omitempty" bson:"is_delay_delivery,omitempty"`

	// invoice
	Invoice *InvoiceRequest `json:"invoice,omitempty" bson:"invoice,omitempty"`

	RequestProcessCreatedTime *time.Time          `json:"requestProcessCreatedTime,omitempty" bson:"request_process_created_time,omitempty"` // thời gian bấm chờ chờ thêm || đi đơn luôn đầu tiên
	RequestProcessAction      enum.HoldOrderValue `json:"requestProcessAction,omitempty" bson:"request_process_action,omitempty"`            // ACTIVE || HOLD
	RequestProcessHoldCount   int                 `json:"requestProcessHoldCount,omitempty" bson:"request_process_hold_count,omitempty"`     // số lần bấm chờ thêm
	RequestProcessUpdatedTime *time.Time          `json:"requestProcessUpdatedTime,omitempty" bson:"request_process_updated_time,omitempty"` // // thời gian bấm chờ chờ thêm || đi đơn luôn mới nhất

	SystemDisplay string `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`

	// đánh dấu: Đã fill phí sản phẩm
	FillProductFee *bool `json:"fillProductFee,omitempty" bson:"fill_product_fee,omitempty"`

	// use query
	/// used only for query, not for save data
	PriceFrom       *int       `json:"priceFrom,omitempty" bson:"-"`
	PriceTo         *int       `json:"priceTo,omitempty" bson:"-"`
	DateFrom        *time.Time `json:"timeFrom,omitempty" bson:"-"`
	DateTo          *time.Time `json:"timeTo,omitempty" bson:"-"`
	StatusIn        []string   `json:"statusIn,omitempty" bson:"-"`
	SaleOrderCodeIn []string   `json:"saleOrderCodeIn,omitempty" bson:"-"`
	StatusNotIn     []string   `json:"statusNotIn,omitempty" bson:"-"`

	// items
	Items          []*OrderItem `json:"orderItems,omitempty" bson:"-"`
	ComplexQuery   []*bson.M    `json:"-" bson:"$and,omitempty"`
	ComplexQueryOr []*bson.M    `json:"-" bson:"$or,omitempty"`

	StartLastUpdatedTime *time.Time `json:"startLastUpdatedTime,omitempty" bson:"-"`
	EndLastUpdatedTime   *time.Time `json:"endLastUpdatedTime,omitempty" bson:"-"`

	StartCompletedTime *time.Time `json:"startCompletedTime,omitempty" bson:"-"`
	EndCompletedTime   *time.Time `json:"endCompletedTime,omitempty" bson:"-"`

	StartCreatedTime *time.Time `json:"startCreatedTime,omitempty" bson:"-"`
	EndCreatedTime   *time.Time `json:"endCreatedTime,omitempty" bson:"-"`

	TransactionID int64 `json:"transactionID,omitempty" bson:"transaction_id,omitempty"`

	// used for show hold order config buttons
	CanClickHoldButton        bool  `json:"canClickHoldButton" bson:"-"`
	CanClickActiveButton      bool  `json:"canClickActiveButton" bson:"-"`
	CanClickCancelButton      bool  `json:"canClickCancelButton" bson:"-"`
	HoldOrderProcessingTime   int64 `json:"holdOrderProcessingTime" bson:"-"` // hour
	HoldOrderCreateTicketTime int64 `json:"-" bson:"-"`                       // hour
	Count                     int   `json:"count,omitempty" bson:"count,omitempty"`

	// warehouse
	WarehouseCode               string                       `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	WarehouseName               string                       `json:"warehouseName,omitempty" bson:"warehouse_name,omitempty"`
	DeliveryOrderCodes          []string                     `json:"deliveryOrderCodes,omitempty" bson:"delivery_order_codes,omitempty"`
	DeliveryOrderStatuses       []*DeliveryOrderStatus       `json:"deliveryOrderStatuses,omitempty" bson:"delivery_order_statuses,omitempty"`
	DeliveryOrderCode           string                       `json:"deliveryOrderCode,omitempty" bson:"-"`
	IsSplitDeliveryOrder        *bool                        `json:"isSplitDeliveryOrder,omitempty" bson:"is_split_delivery_order,omitempty"`
	SplitType                   enum.SplitTypeValue          `json:"splitType,omitempty" bson:"split_type,omitempty"`
	VoucherAmount               int64                        `json:"voucherAmount,omitempty" bson:"-"`
	PaymentMethodDiscountAmount int64                        `json:"paymentMethodDiscountAmount,omitempty" bson:"-"`
	InternalOrderType           *enum.InternalOrderTypeValue `json:"internalOrderType,omitempty" bson:"internal_order_type,omitempty"`
	// brand
	CreatedByAccountID int64  `json:"createdByAccountId,omitempty" bson:"created_by_account_id,omitempty"`
	BrandCode          string `json:"brandCode,omitempty" bson:"brand_code,omitempty"`
	SalesTypeCode      string `json:"salesTypeCode,omitempty" bson:"sales_type_code,omitempty"`

	// online payment
	PartnerPaymentStatus enum.PartnerPaymentStatusValue `json:"partnerPaymentStatus,omitempty" bson:"partner_payment_status,omitempty"` // trạng thái thanh toán online
	PartnerPaymentMethod *PartnerPaymentMethod          `json:"partnerPaymentMethod,omitempty" bson:"partner_payment_method,omitempty"` // Phương thức thanh toán qua cổng thanh toán online

	// FE view
	PaymentLink                     string `json:"paymentLink,omitempty" bson:"-"`
	AutoSendPaymentNotificationUnix int64  `json:"-" bson:"auto_send_payment_notification_unix,omitempty"`                                     // token để gửi thông báo tự động
	AutoCancelTransferPaymentUnix   int64  `json:"autoCancelTransferPaymentUnix,omitempty" bson:"auto_cancel_transfer_payment_unix,omitempty"` // ts để tự động huỷ đơn thanh toán online

	UseFreeDelivery bool `json:"useFreeDelivery,omitempty" bson:"use_free_delivery,omitempty"`

	// For query replica
	IsQueryReplica bool `json:"isQueryReplica,omitempty" bson:"-"`
}

var OrderStatusCanCreateInvoice = []enum.OrderStateValue{
	enum.OrderState.Delivered,
	enum.OrderState.Completed,
}

// CanCreateInvoice checks if the order status allows invoice creation
func (o *Order) CanCreateInvoice() bool {
	for _, validStatus := range OrderStatusCanCreateInvoice {
		if o.Status == validStatus {
			return true
		}
	}
	return false
}

type PartnerPaymentMethod struct {
	// partner payment fee
	FixedBuymedFee   int64 `json:"fixedBuymedFee,omitempty" bson:"fixed_buymed_fee,omitempty"`     // Phí thanh toán cố định qua cổng thanh toán online của sàn
	FixedCustomerFee int64 `json:"fixedCustomerFee,omitempty" bson:"fixed_customer_fee,omitempty"` // Phí thanh toán cố định qua cổng thanh toán online của người mua
	// FixedFee         *int64 `json:"fee,omitempty" bson:"fee,omitempty"`                             // Phí thanh toán cố định qua cổng thanh toán online

	// DynamicBuymedFee   int64 `json:"dynamicBuymedFee,omitempty" bson:"dynamic_buymed_fee,omitempty"`     // Phí thanh toán cố định qua cổng thanh toán online của sàn
	// DynamicCustomerFee int64 `json:"dynamicCustomerFee,omitempty" bson:"dynamic_customer_fee,omitempty"` // Phí thanh toán cố định qua cổng thanh toán online của người mua
	// DynamicFee int64 `json:"dynamicFee,omitempty" bson:"dynamic_fee,omitempty"` // Phí thanh toán cố định qua cổng thanh toán online

	DynamicBuymedPercentage   float64 `json:"dynamicBuymedPercentage,omitempty" bson:"dynamic_buymed_percentage,omitempty"`     // Phần trăm phí thanh toán cố định qua cổng thanh toán online của sàn
	DynamicCustomerPercentage float64 `json:"dynamicCustomerPercentage,omitempty" bson:"dynamic_customer_percentage,omitempty"` // Phần trăm phí thanh toán cố định qua cổng thanh toán online của người mua
	// DynamicPercentage         *float64 `json:"dynamicPercentage,omitempty" bson:"dynamic_percentage,omitempty"`                  // Phần trăm phí thanh toán cố định qua cổng thanh toán online

	TotalCustomerFee      int64 `json:"totalCustomerFee,omitempty" bson:"total_customer_fee,omitempty"`            //
	TotalBuymedSponsorFee int64 `json:"totalBuymedSponsorFee,omitempty" bson:"total_buymed_sponsor_fee,omitempty"` //
	TotalFee              int64 `json:"totalFee,omitempty" bson:"total_fee,omitempty"`                             // Tổng phí thanh toán qua cổng thanh toán online
}

type DeliveryOrderStatus struct {
	Code                   string                 `json:"code,omitempty" bson:"code,omitempty"`
	Status                 enum.OrderStateValue   `json:"status,omitempty" bson:"status,omitempty"`
	DeliveryStatus         string                 `json:"deliveryStatus,omitempty" bson:"delivery_status,omitempty"`                  // trạng thái nhà vận chuyển: đang lấy,...
	DeliveryTrackingNumber string                 `json:"deliveryTrackingNumber,omitempty" bson:"delivery_tracking_number,omitempty"` // mã tracking
	DeliveryCarrier        string                 `json:"deliveryCarrier,omitempty" bson:"delivery_carrier,omitempty"`                // đơn vị vận chuyển
	DeliveryCarrierCode    string                 `json:"deliveryCarrierCode,omitempty" bson:"delivery_carrier_code,omitempty"`       //
	CodAmount              int                    `json:"codAmount,omitempty" bson:"cod_amount,omitempty"`                            // tiền thu hộ
	VoucherDiscount        int                    `json:"voucherDiscount,omitempty" bson:"voucher_discount,omitempty"`                // giảm giá voucher
	PaymentDiscount        int                    `json:"paymentDiscount,omitempty" bson:"payment_discount,omitempty"`                // giảm giá phí thanh toán
	VoucherAmountDetails   []*VoucherAmountDetail `json:"voucherAmountDetails,omitempty" bson:"voucher_amount_details,omitempty"`
}

// FeesApply ...
type FeesApply struct {
	Result     []*FeeValue            `json:"result,omitempty" bson:"result,omitempty"`
	Parameters map[string]interface{} `json:"parameters,omitempty" bson:"parameters"`
	Total      int                    `json:"total,omitempty" bson:"total"`
	Price      int                    `json:"price,omitempty" bson:"price"` // price : gia goc
	Info       interface{}            `json:"info,omitempty" bson:"info,omitempty"`
}

// FeeValue ...
type FeeValue struct {
	FeeCode  string  `json:"feeCode" bson:"fee_code,omitempty"`
	FeeType  string  `json:"feeType" bson:"fee_type,omitempty"`
	FeeValue float64 `json:"feeValue" bson:"fee_value,omitempty"`

	TotalFeeValueDisplay *float64 `json:"totalFeeValueDisplay,omitempty" bson:"-"`
}

// OrderSourceDetail ...
type OrderSourceDetail struct {
	Os             string `json:"os,omitempty" bson:"os,omitempty"`
	OsVersion      string `json:"osVersion,omitempty" bson:"os_version,omitempty"`
	Browser        string `json:"browser,omitempty" bson:"browser,omitempty"`
	BrowserVersion string `json:"browserVersion,omitempty" bson:"browser_version,omitempty"`
	Platform       string `json:"platform,omitempty" bson:"platform,omitempty"`
	IP             string `json:"ip,omitempty" bson:"ip,omitempty"`
}

// OrderDB ...
var OrderDB = &db.Instance{
	ColName:        "order",
	TemplateObject: &Order{},
}

// OrderDeletedDB ...
var OrderDeletedDB = &db.Instance{
	ColName:        "order_deleted",
	TemplateObject: &Order{},
}

var OrderUpdateLog = &db.Instance{
	ColName:        "order_update_log",
	TemplateObject: map[string]interface{}{},
}

var OrderCalcPriceLog = &db.Instance{
	ColName:        "order_calc_log",
	TemplateObject: map[string]interface{}{},
}

var OrderDBReplica = &db.Instance{
	ColName:        "order",
	TemplateObject: &Order{},
}

func InitOrderReadModel(s *mongo.Database) {
	OrderDBReplica.ApplyDatabase(s)
}

// InitOrderModel is func init model sale order
func InitOrderModel(s *mongo.Database) {
	OrderDB.ApplyDatabase(s)
	// t := true
	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "sale_order_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "completed_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "completed_time", Value: 1},
	// 	primitive.E{Key: "last_updated_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "customer_code", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// // for FE search
	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "customer_id", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "payment_method", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // index for summary by customer
	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "customer_id", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "last_updated_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "customer_order_index", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "tags", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

// InitOrderDeletedModel is func init model sale order deleted
func InitOrderDeletedModel(s *mongo.Database) {
	OrderDeletedDB.ApplyDatabase(s)
}

func InitOrderUpdateLog(s *mongo.Database) {
	OrderUpdateLog.ApplyDatabase(s)

	// t := true
	// _ = OrderUpdateLog.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderUpdateLog.CreateIndex(bson.D{
	// 	primitive.E{Key: "code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderUpdateLog.CreateIndex(bson.D{
	// 	primitive.E{Key: "action", Value: 1},
	// 	primitive.E{Key: "order_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderUpdateLog.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "action", Value: 1},
	// 	primitive.E{Key: "order_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// expire := int32((240 * time.Hour).Seconds())
	// _ = OrderUpdateLog.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &expire,
	// })
}

func InitOrderCalcLog(s *mongo.Database) {
	OrderCalcPriceLog.ApplyDatabase(s)

	// t := true
	// _ = OrderCalcPriceLog.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = OrderCalcPriceLog.CreateIndex(bson.D{
	// 	primitive.E{Key: "action", Value: 1},
	// 	primitive.E{Key: "order_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// expire := int32((240 * time.Hour).Seconds())
	// _ = OrderCalcPriceLog.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &expire,
	// })
}
