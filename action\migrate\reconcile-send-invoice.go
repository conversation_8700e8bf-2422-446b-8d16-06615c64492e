package migrate

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateReconcileSendInvoice() {
	fmt.Println("MigrateReconcileSendInvoice")
	defer fmt.Println("MigrateReconcileSendInvoice finished")

	recIndex1 := "20230423.20230416"
	recIndex2 := "20230501.20230423"
	recIndex3 := "20230501"

	cursor := primitive.NewObjectIDFromTimestamp(time.Date(2023, 04, 10, 0, 0, 0, 0, utils.VNTimeZone))

	for {
		reconciliationsRes := model.ReconciliationDB.Query(bson.M{
			"reconcile_schedule_time_index": bson.M{
				"$in": []string{recIndex1, recIndex2, recIndex3},
			},
			"_id": bson.M{
				"$gt": cursor,
			},
		}, 0, 100, &primitive.M{"_id": 1})
		if reconciliationsRes.Status != common.APIStatus.Ok {
			break
		}

		reconciliations := reconciliationsRes.Data.([]*model.Reconciliation)
		for _, reconciliation := range reconciliations {
			if reconciliation.ReconcileScheduleTimeIndex != recIndex1 &&
				reconciliation.ReconcileScheduleTimeIndex != recIndex2 &&
				reconciliation.ReconcileScheduleTimeIndex != recIndex3 {
				continue
			}

			if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Confirmed &&
				reconciliation.ReconciliationStatus != model.ReconciliationStatus.Completed {
				continue
			}

			model.SendInvoiceJob.Push(reconciliation, &job.JobItemMetadata{
				Keys: []string{
					reconciliation.SellerCode,
					reconciliation.ReconcileScheduleTimeIndex,
				},
			})
		}

		cursor = *reconciliations[len(reconciliations)-1].ID
	}
}
