package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
)

const (
	pathCreateBill                 = "/accounting/core/v1/bill"
	pathGetBill                    = "/accounting/core/v1/bill"
	pathCreatePayment              = "/accounting/core/v1/payment"
	pathCallbackOrderPartialCancel = "/accounting/core/v1/callback/order/partial-cancel"
)

type billClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewBillServiceClient ...
func NewBillServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *billClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	billClient := &billClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	billClient.svc.SetDBLog(session)
	return billClient
}

// CreateBill is func create new Bill
func (cli *billClient) CreateBill(orderId int64, doCode string) *common.APIResponse {
	params := map[string]string{}
	payload := struct {
		OrderId int64  `json:"orderId"`
		IsAuto  bool   `json:"isAuto"`
		DoCode  string `json:"doCode"`
	}{
		OrderId: orderId,
		IsAuto:  true,
		DoCode:  doCode,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, payload, pathCreateBill, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_BILL",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CREATE_BILL",
		}
	}

	return result
}

// CompletedBill is func create new Bill
func (cli *billClient) CompletedBill(orderId int64) *common.APIResponse {
	params := map[string]string{}
	payload := struct {
		OrderId int64  `json:"orderId"`
		IsAuto  bool   `json:"isAuto"`
		Status  string `json:"status"`
	}{
		OrderId: orderId,
		IsAuto:  true,
		Status:  "DONE",
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, payload, pathCreateBill, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_COMPLETED_BILL",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_COMPLETED_BILL",
		}
	}

	return result
}

// CancelBill is func cancel Bill
func (cli *billClient) CancelBill(orderId int64) *common.APIResponse {
	params := map[string]string{}
	payload := struct {
		OrderId int64  `json:"orderId"`
		IsAuto  bool   `json:"isAuto"`
		Status  string `json:"status"`
	}{
		OrderId: orderId,
		IsAuto:  true,
		Status:  "CANCEL",
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, payload, pathCreateBill, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_COMPLETED_BILL",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_COMPLETED_BILL",
		}
	}

	return result
}

// GetBill ...
func (cli *billClient) GetBill(orderId int64) ([]*model.Bill, error) {
	payload := struct {
		OrderId int64 `json:"orderId"`
	}{
		OrderId: orderId,
	}
	payloadByte, _ := json.Marshal(payload)
	params := map[string]string{
		"q": string(payloadByte),
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetBill, nil)
	if err != nil {
		return nil, err
	}

	var result *model.OrderBillResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil || len(result.Data) == 0 {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

func (cli *billClient) CreatePayment(orderId, amount, customerId int64, note, senderName, transactionCode string) *common.APIResponse {
	params := map[string]string{}
	payload := struct {
		OrderId         int64     `json:"orderId,omitempty"`
		Amount          int64     `json:"amount"`
		CustomerId      int64     `json:"customerId,omitempty"`
		BankCode        string    `json:"bankCode"`
		Source          string    `json:"source"`
		Status          string    `json:"status"`
		Type            string    `json:"type"`
		Note            string    `json:"note"`
		DateOfPayment   time.Time `json:"dateOfPayment"`
		TransactionCode string    `json:"transactionCode,omitempty"`
		IsAutoCreate    bool      `json:"isAutoCreate,omitempty"`
	}{
		OrderId:         orderId,
		Amount:          amount,
		CustomerId:      customerId,
		BankCode:        "**************", // TECH
		Source:          "BANK",
		Status:          "WAIT",
		Type:            "RECEIVED",
		Note:            note,
		DateOfPayment:   time.Now(),
		TransactionCode: transactionCode,
		IsAutoCreate:    true,
	}

	if senderName == "VPB" || senderName == "VPBANK_ADAPTER" {
		payload.BankCode = "*********" // VPB
	} else if senderName == "ONEPAY_ADAPTER" {
		payload.Source = "ONEPAY"
		payload.BankCode = "*********" // VPB
	}

	keys := make([]string, 0, 2)
	keys = append(keys, fmt.Sprintf("%d", amount))
	if orderId > 0 {
		keys = append(keys, fmt.Sprintf("%d", orderId))
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, payload, pathCreatePayment, &keys)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_BILL",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CREATE_BILL",
		}
	}

	return result
}

// CallbackOrderPartialCancel is func to callback order partial cancel
func (cli *billClient) CallbackOrderPartialCancel(orderId int64, soCode string) *common.APIResponse {
	params := map[string]string{}
	payload := struct {
		OrderId       int64  `json:"orderId"`
		SaleOrderCode string `json:"saleOrderCode"`
	}{
		OrderId:       orderId,
		SaleOrderCode: soCode,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, payload, pathCallbackOrderPartialCancel, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_BILL",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CREATE_BILL",
		}
	}

	return result
}
