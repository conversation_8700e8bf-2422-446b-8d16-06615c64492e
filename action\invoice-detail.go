package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/tool"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func GetInvoiceDetail(input *model.Invoice, getInvoiceItems bool) *common.APIResponse {
	invoiceRes := model.InvoiceDB.QueryOne(input)
	if invoiceRes.Status != common.APIStatus.Ok {
		return invoiceRes
	}

	if getInvoiceItems {
		invoice := invoiceRes.Data.([]*model.Invoice)[0]

		orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{OrderID: invoice.OrderID, OrderCode: invoice.OrderCode}, "processOrderPoint")
		if orderItemPartitionDB == nil {
			return model.PARTITION_NOT_FOUND_RESPONSE
		}

		orderItemsRes := orderItemPartitionDB.Query(model.OrderItem{
			SellerCode: input.SellerCode,
			OrderID:    invoice.OrderID,
		}, 0, 0, nil)
		if orderItemsRes.Status != common.APIStatus.Ok {
			return invoiceRes
		}

		orderItems := orderItemsRes.Data.([]*model.OrderItem)
		tool.CalculateInvoiceReport(invoice, orderItems)
	}

	return invoiceRes
}
