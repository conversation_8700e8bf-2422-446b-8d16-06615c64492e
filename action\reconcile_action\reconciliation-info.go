package reconcile_action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func GetReconciliationInfo(item *model.ReconciliationItem) *common.APIResponse {
	if item == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing reconciliation item",
			ErrorCode: "MISSING_RECONCILIATION_ITEM",
		}
	}

	if item.OrderID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing order ID",
			ErrorCode: "MISSING_ORDER_ID",
		}
	}

	if item.SellerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing seller code",
			ErrorCode: "MISSING_SELLER_CODE",
		}
	}

	riRes := model.ReconciliationItemDB.QueryOne(&model.ReconciliationItem{
		OrderID:    item.OrderID,
		SellerCode: item.SellerCode,
	})
	if riRes.Status != common.APIStatus.Ok {
		return riRes
	}

	ri := riRes.Data.([]*model.ReconciliationItem)[0]

	return model.ReconciliationDB.QueryOne(&model.Reconciliation{
		SellerCode:                 ri.SellerCode,
		ReconcileScheduleTimeIndex: ri.ReconcileScheduleTimeIndex,
	})
}
