package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

type ReturnTicketWMS struct {
	TicketID      int                   `json:"ticketId,omitempty" bson:"ticket_id,omitempty"`
	Type          *enum.ReturnTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	WarehouseCode string                `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	Code          string                `json:"code,omitempty" bson:"code,omitempty"`
	SO            string                `json:"so,omitempty" bson:"so,omitempty"`
	OrderID       int64                 `json:"orderId,omitempty" bson:"order_id,omitempty"`
	RefCode       string                `json:"refCode,omitempty" bson:"ref_code,omitempty"`
	TotalSKU      int                   `json:"totalSKU" bson:"total_sku"`
	TotalItem     int                   `json:"totalItem" bson:"total_item"`
	IsFinish      bool                  `json:"isFinish" bson:"is_finish"`
	EmployeeID    int64                 `json:"employeeId,omitempty" bson:"employee_id"`
	Employee      string                `json:"employee,omitempty" bson:"employee"`
	StartTime     *time.Time            `json:"startTime,omitempty" bson:"start_time"`
	EndTime       *time.Time            `json:"endTime,omitempty" bson:"end_time"`

	Reason            string                 `json:"reason,omitempty" bson:"reason,omitempty"`
	Status            string                 `json:"status,omitempty" bson:"status,omitempty"`
	ReturnTicketItems []*ReturnTicketItemWMS `json:"returnTicketItems" bson:"-"`
}

type ReturnTicketWMSResponse struct {
	Status  string             `json:"status"`
	Message string             `json:"message"`
	Data    []*ReturnTicketWMS `json:"data"`
}

type ReturnTicketItemWMS struct {
	VersionNo       string     `json:"versionNo,omitempty" bson:"version_no,omitempty" `
	CreatedBy       int64      `json:"createdBy,omitempty" bson:"created_by,omitempty" `
	UpdatedBy       int64      `json:"updatedBy,omitempty" bson:"updated_by,omitempty" `
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	TicketID          int                          `json:"ticketId,omitempty" bson:"ticket_id,omitempty"`
	LineID            int                          `json:"lineId,omitempty" bson:"line_id,omitempty"`
	ProductID         int                          `json:"productId,omitempty" bson:"product_id,omitempty"`
	AdminProductID    int                          `json:"adminProductId,omitempty" bson:"admin_product_id,omitempty"`
	SKU               string                       `json:"sku,omitempty" bson:"sku,omitempty"`
	ProductReturnType *enum.ProductReturnTypeValue `json:"productReturnType,omitempty" bson:"product_return_type,omitempty"`
	ItemPerCombo      int                          `json:"itemPerCombo,omitempty" bson:"item_per_combo,omitempty"`
	ComboSKU          string                       `json:"comboSKU,omitempty" bson:"combo_sku,omitempty"`
	SaleLineID        int                          `json:"saleLineId,omitempty" bson:"sale_line_id,omitempty"`
	ImageURL          string                       `json:"imageUrl,omitempty" bson:"image_url,omitempty"`
	Name              string                       `json:"name,omitempty" bson:"name,omitempty"`
	Packaging         string                       `json:"packaging,omitempty" bson:"packaging,omitempty"`
	Lot               string                       `json:"lot" bson:"lot"`
	ExpiredDate       string                       `json:"expiredDate" bson:"expired_date"`
	Vat               int                          `json:"vat" bson:"vat"`
	DemandQuantity    int                          `json:"demandQuantity" bson:"demand_quantity"`
	DoneQuantity      int                          `json:"doneQuantity" bson:"done_quantity"`
	LotDateInfos      []LotDateInfo                `json:"lotDateInfos" bson:"lot_date_infos"`
	Details           []ReturnDetailItem           `json:"details,omitempty" bson:"details"`
}

type ReturnDetailItem struct {
	Key         string `json:"key" bson:"key"`
	Lot         string `json:"lot" bson:"lot"`
	ExpiredDate string `json:"expiredDate" bson:"expired_date"`
	Vat         int64  `json:"vat" bson:"vat"`
	Quantity    int64  `json:"quantity" bson:"quantity"`
}

type LotDateInfo struct {
	Key         string `json:"key" bson:"key,omitempty"`
	Lot         string `json:"lot" bson:"lot"`
	ExpiredDate string `json:"expiredDate" bson:"expired_date"`
}

type ReturnQuantityObject struct {
	SellerCode string
	SKU        string

	ReturnedQuantity int
	MainQuantity     int
	DamageQuantity   int
	MissingQuantity  int
}
