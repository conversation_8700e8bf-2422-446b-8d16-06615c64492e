package reconcile_action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

var SkipIndex = []string{
	"20230116.20230108",
	"20230123.20230116",
	"20230201.20230123",
	"20230201",
}

func ProcessPenaltyFulfillment(
	order *model.Order,
	reconciliation *model.Reconciliation,
	reconciliationItem model.ReconciliationItem,
) {
	if order == nil || reconciliation == nil {
		return
	}

	nowInVn := time.Now().In(utils.VNTimeZone)
	endSkipDay := time.Date(2024, 8, 16, 0, 0, 0, 0, utils.VNTimeZone)
	// TEMP: skip Sale Order form Da Nang warehouse
	if nowInVn.Before(endSkipDay) && strings.HasPrefix(order.SaleOrderCode, "SODN") {
		return
	}

	if utils.Contains(reconciliationItem.ReconcileScheduleTimeIndex, SkipIndex) {
		return
	}

	if reconciliationItem.CompletedQuantity == nil || *reconciliationItem.CompletedQuantity > 0 {
		return
	}

	orderItemPartition := model.GetOrderItemPartitionDB(order, "ProcessPenaltyFulfillment")
	if orderItemPartition == nil {
		return
	}

	orderItemRes := orderItemPartition.QueryOne(&model.OrderItem{OrderID: order.OrderID, Sku: reconciliationItem.Sku})
	if orderItemRes == nil || orderItemRes.Status != common.APIStatus.Ok {
		return
	}

	orderItem := orderItemRes.Data.([]*model.OrderItem)[0]

	if orderItem.SkuStatus != nil && *orderItem.SkuStatus == enum.SkuStatus.LIMIT {
		if orderItem.SkuStatusData != nil && orderItem.SkuStatusData.IsAutoCheckStock {
			return
		}
	}

	pickItems := client.Services.Picking.GetPickTicketItems(order.SaleOrderCode)
	if pickItems == nil {
		return
	}

	shouldCreatePenaltyForSeller := false
	var ticketID int64
	for _, pickItem := range *pickItems {
		if reconciliationItem.Sku != pickItem.SKU {
			continue
		}

		if len(pickItem.LocationDetails) != 0 {
			shouldCreatePenaltyForSeller = false
			break
		}

		shouldCreatePenaltyForSeller = true
		ticketID = pickItem.TicketID
	}

	if !shouldCreatePenaltyForSeller {
		return
	}

	riFilter := model.ReconciliationItem{
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		SellerCode:                 reconciliationItem.SellerCode,
		FeeType:                    enum.FeeType.FULFILLMENT_PENALTY,
		AutoPenaltyFee:             true,

		OrderID:       reconciliationItem.OrderID,
		OrderCode:     reconciliationItem.OrderCode,
		ProductId:     reconciliationItem.ProductId,
		SaleOrderCode: reconciliationItem.SaleOrderCode,
		TicketID:      ticketID,
		Sku:           reconciliationItem.Sku,
	}

	penaltyDescription := fmt.Sprintf(
		"Phí phạt không hoàn thành đơn hàng #%d, sản phẩm #%d đợt %s ~ %s",
		reconciliationItem.OrderID,
		reconciliationItem.ProductId,
		utils.FormatTimeString(reconciliation.FromTime),
		utils.FormatTimeString(reconciliation.ToTime),
	)

	model.ReconciliationItemDB.Upsert(
		riFilter,
		model.ReconciliationItem{
			PenaltyDescription: penaltyDescription,
			PenaltyFee:         getFulfillmentPenaltyFee(&reconciliationItem),
		},
	)
}

func getFulfillmentPenaltyFee(reconciliationItem *model.ReconciliationItem) int {
	if reconciliationItem == nil {
		return 0
	}

	unitPrice := getUnitPrice(reconciliationItem)
	penaltyFee := float64(reconciliationItem.Quantity*unitPrice) * 0.05
	return int(penaltyFee)
}

func getUnitPrice(reconciliationItem *model.ReconciliationItem) int {
	if reconciliationItem == nil || reconciliationItem.Price == nil {
		return 0
	}

	return *reconciliationItem.Price
}
