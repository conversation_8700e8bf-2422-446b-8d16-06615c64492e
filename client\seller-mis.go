package client

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetOrderSearch         = "/seller/mis/v1/order/search"
	pathGetOrderSellerList     = "/seller/mis/v1/seller/order/list"
	pathDeliveryOrderSeller    = "/seller/mis/v1/seller/delivery/order"
	pathOrderSeller            = "/seller/mis/v1/seller/order"
	pathPutOrderSellerStatus   = "/seller/mis/v1/seller/order/status"
	pathPutCompleteOrderSeller = "/seller/mis/v1/seller/order/complete"

	pathPutCreateTicketPenaltyInbound   = "/seller/mis/v1/ticket/reconcile/penalty-inbound"
	pathPutConfirmReconcileOrderSeller  = "/seller/mis/v1/seller/order/reconcile/confirm"
	pathPutCompleteReconcileOrderSeller = "/seller/mis/v1/seller/order/reconcile/complete"

	pathGetSkuAccumulatePointsList = "/seller/mis/v1/sku-accumulate-points"
)

type sellerMisClient struct {
	svc     *client.RestClient
	ticket  *client.RestClient
	getSvc  *client.RestClient
	headers map[string]string
}

func NewSellerMisServiceClient(
	apiHost, apiKey, logName string,
	session *mongo.Database,
) *sellerMisClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}

	misClient := sellerMisClient{
		svc:    client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		ticket: client.NewRESTClient(apiHost, logName+"_ticket", 3*time.Second, 1, 3*time.Second),
		getSvc: client.NewRESTClient(apiHost, logName+"_get", 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}

	misClient.svc.SetDBLog(session)
	misClient.ticket.SetDBLog(session)

	return &misClient
}

func (cli *sellerMisClient) GetOrderSellerList(
	query *model.OrderSeller,
	offset, limit int64,
) []*model.OrderSeller {
	qBytes, err := json.Marshal(query)
	if err != nil {
		return nil
	}

	params := map[string]string{
		"q":      string(qBytes),
		"offset": strconv.FormatInt(offset, 10),
		"limit":  strconv.FormatInt(limit, 10),
	}

	result, err := cli.getSvc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetOrderSellerList, nil)
	if err != nil {
		fmt.Println("GetOrderSellerList failed: " + err.Error())
		return nil
	}

	var osRes OrderSellerRes
	err = json.Unmarshal([]byte(result.Body), &osRes)
	if err != nil {
		fmt.Println("GetOrderSellerList failed: " + err.Error())
		return nil
	}

	if osRes.Status != common.APIStatus.Ok {
		return nil
	}

	return osRes.Data
}

func (cli *sellerMisClient) CreateOrderSeller(orderId int64) *common.APIResponse {
	body := OrderRequest{
		OrderID: orderId,
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathOrderSeller, nil)
	if err != nil {
		fmt.Println("CreateOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var osRes common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &osRes)
	if err != nil {
		fmt.Println("CreateOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &osRes
}

func (cli *sellerMisClient) UpdateDeliveryOrderSeller(orderId int64) *common.APIResponse {
	body := OrderRequest{
		OrderID: orderId,
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathDeliveryOrderSeller, nil)
	if err != nil {
		fmt.Println("UpdateDeliveryOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var osRes common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &osRes)
	if err != nil {
		fmt.Println("UpdateDeliveryOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &osRes
}

func (cli *sellerMisClient) UpdateOrderSeller(order *model.Order) *common.APIResponse {
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, order, pathOrderSeller, nil)
	if err != nil {
		fmt.Println("UpdateOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var osRes common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &osRes)
	if err != nil {
		fmt.Println("UpdateOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &osRes
}

func (cli *sellerMisClient) DeleteOrderSeller(orderId int64) *common.APIResponse {
	params := map[string]string{
		"orderId": strconv.FormatInt(orderId, 10),
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Delete, cli.headers, params, nil, pathOrderSeller, nil)
	if err != nil {
		fmt.Println("DeleteOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var osRes common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &osRes)
	if err != nil {
		fmt.Println("DeleteOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &osRes
}

func (cli *sellerMisClient) UpdateOrderStatus(body *OrderSellerUpdate) *common.APIResponse {
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathPutOrderSellerStatus, nil)
	if err != nil {
		fmt.Println("UpdateOrderStatus failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var osRes common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &osRes)
	if err != nil {
		fmt.Println("UpdateOrderStatus failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &osRes
}

func (cli *sellerMisClient) CompleteOrderSeller(orderId int64) *common.APIResponse {
	body := OrderRequest{
		OrderID: orderId,
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathPutCompleteOrderSeller, nil)
	if err != nil {
		fmt.Println("CompleteOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var osRes common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &osRes)
	if err != nil {
		fmt.Println("CompleteOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &osRes
}

func (cli *sellerMisClient) CreateTicketPenaltyInbound(item *model.ReconciliationItem) *common.APIResponse {
	result, err := cli.ticket.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post, cli.headers, nil, item, pathPutCreateTicketPenaltyInbound, &[]string{
			item.SellerCode,
			item.ReconcileScheduleTimeIndex,
		},
	)
	if err != nil {
		fmt.Println("CreateTicketPenaltyInbound failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var ticketRes common.APIResponse
	if err = json.Unmarshal([]byte(result.Body), &ticketRes); err != nil {
		fmt.Println("CreateTicketPenaltyInbound failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &ticketRes
}

func (cli *sellerMisClient) ConfirmReconcileOrderSeller(sellerCode string, orderIds []int64) *common.APIResponse {
	body := OrderReconcileRequest{
		SellerCode: sellerCode,
		OrderIds:   orderIds,
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathPutConfirmReconcileOrderSeller, nil)
	if err != nil {
		fmt.Println("CompleteOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var osRes common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &osRes)
	if err != nil {
		fmt.Println("CompleteOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &osRes
}

func (cli *sellerMisClient) CompleteReconcileOrderSeller(
	sellerCode string,
	orderIds []int64,
	reconciledTime *time.Time,
	sessionPayment *string,
) *common.APIResponse {
	body := OrderReconcileRequest{
		SellerCode:     sellerCode,
		OrderIds:       orderIds,
		ReconciledTime: reconciledTime,
		SessionPayment: sessionPayment,
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, body, pathPutCompleteReconcileOrderSeller, nil)
	if err != nil {
		fmt.Println("CompleteOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var osRes common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &osRes)
	if err != nil {
		fmt.Println("CompleteOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &osRes
}

func (cli *sellerMisClient) SearchOrderSeller(
	searchQuery *model.SearchOrderQuery,
	offset, limit int64,
	getTotal bool,
) *common.APIResponse {
	qBytes, err := json.Marshal(searchQuery)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MARSHAL",
		}
	}

	params := map[string]string{
		"q":      string(qBytes),
		"offset": strconv.FormatInt(offset, 10),
		"limit":  strconv.FormatInt(limit, 10),
		"total":  strconv.FormatBool(getTotal),
	}

	result, err := cli.getSvc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetOrderSearch, nil)
	if err != nil {
		fmt.Println("SearchOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_MAKE_HTTP_REQUEST",
		}
	}

	var res common.APIResponse
	err = json.Unmarshal([]byte(result.Body), &res)
	if err != nil {
		fmt.Println("SearchOrderSeller failed: " + err.Error())
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "ERROR_UNMARSHAL",
		}
	}

	return &res
}

func (cli *sellerMisClient) GetSkuAccumulatePointsList(
	query *SkuAccumulatePoints,
	offset, limit int64,
) []*SkuAccumulatePoints {
	qBytes, err := json.Marshal(query)
	if err != nil {
		return nil
	}

	params := map[string]string{
		"q":      string(qBytes),
		"offset": strconv.FormatInt(offset, 10),
		"limit":  strconv.FormatInt(limit, 10),
	}

	result, err := cli.getSvc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSkuAccumulatePointsList, nil)
	if err != nil {
		fmt.Println("GetSkuAccumulatePointsList failed: " + err.Error())
		return nil
	}

	var skuRes SkuAccumulatePointsRes
	err = json.Unmarshal([]byte(result.Body), &skuRes)
	if err != nil {
		fmt.Println("GetSkuAccumulatePointsList failed: " + err.Error())
		return nil
	}

	if skuRes.Status != common.APIStatus.Ok {
		return nil
	}

	return skuRes.Data
}
