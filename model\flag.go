package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Flag struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SyncSaleOrder *bool `json:"syncSaleOrder,omitempty" bson:"sync_sale_order,omitempty"`
	OrderTestSync int64 `json:"orderTestSync,omitempty" bson:"order_test_sync,omitempty"`
}

// FlagDB ...
var FlagDB = &db.Instance{
	ColName:        "flag",
	TemplateObject: &Flag{},
}

// InitFlagModel is func init model flag
func InitFlagModel(s *mongo.Database) {
	FlagDB.ApplyDatabase(s)
}
