package model

import (
	"time"
)

// ReturnTicket ...
type ReturnTicket struct {
	Code            string     `json:"code,omitempty" bson:"code,omitempty"`
	TicketID        int64      `json:"ticketId,omitempty" bson:"ticket_id,omitempty"`
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SaleOrderCode string `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	OrderID       int64  `json:"orderId,omitempty" bson:"order_id,omitempty"`     // type = ORDER
	OrderCode     string `json:"orderCode,omitempty" bson:"order_code,omitempty"` // type = ORDER
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	Status ReturnTicketStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	// Tracking Info
	SubmittedTime  *time.Time `json:"submittedTime,omitempty" bson:"submitted_time,omitempty"`   // ngày submit phiếu (ngày tạo)
	ConfirmTime    *time.Time `json:"confirmedTime,omitempty" bson:"confirmed_time,omitempty"`   // ngày confirm phiếu
	ReceivedTime   *time.Time `json:"receivedTime,omitempty" bson:"received_time,omitempty"`     // ngày nhận hàng từ đối tác VC
	ProcessingTime *time.Time `json:"processingTime,omitempty" bson:"processing_time,omitempty"` // ngày xử lý
	CompletedTime  *time.Time `json:"completedTime,omitempty" bson:"completed_time,omitempty"`   // ngày hoàn tất

	// forview
	Items []*ReturnTicketItem `json:"items,omitempty" bson:"-"`

	CreatedFrom   *time.Time `json:"createdFrom,omitempty" bson:"-"`
	CreatedTo     *time.Time `json:"createdTo,omitempty" bson:"-"`
	UpdatedFrom   *time.Time `json:"updatedFrom,omitempty" bson:"-"`
	UpdatedTo     *time.Time `json:"updatedTo,omitempty" bson:"-"`
	CompletedFrom *time.Time `json:"completedFrom,omitempty" bson:"-"`
	CompletedTo   *time.Time `json:"completedTo,omitempty" bson:"-"`
	OrderIDs      string     `json:"orderIDs,omitempty" bson:"-"`
}

type ReturnTicketResponse struct {
	Status  string          `json:"status"`
	Message string          `json:"message"`
	Data    []*ReturnTicket `json:"data"`
}

type ReturnTicketItem struct {
	TicketID   int64  `json:"ticketId,omitempty" bson:"ticket_id,omitempty"`
	TicketCode string `json:"ticketCode,omitempty" bson:"ticket_code,omitempty"`

	// // Danh sach LOT original: lay tu xuat kho cua don hang
	// DeliveryLots []*DeliveryLot `json:"deliveryLots,omitempty" bson:"delivery_lots,omitempty"`

	// Item info
	ProductID  int64  `json:"productId,omitempty" bson:"product_id,omitempty"`
	SKU        string `json:"sku,omitempty" bson:"sku,omitempty"`
	SellerCode string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	// ProductType string `json:"productType,omitempty" bson:"product_type,omitempty"` // Combo/sp thường

	ReturnInfos       []*ReturnLotInfo `json:"returnInfos,omitempty" bson:"return_infos,omitempty"`
	RequestedQuantity *int             `json:"requestedQuantity,omitempty" bson:"requested_quantity,omitempty"`

	// product info
	Price  int     `json:"price,omitempty" bson:"price,omitempty"`
	Weight float64 `json:"weight,omitempty" bson:"weight,omitempty"`

	ComboReturnedQuantity       *int                   `json:"comboReturnedQuantity,omitempty" bson:"combo_returned_quantity,omitempty"`
	ComboReturnReturnedQuantity *int                   `json:"comboReturnReturnedQuantity,omitempty" bson:"combo_return_returned_quantity,omitempty"`
	ComboLackReturnedQuantity   *int                   `json:"comboLackReturnedQuantity,omitempty" bson:"combo_lack_returned_quantity,omitempty"`
	SubItems                    []*ReturnTicketSubItem `json:"subItems,omitempty" bson:"sub_items,omitempty"`

	SumConfirmedQtyDisplay    *int `json:"sumConfirmedQtyDisplay,omitempty"`
	SumReturnedQtyDisplay     *int `json:"sumReturnedQtyDisplay,omitempty"`
	TotalReturnedPriceDisplay *int `json:"totalReturnedPriceDisplay,omitempty"`
}

type ReturnTicketSubItem struct {
	// Item info
	ProductID        int64            `json:"productId,omitempty" bson:"product_id,omitempty"`
	SKU              string           `json:"sku,omitempty" bson:"sku,omitempty"`
	QuantityPerCombo *int             `json:"quantityPerCombo,omitempty" bson:"quantity_per_combo,omitempty"`
	DeliveryLots     []*DeliveryLot   `json:"deliveryLots,omitempty" bson:"delivery_lots,omitempty"`
	ReturnInfos      []*ReturnLotInfo `json:"returnInfos,omitempty" bson:"return_infos,omitempty"`

	RequestedQuantity *int `json:"requestedQuantity,omitempty" bson:"requested_quantity,omitempty"`
	Price             int  `json:"price,omitempty" bson:"price,omitempty"`
}

type DeliveryLot struct {
	Lot         string `json:"lot,omitempty" bson:"lot,omitempty"`
	ExpDate     string `json:"expDate,omitempty" bson:"exp_date,omitempty"`
	FullExpDate string `json:"fullExpDate,omitempty" bson:"full_exp_date,omitempty"`
}

type ReturnLotInfo struct {
	// Reason enum.ReturnReasonValue `json:"reason,omitempty" bson:"reason,omitempty"`
	// Type ALL, MULTIPLE: Chon tat ca hoac chon nhieu lot/date
	// Type enum.ReturnInfoLotTypeValue `json:"type,omitempty" bson:"type,omitempty"`

	// Lots              []*SelectedLot `json:"lots,omitempty" bson:"lots,omitempty"`
	RequestedQuantity *int `json:"requestedQuantity,omitempty" bson:"requested_quantity,omitempty"` // số lượng khách yêu cầu
	ConfirmedQuantity *int `json:"confirmedQuantity,omitempty" bson:"confirmed_quantity,omitempty"` // số lượng cs duyêt
	ReturnedQuantity  *int `json:"returnedQuantity,omitempty" bson:"returned_quantity,omitempty"`   // số lượng hàng thực tế nhận đc
}

// ReturnTicketStatus Value from CS
type ReturnTicketStatusValue string

type returnTicketStatus struct {
	NEW                    ReturnTicketStatusValue // Nháp
	WAIT_TO_CONFIRM        ReturnTicketStatusValue // Chờ duyệt
	CONFIRMED              ReturnTicketStatusValue // Chờ book 3PL
	RETURNING              ReturnTicketStatusValue // Chờ nhận hàng
	WAIT_TO_CONFIRM_RETURN ReturnTicketStatusValue // Chờ xác nhận (Giao hàng thất bại)
	RETURNED               ReturnTicketStatusValue // Chờ xử lý
	PROCESSING             ReturnTicketStatusValue // Đang xử lý
	WAIT_TO_PAY            ReturnTicketStatusValue //Chờ chi tiền
	COMPLETED              ReturnTicketStatusValue // Đã hoàn thành
	CANCELLED              ReturnTicketStatusValue // Hủy
}

var ReturnTicketStatus = &returnTicketStatus{
	NEW:                    "NEW",
	WAIT_TO_CONFIRM:        "WAIT_TO_CONFIRM",
	CONFIRMED:              "CONFIRMED",
	RETURNING:              "RETURNING",
	WAIT_TO_CONFIRM_RETURN: "WAIT_TO_CONFIRM_RETURN",
	RETURNED:               "RETURNED",
	PROCESSING:             "PROCESSING",
	WAIT_TO_PAY:            "WAIT_TO_PAY",
	COMPLETED:              "COMPLETED",
	CANCELLED:              "CANCELLED",
}

var ReturnTicketStatusOrder = map[ReturnTicketStatusValue]int{
	ReturnTicketStatus.NEW:                    0,
	ReturnTicketStatus.WAIT_TO_CONFIRM:        1,
	ReturnTicketStatus.CONFIRMED:              2,
	ReturnTicketStatus.RETURNING:              3,
	ReturnTicketStatus.WAIT_TO_CONFIRM_RETURN: 4,
	ReturnTicketStatus.RETURNED:               5,
	ReturnTicketStatus.PROCESSING:             6,
	ReturnTicketStatus.WAIT_TO_PAY:            7,
	ReturnTicketStatus.COMPLETED:              8,
	ReturnTicketStatus.CANCELLED:              9,
}
