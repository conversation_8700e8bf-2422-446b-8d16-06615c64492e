package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetTaxReportItems        = "/seller/reporting/v1/seller/tax-report-item/list-tax-reconcile"
	pathGetTaxReportItemsByOrder = "/seller/reporting/v1/seller/tax-report-item/list-tax-do"
	pathGetTaxReportItemsByPage  = "/seller/reporting/v1/seller/tax-report-item/list"
	pathGetUnsoldDemand          = "/seller/reporting/v1/inventory/unsold/demand"
)

type sellerReportingClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewProductServiceClient ...
func NewSellerReportingClient(apiHost, apiKey, logName string, session *mongo.Database) *sellerReportingClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}

	sellerReportingClient := &sellerReportingClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	// sellerReportingClient.svc.SetDBLog(session
	return sellerReportingClient
}

// ================ seller reporting tax report item ================
type TaxReportItem struct {
	ID *primitive.ObjectID `json:"id" bson:"_id,omitempty"`

	SellerCode   string             `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	Quarter      string             `json:"quarter,omitempty" bson:"quarter,omitempty"` // yyyy-MM
	Type         enum.TaxReportType `json:"type,omitempty" bson:"type,omitempty"`
	TrackingCode string             `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`

	SellerType SellerType `json:"sellerType,omitempty" bson:"seller_type,omitempty"`

	TrackingDate  *time.Time `json:"trackingDate,omitempty" bson:"tracking_date,omitempty"`
	Reason        string     `json:"reason,omitempty" bson:"reason,omitempty"`
	SaleOrderCode string     `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	OrderId       int64      `json:"orderId,omitempty" bson:"order_id,omitempty"`

	ReconcileSession              string `json:"reconcileSession,omitempty" bson:"reconcile_session,omitempty"`
	TaxSettlementReconcileSession string `json:"taxSettlementReconcileSession,omitempty" bson:"tax_settlement_reconcile_session,omitempty"`

	Debit  *int64 `json:"debit,omitempty" bson:"debit,omitempty"`
	Credit *int64 `json:"credit,omitempty" bson:"credit,omitempty"`

	VATRate  *float64 `json:"vatRate,omitempty" bson:"vat_rate,omitempty"`
	PITRate  *float64 `json:"pitRate,omitempty" bson:"pit_rate,omitempty"`
	IsRefund *bool    `json:"isRefund,omitempty" bson:"is_refund,omitempty"`

	VAT      *int64 `json:"vat,omitempty" bson:"vat,omitempty"`
	PIT      *int64 `json:"pit,omitempty" bson:"pit,omitempty"`
	TotalTax *int64 `json:"totalTax,omitempty" bson:"total_tax,omitempty"`
}

type TaxReportItemResponse struct {
	BaseAPIResponse
	Data []*TaxReportItem `json:"data"`
}

type UnsoldInventory struct {
	ItemCode                     string `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	SKU                          string `json:"sku,omitempty" bson:"sku,omitempty"`
	WarehouseCode                string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	WarehouseID                  int64  `json:"warehouseID,omitempty" bson:"warehouse_id,omitempty"`
	SellerCode                   string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SkuDemand                    *int64 `json:"skuDemand,omitempty" bson:"sku_demand,omitempty"` // from 40 day to 55 day
	InventoryTotalUnsoldQuantity *int64 `json:"inventoryTotalQuantity,omitempty" bson:"inventory_total_quantity,omitempty"`
}

type UnsoldInventoryResponse struct {
	BaseAPIResponse
	Data []*UnsoldInventory `json:"data"`
}

func (cli *sellerReportingClient) GetListTaxReportItem(
	sellerCode, reconcileScheduleTimeIndex, IdFrom string,
) []*TaxReportItem {
	body := map[string]interface{}{
		"sellerCode":                         sellerCode,
		"taxSettlementReconciliationSession": reconcileScheduleTimeIndex,
		"limit":                              1000,
	}

	if IdFrom != "" {
		body["idFrom"] = IdFrom
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathGetTaxReportItems, &[]string{
		sellerCode,
		reconcileScheduleTimeIndex,
	})

	if err != nil {
		return nil
	}

	var res TaxReportItemResponse
	if err = json.Unmarshal([]byte(result.Body), &res); err != nil {
		return nil
	}

	return res.Data
}

func (cli *sellerReportingClient) GetListTaxReportItemByOrder(
	sellerCode string, orderID int64,
) []*TaxReportItem {
	body := map[string]interface{}{
		"sellerCode": sellerCode,
		"type":       "DELIVERY_ORDER",
		"orderID":    orderID,
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathGetTaxReportItemsByOrder, &[]string{
		sellerCode,
		fmt.Sprintf("%d", orderID),
	})

	if err != nil {
		return nil
	}

	var res TaxReportItemResponse
	if err = json.Unmarshal([]byte(result.Body), &res); err != nil {
		return nil
	}

	return res.Data
}

func (cli *sellerReportingClient) GetListTaxReportItemByPage(
	sellerCode, quarter, IdFrom string,
) []*TaxReportItem {
	body := map[string]interface{}{
		"sellerCode": sellerCode,
		"quarter":    quarter,
		"type":       "DELIVERY_ORDER",
		"limit":      1000,
	}

	if IdFrom != "" {
		body["idFrom"] = IdFrom
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathGetTaxReportItemsByPage, &[]string{
		sellerCode,
		quarter,
	})

	if err != nil {
		return nil
	}

	var res TaxReportItemResponse
	if err = json.Unmarshal([]byte(result.Body), &res); err != nil {
		return nil
	}

	return res.Data
}

func (cli *sellerReportingClient) GetSkuUnsoldDemand(
	skuCode,
	warehouseCode string,
) *int64 {
	params := map[string]string{
		"skuCode":       skuCode,
		"warehouseCode": warehouseCode,
	}

	keys := []string{skuCode, warehouseCode}
	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetUnsoldDemand, &keys)
	if err != nil {
		fmt.Println("GetSkuUnsoldDemand failed: " + err.Error())
		return nil
	}

	var inbRes *UnsoldInventoryResponse
	err = json.Unmarshal([]byte(result.Body), &inbRes)
	if err != nil {
		fmt.Println("GetSkuUnsoldDemand failed: " + err.Error())
		return nil
	}

	if inbRes == nil || inbRes.Status != common.APIStatus.Ok {
		return nil
	}

	data := inbRes.Data

	if data[0] == nil {
		return nil
	}

	return data[0].SkuDemand
}
