package client

import (
	"encoding/json"
	"strings"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathSendZnsMessage = "/notification/zalo-notification/v1/message"
)

const (
	// list of topic
	ORDER_CONFIRMATION_TOPIC   = "ORDER_CONFIRMATION"
	DELIVERING_TOPIC           = "DELIVERING"
	SPLIT_DELIVERY_ORDER_TOPIC = "SPLIT_DELIVERY_ORDER"
	MISSING_ITEM_TOPIC         = "MISSING_ITEM"
)

// ZnsClient is model define SMS client
type ZnsClient struct {
	cli     *client.RestClient
	headers map[string]string
}

type ZnsMessage struct {
	DevMode    bool              `json:"devMode,omitempty" bson:"dev_mode,omitempty"`
	Topic      string            `json:"topic,omitempty" bson:"topic,omitempty"`
	BusinessID string            `json:"businessID,omitempty" bson:"business_id,omitempty"`
	Receiver   string            `json:"receiver,omitempty" bson:"receiver,omitempty"`
	Data       map[string]string `json:"data,omitempty" bson:"data,omitempty"`
}

type ZnsMessageResponse struct {
	Status string        `json:"status,omitempty"`
	Data   []*ZnsMessage `json:"data"`
}

// NewZnsClient is func define new Notification client
func NewZnsClient(apiHost, apiKey, logName string, session *mongo.Database) *ZnsClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	client := &ZnsClient{
		cli: client.NewRESTClient(
			apiHost,
			logName,
			time.Duration(3*time.Second),
			1,
			time.Duration(3*time.Second),
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}

	client.cli.SetDBLog(session)

	return client
}

// SendZnsMessage send message to user
func (cli *ZnsClient) SendZnsMessage(in *ZnsMessage) (*ZnsMessageResponse, error) {
	// format receiver data from 0 -> + 84
	if strings.HasPrefix(in.Receiver, "0") {
		in.Receiver = "84" + in.Receiver[1:]
	}
	in.DevMode = conf.Config.ZnsDevMode
	params := map[string]string{}
	res, err := cli.cli.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathSendZnsMessage, nil)
	if err != nil {
		return nil, err
	}

	var result *ZnsMessageResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
