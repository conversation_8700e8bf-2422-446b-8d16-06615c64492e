package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func checkIsDeliveryLimitation(locationCodes ...string) (bool, string) {
	if len(model.LstDeliveryLimitation) > 0 {
		locationCodes = append([]string{"ALL"}, locationCodes...)
		for _, locationCode := range locationCodes {
			if len(locationCode) > 0 {
				if msg, ok := model.LstDeliveryLimitation[locationCode]; ok && len(msg) > 0 {
					return true, msg
				}
			}
		}
	}
	return false, ""
}

func WarmUpDeliveryLimitation() error {
	lst := make(map[string]string)
	qResult := model.DeliveryLimitationDB.QueryOne(&model.DeliveryLimitation{})
	if qResult.Status != common.APIStatus.Ok {
		model.LstDeliveryLimitation = lst
		return fmt.Errorf("%v", qResult.Message)
	}
	if qResult.Data != nil {
		qData := qResult.Data.([]*model.DeliveryLimitation)[0]

		if qData.IsLimitAll != nil && *qData.IsLimitAll {
			lst["ALL"] = qData.Message
		} else {
			if qData.ProvinceCodes != nil && len(*qData.ProvinceCodes) > 0 {
				for _, provinceCode := range *qData.ProvinceCodes {
					if msg, ok := lst[provinceCode]; !ok && len(msg) == 0 {
						// TODO ADD LIMIT OPTION HERE
						lst[provinceCode] = qData.Message
					}
				}
			}
			if qData.DistrictCodes != nil && len(*qData.DistrictCodes) > 0 {
				for _, districtCode := range *qData.DistrictCodes {
					if msg, ok := lst[districtCode]; !ok && len(msg) == 0 {
						// TODO ADD LIMIT OPTION HERE
						lst[districtCode] = qData.Message
					}
				}
			}
			if qData.WardCodes != nil && len(*qData.WardCodes) > 0 {
				for _, wardCode := range *qData.WardCodes {
					if msg, ok := lst[wardCode]; !ok && len(msg) == 0 {
						// TODO ADD LIMIT OPTION HERE
						lst[wardCode] = qData.Message
					}
				}
			}
		}
		model.LstDeliveryLimitation = lst
	}

	return nil
}

func UpdateDeliveryLimitation(in *model.DeliveryLimitation) *common.APIResponse {
	if in.Message == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng nhập nội dung thông báo",
			ErrorCode: "MESSAGE_MISSING",
		}
	}

	if in.ProvinceCodes != nil && len(*in.ProvinceCodes) > 0 {
		provinceCode, check := checkDuplicateArrString(*in.ProvinceCodes)
		if check {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Vui lòng không nhập trùng tỉnh thành (Mã %v bị trùng lặp)", provinceCode),
				ErrorCode: "PROVINCE_DUPLICATE",
			}
		}
	}
	if in.DistrictCodes != nil && len(*in.DistrictCodes) > 0 {
		districtCode, check := checkDuplicateArrString(*in.DistrictCodes)
		if check {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Vui lòng không nhập trùng quận huyện (Mã %v bị trùng lặp)", districtCode),
				ErrorCode: "DISTRICT_DUPLICATE",
			}
		}
	}
	if in.WardCodes != nil && len(*in.WardCodes) > 0 {
		wardCode, check := checkDuplicateArrString(*in.WardCodes)
		if check {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Vui lòng không nhập trùng phường xã (Mã %v bị trùng lặp)", wardCode),
				ErrorCode: "WARD_DUPLICATE",
			}
		}
	}
	qResult := model.DeliveryLimitationDB.QueryOne(&model.DeliveryLimitation{})
	if qResult.Status == common.APIStatus.NotFound {
		return model.DeliveryLimitationDB.Create(in)
	}
	deliveryLimit := qResult.Data.([]*model.DeliveryLimitation)[0]

	updater := &model.DeliveryLimitation{
		IsLimitAll:    in.IsLimitAll,
		ProvinceCodes: in.ProvinceCodes,
		DistrictCodes: in.DistrictCodes,
		WardCodes:     in.WardCodes,
		Message:       in.Message,
	}
	qUpdate := model.DeliveryLimitationDB.UpdateOne(&model.DeliveryLimitation{
		ID: deliveryLimit.ID,
	}, updater)
	return qUpdate
}

func GetDeliveryLimitation() *common.APIResponse {
	return model.DeliveryLimitationDB.QueryOne(&model.DeliveryLimitation{})
}

func checkDuplicateArrString(arr []string) (string, bool) {
	lst := make(map[string]bool)
	for _, str := range arr {
		if check, ok := lst[str]; ok && check {
			return str, true
		}
		lst[str] = true
	}
	return "", false
}
