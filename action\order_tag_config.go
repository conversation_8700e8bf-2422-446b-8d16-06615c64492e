package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func GetOrderTagConfigByCode(code string) *common.APIResponse {
	return model.OrderTagConfigDB.QueryOne(model.OrderTagConfig{TagCode: code})
}

func UpsertOrderTagConfig(input *model.OrderTagConfig) *common.APIResponse {
	return model.OrderTagConfigDB.UpdateOne(model.OrderTagConfig{TagCode: input.TagCode}, input, &options.FindOneAndUpdateOptions{Upsert: utils.ParseBoolToPointer(true)})
}

func GetListOrderTagConfig(query *model.OrderTagConfig, offset, limit int64) *common.APIResponse {
	return model.OrderTagConfigDB.Query(query, offset, limit, &primitive.M{"_id": -1})
}
