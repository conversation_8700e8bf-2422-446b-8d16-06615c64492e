package client

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

const (
	pathGetSellerByAccountID              = "/seller/core/v1/account"
	pathUpdateSkuActiveSeller             = "/seller/core/v1/seller/store"
	pathGetSellerConfig                   = "/seller/core/v1/seller/config"
	pathGetSellers                        = "/seller/core/v1/account/list"
	pathUpdateSellersRevenue              = "/seller/core/v1/seller/seller-revenue"
	pathGetSellerReconciliationFeeConfigs = "/seller/core/v1/seller/reconciliation-fee-config"
	pathGetSettingForSC                   = "/seller/core/v1/seller-center/setting"
	pathGetReconciliationScheduleSetting  = "/seller/core/v1/seller-center/setting/reconciliation-schedule"
	pathGetSellerBySellerCode             = "/seller/core/v1/account"
	pathGetVendorStoreList                = "/seller/core/v1/vendor-store/list"
)

type sellerClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewSellerServiceClient ...
func NewSellerServiceClient(apiHost, apiKey, logName string) *sellerClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	return &sellerClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
}

// GetSellerByAccountID ...
func (cli *sellerClient) GetSellerByAccountID(accountID int64) (*Seller, error) {
	params := map[string]string{
		"accountID": fmt.Sprintf("%v", accountID),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSellerByAccountID, nil)
	if err != nil {
		return nil, err
	}

	var result *SellerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

func (cli *sellerClient) GetSellerByCode(Code string) (*Seller, error) {
	params := map[string]string{
		"sellerCode": Code,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSellerByAccountID, nil)
	if err != nil {
		return nil, err
	}

	var result *SellerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

func (cli *sellerClient) UpdateRevenueStoreSeller(in model.SellerStore, updateRevenueNew bool) error {
	params := map[string]string{
		"updateRevenueNew": fmt.Sprintf("%v", updateRevenueNew),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, in, pathUpdateSkuActiveSeller, nil)
	if err != nil {
		return nil
	}
	var result *SellerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil
	}

	return nil
}

// GetSellerConfig ...
func (cli *sellerClient) GetSellerConfig(sellerCode string) (*SellerConfig, error) {
	params := map[string]string{
		"sellerCode": sellerCode,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSellerConfig, nil)
	if err != nil {
		return nil, err
	}

	var result *SellerConfigResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

// GetSellerList ...
func (cli *sellerClient) GetSellerList(sellerClass string, offset, limit int) ([]*Seller, int, error) {
	params := map[string]string{
		"sellerClass":      sellerClass,
		"getTotal":         "true",
		"getReconcileInfo": "true",
		"offset":           strconv.Itoa(offset),
		"limit":            strconv.Itoa(limit),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSellers, nil)
	if err != nil {
		return nil, 0, err
	}

	var result *SellerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, 0, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, 0, fmt.Errorf("%v", result.Message)
	}

	return result.Data, int(result.Total), nil
}

func (cli *sellerClient) GetSeller(sellerCode string) (*Seller, error) {
	params := map[string]string{
		"sellerCode":       sellerCode,
		"getReconcileInfo": "true",
		"offset":           strconv.Itoa(0),
		"limit":            strconv.Itoa(1),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSellers, nil)
	if err != nil {
		return nil, err
	}

	var result SellerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	if len(result.Data) == 0 {
		return nil, fmt.Errorf("seller not found")
	}

	return result.Data[0], nil
}

func (cli *sellerClient) GetSellerMapFromCodes(sellerCodes []string) (sellerMap map[string]*Seller) {
	sellerMap = make(map[string]*Seller)
	offset, limit, inc := 0, 20, 20

	for {
		if offset > len(sellerCodes) {
			break
		}
		if limit > len(sellerCodes) {
			limit = len(sellerCodes)
		}

		sellersRes := cli.GetSellerListWithParams(ReqSellerList{SellerCodes: sellerCodes[offset:limit]})
		if sellersRes.Status != common.APIStatus.Ok {
			break
		}
		for _, s := range sellersRes.Data {
			sellerMap[s.Code] = s
		}

		offset += inc
		limit += inc
	}

	return
}

func (cli *sellerClient) GetSellerListWithParams(params ReqSellerList) *SellerResponse {
	res, err := cli.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Get,
		cli.headers,
		params.ToParamsString(),
		nil,
		pathGetSellers,
		nil,
	)
	if err != nil {
		return &SellerResponse{
			BaseAPIResponse: BaseAPIResponse{
				Status:    common.APIStatus.Error,
				Message:   err.Error(),
				ErrorCode: "ERROR_GET_SELLER",
			},
		}
	}

	var result *SellerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &SellerResponse{
			BaseAPIResponse: BaseAPIResponse{
				Status:    common.APIStatus.Error,
				Message:   fmt.Sprintf("Unmarshal \"%s\" got error: %s", res.Body, err.Error()),
				ErrorCode: "UNMARSHAL_GET_SELLER",
			},
		}
	}

	return result
}

func (cli *sellerClient) UpdateSellerRevenue(updatedData SellerRevenue) error {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, updatedData, pathUpdateSellersRevenue, nil)

	if err != nil {
		return nil
	}
	var result *SellerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil
	}

	return nil
}

func (cli *sellerClient) GetSellerReconciliationFeeConfigs() *common.APIResponse {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, nil, nil, pathGetSellerReconciliationFeeConfigs, nil)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Error Query VIP Seller Reconciliation config %v", err),
		}
	}

	var result SellerReconciliationFeeConfigResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Error Unmarshal VIP Seller Reconciliation config %v", err),
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Query VIP Seller Reconciliation config successfully",
		Data:    result.Data,
	}
}

func (cli *sellerClient) GetSettingForSC(codes string) *common.APIResponse {
	params := map[string]string{
		"codes": codes,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Get,
		cli.headers,
		params,
		nil,
		pathGetSettingForSC,
		nil,
	)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Error Query GetSettingForSC%v", err),
		}
	}

	var result SettingForSCResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Error Unmarshal GetSettingForSC %v", err),
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Query SellerSetting successfully",
		Data:    result.Data,
	}
}

func (cli *sellerClient) GetReconciliationScheduleSetting() []*ReconciliationScheduleSetting {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers,
		nil,
		nil,
		pathGetReconciliationScheduleSetting,
		nil,
	)
	if err != nil {
		log.Printf("GetReconciliationScheduleSetting failed: %#v\n", err)
		return nil
	}
	var result ReconciliationScheduleSettingResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		log.Printf("Error Unmarshal GetSettingForSC %v", err)
		return nil
	}

	return result.Data
}

// GetSellerBySellerCode ...
func (cli *sellerClient) GetSellerBySellerCode(sellerCode string) (*Seller, error) {
	params := map[string]string{
		"sellerCode": sellerCode,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSellerBySellerCode, nil)
	if err != nil {
		return nil, err
	}

	var result *SellerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

// GetVendorStoreList ...
func (cli *sellerClient) GetVendorStoreList(offset, limit int) ([]*VendorStore, error) {
	params := map[string]string{}
	type q struct {
		Status string `json:"status"`
	}
	body := struct {
		Q            q    `json:"q"`
		GetTotal     bool `json:"getTotal"`
		GetStoreInfo bool `json:"getStoreInfo"`
		QueryForWeb  bool `json:"queryForWeb"`
		Offset       int  `json:"offset"`
		Limit        int  `json:"limit"`
	}{
		Q:            q{Status: "ACTIVE"},
		GetTotal:     true,
		GetStoreInfo: true,
		QueryForWeb:  true,
		Offset:       offset,
		Limit:        limit,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, pathGetVendorStoreList, nil)
	if err != nil {
		return nil, err
	}

	var result *VendorStoreResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}
