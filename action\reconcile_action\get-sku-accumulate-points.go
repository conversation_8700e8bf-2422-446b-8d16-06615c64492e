package reconcile_action

import "gitlab.com/thuocsi.vn/marketplace/order-v2/client"

func GetSellerSkuAccumulatePoints(sellerCode string) map[string]*client.SkuAccumulatePoints {
	result := make(map[string]*client.SkuAccumulatePoints)
	var offset, limit int64 = 0, 100

	for {
		skus := client.Services.SellerMis.GetSkuAccumulatePointsList(&client.SkuAccumulatePoints{
			SellerCode: sellerCode,
		}, offset, limit)
		if len(skus) == 0 {
			break
		}

		for _, sku := range skus {
			result[sku.SKU] = sku
		}

		if len(skus) < int(limit) {
			break
		}
		offset += limit
	}

	return result
}
