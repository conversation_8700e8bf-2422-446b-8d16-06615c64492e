package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var SkuFirstOrderDB = &db.Instance{
	ColName:        "sku_first_order",
	TemplateObject: &SkuFirstOrder{},
}

type SkuFirstOrder struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SkuCode    string `json:"skuCode,omitempty" bson:"sku_code,omitempty"`
	SellerCode string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	OrderId    int64  `json:"orderId,omitempty" bson:"order_id,omitempty"`
	OrderCode  string `json:"orderCode,omitempty" bson:"order_code,omitempty"`
}

func InitSkuFirstOrderModel(s *mongo.Database) {
	SkuFirstOrderDB.ApplyDatabase(s)
}
