package reconcile_action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func RemoveReconciliationItem(id *primitive.ObjectID, as *model.ActionSource, callback SyncRIFunc) *common.APIResponse {
	itemF := model.ReconciliationItem{ID: id}
	result := model.ReconciliationItemDB.QueryOne(itemF)
	if result.Status != common.APIStatus.Ok {
		return result
	}
	rItem := result.Data.([]*model.ReconciliationItem)[0]

	reconciliationF := model.Reconciliation{
		SellerCode:                 rItem.SellerCode,
		ReconcileScheduleTimeIndex: rItem.ReconcileScheduleTimeIndex,
	}
	result = model.ReconciliationDB.QueryOne(reconciliationF)
	if result.Status != common.APIStatus.Ok {
		return result
	}
	reconciliation := result.Data.([]*model.Reconciliation)[0]

	if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      result.Data,
			Message:   "Trạng thái đối soát không phù hợp để chính sửa",
			ErrorCode: "INVALID_RECONCILIATION_STATUS",
		}
	}

	result = model.ReconciliationItemDB.Delete(itemF)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	// Clear fined invoice data
	switch rItem.FeeType {
	case enum.FeeType.INVOICE_OVERDUE_FEE:
		handleRemoveInvoiceOverdueFee(rItem)
	case enum.FeeType.POOR_QUALITY_PRODUCT_FEE:
		handleRemovePoorQualityProductFee(rItem)
	}

	model.ReconcileLogReqDb.Create(&model.ReconcileLogReq{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,

		FeeType:            rItem.FeeType,
		PenaltyDescription: rItem.PenaltyDescription,
		PenaltyFee:         rItem.PenaltyFee,
		PenaltyBMFee:       rItem.PenaltyBMFee,
		PenaltyBMLFee:      rItem.PenaltyBMLFee,

		AutoPenaltyFee:    rItem.AutoPenaltyFee,
		TicketID:          rItem.TicketID,
		WarehouseCode:     rItem.WarehouseCode,
		RetailPrice:       rItem.RetailPrice,
		PenaltyRate:       rItem.PenaltyRate,
		InboundCode:       rItem.InboundCode,
		DaysInbound:       rItem.DaysInbound,
		AvailableQuantity: rItem.AvailableQuantity,

		ReqType:      model.ReconcileLogReqType.Deleted,
		ActionSource: as.Account,
	})

	// Delete reconciliation if no item left
	total := model.ReconciliationItemDB.Count(reconciliationF).Total
	if total == 0 {
		res := model.ReconciliationDB.Delete(reconciliationF)
		if res.Status != common.APIStatus.Ok {
			return res
		}

		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Data:    nil,
			Message: "Xoá item cuối cùng. Xóa lượt phí đối soát thành công",
		}
	}

	// Else, update reconciliation calculation
	value, err := CalculateReconciliation(reconciliation.SellerCode, reconciliation.ReconcileScheduleTimeIndex)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: "Cập nhật dữ liệu đối soát không thành công",
		}
	}

	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}
	result = model.ReconciliationDB.UpdateOne(reconciliationF, updater)

	if callback != nil {
		// TODO: turn off billing
		// go callback(rItem)
	}
	return result
}

func RemoveMultipleReconciliationItem(ids []*primitive.ObjectID, as *model.ActionSource, callback SyncRIFunc) *common.APIResponse {
	var parentIndex string
	reconcileItems := make([]*model.ReconciliationItem, 0)
	successDeletedItems := make([]*model.ReconciliationItem, 0)
	for _, id := range ids {
		itemF := model.ReconciliationItem{ID: id}
		result := model.ReconciliationItemDB.QueryOne(itemF)
		if result.Status != common.APIStatus.Ok {
			continue
		}

		rItem := result.Data.([]*model.ReconciliationItem)[0]

		reconcileItems = append(reconcileItems, rItem)
	}

	if len(reconcileItems) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      nil,
			Message:   "Không tìm thấy các lượt phí đối soát",
			ErrorCode: "INVALID_RECONCILIATION_ITEM",
		}
	}

	parentIndex = reconcileItems[0].ReconcileScheduleTimeIndex
	for _, rItem := range reconcileItems {
		if rItem.ReconcileScheduleTimeIndex != parentIndex {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Data:      nil,
				Message:   "Không thể xóa nhiều đối soát cùng lúc",
				ErrorCode: "INVALID_RECONCILIATION_ITEM",
			}
		}
	}

	reconciliationF := model.Reconciliation{
		SellerCode:                 reconcileItems[0].SellerCode,
		ReconcileScheduleTimeIndex: reconcileItems[0].ReconcileScheduleTimeIndex,
	}

	result := model.ReconciliationDB.QueryOne(reconciliationF)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	reconciliation := result.Data.([]*model.Reconciliation)[0]
	if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      result.Data,
			Message:   "Trạng thái đối soát không phù hợp để chính sửa",
			ErrorCode: "INVALID_RECONCILIATION_STATUS",
		}
	}

	for _, rItem := range reconcileItems {
		itemF := model.ReconciliationItem{ID: rItem.ID}
		result = model.ReconciliationItemDB.Delete(itemF)
		if result.Status != common.APIStatus.Ok {
			continue
		}

		successDeletedItems = append(successDeletedItems, rItem)

		model.ReconcileLogReqDb.Create(&model.ReconcileLogReq{
			SellerCode:                 reconciliation.SellerCode,
			ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,

			FeeType:            rItem.FeeType,
			PenaltyDescription: rItem.PenaltyDescription,
			PenaltyFee:         rItem.PenaltyFee,
			PenaltyBMFee:       rItem.PenaltyBMFee,
			PenaltyBMLFee:      rItem.PenaltyBMLFee,

			AutoPenaltyFee:    rItem.AutoPenaltyFee,
			TicketID:          rItem.TicketID,
			WarehouseCode:     rItem.WarehouseCode,
			RetailPrice:       rItem.RetailPrice,
			PenaltyRate:       rItem.PenaltyRate,
			InboundCode:       rItem.InboundCode,
			DaysInbound:       rItem.DaysInbound,
			AvailableQuantity: rItem.AvailableQuantity,

			ReqType:      model.ReconcileLogReqType.Deleted,
			ActionSource: as.Account,
		})
	}

	poorQuantityMap := make(map[string][]string, 0)
	for _, rItem := range successDeletedItems {
		if rItem.FeeType == enum.FeeType.POOR_QUALITY_PRODUCT_FEE {
			if _, ok := poorQuantityMap[rItem.TicketCode]; !ok {
				poorQuantityMap[rItem.TicketCode] = make([]string, 0)
				poorQuantityMap[rItem.TicketCode] = append(poorQuantityMap[rItem.TicketCode], rItem.Sku)
			} else {
				poorQuantityMap[rItem.TicketCode] = append(poorQuantityMap[rItem.TicketCode], rItem.Sku)
			}
		}

		if rItem.FeeType == enum.FeeType.INVOICE_OVERDUE_FEE {
			handleRemoveInvoiceOverdueFee(rItem)
		}
	}

	if len(poorQuantityMap) > 0 {
		for ticketCode, skuCodes := range poorQuantityMap {
			handleRemovePoorQuantityProductFees(ticketCode, skuCodes)
		}
	}

	value, err := CalculateReconciliation(reconciliation.SellerCode, reconciliation.ReconcileScheduleTimeIndex)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: "Cập nhật dữ liệu đối soát không thành công",
		}
	}

	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}

	result = model.ReconciliationDB.UpdateOne(reconciliationF, updater)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	if callback != nil {
		for _, rItem := range successDeletedItems {
			if rItem.FeeType == enum.FeeType.INVOICE_OVERDUE_FEE {
				handleRemoveInvoiceOverdueFee(rItem)
			}

			// go callback(rItem)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    successDeletedItems,
		Message: "Xóa lượt phí đối soát thành công",
	}
}

func handleRemoveInvoiceOverdueFee(rItem *model.ReconciliationItem) {
	invoiceF := model.Invoice{
		InvoiceID: rItem.InvoiceID,
	}

	updater := bson.M{
		"is_fined":                      false,
		"reconcile_schedule_time_index": nil,
		"reconcile_item_id":             nil,
	}

	model.InvoiceDB.UpdateOne(invoiceF, updater)
}

func handleRemovePoorQualityProductFee(rItem *model.ReconciliationItem) {
	ticket, err := client.Services.Ticket.GetTicketByCode(rItem.TicketCode)
	if err != nil || ticket == nil {
		return
	}

	orderItems := make([]model.TicketOrderItem, 0)
	for _, orderItem := range *ticket.OrderItems {
		if orderItem.Sku == rItem.Sku {
			orderItem.PoorQuality = utils.ParseBoolToPointer(false)
			orderItem.Price = 0
		}

		orderItems = append(orderItems, orderItem)
	}

	updater := model.TicketCS{
		Code:       rItem.TicketCode,
		OrderItems: &orderItems,
	}

	client.Services.Ticket.UpdateTicketCS(&updater)
}

func handleRemovePoorQuantityProductFees(ticketCode string, skuCodes []string) {
	ticket, err := client.Services.Ticket.GetTicketByCode(ticketCode)
	if err != nil || ticket == nil {
		return
	}

	orderItems := make([]model.TicketOrderItem, 0)
	for _, orderItem := range *ticket.OrderItems {
		if utils.Contains(orderItem.Sku, skuCodes) {
			orderItem.PoorQuality = utils.ParseBoolToPointer(false)
			orderItem.Price = 0
		}

		orderItems = append(orderItems, orderItem)
	}

	updater := model.TicketCS{
		Code:       ticketCode,
		OrderItems: &orderItems,
	}

	client.Services.Ticket.UpdateTicketCS(&updater)
}
