package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// DealApplyResult ...
type DealApplyResult struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SKU         string      `json:"sku,omitempty" bson:"sku,omitempty"`
	DealCode    *string     `json:"dealCode,omitempty" bson:"deal_code,omitempty"`
	Quantity    *int        `json:"quantity" bson:"quantity,omitempty"`
	MaxQuantity *int        `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`
	Type        string      `json:"type,omitempty" bson:"type,omitempty"`
	Data        interface{} `json:"data,omitempty" bson:"data,omitempty"`
}

// DealApplyResultDB ...
var DealApplyResultDB = &db.Instance{
	ColName:        "deal_apply_result",
	TemplateObject: &DealApplyResult{},
}

// InitDealApplyResultModel is func init model deal apply result
func InitDealApplyResultModel(s *mongo.Database) {
	DealApplyResultDB.ApplyDatabase(s)
	// t := true
	// _ = DealApplyResultDB.CreateIndex(bson.D{
	// 	bson.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = DealApplyResultDB.CreateIndex(bson.D{
	// 	bson.E{Key: "deal_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
