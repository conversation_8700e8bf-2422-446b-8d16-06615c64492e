package action

import (
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

func CalReturnItems(ticket *model.ReturnTicket, sellerCode string) {
	if ticket == nil {
		return
	}

	items := make([]*model.ReturnTicketItem, 0, len(ticket.Items))
	for _, item := range ticket.Items {
		if item.SellerCode != sellerCode {
			continue
		}
		sumConfirmedQty := 0
		sumReturnedQty := 0

		if len(item.SubItems) == 0 {
			for _, returnInfo := range item.ReturnInfos {
				if returnInfo.ConfirmedQuantity != nil {
					sumConfirmedQty += *returnInfo.ConfirmedQuantity
				}
				if returnInfo.ReturnedQuantity != nil {
					sumReturnedQty += *returnInfo.ReturnedQuantity
				}
			}
		} else {
			if item.ComboReturnedQuantity != nil {
				sumConfirmedQty = *item.ComboReturnedQuantity
			}
			if item.ComboReturnReturnedQuantity != nil {
				sumReturnedQty = *item.ComboReturnReturnedQuantity
			}
		}

		item.SumConfirmedQtyDisplay = &sumConfirmedQty
		item.SumReturnedQtyDisplay = &sumReturnedQty
		item.TotalReturnedPriceDisplay = utils.ParseIntToPointer(sumReturnedQty * item.Price)

		if sumConfirmedQty > 0 {
			items = append(items, item)
		}
	}
	ticket.Items = items
}
