package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// OrderAudit ...
type OrderAudit struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	OrderedAt       *time.Time         `json:"orderedAt,omitempty" bson:"ordered_at,omitempty"`

	CustomerID       int64             `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	CustomerCode     string            `json:"customerCode,omitempty" bson:"customer_code,omitempty"`
	SaleOrderCode    string            `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	OrderID          int64             `json:"orderID,omitempty" bson:"order_id,omitempty"`
	OrderCode        string            `json:"orderCode,omitempty" bson:"order_code,omitempty"`
	ActionAt         int64             `json:"actionAt,omitempty" bson:"action_at,omitempty"`
	Retry            int64             `json:"retry,omitempty" bson:"retry,omitempty"`
	Status           string            `json:"status,omitempty" bson:"status,omitempty"`
	OrderStatus      string            `json:"orderStatus,omitempty" bson:"order_status,omitempty"`
	Note             string            `json:"note,omitempty" bson:"note,omitempty"`
	OrderAuditDetail *OrderAuditDetail `json:"orderAuditDetail,omitempty" bson:"order_audit_detail,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type OrderAuditDetail struct {
	SO                string `json:"so,omitempty" bson:"so,omitempty"`
	OrderStatus       string `json:"orderStatus,omitempty" bson:"order_status,omitempty"`
	PaymentMethod     string `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	PaymentMethodFee  int64  `json:"paymentMethodFee,omitempty" bson:"payment_method_fee,omitempty"`
	DeliveryMethodFee int64  `json:"deliveryMethodFee,omitempty" bson:"delivery_method_fee,omitempty"`
	ExtraFee          int64  `json:"extraFee,omitempty" bson:"extra_fee,omitempty"`
	TotalDiscount     int64  `json:"totalDiscount,omitempty" bson:"total_discount,omitempty"`
	TotalPrice        int    `json:"totalPrice,omitempty" bson:"total_price,omitempty"`

	WmsSO                string `json:"wmsSo,omitempty" bson:"wms_so,omitempty"`
	WmsOrderStatus       string `json:"wmsOrderStatus,omitempty" bson:"wms_order_status,omitempty"`
	WmsPaymentMethodFee  int64  `json:"wmsPaymentMethodFee,omitempty" bson:"wms_payment_method_fee,omitempty"`
	WmsDeliveryMethodFee int64  `json:"wmsDeliveryMethodFee,omitempty" bson:"wms_delivery_method_fee,omitempty"`
	WmsExtraFee          int64  `json:"wmsExtraFee,omitempty" bson:"wms_extra_fee,omitempty"`
	WmsTotalDiscount     int64  `json:"wmsTotalDiscount,omitempty" bson:"wms_total_discount,omitempty"`
	WmsTotalPrice        int    `json:"wmsTotalPrice,omitempty" bson:"wms_total_price,omitempty"`
	WmsPaymentMethod     string `json:"wmsPaymentMethod,omitempty" bson:"wms_payment_method,omitempty"`
}

type OrderLineAudit struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	OrderID              int64  `json:"orderID,omitempty" bson:"order_id,omitempty"`
	ProductID            int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU                  string `json:"sku,omitempty" bson:"sku,omitempty"`
	OrderStatus          string `json:"orderStatus,omitempty" bson:"order_status,omitempty"`
	ReservedQuantity     *int   `json:"reservedQuantity,omitempty" bson:"reserved_quantity,omitempty"`
	WmsReservedQuantity  *int   `json:"wmsReservedQuantity,omitempty" bson:"wms_reserved_quantity,omitempty"`
	OutboundQuantity     *int   `json:"outboundQuantity,omitempty" bson:"outbound_quantity,omitempty"`
	WmsOutboundQuantity  *int   `json:"wmsOutboundQuantity,omitempty" bson:"wms_outbound_quantity,omitempty"`
	DeliveredQuantity    *int   `json:"deliveredQuantity,omitempty" bson:"delivered_quantity,omitempty"`
	WmsDeliveredQuantity *int   `json:"wmsDeliveredQuantity,omitempty" bson:"wms_delivered_quantity,omitempty"`
	ReturnQuantity       *int   `json:"returnQuantity,omitempty" bson:"return_quantity,omitempty"`
	WmsReturnQuantity    *int   `json:"wmsReturnQuantity,omitempty" bson:"wms_return_quantity,omitempty"`
	Price                *int64 `json:"price,omitempty" bson:"price,omitempty"`
	WmsPrice             *int64 `json:"wmsPrice,omitempty" bson:"wms_price,omitempty"`
	TotalPrice           *int64 `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
	WmsTotalPrice        *int64 `json:"wmsTotalPrice,omitempty" bson:"wms_total_price,omitempty"`

	Status string `json:"status,omitempty" bson:"status,omitempty"`
	Note   string `json:"note,omitempty" bson:"note,omitempty"`
}

// OrderAuditDB ...
var OrderAuditDB = &db.Instance{
	ColName:        "order_audit_v2",
	TemplateObject: &OrderAudit{},
}

var OrderLineAuditDB = &db.Instance{
	ColName:        "order_line_audit_v2",
	TemplateObject: &OrderLineAudit{},
}

// InitOrderAuditModel ...
func InitOrderAuditModel(s *mongo.Database) {
	OrderAuditDB.ApplyDatabase(s)
	OrderLineAuditDB.ApplyDatabase(s)

	t := true
	_ = OrderAuditDB.CreateIndex(bson.D{
		bson.E{Key: "order_id", Value: 1},
		bson.E{Key: "order_status", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	_ = OrderAuditDB.CreateIndex(bson.D{
		bson.E{Key: "order_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = OrderAuditDB.CreateIndex(bson.D{
		bson.E{Key: "action_at", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = OrderAuditDB.CreateIndex(bson.D{
		bson.E{Key: "ordered_at", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = OrderLineAuditDB.CreateIndex(bson.D{
		primitive.E{Key: "order_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = OrderLineAuditDB.CreateIndex(bson.D{
		primitive.E{Key: "product_id", Value: 1},
		primitive.E{Key: "order_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	_ = OrderLineAuditDB.CreateIndex(bson.D{
		primitive.E{Key: "sku", Value: 1},
		primitive.E{Key: "order_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})
}
