package reconcile_action

import (
	"fmt"
	"math"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

func ProcessCalculateApplyVoucher(
	order *model.Order,
	orderItems []*model.OrderItem,
	reconciliation *model.Reconciliation,
	mapOrderArrSKUReturn *map[int64][]*model.ReturnQuantityObject,
) {
	if len(order.RedeemApplyResult) == 0 {
		return
	}

	if reconciliation == nil {
		return
	}

	applyVoucherAmount := 0 // tổng tiền voucher áp dụng
	applyVoucherCodes := make(map[string]bool, 0)

	if len(order.DeliveryOrderStatuses) == 0 {
		return
	}

	sellerApplyVouchers := make(map[string]bool, 0)
	for _, applyResult := range order.RedeemApplyResult {
		if applyResult.ChargeFee != "SELLER_CENTER" {
			continue
		}

		if reconciliation.SellerCode != applyResult.SellerCode {
			continue
		}

		sellerApplyVouchers[applyResult.Code] = true
	}

	acceptedDOs, _ := getAcceptedDOs(order.SaleOrderCode)

	for _, doStatus := range order.DeliveryOrderStatuses {
		if !acceptedDOs[doStatus.Code] {
			return
		}

		if doStatus.VoucherDiscount <= 0 {
			continue
		}

		if len(doStatus.VoucherAmountDetails) == 0 {
			continue
		}

		for _, voucherAmountDetail := range doStatus.VoucherAmountDetails {
			if voucherAmountDetail.DiscountAmount <= 0 {
				continue
			}

			if !utils.Contains(reconciliation.SellerCode, voucherAmountDetail.SellerCodes) {
				continue
			}

			if ok := sellerApplyVouchers[voucherAmountDetail.Code]; !ok {
				continue
			}

			applyVoucherAmount += int(voucherAmountDetail.DiscountAmount)
			applyVoucherCodes[voucherAmountDetail.Code] = true
		}
	}

	refundVoucherAmount := 0
	for _, item := range orderItems {
		if item.DiscountDetail == nil {
			continue
		}
		if len(item.DiscountDetail.VoucherDetails) == 0 {
			continue
		}

		returnObj := findOrderItemReturn(item.Sku, item.OrderID, mapOrderArrSKUReturn)
		if returnObj.ReturnedQuantity == 0 {
			continue
		}

		returnQty := returnObj.ReturnedQuantity
		refundAmount := 0
		itemVoucherAmount := 0

		for _, voucherApplyDetail := range item.DiscountDetail.VoucherDetails {
			if !voucherApplyDetail.IsApply || !utils.Contains(reconciliation.SellerCode, voucherApplyDetail.SellerCodes) {
				continue
			}

			if ok := sellerApplyVouchers[voucherApplyDetail.VoucherCode]; !ok {
				continue
			}

			itemVoucherAmount += voucherApplyDetail.DiscountValue
		}

		if *item.DeliveredQuantity == returnQty {
			refundAmount = itemVoucherAmount
			refundVoucherAmount += refundAmount
		} else {
			calc := float64(itemVoucherAmount * returnQty / *item.DeliveredQuantity)
			refundVoucherAmount += int(math.Round(calc))
		}
	}

	if refundVoucherAmount > 0 {
		applyVoucherAmount -= refundVoucherAmount
	}

	applyCodes := make([]string, 0)
	for code := range applyVoucherCodes {
		applyCodes = append(applyCodes, code)
	}

	filter := model.ReconciliationItem{
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		SellerCode:                 reconciliation.SellerCode,
		FeeType:                    enum.FeeType.VOUCHER_DEDUCTION,
		OrderID:                    order.OrderID,
	}

	if applyVoucherAmount <= 0 {
		model.ReconciliationItemDB.Delete(filter)
		return
	}

	description := fmt.Sprintf(
		"Áp dụng voucher giảm giá %s cho đơn hàng %s đợt %s ~ %s",
		strings.Join(applyCodes, ", "),
		order.SaleOrderCode,
		utils.FormatTimeString(reconciliation.FromTime),
		utils.FormatTimeString(reconciliation.ToTime),
	)

	model.ReconciliationItemDB.Upsert(filter, model.ReconciliationItem{
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		SellerCode:                 reconciliation.SellerCode,
		FeeType:                    enum.FeeType.VOUCHER_DEDUCTION,
		AutoPenaltyFee:             true,
		OrderID:                    order.OrderID,
		SaleOrderCode:              order.SaleOrderCode,

		PenaltyBMFee:       applyVoucherAmount,
		PenaltyDescription: description,
		VoucherCodes:       applyCodes,
	})
}

func ProcessCalculateRefundVoucher(
	order *model.Order,
	orderItems []*model.OrderItem,
	reconciliation *model.Reconciliation,
	mapOrderArrSKUReturn *map[int64][]*model.ReturnQuantityObject,
) {
	if order == nil {
		return
	}

	if len(order.RedeemApplyResult) == 0 {
		return
	}

	sellerApplyVouchers := make(map[string]bool, 0)
	for _, applyResult := range order.RedeemApplyResult {
		if applyResult.ChargeFee != "SELLER_CENTER" {
			continue
		}

		if reconciliation.SellerCode != applyResult.SellerCode {
			continue
		}

		sellerApplyVouchers[applyResult.Code] = true
	}

	// check prev reconciliation
	filter := model.ReconciliationItem{
		SellerCode: reconciliation.SellerCode,
		FeeType:    enum.FeeType.VOUCHER_DEDUCTION,
		OrderID:    order.OrderID,
	}

	deductionRes := model.ReconciliationItemDB.Query(filter, 0, 1, nil)

	if deductionRes.Status != common.APIStatus.Ok {
		return
	}

	deductionReconcile := deductionRes.Data.([]*model.ReconciliationItem)[0]

	if deductionReconcile == nil {
		return
	}

	refundVoucherAmount := 0 // tổng tiền voucher áp dụng
	refundVoucherCodes := make(map[string]bool, 0)
	for _, item := range orderItems {
		if item.DiscountDetail == nil {
			continue
		}
		if len(item.DiscountDetail.VoucherDetails) == 0 {
			continue
		}

		totalVoucherAmount := 0
		for _, voucherApplyDetail := range item.DiscountDetail.VoucherDetails {
			if !voucherApplyDetail.IsApply || !utils.Contains(item.SellerCode, voucherApplyDetail.SellerCodes) {
				continue
			}

			if ok := sellerApplyVouchers[voucherApplyDetail.VoucherCode]; !ok {
				continue
			}

			totalVoucherAmount += voucherApplyDetail.DiscountValue
			refundVoucherCodes[voucherApplyDetail.VoucherCode] = true
		}

		returnObj := findOrderItemReturn(item.Sku, item.OrderID, mapOrderArrSKUReturn)
		returnQty := 0

		if returnObj.ReturnedQuantity != 0 {
			returnQty = returnObj.ReturnedQuantity
		}

		if item.DeliveredQuantity == &returnQty {
			refundVoucherAmount += totalVoucherAmount
		} else {
			calc := float64(totalVoucherAmount * returnQty / *item.DeliveredQuantity)
			refundVoucherAmount += int(math.Round((calc)))
		}
	}

	filter = model.ReconciliationItem{
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		SellerCode:                 reconciliation.SellerCode,
		FeeType:                    enum.FeeType.VOUCHER_REFUND,
		OrderID:                    order.OrderID,
	}

	if refundVoucherAmount <= 0 {
		model.ReconciliationItemDB.Delete(filter)
		return
	}

	applyCodes := make([]string, 0)
	for code := range refundVoucherCodes {
		applyCodes = append(applyCodes, code)
	}

	description := fmt.Sprintf(
		"Khách hàng hoàn trả voucher giảm giá %s cho đơn hàng %s đợt %s ~ %s",
		strings.Join(applyCodes, ", "),
		order.SaleOrderCode,
		utils.FormatTimeString(reconciliation.FromTime),
		utils.FormatTimeString(reconciliation.ToTime),
	)

	model.ReconciliationItemDB.Upsert(filter, model.ReconciliationItem{
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		SellerCode:                 reconciliation.SellerCode,
		FeeType:                    enum.FeeType.VOUCHER_REFUND,
		AutoPenaltyFee:             true,
		OrderID:                    order.OrderID,
		SaleOrderCode:              order.SaleOrderCode,

		BonusBMAmount:                        refundVoucherAmount,
		BonusDescription:                     description,
		VoucherCodes:                         applyCodes,
		PreviousReconcileScheduleTimeIndexes: []string{deductionReconcile.ReconcileScheduleTimeIndex},
	})
}

func findOrderItemReturn(
	sku string,
	orderID int64,
	mapOrderArrSKUReturn *map[int64][]*model.ReturnQuantityObject,
) model.ReturnQuantityObject {
	obj := model.ReturnQuantityObject{
		SKU: sku,
	}

	arrSKU := (*mapOrderArrSKUReturn)[orderID]
	for _, it := range arrSKU {
		if sku == it.SKU {
			obj.ReturnedQuantity += it.ReturnedQuantity
			obj.MainQuantity += it.MainQuantity
			obj.DamageQuantity += it.DamageQuantity
			obj.MissingQuantity += it.MissingQuantity
		}
	}

	return obj
}

func getAcceptedDOs(saleOrderCode string) (map[string]bool, []*model.DeliveryOrder) {
	m := map[string]bool{}
	dosRes, err := client.Services.Warehouse.GetDeliveryOrders(&model.DeliveryOrderReq{
		SaleOrderCode: saleOrderCode,
	})
	if err != nil {
		return m, nil
	}

	for _, do := range dosRes {
		if do.Status == nil {
			continue
		}

		if *do.Status == enum.SaleOrderStatus.Delivered ||
			*do.Status == enum.SaleOrderStatus.Completed {
			m[do.DeliveryOrderCode] = true
		}
	}

	return m, dosRes
}
