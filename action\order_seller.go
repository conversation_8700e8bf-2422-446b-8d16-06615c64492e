package action

import (
	"encoding/json"
	"strconv"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetRevenueSellerList is func get list Revenue seller have pagination
func GetRevenueSellerList(account *model.Account, query model.Reconciliation, offset, limit int64, getTotal bool) *common.APIResponse {
	if query.SellerCode == "" {
		// get seller info
		seller, errSeller := getSellerInfo(account)
		if errSeller != nil {
			return errSeller
		}
		query.SellerCode = seller.Code
	}
	qData := model.Reconciliation{
		SellerCode:           query.SellerCode,
		ReconciliationStatus: query.ReconciliationStatus,
	}
	var result *common.APIResponse
	if query.ReconciliationStatus == model.ReconciliationStatus.Waiting {
		result = model.ReconciliationDB.Query(qData, offset, limit, &primitive.M{"reconcile_schedule_time_index": 1})
	} else {
		result = model.ReconciliationDB.Query(qData, offset, limit, &primitive.M{"_id": -1})
	}

	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "RECONCILIATION_NOT_FOUND"
		return result
	}

	if getTotal {
		result.Total = model.ReconciliationDB.Count(qData).Total
	}
	return result
}

// GetRevenueItemSellerList is func get list Revenue seller item have pagination
func GetRevenueItemSellerList(account *model.Account, query model.ReconciliationItem, offset, limit int64, getTotal bool) *common.APIResponse {
	if query.SellerCode == "" || query.SellerCode == ":sellerCode" {
		// get seller info
		seller, errSeller := getSellerInfo(account)
		if errSeller != nil {
			return errSeller
		}
		query.SellerCode = seller.Code
	}

	qData := model.ReconciliationItem{
		ReconcileScheduleTimeIndex: query.ReconcileScheduleTimeIndex,
		SellerCode:                 query.SellerCode,
		FeeType:                    query.FeeType,
	}
	result := model.ReconciliationItemDB.Query(qData, offset, limit, &primitive.M{"_id": 1})
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "RECONCILIATION_NOT_FOUND"
		return result
	}

	if getTotal {
		result.Total = model.ReconciliationItemDB.Count(qData).Total
	}

	return result
}

// GetRevenueList is func get list Revenue have pagination
func GetRevenueList(
	query model.Reconciliation,
	offset, limit int64,
	getTotal bool,
) *common.APIResponse {
	qData := model.Reconciliation{
		SellerCode:                 query.SellerCode,
		ReconcileScheduleTimeIndex: query.ReconcileScheduleTimeIndex,
		ReconciliationStatus:       query.ReconciliationStatus,
		PaidFor:                    query.PaidFor,
		RecCode:                    query.RecCode,
	}

	var flagshipSellerCodes []string
	if query.IsFlagshipStore != nil {
		payload := struct {
			IsVip bool `json:"isVip"`
		}{
			IsVip: true,
		}
		payloadByte, _ := json.Marshal(payload)

		offset := 0
		limit := 1000
		for {
			sellersRes := client.Services.Seller.GetSellerListWithParams(client.ReqSellerList{
				Q:      string(payloadByte),
				Offset: offset,
				Limit:  limit,
			})
			if sellersRes.Status != common.APIStatus.Ok {
				break
			}

			for _, seller := range sellersRes.Data {
				flagshipSellerCodes = append(flagshipSellerCodes, seller.Code)
			}
			offset += limit
		}

		qData.OperationAnd = []*bson.M{{
			"seller_code": &bson.M{
				"$nin": flagshipSellerCodes,
			},
		}}
		if *query.IsFlagshipStore {
			qData.OperationAnd = []*bson.M{{
				"seller_code": &bson.M{
					"$in": flagshipSellerCodes,
				},
			}}
		}
	}

	if query.HasOrderID != 0 {
		var complexQuery []*bson.M
		res := model.ReconciliationItemDB.Query(model.ReconciliationItem{
			OrderID: query.HasOrderID,
		}, 0, 0, &primitive.M{"_id": 1})
		if res.Status != common.APIStatus.Ok {
			return res
		}
		for _, item := range res.Data.([]*model.ReconciliationItem) {
			complexQuery = append(complexQuery, &bson.M{
				"seller_code":                   item.SellerCode,
				"reconcile_schedule_time_index": item.ReconcileScheduleTimeIndex,
			})
		}
		qData.OperationOr = complexQuery
	}

	// format yyyymmdd - ex: 20221028
	if query.ScheduleTimeFrom != "" && query.ScheduleTimeTo != "" {
		from, err := strconv.Atoi(query.ScheduleTimeFrom)
		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Invalid scheduleTimeFrom format",
				ErrorCode: "INVALID_TIME",
			}
		}

		to, err := strconv.Atoi(query.ScheduleTimeTo)
		if err != nil {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Invalid scheduleTimeTo format",
				ErrorCode: "INVALID_TIME",
			}
		}

		if to-from > 200 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Time range too large",
				ErrorCode: "INVALID_TIME_RANGE",
			}
		}

		qData.OperationAnd = append(qData.OperationAnd, &bson.M{
			"reconcile_schedule_time_index": bson.M{"$gte": query.ScheduleTimeFrom},
		})
		qData.OperationAnd = append(qData.OperationAnd, &bson.M{
			"reconcile_schedule_time_index": bson.M{"$lte": query.ScheduleTimeTo},
		})
	}

	result := model.ReconciliationDB.Query(qData, offset, limit, &primitive.M{"_id": -1})
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "RECONCILIATION_NOT_FOUND"
		return result
	}

	if getTotal {
		result.Total = model.ReconciliationDB.Count(qData).Total
	}
	return result
}

// GetRevenue is func get Revenue
// Deprecated: use GetRevenueList instead
func GetRevenue(query model.Reconciliation) *common.APIResponse {
	if query.SellerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing seller code",
			ErrorCode: "MISSING_SELLER_CODE",
		}
	}
	result := model.ReconciliationDB.QueryOne(query)
	if result.Status != common.APIStatus.Ok {
		return result
	}

	reconciliation := result.Data.([]*model.Reconciliation)[0]
	filter := model.ReconciliationItem{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
	}
	total := model.ReconciliationItemDB.Count(filter).Total
	itemResult := model.ReconciliationItemDB.Query(filter, 0, total, &primitive.M{"_id": 1})
	if itemResult.Status != common.APIStatus.Ok {
		return result
	}

	items := itemResult.Data.([]*model.ReconciliationItem)
	reconciliation.ReconciliationItems = items
	result.Data = []*model.Reconciliation{reconciliation}
	return result
}

// GetRevenueItemList is func get list Revenue item have pagination
func GetRevenueItemList(query model.ReconciliationItem, offset, limit int64, getTotal bool) *common.APIResponse {
	if query.SellerCodes != nil && len(query.SellerCodes) > 0 {
		query.OperationAnd = []bson.M{
			{"seller_code": &bson.M{"$in": query.SellerCodes}},
		}
	}
	if query.FeeTypes != nil && len(query.FeeTypes) > 0 {
		query.OperationAnd = []bson.M{
			{"fee_type": bson.M{"$in": query.FeeTypes}},
		}
	}

	if query.LastQueryID != nil {
		query.OperationAnd = []bson.M{
			{"_id": bson.M{"$gt": query.LastQueryID}},
		}
	}

	result := model.ReconciliationItemDB.Query(query, offset, limit, &primitive.M{"_id": 1})
	if result.Status != common.APIStatus.Ok {
		result.ErrorCode = "RECONCILIATION_NOT_FOUND"
		return result
	}

	if getTotal {
		result.Total = model.ReconciliationItemDB.Count(query).Total
	}

	return result
}
