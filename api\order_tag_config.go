package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func GetOrderTagConfigByCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	if code := req.GetParam("code"); code != "" {
		return resp.Respond(action.GetOrderTagConfigByCode(code))
	}
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Missing code",
		ErrorCode: "MISSING_CODE",
	})
}
func UpsertOrderTagConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.OrderTagConfig

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if input.TagCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing code",
			ErrorCode: "MISSING_CODE",
		})
	}
	if input.ProvinceAppliedValue != nil && *input.ProvinceAppliedValue < 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "ProvinceAppliedValue lower than zero",
			ErrorCode: "INVALID_VALUE",
		})
	}
	return resp.Respond(action.UpsertOrderTagConfig(&input))
}

func GetListOrderTagConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit  = sdk.ParseInt64(req.GetParam("limit"), 20)
		q      = req.GetParam("q")
	)
	if limit < 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}
	var query = model.OrderTagConfig{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}
	return resp.Respond(action.GetListOrderTagConfig(&query, offset, limit))
}
