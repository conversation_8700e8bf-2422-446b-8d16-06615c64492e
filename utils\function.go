package utils

import (
	"context"
	"sync"

	"github.com/speps/go-hashids"
)

// Task ...
type Task func(ctx context.Context) error

// FetchTasks ...
func FetchTasks(tasks ...Task) error {
	errors := make([]error, len(tasks))
	wg := sync.WaitGroup{}
	wg.Add(len(tasks))

	ctx := context.Background()
	for i, task := range tasks {
		go func(i int, task func(ctx context.Context) error) {
			errors[i] = task(ctx)
			wg.Done()
		}(i, task)
	}
	wg.Wait()
	return collectErrors(errors)
}

func collectErrors(errors []error) error {
	if len(errors) == 0 {
		return nil
	}
	if len(errors) == 1 {
		return errors[0]
	}
	for _, err := range errors {
		if err != nil {
			return err
		}
	}
	return nil
}

// GenHashID ...
func GenHashID(numbers ...int) string {
	hd := hashids.NewData()
	hd.Salt = "thuocsi v2"
	hd.MinLength = 10
	h, _ := hashids.NewWithData(hd)
	e, _ := h.Encode(numbers)
	return e
}

