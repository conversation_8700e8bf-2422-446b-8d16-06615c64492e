package reconcile_action

import (
	"log"
	"math"
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

const (
	threePercent  = 0.03
	fivePercent   = 0.05
	ninePercent   = 0.09
	elevenPercent = 0.11
)

const (
	fortyFive   = 45
	sixty       = 60
	seventyFive = 75
	nineTy      = 90
)

func ProcessPenaltyInboundOverdue(
	seller client.Seller,
	reconciliation *model.Reconciliation,
	from, to, timeIndex string,
	_time time.Time,
) {
	if reconciliation == nil {
		var err error
		reconciliation, err = CreateReconciliationFromIndex(seller.Code, timeIndex, from, to, _time)
		if err != nil {
			log.Println("ProcessPenaltyInboundOverdue", err.Error())
			return
		}
	}

	if reconciliation.ReconciliationStatus != model.ReconciliationStatus.Waiting {
		return
	}

	if time.Now().After(_time.Add(12 * time.Hour)) {
		return
	}

	// prevent penalty befor 2023-06-16
	if reconciliation.ReconcileScheduleTimeIndex <= "20230616.20230608" {
		return
	}

	if seller.ReconcileInfo != nil &&
		seller.ReconcileInfo.SkipInboundPenalty {
		return
	}

	warehouseMap := getWarehouseMap()
	if len(warehouseMap) == 0 {
		return
	}

	for offset, limit := 0, 100; ; offset += limit {
		skusInventory := client.Services.Inventory.
			GetSkuInventory(reconciliation.SellerCode, offset, limit)
		if len(skusInventory) == 0 {
			break
		}

		for _, skuInventory := range skusInventory {
			if skuInventory == nil ||
				skuInventory.AvailableQuantity <= 0 {
				continue
			}

			// skip Da Nang warehouse
			if skuInventory.WarehouseCode == "DN" {
				continue
			}

			warehouseId, ok := warehouseMap[skuInventory.WarehouseCode]
			if !ok || warehouseId == 0 {
				continue
			}
			processPenaltyInboundOverdue(skuInventory, reconciliation)

		}
	}
}

func processPenaltyInboundOverdue(
	skuInventory *client.SKUInventory,
	reconciliation *model.Reconciliation,
) {
	latestPutInfo, lastInboundTime := getInboundTimeByPutTicket(skuInventory)
	daysSinceLastInbound := getDaysInbound(lastInboundTime)

	if daysSinceLastInbound < fortyFive {
		return
	}

	retailPrice := getSkuRetailPrice(skuInventory.Sku)
	if retailPrice == 0 {
		return
	}

	sku := client.Services.Product.GetSkuInfo(skuInventory.Sku)
	if sku != nil && !sku.IsActive {
		return
	}

	var inboundCode string
	if latestPutInfo != nil {
		inboundCode = latestPutInfo.InboundCode
	}

	upsertData := model.ReconciliationItem{
		SellerCode:                 reconciliation.SellerCode,
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,

		Sku:               skuInventory.Sku,
		ProductId:         skuInventory.ProductID,
		WarehouseCode:     skuInventory.WarehouseCode,
		AvailableQuantity: skuInventory.AvailableQuantity,

		AutoPenaltyFee: true,
		FeeType:        enum.FeeType.INBOUND_OVERDUE_PENALTY,

		InboundCode: inboundCode,
		InboundTime: lastInboundTime,
		DaysInbound: daysSinceLastInbound,
		RetailPrice: retailPrice,
	}

	totalPrice := float64(retailPrice * skuInventory.AvailableQuantity)
	startTime := time.Date(2024, 9, 2, 0, 0, 0, 0, model.VNTimeZone)
	nowInVN := time.Now().In(model.VNTimeZone)
	var minPenaltyQty int64 = 5
	if nowInVN.After(startTime) {
		minPenaltyQty = 10
	}

	switch {
	case daysSinceLastInbound >= nineTy:
		upsertData.PenaltyTimeline = nineTy
		upsertData.PenaltyRate = fivePercent
		upsertData.PenaltyBMLFee = int(totalPrice * upsertData.PenaltyRate)
		ProcessPenaltyReconciliationItem(upsertData, reconciliation)

	case daysSinceLastInbound >= sixty:
		upsertData.PenaltyTimeline = sixty
		upsertData.PenaltyRate = fivePercent

		var skuDemand int64
		unsoldDemand := client.Services.SellerReporting.GetSkuUnsoldDemand(skuInventory.Sku, skuInventory.WarehouseCode)
		time.Sleep(10 * time.Millisecond)
		if unsoldDemand != nil {
			skuDemand = *unsoldDemand
		}
		feeFreeQuantity := math.Max(float64(minPenaltyQty), float64(skuDemand))
		penaltyQuantity := deductionUnit(skuInventory.AvailableQuantity, int64(feeFreeQuantity))

		totalPrice = float64(retailPrice * penaltyQuantity)
		upsertData.PenaltyBMLFee = int(totalPrice * upsertData.PenaltyRate)
		ProcessPenaltyReconciliationItem(upsertData, reconciliation)

	case daysSinceLastInbound >= fortyFive:
		if hasNoOrderFirst45Days(skuInventory.Sku, *lastInboundTime) {
			upsertData.PenaltyRate = fivePercent
			upsertData.PenaltyTimeline = fortyFive
			totalPrice = float64(retailPrice * deductionUnit(skuInventory.AvailableQuantity, minPenaltyQty))
			upsertData.PenaltyBMLFee = int(totalPrice * upsertData.PenaltyRate)
			ProcessPenaltyReconciliationItem(upsertData, reconciliation)
		} else if hasNoOrderFromDay30To45(skuInventory.Sku, *lastInboundTime) {
			upsertData.PenaltyRate = threePercent
			upsertData.PenaltyTimeline = fortyFive
			totalPrice = float64(retailPrice * deductionUnit(skuInventory.AvailableQuantity, minPenaltyQty))
			upsertData.PenaltyBMLFee = int(totalPrice * upsertData.PenaltyRate)
			ProcessPenaltyReconciliationItem(upsertData, reconciliation)
		}
	}
}

func hasNoOrderFirst45Days(skuCode string, inboundTime time.Time) bool {
	return getOrderInTimeRange(skuCode, inboundTime, inboundTime.AddDate(0, 0, 45)) == nil
}

func hasNoOrderFromDay30To45(skuCode string, inboundTime time.Time) bool {
	return getOrderInTimeRange(skuCode, inboundTime.AddDate(0, 0, 30), inboundTime.AddDate(0, 0, 45)) == nil
}

func deductionUnit(quantity, deductionQty int64) int64 {
	max := math.Max(0, float64(quantity-deductionQty))

	return int64(max)
}
