package reconcile_action

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

// GetSchedule returns schedule for reconciliation
// for specific seller at completed time given
func GetSchedule(seller *client.Seller, completed time.Time) string {
	completed = completed.In(utils.VNTimeZone)

	var appliedBefore time.Time
	if completed.Day() <= 15 {
		appliedBefore = time.Date(completed.Year(), completed.Month(), 1, 0, 0, 0, 0, utils.VNTimeZone)
	} else {
		appliedBefore = time.Date(completed.Year(), completed.Month(), 16, 0, 0, 0, 0, utils.VNTimeZone)
	}
	if Apply3DaysR<PERSON>oncile(seller, appliedBefore) {
		switch d := completed.Day(); {
		case 1 <= d && d <= 3:
			return "SCHEDULE_01_TO_03"
		case 4 <= d && d <= 6:
			return "SCHEDULE_04_TO_06"
		case 7 <= d && d <= 9:
			return "SCHEDULE_07_TO_09"
		case 10 <= d && d <= 12:
			return "SCHEDULE_10_TO_12"
		case 13 <= d && d <= 15:
			return "SCHEDULE_13_TO_15"
		case 16 <= d && d <= 18:
			return "SCHEDULE_16_TO_18"
		case 19 <= d && d <= 21:
			return "SCHEDULE_19_TO_21"
		case 22 <= d && d <= 24:
			return "SCHEDULE_22_TO_24"
		case 25 <= d && d <= 27:
			return "SCHEDULE_25_TO_27"
		case 28 <= d && d <= 31:
			return "SCHEDULE_28_TO_END"
		}
	}

	var level enum.LevelSellerValue
	if !seller.LevelInfo.Date.IsZero() &&
		appliedBefore.After(seller.LevelInfo.Date) {
		level = enum.LevelSellerValue(*seller.Level)
	} else {
		level = enum.LevelSellerValue(seller.LevelInfo.LevelPrev)
	}

	switch level {
	case enum.LevelSeller.MARKET:
		fallthrough
	case enum.LevelSeller.LEVEL_1: // 4 lượt đối soát
		switch d := completed.Day(); {
		case 1 <= d && d <= 7:
			return "SCHEDULE_01_TO_07"
		case 8 <= d && d <= 15:
			return "SCHEDULE_08_TO_15"
		case 16 <= d && d <= 22:
			return "SCHEDULE_16_TO_22"
		case 23 <= d:
			return "SCHEDULE_23_TO_END"
		}
	case enum.LevelSeller.LEVEL_2: // 2 lượt đối soát
		switch d := completed.Day(); {
		case 1 <= d && d <= 15:
			return "SCHEDULE_01_TO_15"
		case 16 <= d:
			return "SCHEDULE_16_TO_END"
		}
	}

	return ""
}
