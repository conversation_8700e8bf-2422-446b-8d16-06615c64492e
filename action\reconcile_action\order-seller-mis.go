package reconcile_action

import (
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func GetOrderSellers(parentOrderId int64) (result []*model.OrderSeller) {
	for offset, limit := int64(0), int64(20); ; offset += limit {
		orderSellers := client.Services.SellerMis.GetOrderSellerList(&model.OrderSeller{
			ParentOrderID: parentOrderId,
		}, offset, limit)
		if len(orderSellers) == 0 {
			break
		}

		result = append(result, orderSellers...)

		if len(orderSellers) < int(limit) {
			break
		}
	}
	return
}
