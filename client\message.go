package client

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathSendMessage = "/integration/messaging/v1/message"
)

// MessageClient is model define SMS client
type MessageClient struct {
	cli     *client.RestClient
	headers map[string]string
}

type Message struct {
	Topic    string `json:"topic,omitempty" bson:"topic,omitempty"`
	Source   string `json:"source,omitempty" bson:"source,omitempty"`
	Receiver string `json:"receiver,omitempty" bson:"receiver,omitempty"`

	// template
	UseTemplate *bool             `json:"useTemplate,omitempty" bson:"use_template,omitempty"`
	Dictionary  map[string]string `json:"dictionary,omitempty" bson:"dictionary,omitempty"`
}

type MessageResponse struct {
	Status string     `json:"status,omitempty"`
	Data   []*Message `json:"data"`
}

// NewMessageClient is func define new Notification client
func NewMessageClient(apiHost, apiKey, logName string, session *mongo.Database) *MessageClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	client := &MessageClient{
		cli: client.NewRESTClient(
			apiHost,
			logName,
			time.Duration(3*time.Second),
			1,
			time.Duration(3*time.Second),
		),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}

	client.cli.SetDBLog(session)

	return client
}

// SendMessage send message to user
func (cli *MessageClient) SendMessage(in *Message) (*MessageResponse, error) {

	params := map[string]string{}
	res, err := cli.cli.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathSendMessage, nil)
	if err != nil {
		return nil, err
	}

	var result *MessageResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
