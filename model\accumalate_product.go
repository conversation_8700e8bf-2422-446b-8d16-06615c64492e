package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type AccumulateProduct struct {
	ID            *primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	CreatedTime   *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CustomerID    int64               `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	Version       string              `json:"version,omitempty" bson:"version,omitempty"`
	ProductIDs    []int64             `json:"productIds,omitempty" bson:"product_ids,omitempty"`
	CountProducts int                 `json:"countProducts,omitempty" bson:"count_products,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type AccumulateProductResponse struct {
	OldAccumulateProductCount int64 `json:"oldAccumulateProductount,omitempty"`
	NewAccumulateProductCount int64 `json:"newAccumulateProductCount,omitempty"`
}

// AccumalateProductDB ...
var AccumulateProductDB = &db.Instance{
	ColName:        "accumalate_product",
	TemplateObject: &AccumulateProduct{},
}

func InitAccumulateProductModel(s *mongo.Database) {
	AccumulateProductDB.ApplyDatabase(s)

	// t := true
	// _ = AccumulateProductDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "customer_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = AccumulateProductDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "version", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
