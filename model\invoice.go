package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const INVOICE_DELAY_CREATE_DAY = 0

var InvoiceDB = &db.Instance{
	ColName:        "invoice",
	TemplateObject: &Invoice{},
}

type Invoice struct {
	ID              *primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	InvoiceDeadline *time.Time `json:"invoiceDeadline,omitempty" bson:"invoice_deadline,omitempty"` // deadline for sending invoice
	IssuedTime      *time.Time `json:"issuedTime,omitempty" bson:"issued_time,omitempty"`           // time when invoice is issued (seller send to system)
	OutboundTime    *time.Time `json:"outboundTime,omitempty" bson:"outbound_time,omitempty"`       // Time order start outbound
	DeliveredTime   *time.Time `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`     // Time order delivered to customer

	OrderID         int64  `json:"orderID" bson:"order_id,omitempty"`
	OrderCode       string `json:"orderCode" bson:"order_code,omitempty"`
	InvoiceID       int64  `json:"invoiceID" bson:"invoice_id,omitempty"`
	InvoiceCode     string `json:"invoiceCode" bson:"invoice_code,omitempty"`
	SellerID        int64  `json:"sellerID" bson:"seller_id,omitempty"`
	SellerCode      string `json:"sellerCode" bson:"seller_code,omitempty"`
	CustomerID      int64  `json:"customerID" bson:"customer_id,omitempty"`
	CustomerCode    string `json:"customerCode" bson:"customer_code,omitempty"`
	OrderSellerID   int64  `json:"orderSellerId,omitempty" bson:"order_seller_id,omitempty"`
	OrderSellerCode string `json:"orderSellerCode,omitempty" bson:"order_seller_code,omitempty"`

	CustomerTax *CustomerTaxInfo `json:"customerTax,omitempty" bson:"customer_tax,omitempty"`

	CircaIssuance *bool `json:"circaIssuance,omitempty" bson:"circa_issuance,omitempty"`
	NoNeedInvoice *bool `json:"noNeedInvoice,omitempty" bson:"no_need_invoice,omitempty"`

	InvoicePrice         int                             `json:"-" bson:"invoice_price,omitempty"` // track invoice total price at created time
	InvoiceDocumentName  *string                         `json:"invoiceDocumentName" bson:"invoice_document_name,omitempty"`
	InvoiceDocumentURL   *string                         `json:"invoiceDocumentUrl" bson:"invoice_document_url,omitempty"`
	InvoiceStatus        InvoiceStatusValue              `json:"invoiceStatus" bson:"invoice_status,omitempty"`
	IsLate               *bool                           `json:"isLate" bson:"is_late,omitempty"`
	IsConfirmed          *bool                           `json:"isConfirmed" bson:"is_confirmed,omitempty"`
	Request              *bool                           `json:"request" bson:"request,omitempty"`
	DownloadedFile       *bool                           `json:"downloadedFile" bson:"downloaded_file,omitempty"`
	SellerDownloadedFile *bool                           `json:"sellerDownloadedFile" bson:"seller_downloaded_file,omitempty"`
	SellerSentMail       *bool                           `json:"sellerSentMail,omitempty" bson:"seller_sent_mail,omitempty"`
	MailFrom             string                          `json:"mailFrom,omitempty" bson:"mail_from,omitempty"`
	Helo                 string                          `json:"helo,omitempty" bson:"helo,omitempty"`
	MailProcessingStatus enum.MailProcessingStatusValue  `json:"mailProcessingStatus,omitempty" bson:"mail_processing_status,omitempty"`
	RejectedReason       enum.RejectedReasonValue        `json:"rejectedReason,omitempty" bson:"rejected_reason,omitempty"`
	ReceiveInvoiceInfoBy *enum.ReceiveInvoiceInfoByValue `json:"receiveInvoiceInfoBy,omitempty" bson:"receive_invoice_info_by,omitempty"`
	DeliveryOrderCode    *string                         `json:"deliveryOrderCode,omitempty" bson:"delivery_order_code,omitempty"`
	DeliveryOrderStatus  enum.OrderStateValue            `json:"deliveryOrderStatus,omitempty" bson:"delivery_order_code_status,omitempty"`

	AutoChangeToOverdue *bool `json:"autoChangeToOverdue,omitempty" bson:"auto_change_to_overdue,omitempty"`

	// Reconcile Penalty Fee
	IsFined                    *bool               `json:"isFined,omitempty" bson:"is_fined,omitempty"` // track invoice is fined or not
	ReconcileItemID            *primitive.ObjectID `json:"_,omitempty" bson:"reconcile_item_id,omitempty"`
	ReconcileScheduleTimeIndex *string             `json:"reconcileScheduleTimeIndex" bson:"reconcile_schedule_time_index,omitempty"`

	// Return info
	ReturnTicketIDs []int64                   `json:"returnTicketIDs,omitempty" bson:"return_ticket_ids,omitempty"`
	ReturnStatus    *InvoiceReturnStatusValue `json:"returnStatus,omitempty" bson:"return_status,omitempty"`

	InvoiceItems          []*InvoiceItem `json:"items,omitempty" bson:"items,omitempty"`
	TotalActualPrice      *int           `json:"totalActualPrice,omitempty" bson:"-"`
	TotalCompleteQuantity *int           `json:"totalCompleteQuantity,omitempty" bson:"-"`
	NumberOfItems         *int           `json:"numberOfItems,omitempty" bson:"-"`

	InvoiceV2Code string                    `json:"invoiceV2Code,omitempty" bson:"invoice_v2_code,omitempty"`
	PartnerStatus InvoicePartnerStatusValue `json:"partnerStatus,omitempty" bson:"partner_status,omitempty"`
	Config        *InvoicePartnerConfig     `json:"config,omitempty" bson:"config,omitempty"`

	OperationAnd interface{} `json:"-" bson:"$and,omitempty"`
	OperationOr  interface{} `json:"-" bson:"$or,omitempty"`

	InvoiceMethodForVoucher   *enum.InvoiceMethodForVoucherValue `json:"invoiceMethodForVoucher,omitempty" bson:"-"`
	InvoicePriceAfterIssuance int64                              `json:"invoicePriceAfterIssuance,omitempty" bson:"-"`
	TradeDiscountAmount       int64                              `json:"tradeDiscountAmount,omitempty" bson:"-"`
	ExcludeTags               []enum.InvoiceExcludeTagValue      `json:"excludeTags,omitempty" bson:"exclude_tags,omitempty"`

	Logs []string `json:"logs,omitempty" bson:"logs,omitempty"`
}

type CustomerTaxGOV struct {
	CustomerTaxCode           string                     `json:"customerTaxCode,omitempty" bson:"customer_tax_code,omitempty"` // bắt buộc nếu là KH doanh nghiệp
	CustomerName              string                     `json:"customerName,omitempty" bson:"customer_name,omitempty"`
	CustomerAddress           string                     `json:"customerAddress,omitempty" bson:"customer_address,omitempty"`
	CustomerTaxGOVDescription string                     `json:"customerTaxGOVDescription,omitempty" bson:"customer_tax_gov_description,omitempty"` // Mô tả Trạng thái MST của CQT
	CustomerTaxGOVStatus      enum.CustomerTaxStatusType `json:"customerTaxGOVStatus,omitempty" bson:"customer_tax_gov_status,omitempty"`           // Trạng thái MST của CQT
	IsCustomerTaxCodeValid    bool                       `json:"isCustomerTaxCodeValid,omitempty" bson:"is_customer_tax_code_valid,omitempty"`      // Trạng thái MST hợp lệ

	// When there is an incident with HILO, we cannot verify the tax
	// correctly. So we need to set this field to true to bypass the tax
	// verification. And, we will process order with this flag later.
	IsIncidentTaxCode *bool `json:"isIncidentTaxCode,omitempty" bson:"is_incident_tax_code,omitempty"`
}

type CustomerTaxInfo struct {
	CustomerScope        string                     `json:"customerScope,omitempty" bson:"customer_scope,omitempty"`
	BuyerName            string                     `json:"buyerName,omitempty" bson:"buyer_name,omitempty"`
	CustomerTaxGOVStatus enum.CustomerTaxStatusType `json:"customerTaxGOVStatus,omitempty" bson:"customer_tax_gov_status,omitempty"` // Trạng thái MST của CQT
	RequestInfo          InvoiceRequest             `json:"invoiceRequestInfo,omitempty" bson:"invoice_request_info,omitempty"`
	TaxGov               CustomerTaxGOV             `json:"taxGov,omitempty" bson:"tax_gov,omitempty"`
}

type InvoicePartnerConfig struct {
	TaxOfCode           string             `json:"taxOfCode,omitempty" bson:"tax_of_code,omitempty"`
	PartnerCode         string             `json:"partnerCode,omitempty" bson:"partner_code,omitempty"`
	Pattern             string             `json:"pattern,omitempty" bson:"pattern,omitempty"`
	Series              string             `json:"series,omitempty" bson:"series,omitempty"`
	SignedBy            enum.SignedByValue `json:"signedBy,omitempty" bson:"signed_by,omitempty"`
	InvoiceTemplateCode string             `json:"invoiceTemplateCode,omitempty" bson:"invoice_template_code,omitempty"`

	SellerTax         string `json:"sellerTaxCode,omitempty" bson:"seller_tax_code,omitempty"`
	SellerEmail       string `json:"sellerEmail,omitempty" bson:"seller_email,omitempty"`
	SellerPhoneNumber string `json:"sellerPhoneNumber,omitempty" bson:"seller_phone_number,omitempty"`

	ExportBuyerNoRequest bool `json:"exportBuyerNoRequest,omitempty" bson:"export_buyer_no_request,omitempty"`
}

// InvoiceStatusValue
type InvoiceStatusValue string
type invoiceStatus struct {
	Waiting      InvoiceStatusValue
	LateDeadline InvoiceStatusValue
	Completed    InvoiceStatusValue
	Returned     InvoiceStatusValue
}

// InvoiceStatus ...
var InvoiceStatus = &invoiceStatus{
	Waiting:      "WAITING",
	LateDeadline: "LATE_DEADLINE",
	Completed:    "COMPLETED",
	Returned:     "RETURNED",
}

// ReturnStatusValue
type InvoiceReturnStatusValue string
type invoiceReturnStatusValue struct {
	NEW       InvoiceReturnStatusValue
	COMPLETED InvoiceReturnStatusValue
}

var InvoiceReturnStatusState = &invoiceReturnStatusValue{
	NEW:       "NEW",
	COMPLETED: "COMPLETED",
}

// InitInvoiceModel is func init model invoice
func InitInvoiceModel(s *mongo.Database) {
	InvoiceDB.ApplyDatabase(s)

	// t := true
	// _ = InvoiceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "invoice_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = InvoiceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "invoice_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = InvoiceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "order_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = InvoiceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "order_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = InvoiceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "invoice_status", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = InvoiceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "invoice_status", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	//TODO: deploy when prodution
	// _, _ = InvoiceDB.
	// 	GetClient().
	// 	Database(InvoiceDB.DBName).
	// 	Collection(InvoiceDB.ColName).
	// 	Indexes().DropOne(context.TODO(), "seller_code_1_order_code_1")

	// _ = InvoiceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "order_code", Value: 1},
	// 	primitive.E{Key: "delivery_order_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _, _ = InvoiceDB.
	// 	GetClient().
	// 	Database(InvoiceDB.DBName).
	// 	Collection(InvoiceDB.ColName).
	// 	Indexes().DropOne(context.TODO(), "order_seller_id_1")

	// _ = InvoiceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_seller_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _, _ = InvoiceDB.
	// 	GetClient().
	// 	Database(InvoiceDB.DBName).
	// 	Collection(InvoiceDB.ColName).
	// 	Indexes().DropOne(context.TODO(), "seller_code_1_order_id_1")

	// _ = InvoiceDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "delivery_order_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
}

type BuyerServiceInvoice struct {
	OrderID         int64                               `json:"orderId,omitempty"`
	ReconcileTime   *time.Time                          `json:"reconcileTime,omitempty"`
	Reconciliations []BuyerServiceInvoiceReconciliation `json:"reconciliations,omitempty"`
}

type BuyerServiceInvoiceReconciliation struct {
	SellerCode                 string `json:"sellerCode,omitempty"`
	ReconcileScheduleTimeIndex string `json:"reconcileScheduleTimeIndex,omitempty"`
}

type InvoicePartnerStatusValue string
type invoicePartnerStatusValue struct {
	None      InvoicePartnerStatusValue
	Synced    InvoicePartnerStatusValue
	Error     InvoicePartnerStatusValue
	Draft     InvoicePartnerStatusValue
	Issuing   InvoicePartnerStatusValue
	Completed InvoicePartnerStatusValue
}

// InvoiceStatus ...
var InvoicePartnerStatus = &invoicePartnerStatusValue{
	None:      "",
	Synced:    "SYNCED",
	Error:     "ERROR",
	Draft:     "DRAFT",
	Issuing:   "ISSUING",
	Completed: "COMPLETED",
}

type InvoiceV2Callback struct {
	Code                string                 `json:"code,omitempty"`
	SellerCode          string                 `json:"sellerCode,omitempty"`
	InvoiceTemplateCode string                 `json:"invoiceTemplateCode,omitempty"`
	DoCode              string                 `json:"doCode,omitempty"`
	OrderCode           string                 `json:"orderCode,omitempty"`
	OrderID             int64                  `json:"orderId,omitempty"`
	Status              string                 `json:"status,omitempty"`
	ResponseCallback    *ResponseCallback      `json:"responseCallback,omitempty"`
	InvoiceData         *[]InvoiceCallbackData `json:"invoiceData,omitempty"`
}

type ResponseCallback struct {
	Status    string `json:"status,omitempty"`
	Message   string `json:"message,omitempty"`
	ErrorCode string `json:"errorCode,omitempty"`
}

type InvoiceCallbackData struct {
	InvoiceNo string `json:"invoiceNo,omitempty"`
	PdfUrl    string `json:"pdfUrl,omitempty"`
}

// mail
type InvoiceEmail struct {
	Invoices []InvoiceEmailItem `json:"invoices,omitempty"`
}

type InvoiceEmailItem struct {
	OrderID         int64
	InvoiceCode     string
	DeliveredTime   *time.Time
	InvoiceDeadline *time.Time
	ReturnedTime    *time.Time
}
