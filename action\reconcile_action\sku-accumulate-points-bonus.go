package reconcile_action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func ProcessSkuAccumulatePointsBonus(
	reconciliation *model.Reconciliation,
	reconciliationItem *model.ReconciliationItem,
) {
	if reconciliation == nil ||
		reconciliationItem == nil {
		return
	}

	previousFeeRes := model.ReconciliationItemDB.QueryOne(model.ReconciliationItem{
		OrderID: reconciliationItem.OrderID,
		Sku:     reconciliationItem.Sku,
		FeeType: enum.FeeType.ACCUMULATE_POINTS_FEE,
	})
	if previousFeeRes.Status != common.APIStatus.Ok {
		return
	}

	riFilter := model.ReconciliationItem{
		ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		SellerCode:                 reconciliationItem.SellerCode,
		FeeType:                    enum.FeeType.ACCUMULATE_POINTS_BONUS,
		AutoPenaltyFee:             true,

		OrderID:       reconciliationItem.OrderID,
		OrderCode:     reconciliationItem.OrderCode,
		SaleOrderCode: reconciliationItem.SaleOrderCode,
		Sku:           reconciliationItem.Sku,
	}

	bonusDescription := fmt.Sprintf(
		"Hoàn phí hàng điểm đơn hàng #%d, sản phẩm %s đợt %s ~ %s",
		reconciliationItem.OrderID,
		reconciliationItem.Sku,
		utils.FormatTimeString(reconciliation.FromTime),
		utils.FormatTimeString(reconciliation.ToTime),
	)

	model.ReconciliationItemDB.Upsert(
		riFilter,
		bson.M{
			"bonus_description": bonusDescription,
			"bonus_bm_amount":   getSkuAccumulatePointsBonus(reconciliationItem),
		},
	)
}

func getSkuAccumulatePointsBonus(reconciliationItem *model.ReconciliationItem) int {
	if reconciliationItem == nil ||
		reconciliationItem.Price == nil ||
		reconciliationItem.ReturnQuantity == nil {
		return 0
	}

	totalRevenue := *reconciliationItem.Price * *reconciliationItem.ReturnQuantity
	return int(float64(totalRevenue) * accumulateProductPointsRate)
}
