package consumer

import (
	"fmt"
	"runtime/debug"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"go.mongodb.org/mongo-driver/bson"
)

func HandleCallbackFeeInvoiceToAccounting(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return nil
	}

	var input client.SellerInvoiceFee
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return nil
	}

	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic: %s", string(debug.Stack()))
			fmt.Printf("[%s - %s] %s\n", input.SellerCode, input.ReconcileScheduleTimeIndex, err.Error())
		}
	}()

	now := time.Now()

	client.Services.Invoice.SendSellerInvoiceFee(&client.SellerInvoiceFee{
		SellerCode:                 input.SellerCode,
		ReconcileScheduleTimeIndex: input.ReconcileScheduleTimeIndex,
		ReconcileScheduleTime:      &now,
	})

	return nil
}
