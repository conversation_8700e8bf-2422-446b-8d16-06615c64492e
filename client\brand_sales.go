package client

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetSalesType     = "/marketplace/brand-sales/v1/sales-type"
	pathGetSalesTypeList = "/marketplace/brand-sales/v1/sales-type/list"
)

type brandSalesClient struct {
	svc     *client.RestClient
	headers map[string]string
}

type salesTypeResponse struct {
	BaseAPIResponse
	Data []*SalesType `json:"data"`
}

// NewBrandSalesServiceClient ...
func NewBrandSalesServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *brandSalesClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	recClient := &brandSalesClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	recClient.svc.SetDBLog(session)
	return recClient
}

// GetSalesType is func...
func (cli *brandSalesClient) GetSalesType(code string) (*SalesType, error) {
	// if code == "" {
	// 	return nil, fmt.Errorf("code is required")
	// }
	params := map[string]string{
		"code": code,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSalesType, nil)
	if err != nil {
		return nil, err
	}

	var result *salesTypeResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}
	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

func (cli *brandSalesClient) GetSalesTypeList(offset, limit int) ([]*SalesType, *common.APIResponse) {

	var params = map[string]string{}
	if offset >= 0 {
		params["offset"] = strconv.Itoa(offset)
	}
	if limit > 0 {
		params["limit"] = strconv.Itoa(offset)
	}

	emptyKeys := []string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSalesTypeList, &emptyKeys)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST",
		}
	}

	var result *salesTypeResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result.Data, nil
}
