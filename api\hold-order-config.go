package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
)

func GetHoldOrderConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetOrderHoldConfiguration(acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}

func UpdateHoldOrderConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = model.HoldOrderConfigRequest{}

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if input.Type != enum.HoldOrder.Hold && input.Type != enum.HoldOrder.Active && input.Type != enum.HoldOrder.RequestCancel && input.Type != enum.HoldOrder.AutoCancelBankOrder && input.Type != enum.HoldOrder.AutoSendPaymentRemind {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid type",
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateOrderHoldConfiguration(acc, &input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Tài khoản của bạn không thể thực hiện thao tác này",
		ErrorCode: "ACTION_NOT_FOUND",
	})
}
