package reconcile_action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func RevertReconciliationStatus(input *model.Reconciliation, as *model.ActionSource) *common.APIResponse {
	if input.SellerCode == "" || input.ReconcileScheduleTimeIndex == "" {
		if input.ID == nil || input.ID.IsZero() {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Missing id || seller_code && time_index",
			}
		}
	}

	reconciliationResp := model.ReconciliationDB.QueryOne(
		model.Reconciliation{
			ID:                         input.ID,
			SellerCode:                 input.SellerCode,
			ReconcileScheduleTimeIndex: input.ReconcileScheduleTimeIndex,
		},
	)

	if reconciliationResp.Status == common.APIStatus.Ok {
		recData := reconciliationResp.Data.([]*model.Reconciliation)[0]
		if recData.CreatedTransferRequestCode != nil && *recData.CreatedTransferRequestCode != "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				ErrorCode: "RECONCILIATION_HAS_TRANSFER_REQUEST",
				Message:   "Cannot revert reconciliation when reconciliation has transfer request",
			}
		}
	}

	model.ReconcileLogReqDb.Create(&model.ReconcileLogReq{
		SellerCode:                 input.SellerCode,
		ReconcileScheduleTimeIndex: input.ReconcileScheduleTimeIndex,
		ReqType:                    model.ReconcileLogReqType.Reverted,
		ActionSource:               as.Account,
	})

	reconciliationF := model.Reconciliation{
		ID:                         input.ID,
		SellerCode:                 input.SellerCode,
		ReconcileScheduleTimeIndex: input.ReconcileScheduleTimeIndex,
	}
	updater := model.Reconciliation{
		ReconciliationStatus: model.ReconciliationStatus.Waiting,
	}
	result := model.ReconciliationDB.UpdateOne(reconciliationF, updater)
	return result
}
