package utils

import (
	"fmt"
	"time"
)

var (
	TimeZoneVN *time.Location = time.FixedZone("UTC+7", +7*60*60)
)

func GetTimeNow() time.Time {
	t := time.Now()
	return t.In(TimeZoneVN)
}

func GetStartAndEndTime(referenceTime time.Time) (time.Time, time.Time) {
	// Get the year and month from the reference time
	year, month, _ := referenceTime.Date()

	// Calculate the first day of the month
	startTime := time.Date(year, month, 1, 0, 0, 0, 0, referenceTime.Location())

	// Calculate the first day of the next month and subtract one second to get the last second of the current month
	endTime := startTime.AddDate(0, 1, 0).Add(-time.Second)

	return startTime, endTime
}

func MonthsBetween(dateFrom, dateTo time.Time) ([]string, error) {
	var months []string

	// Check if dateFrom is before dateTo
	if dateFrom.After(dateTo) {
		return months, fmt.Errorf("dateFrom must be before or equal to dateTo")
	}

	// Add each month to the `months` slice until it reaches dateTo
	for d := dateFrom; !d.After(dateTo); d = d.AddDate(0, 1, 0) {
		months = append(months, d.Format("2006-01"))
	}

	return months, nil
}
