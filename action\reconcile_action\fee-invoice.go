package reconcile_action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func ScheduleSendFeeInvoice(reconciliation *model.Reconciliation) {
	jobFilter := bson.M{
		"data.sellercode":                 reconciliation.SellerCode,
		"data.reconcilescheduletimeindex": reconciliation.ReconcileScheduleTimeIndex,
		"topic":                           "FEE_INVOICE",
	}

	jobRes := model.FeeInvoiceJob.GetJobDB().QueryOne(jobFilter)

	readyTime := time.Now().Add(conf.Config.FeeInvoice)

	if jobRes.Status == common.APIStatus.Ok {
		model.FeeInvoiceJob.GetJobDB().Upsert(jobFilter, job.JobItem{
			ReadyTime: &readyTime,
		})
		return
	}

	if jobRes.Status == common.APIStatus.NotFound {
		model.FeeInvoiceJob.Push(&client.SellerInvoiceFee{
			SellerCode:                 reconciliation.SellerCode,
			ReconcileScheduleTimeIndex: reconciliation.ReconcileScheduleTimeIndex,
		}, &job.JobItemMetadata{
			Keys: []string{
				"FEE_INVOICE",
			},
			Topic:     "FEE_INVOICE",
			ReadyTime: &readyTime,
		})
	}
}
