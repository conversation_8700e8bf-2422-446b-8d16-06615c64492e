package generic

import (
	"sync"
	"time"
)

type cacheItem[V any] struct {
	value        V
	lastAccessed int64
}

// LCache ...
type LCache[K comparable, V any] struct {
	m          map[K]*cacheItem[V]
	l          sync.RWMutex
	ttl        int64
	maxItem    int
	refreshTTL bool
}

// NewLCache ...
// maxItem: maximum item
// ttl: time to live (second)
func NewLCache[K comparable, V any](maxItem int, ttl int) (m *LCache[K, V]) {
	m = NewLCacheRefreshMode[K, V](maxItem, ttl, true)
	return
}

// NewLCacheRefreshMode ...
// maxItem: maximum item
// ttl: time to live (second)
// refreshTTL : if refreshTTL = true, when someone access item, ttl of item will be refresh
func NewLCacheRefreshMode[K comparable, V any](maxItem int, ttl int, refreshTTL bool) (m *LCache[K, V]) {
	m = &LCache[K, V]{
		m:          make(map[K]*cacheItem[V], maxItem),
		maxItem:    maxItem,
		refreshTTL: refreshTTL,
		ttl:        int64(ttl),
	}

	go func() {
		ticker := time.NewTicker(10 * time.Second)
		for range ticker.C {
			m.l.Lock()
			for k, v := range m.m {
				if timeElapsed := time.Now().Unix() - v.lastAccessed; timeElapsed > int64(m.ttl) {
					delete(m.m, k)
				}
			}
			m.l.Unlock()
		}
	}()
	return
}

// Len ...
func (c *LCache[K, V]) Len() int {
	return len(c.m)
}

// Put ...
func (c *LCache[K, V]) Put(k K, v V) {
	c.l.Lock()
	if c.Len() >= c.maxItem {
		// Remove the first item in the map
		for k := range c.m {
			delete(c.m, k)
			break
		}
	}
	c.m[k] = &cacheItem[V]{value: v, lastAccessed: time.Now().Unix()}
	c.l.Unlock()
}

// Get ...
func (c *LCache[K, V]) Get(k K) (v V, ok bool) {
	c.l.RLock()
	item, found := c.m[k]
	c.l.RUnlock()

	if !found {
		return v, false
	}
	if timeElapsed := time.Now().Unix() - item.lastAccessed; timeElapsed > int64(c.ttl) {
		// Item is expired, delete it
		c.l.Lock()
		delete(c.m, k)
		c.l.Unlock()
		return v, false
	}
	if c.refreshTTL {
		c.l.Lock()
		item.lastAccessed = time.Now().Unix()
		c.l.Unlock()
	}
	return item.value, true
}

// Remove ...
func (c *LCache[K, V]) Remove(k K) {
	c.l.Lock()
	delete(c.m, k)
	c.l.Unlock()
}

// Cleanup Remove all data
func (c *LCache[K, V]) Cleanup() {
	c.l.Lock()
	c.m = make(map[K]*cacheItem[V], c.maxItem)
	c.l.Unlock()
}
