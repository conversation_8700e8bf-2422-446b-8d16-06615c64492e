package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CustomerWarehouseInvoice struct {
	Address  string `json:"address"`
	Code     int    `json:"code"`
	Name     string `json:"name"`
	Phone    string `json:"phone"`
	Province string `json:"province"`
}
type CustomerWarehouseDelivery struct {
	Address      string `json:"address"`
	BusinessName string `json:"businessName"`
	Code         int    `json:"code"`
	District     string `json:"district"`
	Name         string `json:"name"`
	Phone        string `json:"phone"`
	Province     string `json:"province"`
	Ward         string `json:"ward"`
}
type CustomerWarehouse struct {
	Address  string                     `json:"address"`
	Code     int                        `json:"code"`
	Delivery *CustomerWarehouseDelivery `json:"delivery"`
	Invoice  *CustomerWarehouseInvoice  `json:"invoice"`
	Name     string                     `json:"name"`
	Phone    string                     `json:"phone"`
	Province string                     `json:"province"`
}

type OrderLineWarehouse struct {
	AdminID           int       `json:"adminId"`
	AdminProductID    int64     `json:"adminProductId"`
	CreatedTime       time.Time `json:"createdTime"`
	DeliveredQuantity int       `json:"deliveredQuantity"`
	ErpProductID      int       `json:"erpProductId"`
	IsRedTag          bool      `json:"isRedTag"`
	LineID            int       `json:"lineId"`
	Location          string    `json:"location"`
	Name              string    `json:"name"`
	OutboundQuantity  int       `json:"outboundQuantity"`
	Packaging         string    `json:"packaging"`
	ProductID         int       `json:"productId"`
	ProductName       string    `json:"productName"`
	ProductType       string    `json:"productType"`
	Quantity          int       `json:"quantity"`
	RateTax           int       `json:"rateTax"`
	ReservedQuantity  int       `json:"reservedQuantity"`
	ReturnedQuantity  int       `json:"returnedQuantity"`
	SaleOrderCode     string    `json:"saleOrderCode"`
	Status            string    `json:"status"`
	UnitPrice         float64   `json:"unitPrice"`
	VatAmount         int       `json:"vatAmount"`
	VersionNo         string    `json:"versionNo"`
	SellerCode        string    `json:"sellerCode"`
	Sku               string    `json:"sku"`
}

type OrderWarehouse struct {
	AdminID               int                   `json:"adminId"`
	CodAmount             float64               `json:"codAmount"`
	DeliveryAmount        float64               `json:"deliveryAmount"`
	CollectOnDelivery     bool                  `json:"collectOnDelivery"`
	CreatedTime           time.Time             `json:"createdTime"`
	LastUpdatedTime       time.Time             `json:"lastUpdatedTime"`
	Customer              *CustomerWarehouse    `json:"customer"`
	OrderLines            []*OrderLineWarehouse `json:"orderLines"`
	OrderPrice            int                   `json:"orderPrice"`
	PaymentMethod         string                `json:"paymentMethod"`
	PaymentMethodName     string                `json:"paymentMethodName"`
	Priority              int                   `json:"priority"`
	SaleOrderCode         string                `json:"saleOrderCode"`
	SaleOrderTime         int                   `json:"saleOrderTime"`
	Status                string                `json:"status"`
	TotalAmount           int                   `json:"totalAmount"`
	TotalAmountWithoutVat int                   `json:"totalAmountWithoutVat"`
	TplCode               string                `json:"tplCode"`
	TrackingCode          string                `json:"trackingCode"`
	VatAmount             int                   `json:"vatAmount"`
	VersionNo             string                `json:"versionNo"`
	WarehouseCode         string                `json:"warehouseCode"`

	DiscountAmount      *DiscountAmountWarehouse `json:"discountAmount"`
	Fee                 *FeeWarehouse            `json:"fee"`
	ReservedTotalAmount interface{}              `json:"reservedTotalAmount"`
}

type DeliveryOrder struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	AdminId           int64  `json:"adminId,omitempty" bson:"admin_id,omitempty"`
	SaleOrderCode     string `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	DeliveryOrderCode string `json:"deliveryOrderCode,omitempty" bson:"delivery_order_code,omitempty"`
	WarehouseCode     string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	DeliveredTime *time.Time                `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`
	OutboundTime  *time.Time                `json:"outboundTime,omitempty" bson:"outbound_time,omitempty"`
	Status        *enum.SaleOrderStateValue `json:"status,omitempty" bson:"status,omitempty"`
}

type DeliveryOrderReq struct {
	SaleOrderCode     string     `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	FromDeliveredTime *time.Time `json:"fromDeliveredTime,omitempty" bson:"from_delivered_time,omitempty"`
	ToDeliveredTime   *time.Time `json:"toDeliveredTime,omitempty" bson:"to_delivered_time,omitempty"`
}

type OrderWarehouseResponse struct {
	Status    string            `json:"status"`
	ErrorCode string            `json:"errorCode"`
	Message   string            `json:"message"`
	Data      []*OrderWarehouse `json:"data"`
}

type DiscountAmountWarehouse struct {
	PaymentMethod *float64 `json:"paymentMethod"`
	VoucherAmount *float64 `json:"voucherAmount"`
}

type FeeWarehouse struct {
	DeliveryFee *float64 `json:"deliveryFee"`
	ExtraFee    *float64 `json:"extraFee"`
}

type OrderBaseResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}

type WarehouseResponse struct {
	Status    string       `json:"status"`
	ErrorCode string       `json:"errorCode"`
	Message   string       `json:"message"`
	Data      []*Warehouse `json:"data"`
}

type Warehouse struct {
	Code  string    `json:"code"`
	Name  string    `json:"name"`
	Areas *[]string `json:"areas"`
}

type DeliveryOrderResponse struct {
	Status    string           `json:"status"`
	ErrorCode string           `json:"errorCode"`
	Message   string           `json:"message"`
	Data      []*DeliveryOrder `json:"data"`
}
