package client

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"
)

type sendGridClient struct {
	sendmail *client.RestClient
	headers  map[string]string
}

func NewSendGridClient(database *mongo.Database) *sendGridClient {
	sendGridClient := sendGridClient{
		sendmail: client.NewRESTClient(
			"https://api.sendgrid.com/v3/mail/send",
			"sendgrid",
			3*time.Second, 1, 500*time.Millisecond,
		),
		headers: map[string]string{
			"Content-Type":  "application/json; charset=utf-8",
			"Authorization": "Bearer *********************************************************************",
		},
	}

	if database != nil {
		sendGridClient.sendmail.SetDBLog(database)
	}
	sendGridClient.sendmail.AcceptHTTPError(true)

	return &sendGridClient
}

func (c *sendGridClient) SendMailFromSellerCenter(
	to []model.Email,
	subject string,
	content []model.Content,
	keys *[]string,
) *common.APIResponse {
	req := model.ReqSendMail{
		Personalizations: []model.Personalization{
			{
				To:      to,
				Subject: subject,
				Bcc:     []model.Email{{Email: conf.Config.BuymedCsMail}},
			},
		},
		From: conf.Config.FromSellerCenterEmail,
		ReplyTo: model.Email{
			Email: "<EMAIL>",
		},
		Content: content,
	}

	return c.SendMail(req, keys)
}

func (c *sendGridClient) SendMailHilo(
	to []model.Email,
	subject string,
	content []model.Content,
	keys *[]string,
) *common.APIResponse {
	bcc := []model.Email{
		{Email: conf.Config.BuymedCsMail},
	}

BccLoop:
	for _, bccMail := range conf.Config.HiloBccNotify {
		// check to
		for _, toEmail := range to {
			if toEmail.Email == bccMail.Email {
				continue BccLoop
			}
		}

		if bccMail.Email == conf.Config.BuymedCsMail {
			continue BccLoop
		}

		bcc = append(bcc, bccMail)
	}

	req := model.ReqSendMail{
		Personalizations: []model.Personalization{
			{
				To:      to,
				Subject: subject,
				Bcc:     bcc,
			},
		},
		From: conf.Config.FromSellerCenterEmail,
		ReplyTo: model.Email{
			Email: "<EMAIL>",
		},
		Content: content,
	}

	return c.SendMail(req, keys)
}

func (c *sendGridClient) SendMail(req model.ReqSendMail, keys *[]string) *common.APIResponse {
	result, err := c.sendmail.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		c.headers,
		nil,
		req,
		"",
		keys,
	)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_MAIL_REQUEST_ERROR",
		}
	}

	switch result.Code {
	case 200, 201, 202:

		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Send invitation successfully",
		}
	case 400:
		var response model.ResSendMail
		_ = json.Unmarshal(result.Content, &response)

		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      response.Errors,
			Message:   "Send invitation failed",
			ErrorCode: common.APIStatus.Invalid,
		}
	case 401:
		var response model.ResSendMail
		_ = json.Unmarshal(result.Content, &response)

		return &common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Data:      response.Errors,
			Message:   "PERMISSION_DENIED",
			ErrorCode: common.APIStatus.Unauthorized,
		}
	case 403:
		var response model.ResSendMail
		_ = json.Unmarshal(result.Content, &response)

		return &common.APIResponse{
			Status:    common.APIStatus.Forbidden,
			Data:      response.Errors,
			Message:   "PERMISSION_DENIED",
			ErrorCode: common.APIStatus.Forbidden,
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Error,
		Data: []string{
			string(result.Content),
		},
		ErrorCode: "UNHANDLED_ERROR",
	}
}
