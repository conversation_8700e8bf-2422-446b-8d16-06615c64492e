package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CalOrderDiscountHistory struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	OrderID                int64                  `json:"orderId,omitempty" bson:"order_id,omitempty"`
	SaleOrderCode          string                 `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	DeliveryOrderCode      string                 `json:"deliveryOrderCode,omitempty" bson:"delivery_order_code,omitempty"`
	PaymentAmount          int64                  `json:"paymentAmount,omitempty" bson:"payment_amount,omitempty"`
	VoucherAmount          int64                  `json:"voucherAmount,omitempty" bson:"voucher_amount,omitempty"`
	VoucherAmountDetailMap map[string]int64       `json:"voucherAmountDetailMap,omitempty" bson:"voucher_amount_detail_map,omitempty"`
	VoucherAmountDetails   []*VoucherAmountDetail `json:"voucherAmountDetails,omitempty" bson:"voucher_amount_details,omitempty"`
}

type VoucherAmountDetail struct {
	Code           string   `json:"code,omitempty" bson:"code,omitempty"`
	SellerCodes    []string `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	DiscountAmount int64    `json:"discountAmount,omitempty" bson:"discount_amount,omitempty"`
	StoreCode      string   `json:"storeCode,omitempty" bson:"store_code,omitempty"`
}

// CalOrderDiscountHistoryDB ...
var CalOrderDiscountHistoryDB = &db.Instance{
	ColName:        "cal_order_discount_history",
	TemplateObject: &CalOrderDiscountHistory{},
}

// InitCalOrderDiscountHistoryModel ...
func InitCalOrderDiscountHistoryModel(s *mongo.Database) {
	CalOrderDiscountHistoryDB.ApplyDatabase(s)
}
