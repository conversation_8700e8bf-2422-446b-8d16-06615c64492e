package tool

import (
	"fmt"
	"runtime/debug"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func FillOrderProductFeeConsumer(item *job.JobItem) error {
	if item.FailCount > 50 {
		return nil
	}
	data, err := bson.Marshal(item.Data)
	if err != nil {
		fmt.Println("[ERROR] - fillOrderProductFeeConsumer", item.FailCount, err)
		return nil
	}
	var input model.Order
	err = bson.Unmarshal(data, &input)
	if err != nil {
		fmt.Println("[ERROR] - fillOrderProductFeeConsumer", item.FailCount, err)
		return nil
	}

	if input.OrderID == 0 {
		return nil
	}

	FillOrderProductFee(input.OrderID)
	return nil
}

func FillOrderProductFee(orderID int64) {

	// recover from panic
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("[ERROR] - fillOrderProductFee", orderID, r, string(debug.Stack()))
		}
	}()

	orderQuery := model.OrderDB.QueryOne(&model.Order{
		OrderID: orderID,
	})
	if orderQuery.Status != common.APIStatus.Ok {
		fmt.Println("[ERROR] - fillOrderProductFee", orderID, orderQuery.Status, orderQuery.Message)
		return
	}
	order := orderQuery.Data.([]*model.Order)[0]

	// Fill order items
	orderItemPartitionDB := model.GetOrderItemPartitionDB(order, "fillOrderProductFee")
	orderItemRes := orderItemPartitionDB.Query(&model.OrderItem{OrderID: order.OrderID}, 0, 0, nil)
	if orderItemRes.Status != common.APIStatus.Ok {
		return
	}
	order.Items = orderItemRes.Data.([]*model.OrderItem)

	// Fill product fee
	handleProductFee(order)

	// Update order items
	for _, item := range order.Items {
		if item.ProductFees == nil || len(item.ProductFees) == 0 {
			continue
		}
		resp := orderItemPartitionDB.UpdateOne(model.OrderItem{
			OrderID: order.OrderID,
			Sku:     item.Sku,
			Type:    item.Type,
		}, model.OrderItem{
			ProductFees: item.ProductFees,
		})

		if resp.Status != common.APIStatus.Ok {
			fmt.Println("[ERROR] - fillOrderProductFee", order.OrderID, item.ProductID, resp.Message)
		}
	}

	t := true
	// Update order
	model.OrderDB.UpdateOne(&model.Order{
		OrderID: order.OrderID,
	}, model.Order{
		FillProductFee: &t,
	})
}

// handleProductFee is a function that handles the product fee for an order.
func handleProductFee(order *model.Order) {

	productIDs := make([]int64, 0)
	for _, item := range order.Items {

		// Skip fee for items that are not external
		if item.SellerClass == nil || *item.SellerClass != enum.SellerClass.EXTERNAL {
			continue
		}

		if item.SubItems != nil && len(*item.SubItems) > 0 {
			// Nếu là combo
			for _, subItem := range *item.SubItems {
				if subItem.Type == enum.ItemType.GIFT {
					continue
				}
				productIDs = append(productIDs, subItem.ProductID)
			}
		} else {

			if item.Type == enum.ItemType.GIFT {
				continue
			}
			productIDs = append(productIDs, item.ProductID)
		}
	}

	// Get all ProductFeeConfig
	uniqueProductFee := utils.UniqueIntSlice(productIDs)
	productFeeConfigs := make(map[int64]*client.ProductFeeConfig, 0)
	regionCode := order.ProvinceCode

	// split productIDs into chunks of 100
	chunkSize := 100
	for i := 0; i < len(uniqueProductFee); i += chunkSize {
		end := i + chunkSize
		if end > len(uniqueProductFee) {
			end = len(uniqueProductFee)
		}
		chunk := uniqueProductFee[i:end]
		productFeeResp := client.Services.Product.GetProductFee(chunk, regionCode, order.CreatedTime, []string{order.OrderCode, fmt.Sprintf("%d", order.OrderID)})
		if productFeeResp.Status != common.APIStatus.Ok {
			continue
		}
		dataMap := productFeeResp.Data[0]
		for _, item := range dataMap.FeeMap {
			if item.ProductID == 0 {
				continue
			}
			productFeeConfigs[item.ProductID] = item
		}
	}

	// Set product fee for each item in the order
	for _, item := range order.Items {
		if item.SellerClass == nil || *item.SellerClass != enum.SellerClass.EXTERNAL {
			continue
		}

		if item.SubItems != nil && len(*item.SubItems) > 0 {

			// Nếu là combo
			item.ProductFees = []*model.ProductFee{}
			for _, subItem := range *item.SubItems {

				if subItem.Type == enum.ItemType.GIFT {
					continue
				}

				if feeConfig, ok := productFeeConfigs[subItem.ProductID]; ok {
					fee := &model.ProductFee{
						ProductID: subItem.ProductID,
					}

					if feeConfig.FulfillmentFeePercent != nil {
						fee.FulfillmentFeePercent = *feeConfig.FulfillmentFeePercent
					}
					if feeConfig.SaleFeePercent != nil {
						fee.SaleFeePercent = *feeConfig.SaleFeePercent
					}
					item.ProductFees = append(item.ProductFees, fee)
				} else {
					fmt.Println("ProductID not found in productFeeConfigs: ", subItem.ProductID, order.OrderID)
				}
			}
		} else {
			if item.Type == enum.ItemType.GIFT {
				continue
			}
			if feeConfig, ok := productFeeConfigs[item.ProductID]; ok {
				fee := &model.ProductFee{
					ProductID: item.ProductID,
				}
				if feeConfig.FulfillmentFeePercent != nil {
					fee.FulfillmentFeePercent = *feeConfig.FulfillmentFeePercent
				}
				if feeConfig.SaleFeePercent != nil {
					fee.SaleFeePercent = *feeConfig.SaleFeePercent
				}

				item.ProductFees = []*model.ProductFee{fee}
			} else {
				fmt.Println("ProductID not found in productFeeConfigs: ", item.ProductID, order.OrderID)
			}
		}
	}

}
