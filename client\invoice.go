package client

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
)

const (
	pathCreateInvoiceDraft         = "/accounting/invoice/v1/order-invoice/draft"
	pathExportInvoice              = "/accounting/invoice/v1/order-invoice/export"
	pathReplaceInvoice             = "/accounting/invoice/v1/order-invoice/replace"
	pathUpdateInvoice              = "/accounting/invoice/v1/order-invoice"
	pathVerifyUpdateInvoice        = "/accounting/invoice/v1/order-invoice/verify"
	pathSyncOrderInvoiceItem       = "/accounting/invoice/v1/callback/seller/order-invoice-item"
	pathSyncOrderInvoiceItemHILO   = "/accounting/invoice/v1/callback/seller/order-invoice/export"
	pathCalcTransferingDifference  = "/accounting/core/v1/callback/order/calc-transferring-difference"
	pathSendInvoiceReconciledOrder = "/accounting/invoice/v1/callback/seller/buyer-service-order"
	pathSendInvoiceReconciledFee   = "/accounting/invoice/v1/callback/seller/fee-invoice"
	pathCompleteOrder              = "/accounting/invoice/v1/webhook/order/completed"
	pathSellerSendExportedInvoice  = "/accounting/invoice/v1/order-invoice/from-seller"

	pathGetTaxInfo = "/accounting/invoice/v1/tax-code"
)

type invoiceClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewInvoiceServiceClient ...
func NewInvoiceServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *invoiceClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	invoiceClient := &invoiceClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	invoiceClient.svc.SetDBLog(session)
	return invoiceClient
}

// CreateInvoiceDraft is func create new Invoice
func (cli *invoiceClient) CreateInvoiceDraft(in *InvoiceRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathCreateInvoiceDraft, &[]string{fmt.Sprint(in.OrderID), "CreateInvoiceDraft"})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_INVOICE_DRAFT",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CREATE_INVOICE_DRAFT",
		}
	}

	return result
}

// ExportInvoice is func export Invoice
func (cli *invoiceClient) ExportInvoice(in *InvoiceRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathExportInvoice, &[]string{fmt.Sprint(in.OrderID), "ExportInvoice"})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_INVOICE_DRAFT",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CREATE_INVOICE_DRAFT",
		}
	}

	return result
}

// ReplaceInvoice is func replace Invoice
func (cli *invoiceClient) ReplaceInvoice(in *InvoiceRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathReplaceInvoice, &[]string{fmt.Sprint(in.OrderID), "ReplaceInvoice"})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CREATE_INVOICE_DRAFT",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CREATE_INVOICE_DRAFT",
		}
	}

	return result
}

// UpdateInvoice is func update Invoice
func (cli *invoiceClient) UpdateInvoice(in *UpdateInvoiceRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, in, pathUpdateInvoice, &[]string{fmt.Sprint(in.OrderID), "UpdateInvoice"})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UPDATE_INVOICE",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UPDATE_INVOICE",
		}
	}

	return result
}

// VerifyUpdateInvoice is func verify update Invoice
func (cli *invoiceClient) VerifyUpdateInvoice(in *UpdateInvoiceRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, in, pathVerifyUpdateInvoice, &[]string{fmt.Sprint(in.OrderID), "VerifyUpdateInvoice"})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_VERIFY_UPDATE_INVOICE",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_VERIFY_UPDATE_INVOICE",
		}
	}

	return result
}

// UpdateInvoice is func update Invoice
func (cli *invoiceClient) SyncOrderInvoiceItem(in model.Invoice) *common.APIResponse {
	// không xài hàm này nữa
	params := map[string]string{}

	var res *client.RestResult
	var err error
	keys := []string{fmt.Sprint(in.OrderID), fmt.Sprint(in.InvoiceID), "SyncOrderInvoiceItem"}
	if in.Config != nil {
		res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathSyncOrderInvoiceItemHILO, &keys)
	} else {
		res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathSyncOrderInvoiceItem, &keys)
	}

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UPDATE_INVOICE",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UPDATE_INVOICE",
		}
	}

	return result
}

func (cli *invoiceClient) SyncSellerExportedOrderInvoiceItem(in model.Invoice) *common.APIResponse {
	params := map[string]string{}

	var res *client.RestResult
	var err error
	keys := []string{fmt.Sprint(in.OrderID), fmt.Sprint(in.InvoiceID), "SyncSellerExportedOrderInvoiceItem", in.SellerCode}

	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathSyncOrderInvoiceItem, &keys)

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UPDATE_INVOICE",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UPDATE_INVOICE",
		}
	}

	return result
}

// Không xài chỉ xài api seller tự xuất
func (cli *invoiceClient) SyncSellerSupportExportOrderInvoiceHILO(in model.Invoice) *common.APIResponse {

	params := map[string]string{}

	var res *client.RestResult
	var err error
	keys := []string{fmt.Sprint(in.OrderID), fmt.Sprint(in.InvoiceID), "SyncSellerSupportExportOrderInvoiceHILO", in.SellerCode}
	res, err = cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathSyncOrderInvoiceItemHILO, &keys)

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UPDATE_INVOICE",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UPDATE_INVOICE",
		}
	}

	return result
}

// For reconciled order (each order-seller is reconciled), trigger send invoice to buyer
func (cli *invoiceClient) SendInvoiceForReconciledOrder(in *model.BuyerServiceInvoice) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, in, pathSendInvoiceReconciledOrder, &[]string{fmt.Sprint(in.OrderID), "SendInvoiceForReconciledOrder"})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CALLBACK_SEND_INVOICE",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CALLBACK_SEND_INVOICE",
		}
	}

	return result
}

func (cli *invoiceClient) SendSellerInvoiceFee(data *SellerInvoiceFee) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post, cli.headers, params, data, pathSendInvoiceReconciledFee,
		&[]string{
			data.SellerCode,
			"SendSellerInvoiceFee",
		},
	)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CALLBACK_FEE_INVOICE",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CALLBACK_FEE_INVOICE",
		}
	}

	return result
}

func (cli *invoiceClient) CalcTransferingDifference(body model.CalcTransferingDifferenceRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, body, pathCalcTransferingDifference, &[]string{fmt.Sprint(body.OrderID), "CalcTransferingDifference"})
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_TRANSFERING_DIFFERENCE",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_TRANSFERING_DIFFERENCE",
		}
	}
	return result
}

func (cli *invoiceClient) CompleteOrder(order *model.Order) *common.APIResponse {
	keys := []string{
		fmt.Sprintf("%v", order.OrderID),
		order.SaleOrderCode,
	}
	params := map[string]string{
		"saleOrderCode": order.SaleOrderCode,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, nil, pathCompleteOrder, &keys)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_COMPLETE_SALE_ORDER",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_COMPLETE_SALE_ORDER",
		}
	}
	return result
}

func (cli *invoiceClient) GetTaxInfo(taxCode string) *model.CustomerTaxGOV {
	if taxCode == "" {
		return nil
	}

	body := map[string]string{
		"taxCode": taxCode,
	}

	keys := []string{
		taxCode,
		"GetTaxInfo",
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathGetTaxInfo, &keys)
	if err != nil {
		return nil
	}

	var result *TaxGOVResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil || result == nil {
		return nil
	}

	if len(result.Data) == 0 {
		return nil
	}

	return result.Data[0]
}

func (cli *invoiceClient) SendSellerExportedInvoice(req model.SellerSendInvoice) *common.APIResponse {
	keys := []string{
		"SendSellerExportedInvoice",
		fmt.Sprintf("%v", req.OrderID),
		req.SellerCode,
		req.InvoiceNo,
		req.Filename,
		req.DocUrl,
	}
	restResp, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, req, pathSellerSendExportedInvoice, &keys)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	var response *common.APIResponse
	err = json.Unmarshal([]byte(restResp.Body), &response)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_RESPONSE",
		}
	}
	return response
}
