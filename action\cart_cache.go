package action

import (
	"encoding/json"
	"reflect"
	"sort"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func GetCartCacheWithVoucher(acc *model.Account, getVoucherAuto bool, redeemCodeRemovedArr []string, autoRemoveVoucherAutoInvalid bool, screen string) *common.APIResponse {
	defer func() {
		go sdk.Execute(func() {
			SyncCartCache(acc, getVoucherAuto, redeemCodeRemovedArr, autoRemoveVoucherAutoInvalid, screen)
		})
	}()

	// Query cart
	cartRes := model.CartCacheDB.QueryOne(&model.CartCache{
		AccountID: acc.AccountID,
	})
	if cartRes.Status != common.APIStatus.Ok {
		qCart := model.CartDB.QueryOne(&model.Cart{AccountID: acc.AccountID})

		if qCart.Status == common.APIStatus.Ok {
			res := GetCartInfo(acc, getVoucherAuto, redeemCodeRemovedArr, autoRemoveVoucherAutoInvalid, screen)
			return res
		}
		return cartRes
	}
	cart := cartRes.Data.([]*model.CartCache)[0]
	cart.GetVoucherAutoApply = getVoucherAuto
	cart.RedeemCodeRemovedArr = cart.RedeemCodeRemoved
	cart.AutoRemoveVoucherAutoInvalid = true
	cart.Screen = screen
	cart.SourceDetail = acc.SourceDetail

	// Query cartItem
	cartItemRes := model.CartItemCacheDB.Query(&model.CartItemCache{CartID: cart.CartID}, 0, 0, nil)
	if cartItemRes.Status != common.APIStatus.Ok {
		go model.CartCacheDB.Delete(bson.M{"account_id": acc.AccountID})
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Not found any matched cart",
		}
	}
	cart.Items = cartItemRes.Data.([]*model.CartItemCache)

	// Calculate cart price
	calcCartPrice(cart)

	// Apply voucher code
	_ = cartCacheHandleApplyVoucherCode(cart, "GetCartInfo")

	_ = cartCacheHandlePaymentCreditFee(cart)

	//This loop make sure item always have LastUpdatedTime
	for _, item := range cart.Items {
		if item.LastAdded == nil {
			item.LastAdded = item.CreatedTime
		}
	}

	// sort liteCart.CartItemGroups by LastAdded the newest to the oldest
	sort.Slice(cart.Items, func(i, j int) bool {
		if cart.Items[i].LastAdded == nil && cart.Items[j].LastAdded == nil {
			return false
		}

		// If one LastAdded is nil, move it to the end
		if cart.Items[i].LastAdded == nil && cart.Items[j].LastAdded != nil {
			return false // i should be after j
		}
		if cart.Items[i].LastAdded != nil && cart.Items[j].LastAdded == nil {
			return true // j should be after i
		}

		return (*cart.Items[i].LastAdded).After(*cart.Items[j].LastAdded)
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    []*model.CartCache{cart},
		Message: "Get cart success",
	}
}

func calcCartPrice(cart *model.CartCache) {
	price, totalPrice, totalQuantity, totalItem, totalQuantitySelected, totalItemSelected, totalPriceAllItem, subPrice := 0, 0, 0, 0, 0, 0, 0, 0

	for _, item := range cart.Items {
		//item.Price = int(utils.Ceil(int64(item.Price)))
		if item.Type == enum.ItemType.GIFT {
			continue
		}
		if item.IsSelected != nil && *item.IsSelected {
			totalQuantitySelected += item.Quantity
			totalItemSelected++
			price += item.Price * item.Quantity
			totalPrice += item.TotalPrice
		}
		totalQuantity += item.Quantity
		totalItem++
		totalPriceAllItem += item.TotalPrice
	}
	subPrice = price
	totalPrice = price
	if cart.PaymentMethodFee != 0 {
		totalPrice += int(cart.PaymentMethodFee)
	}
	if cart.DeliveryMethodFee != 0 {
		totalPrice += int(cart.DeliveryMethodFee)
	}
	cart.Price = price
	cart.TotalPrice = totalPrice
	cart.TotalQuantity = &totalQuantity
	cart.TotalItem = &totalItem
	cart.TotalQuantitySelected = &totalQuantitySelected
	cart.TotalItemSelected = &totalItemSelected
	cart.TotalPriceAllItem = totalPriceAllItem
	cart.SubPrice = subPrice
	cart.Discount = 0
	cart.TotalPrice = cart.TotalPrice + int(cart.ExtraFee)
}

func cartCacheHandleApplyVoucherCode(cart *model.CartCache, source string) (errRes *common.APIResponse) {
	cart.SubPrice = cart.Price
	tempCartItems := make([]*model.CartItemCache, 0)
	for _, item := range cart.Items {
		if item.IsSelected != nil && *item.IsSelected {
			tempCartItems = append(tempCartItems, item)
		}
	}
	tempCart := *cart
	tempCart.TotalPrice = cart.TotalPrice
	tempCart.TotalQuantity = cart.TotalQuantitySelected
	tempCart.TotalItem = cart.TotalItemSelected
	tempCart.Items = tempCartItems

	emptyArr := make([]*string, 0)
	if cart.RedeemCode == nil {
		cart.RedeemCode = &emptyArr
	}
	if cart.Screen == "Payment" {
		cart.RedeemCodeRemovedArr = []string{}
	}
	redeemApplyResult := make([]*model.PromoApplyResult, 0)
	promoApply, errCheck := client.Services.Promotion.CheckVoucherCode(&client.CheckVoucherRequest{
		Cart:                    &tempCart,
		VoucherCode:             *cart.RedeemCode,
		AccountID:               cart.AccountID,
		GetVoucherAutoApply:     cart.GetVoucherAutoApply,
		GetVoucherAutoByPayment: cart.Screen == "Payment" || (cart.PaymentMethod != "" && cart.GetVoucherAutoApply),
		SystemDisplay:           cart.SystemDisplay,
		RedeemCodeRemovedArr:    cart.RedeemCodeRemovedArr,

		Source:                     source,
		SourceDetail:               cart.SourceDetail,
		SkipVoucherByPaymentMethod: cart.Screen != "Payment",
	})
	if errCheck != nil {
		errRes = &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   errCheck.Error(),
			ErrorCode: "VOUCHER_CODE_INVALID",
		}
		makeCartCacheError(errRes, cart)

		for _, redeem := range *cart.RedeemCode {
			if redeem != nil {
				redeemApplyResult = append(redeemApplyResult, &model.PromoApplyResult{
					Code:    *redeem,
					CanUse:  false,
					Message: "Mã khuyến mãi không hợp lệ",
				})
			}
		}
	}
	redeemApplyResult = make([]*model.PromoApplyResult, 0)
	redeemCodeMap := make([]*model.RedeemApply, 0)
	curCodes := emptyArr
	mapItemDiscount := make(map[string]*model.DiscountDetail)
	for _, redeem := range promoApply {
		message := ""
		redeem.DiscountValue = int(utils.Ceil(int64(redeem.DiscountValue)))
		if cart.AutoRemoveVoucherAutoInvalid && redeem.AutoApply && !redeem.CanUse {
			continue
		}
		curCodes = append(curCodes, &redeem.VoucherCode)
		if redeem.CanUse {
			if cart.Screen == "Payment" && redeem.PaymentMethod != "" && cart.PaymentMethod == "" {
				cart.PaymentMethod = redeem.PaymentMethod
			}
			cart.Discount = cart.Discount + int(utils.Ceil(int64(redeem.DiscountValue)))
			if redeem.DiscountInfos != nil {
				for _, discountInfo := range redeem.DiscountInfos {
					if data, ok := mapItemDiscount[discountInfo.Sku]; ok {
						msg := data.Message
						if discountInfo.Message != "" {
							msg = discountInfo.Message
						}
						if data.Message != "" && discountInfo.Message != "" {
							msg = "Mã giảm giá không áp dụng cho sản phẩm này"
						}
						mapItemDiscount[discountInfo.Sku] = &model.DiscountDetail{
							TotalDiscount: data.TotalDiscount + discountInfo.Discount,
							Message:       msg,
							VoucherDetails: append(data.VoucherDetails, &model.VoucherDetail{
								VoucherCode:   redeem.VoucherCode,
								DiscountValue: discountInfo.Discount,
								IsApply:       discountInfo.IsApply,
								SellerCodes:   discountInfo.SellerCodes,
								StoreCode:     redeem.StoreCode,
							}),
						}
					} else {
						mapItemDiscount[discountInfo.Sku] = &model.DiscountDetail{
							TotalDiscount: discountInfo.Discount,
							Message:       discountInfo.Message,
							VoucherDetails: []*model.VoucherDetail{
								{
									VoucherCode:   redeem.VoucherCode,
									DiscountValue: discountInfo.Discount,
									IsApply:       discountInfo.IsApply,
									SellerCodes:   discountInfo.SellerCodes,
									StoreCode:     redeem.StoreCode,
								},
							},
						}
					}

				}
			}
		} else {
			errRes = &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   redeem.ErrorMessage,
				ErrorCode: "VOUCHER_CODE_INVALID",
			}
			makeCartCacheError(errRes, cart)
			message = redeem.ErrorMessage
		}
		redeemApplyResult = append(redeemApplyResult, &model.PromoApplyResult{
			Code:          redeem.VoucherCode,
			CanUse:        redeem.CanUse,
			Message:       message,
			Gift:          redeem.Gifts,
			AutoApply:     redeem.AutoApply,
			MatchSeller:   redeem.MatchSeller,
			MatchProducts: redeem.MatchProducts,
			DiscountValue: redeem.DiscountValue,
			SellerCode:    redeem.SellerCode,
			SellerCodes:   redeem.SellerCodes,
			ApplySkus:     redeem.ApplySkus,
			NotApplySkus:  redeem.NotApplySkus,
			StoreCode:     redeem.StoreCode,
			PaymentMethod: redeem.PaymentMethod,
			Name:          redeem.VoucherName,
			ChargeFee:     redeem.ChargeFee,
		})
		redeemCodeMap = append(redeemCodeMap, &model.RedeemApply{
			Code:      redeem.VoucherCode,
			AutoApply: redeem.AutoApply,
		})
		//}
	}
	if len(mapItemDiscount) > 0 {
		for _, item := range cart.Items {
			if data, ok := mapItemDiscount[item.Sku]; ok {
				item.DiscountDetail = data
			}
		}
	}
	cart.RedeemCode = &curCodes
	if cart.Discount > cart.TotalPrice {
		cart.Discount = cart.TotalPrice
	}
	cart.TotalPrice = cart.TotalPrice - cart.Discount
	cart.SubPrice = cart.Price - cart.Discount
	cart.RedeemApplyResult = redeemApplyResult
	cart.RedeemCodeMap = redeemCodeMap

	return errRes
}

func cartCacheHandlePaymentCreditFee(cart *model.CartCache) *common.APIResponse {
	if !IsContainsT(PARTNER_PAYMENT_METHOD, (cart.PaymentMethod)) {
		return nil
	}

	cart.TotalPriceBeforePartnerPaymentFee = cart.TotalPrice

	// partner payment fee
	if fee, ok := mapPaymentMethodConfig[cart.PaymentMethod]; ok {
		var partnerPaymentMethod *model.PartnerPaymentMethod
		locationOffset := []string{cart.CustomerWardCode, cart.CustomerDistrictCode, cart.CustomerProvinceCode, cart.CustomerRegionCode, "00"}
		for _, locationCode := range locationOffset {
			if locationCode == "" {
				continue
			}
			partnerPaymentMethod = calculateOnepayPaymentMethod(locationCode, cart.TotalPrice, fee)
			if partnerPaymentMethod != nil {
				break
			}
		}
		if partnerPaymentMethod != nil {
			cart.PartnerPaymentMethod = partnerPaymentMethod
		}
	}

	if cart.PartnerPaymentMethod != nil {
		if cart.PartnerPaymentMethod.TotalCustomerFee > 0 {
			cart.TotalPrice += int(cart.PartnerPaymentMethod.TotalCustomerFee)
		}
	}

	return nil
}
func makeCartCacheError(err *common.APIResponse, cart *model.CartCache) {
	cart.ErrorCode = &err.ErrorCode
	cart.ErrorMessage = &err.Message
}

func SyncCartCache(account *model.Account, getVoucherAuto bool, redeemCodeRemovedArr []string, autoRemoveVoucherAutoInvalid bool, screen string) {
	// Init new cart cache, get from the current cart
	accountID := account.AccountID
	cartRes := GetCartInfo(account, getVoucherAuto, redeemCodeRemovedArr, autoRemoveVoucherAutoInvalid, screen)
	if cartRes.Status != common.APIStatus.Ok {
		// delete cart cache
		qCart := model.CartCacheDB.QueryOne(&model.CartCache{AccountID: accountID})
		if qCart.Status == common.APIStatus.Ok {
			model.CartCacheDB.Delete(bson.M{"account_id": accountID})
			model.CartItemCacheDB.Delete(bson.M{"cart_id": qCart.Data.([]*model.CartCache)[0].CartID})
		}
		return
	}
	cart := cartRes.Data.([]*model.Cart)[0]
	qCache := bson.M{
		"account_id": accountID,
	}
	// cart -> cartCache
	cartByte, _ := json.Marshal(cart)
	newCartCache := &model.CartCache{}
	_ = json.Unmarshal(cartByte, newCartCache)

	// Init old cart cache
	cartCacheRes := model.CartCacheDB.QueryOne(qCache)
	if cartCacheRes.Status == common.APIStatus.NotFound {
		cartCacheRes = model.CartCacheDB.Create(newCartCache)
		if cartCacheRes.Status != common.APIStatus.Ok {
			return
		}
		model.CartItemCacheDB.CreateMany(newCartCache.Items)
		return
	}

	// In case of error, delete cache
	if cartCacheRes.Status == common.APIStatus.Error {
		cartCacheRes = model.CartCacheDB.Delete(model.CartCache{AccountID: accountID})
		if cartCacheRes.Status != common.APIStatus.Ok {
			return
		}
		model.CartItemCacheDB.Delete(model.CartItemCache{CartID: newCartCache.CartID})

		return
	}

	oldCartCache := cartCacheRes.Data.([]*model.CartCache)[0]
	qItemCache := model.CartItemCacheDB.Query(&model.CartItemCache{CartID: oldCartCache.CartID}, 0, 0, nil)
	if qItemCache.Status != common.APIStatus.Ok {
		return
	}
	oldCartCache.Items = qItemCache.Data.([]*model.CartItemCache)

	// Compare old and new cart, get the changes
	newCartMap, _ := structToMapUsingBSON(newCartCache)
	oldCartMap, _ := structToMapUsingBSON(oldCartCache)
	changes := findChanges(oldCartMap, newCartMap)
	if len(changes) > 0 {
		updater := bson.M{}
		for key, val := range changes {
			if valMap, ok := val.(map[string]interface{}); ok {
				updater[key] = valMap["new"]
			}
		}
		err := model.CartCacheDB.UpdateOneWithOption(qCache, bson.M{"$set": updater})
		if err != nil {
			//return
		}
	}

	// Compare old and new cart_item, get the changes
	oldItemsMap := make(map[string]*model.CartItemCache)
	for _, item := range oldCartCache.Items {
		// check exist
		if _, ok := oldItemsMap[item.Sku+"_"+string(item.Type)]; !ok {
			oldItemsMap[item.Sku+"_"+string(item.Type)] = item
		} else {
			model.CartItemCacheDB.Delete(bson.M{"cart_id": item.CartID, "sku": item.Sku, "type": item.Type})
			delete(oldItemsMap, item.Sku+"_"+string(item.Type))
		}
	}

	for _, newItem := range newCartCache.Items {
		if oldItem, ok := oldItemsMap[newItem.Sku+"_"+string(newItem.Type)]; ok {
			delete(oldItemsMap, newItem.Sku+"_"+string(newItem.Type))
			oldCartItemsMap, err := structToMapUsingBSON(oldItem)
			if err != nil {
				continue
			}

			newCartItemsMap, _ := structToMapUsingBSON(newItem)
			//mapCartItemFields := createJSONToBSONMap(&model.CartItemCache{})
			changes := findChanges(oldCartItemsMap, newCartItemsMap)
			if len(changes) > 0 {
				updater := bson.M{}
				for key, val := range changes {
					if valMap, ok := val.(map[string]interface{}); ok {
						updater[key] = valMap["new"]
					}
				}
				model.CartItemCacheDB.UpdateOneWithOption(bson.M{"sku": oldItem.Sku, "type": oldItem.Type, "cart_id": oldItem.CartID}, bson.M{"$set": updater})
			}
		} else {
			errCreate := model.CartItemCacheDB.Create(newItem)
			if errCreate.Status != common.APIStatus.Ok {

			}
		}
	}

	for _, delItem := range oldItemsMap {
		model.CartItemCacheDB.Delete(bson.M{"cart_id": delItem.CartID, "sku": delItem.Sku, "type": delItem.Type})
	}
}

// Function to compare two maps and return the fields that have changed
func findChanges(map1, map2 map[string]interface{}) map[string]interface{} {
	changes := make(map[string]interface{})

	for key, val1 := range map1 {
		if val2, ok := map2[key]; ok {
			// If both values are maps, compare them recursively
			if reflect.TypeOf(val1) == reflect.TypeOf(map[string]interface{}{}) && reflect.TypeOf(val2) == reflect.TypeOf(map[string]interface{}{}) {
				subChanges := findChanges(val1.(map[string]interface{}), val2.(map[string]interface{}))
				if len(subChanges) > 0 {
					changes[key] = map[string]interface{}{
						"old": val1,
						"new": val2,
					}
				}
			} else if !reflect.DeepEqual(val1, val2) {
				// If values are different, record the change
				changes[key] = map[string]interface{}{
					"old": val1,
					"new": val2,
				}
			}
		} else {
			// Field only in map1
			changes[key] = map[string]interface{}{
				"old": val1,
				"new": nil,
			}
		}
	}

	// Check for fields that are only in map2
	for key, val2 := range map2 {
		if _, ok := map1[key]; !ok {
			changes[key] = map[string]interface{}{
				"old": nil,
				"new": val2,
			}
		}
	}

	return changes
}

func structToMapUsingBSON(s interface{}) (map[string]interface{}, error) {
	// Chuyển struct thành JSON
	data, err := bson.Marshal(s)
	if err != nil {
		return nil, err
	}

	// Giải mã JSON thành map
	var result map[string]interface{}
	err = bson.Unmarshal(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func createJSONToBSONMap(v interface{}) map[string]string {
	result := make(map[string]string)

	val := reflect.ValueOf(v)
	typ := reflect.TypeOf(v)

	// Đảm bảo rằng đầu vào là struct
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
		typ = typ.Elem()
	}

	// Duyệt qua từng trường của struct
	for i := 0; i < typ.NumField(); i++ {
		jsonTag := typ.Field(i).Tag.Get("json")
		bsonTag := typ.Field(i).Tag.Get("bson")

		// Bỏ phần `,omitempty` trong các tag nếu có
		if idx := strings.Index(jsonTag, ","); idx != -1 {
			jsonTag = jsonTag[:idx]
		}
		if idx := strings.Index(bsonTag, ","); idx != -1 {
			bsonTag = bsonTag[:idx]
		}

		// Chỉ thêm vào map nếu cả jsonTag và bsonTag đều không rỗng
		if jsonTag != "" && bsonTag != "" {
			result[jsonTag] = bsonTag
		}
	}

	return result
}

func SyncCartWhenUpdateItemQuantity(item *model.CartItem) {
	itemUpdater := bson.M{
		"quantity":    item.Quantity,
		"total_price": item.TotalPrice,
	}
	model.CartItemCacheDB.UpdateOneWithOption(bson.M{"cart_id": item.CartID, "sku": item.Sku, "type": item.Type}, bson.M{"$set": itemUpdater})
}

func SyncCartWhenRemoveItem(item *model.CartItem) {
	model.CartItemCacheDB.Delete(bson.M{"cart_id": item.CartID, "sku": item.Sku, "type": item.Type})
}
