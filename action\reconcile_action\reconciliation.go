package reconcile_action

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateReconciliationFromTime(
	sellerCode string,
	now time.Time,
) (*model.Reconciliation, error) {
	seller, err := client.Services.Seller.GetSeller(sellerCode)
	if err != nil {
		return nil, err
	}

	now = now.In(utils.VNTimeZone)

	schedule := GetSchedule(seller, now)
	if schedule == "" {
		return nil, fmt.Errorf("CreateReconciliationFromTime: schedule is empty")
	}

	scheduleSettings := client.Services.Seller.GetReconciliationScheduleSetting()
	from, scheduleTime, index, _, err := GetReconcileTime(schedule, now, scheduleSettings)
	if err != nil {
		return nil, err
	}

	fromDate := from.Format("2006-01-02")
	toDate := scheduleTime.AddDate(0, 0, -1).Format("2006-01-02")

	return CreateReconciliationFromIndex(sellerCode, index, fromDate, toDate, scheduleTime)
}

func CreateReconciliationFromIndex(
	sellerCode, timeIndex string,
	fromTime, toTime string,
	reconciledTime time.Time,
) (*model.Reconciliation, error) {
	reconciliationRes := model.ReconciliationDB.QueryOne(model.Reconciliation{
		SellerCode:                 sellerCode,
		ReconcileScheduleTimeIndex: timeIndex,
	})
	if reconciliationRes.Status == common.APIStatus.Ok {
		return reconciliationRes.Data.([]*model.Reconciliation)[0], nil
	}

	recRes := model.ReconciliationDB.Create(model.Reconciliation{
		RecCode:                    fmt.Sprintf("%s_%s", sellerCode, timeIndex),
		ReconciledTime:             &reconciledTime,
		AllowConfirmTime:           reconciledTime.AddDate(0, 0, conf.Config.AllowConfirmReconciliationAfter),
		FromTime:                   fromTime,
		ToTime:                     toTime,
		SellerCode:                 sellerCode,
		ReconcileScheduleTimeIndex: timeIndex,
		ReconciliationStatus:       model.ReconciliationStatus.Waiting,
	})
	if recRes.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("CreateReconciliation: %s", recRes.Message)
	}

	return recRes.Data.([]*model.Reconciliation)[0], nil
}

func DeleteReconciliationIfEmpty(sellerCode, timeIndex string) {
	if sellerCode == "" || timeIndex == "" {
		return
	}

	recItemsCountRes := model.ReconciliationItemDB.Count(model.ReconciliationItem{
		SellerCode:                 sellerCode,
		ReconcileScheduleTimeIndex: timeIndex,
	})
	if recItemsCountRes.Status == common.APIStatus.Ok &&
		recItemsCountRes.Total == 0 {
		model.ReconciliationDB.Delete(model.Reconciliation{
			SellerCode:                 sellerCode,
			ReconcileScheduleTimeIndex: timeIndex,
			ReconciliationStatus:       model.ReconciliationStatus.Waiting,
		})
	}
}

func GetSellerCurrentReconciliation(sellerCode string) *common.APIResponse {
	var reconciliation *model.Reconciliation
	var err error

	now := time.Now()

	recRes := model.ReconciliationDB.Query(model.Reconciliation{
		SellerCode:           sellerCode,
		ReconciliationStatus: model.ReconciliationStatus.Waiting,
	}, 0, 1, &primitive.M{"_id": -1})
	if recRes.Status == common.APIStatus.Ok {
		reconciliation = recRes.Data.([]*model.Reconciliation)[0]

		fromDate, toDate := GetFromToDateFromIndex(reconciliation.ReconcileScheduleTimeIndex)
		if !(now.After(fromDate) && now.Before(toDate)) {
			reconciliation = nil
		}
	}

	if recRes.Status == common.APIStatus.NotFound || reconciliation == nil {
		reconciliation, err = CreateReconciliationFromTime(sellerCode, now)
	}

	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	if reconciliation == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Not found reconciliation",
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    []*model.Reconciliation{reconciliation},
		Message: "Success",
	}
}
