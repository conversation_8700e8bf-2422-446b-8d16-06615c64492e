package consumer

import (
	"fmt"
	"runtime/debug"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
)

func HandleSendInvoiceForConfirmedOrder(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return nil
	}

	var input *model.Reconciliation
	err = bson.Unmarshal(data, &input)
	if err != nil {
		return nil
	}

	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic: %s", string(debug.Stack()))
			fmt.Printf("[%s - %s] %s\n", input.SellerCode, input.ReconcileScheduleTimeIndex, err.Error())
		}
	}()

	sendInvoiceRes := reconcile_action.SendInvoiceForConfirmedOrder(input)
	if sendInvoiceRes.Status != common.APIStatus.Ok {
		return fmt.Errorf("HandleSendInvoice failed: %s", sendInvoiceRes.Message)
	}

	return nil
}
