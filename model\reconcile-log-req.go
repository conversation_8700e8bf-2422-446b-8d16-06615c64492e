package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var ReconcileLogReqDb = &db.Instance{
	ColName:        "reconcile_log_req",
	TemplateObject: &ReconcileLogReq{},
}

func InitReconcileLogReqModel(s *mongo.Database) {
	ReconcileLogReqDb.ApplyDatabase(s)
}

type ReconcileLogReq struct {
	ID          primitive.ObjectID `json:"id" bson:"_id,omitempty" `
	CreatedTime *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`

	SellerCode                 string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	ReconcileScheduleTimeIndex string `json:"reconcileScheduleTimeIndex,omitempty" bson:"reconcile_schedule_time_index,omitempty"`

	FeeType enum.FeeTypeValue `json:"feeType" bson:"fee_type,omitempty"`

	PenaltyDescription string `json:"penaltyDescription,omitempty" bson:"penalty_description,omitempty"`
	PenaltyFee         int    `json:"penaltyFee,omitempty" bson:"penalty_fee,omitempty"`
	PenaltyBMFee       int    `json:"penaltyBMFee,omitempty" bson:"penalty_bm_fee,omitempty"`
	PenaltyBMLFee      int    `json:"penaltyBMLFee,omitempty" bson:"penalty_bml_fee,omitempty"`

	AutoPenaltyFee    bool    `json:"autoPenaltyFee,omitempty" bson:"auto_penalty_fee,omitempty"`
	TicketID          int64   `json:"ticketID,omitempty" bson:"ticket_id,omitempty"`
	WarehouseCode     string  `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	RetailPrice       int64   `json:"retailPrice,omitempty" bson:"retail_price,omitempty"`
	PenaltyRate       float64 `json:"penaltyRate,omitempty" bson:"penalty_rate,omitempty"`
	InboundCode       string  `json:"inboundCode,omitempty" bson:"inbound_code,omitempty"`
	DaysInbound       int     `json:"daysInbound,omitempty" bson:"days_inbound,omitempty"`
	AvailableQuantity int64   `json:"availableQuantity,omitempty" bson:"available_quantity,omitempty"`

	ReqType      ReqTypeValue `json:"reqType,omitempty" bson:"req_type,omitempty"`
	ActionSource *Account     `json:"actionSource,omitempty" bson:"action_source,omitempty"`
}

type ReqTypeValue string

var ReconcileLogReqType = struct {
	Reverted  ReqTypeValue
	Confirmed ReqTypeValue
	Completed ReqTypeValue
	Deleted   ReqTypeValue
}{
	Reverted:  "REVERTED",
	Confirmed: "CONFIRMED",
	Completed: "COMPLETED",
	Deleted:   "DELETED",
}
