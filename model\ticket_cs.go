package model

type TicketCS struct {
	Code       string             `json:"code,omitempty" bson:"code,omitempty"`
	OrderItems *[]TicketOrderItem `json:"orderItems,omitempty" bson:"order_items,omitempty"`
}

type TicketOrderItem struct {
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`
	Sku         string `json:"sku,omitempty" bson:"sku,omitempty"`
	BackupSku   string `json:"backupSku,omitempty" bson:"backup_sku,omitempty"`
	PoorQuality *bool  `json:"poorQuality,omitempty" bson:"poor_quality,omitempty"`
	Price       int64  `json:"price,omitempty" bson:"price,omitempty"`
}

type TicketCSResponse struct {
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Data    []*TicketCS `json:"data"`
}
