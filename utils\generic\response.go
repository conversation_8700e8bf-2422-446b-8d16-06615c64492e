package generic

import (
	"encoding/json"
	"net/http"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

type BaseAPIResponse struct {
	Status    string            `json:"status"`
	Message   string            `json:"message"`
	ErrorCode string            `json:"errorCode,omitempty"`
	Total     int64             `json:"total,omitempty"`
	Headers   map[string]string `json:"headers,omitempty"`
}

type GResponse[D any] struct {
	BaseAPIResponse
	Data D `json:"data,omitempty"`
}

// Khi forward api (dùng auth từ client gởi lên để call api X của service khác)
// cần lưu ý handle trong trường hợp auth client gởi lên k có quyền call api X
// (trả về 403) thì cấn parse data lỗi api X trả về để trả cho client

func parseForbiddenResp(res *client.RestResult) *common.APIResponse {

	var result *common.APIResponse

	err := json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	return result
}

func ToAPIResponse[D any](res *client.RestResult, resError error) *common.APIResponse {
	if resError != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: resError.Error(),
		}
	}

	// check StatusForbidden -> parse and return user info
	if res.Code == http.StatusForbidden {
		return parseForbiddenResp(res)
	}

	var tmpResp = &GResponse[D]{}
	err := json.Unmarshal([]byte(res.Body), tmpResp)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	return &common.APIResponse{
		Status:    tmpResp.Status,
		Message:   tmpResp.Message,
		ErrorCode: tmpResp.ErrorCode,
		Total:     tmpResp.Total,
		Headers:   tmpResp.Headers,
		Data:      tmpResp.Data,
	}
}

func ToAPIResponseStatus[D any](res *client.RestResult, resError error) *common.APIResponse {
	if resError != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: resError.Error(),
		}
	}

	// check StatusForbidden -> parse and return user info
	if res.Code == http.StatusForbidden {
		return parseForbiddenResp(res)
	}

	var result *common.APIResponse
	err := json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}
	}

	return result
}
