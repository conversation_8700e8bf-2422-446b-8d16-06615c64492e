package client

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
)

const (
	// Core
	pathGetWarehouse       = "/warehouse/core/v1/warehouse"
	pathCancelOrder        = "/warehouse/core/v1/sale-orders/cancel"
	pathGetSaleOrder       = "/warehouse/core/v1/sale-orders"
	pathCreateSaleOrder    = "/warehouse/core/v1/sale-order"
	pathUpdateShippingInfo = "/warehouse/core/v1/sale-orders/address/update"
	pathRequestProcessing  = "/warehouse/core/v1/sale-order/request-processing" // request kho chờ đủ hàng
	pathUpdateDeliveryInfo = "/warehouse/core/v1/sale-orders/delivery-info"
	pathGetWarehousesList  = "/warehouse/core/v1/warehouse"
	pathInvoiceRequest     = "/warehouse/core/v1/sale-orders/invoice-request"
	pathGetDeliveryOrders  = "/warehouse/core/v1/delivery-order"
	// Inbound
	pathGetReturnTicketWMS = "/warehouse/inbound/v1/return-ticket"
	pathGetPutTicket       = "/warehouse/inbound/v1/put-ticket"
	pathGetLastPut         = "/warehouse/inbound/v1/put-ticket-item/last-put"
	pathGetLeadTime        = "/warehouse/core/v1/lead-time/report"
)

type warehouseClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewWarehouseServiceClient ...
func NewWarehouseServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *warehouseClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	warehouseClient := &warehouseClient{
		svc: client.NewRESTClient(apiHost, logName, 3*time.Second, 0, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	warehouseClient.svc.SetDBLog(session)
	return warehouseClient
}

// CancelOrder is func create new Warehouse
func (cli *warehouseClient) CancelOrder(in *CancelOrderRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, in, pathCancelOrder, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CANCEL_ORDER",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CANCEL_ORDER",
		}
	}

	return result
}

// https://v2api.thuocsi.vn/warehouse/core/v1/sale-orders?adminId=752081
func (cli *warehouseClient) GetSaleOrder(orderID int64, so string) (*model.OrderWarehouse, error) {
	params := map[string]string{
		"adminId": fmt.Sprintf("%v", orderID),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSaleOrder, nil)
	if err != nil {
		return nil, err
	}

	var result *model.OrderWarehouseResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	for _, item := range result.Data {
		if item.SaleOrderCode == so {
			return item, nil
		}
	}

	return nil, fmt.Errorf("%v", "Not found "+so)
}

func (cli *warehouseClient) UpdateShippingInfo(info *model.UpdateShippingInfoRequest) error {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, params, info, pathUpdateShippingInfo, nil)
	if err != nil {
		return err
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return err
	}
	return nil
}

func (cli *warehouseClient) GetReturnTicketWMS(query *model.ReturnTicketWMS) ([]*model.ReturnTicketWMS, error) {
	params := make(map[string]string)
	// q
	if query != nil {
		bytes, _ := json.Marshal(query)
		params["q"] = string(bytes)
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetReturnTicketWMS, &[]string{"RETURN_TICKET", query.RefCode})
	if err != nil {
		return nil, err
	}

	var result *model.ReturnTicketWMSResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	if len(result.Data) > 0 {
		return result.Data, nil
	}

	return nil, fmt.Errorf("%v", "Not found")
}

func (cli *warehouseClient) RequestProcessing(request *model.OrderProcessRequestToWms) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, request, pathRequestProcessing, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_ORDER_PROCESS",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_REQUEST_ORDER_PROCESS",
		}
	}
	return result
}

func (cli *warehouseClient) CreateSaleOrder(payload *CreateSaleOrderRequest) (*model.OrderWarehouse, error) {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, payload, pathCreateSaleOrder, nil)
	if err != nil {
		return nil, err
	}

	var result *model.OrderWarehouseResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%s _ %v", result.ErrorCode, result.Message)
	}

	return result.Data[0], nil
}

func (cli *warehouseClient) UpdateDeliveryInfo(payload *UpdateDeliveryInfoRequest) (*model.OrderWarehouse, error) {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, payload, pathUpdateDeliveryInfo, nil)
	if err != nil {
		return nil, err
	}

	var result *model.OrderWarehouseResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%s _ %v", result.ErrorCode, result.Message)
	}

	return result.Data[0], nil
}

func (cli *warehouseClient) GetWarehouses() []*Warehouse {
	params := map[string]string{
		"limit": strconv.Itoa(100),
	}

	result, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetWarehouse, nil)
	if err != nil {
		fmt.Println("GetWarehouses failed: " + err.Error())
		return nil
	}

	var whRes *WarehouseRes
	err = json.Unmarshal([]byte(result.Body), &whRes)
	if err != nil {
		fmt.Println("GetWarehouses failed: " + err.Error())
		return nil
	}

	if whRes == nil || whRes.Status != common.APIStatus.Ok {
		return nil
	}

	if len(whRes.Data) == 0 {
		return nil
	}

	return whRes.Data
}

func (cli *warehouseClient) GetWarehousesList() ([]*model.Warehouse, error) {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, nil, nil, pathGetWarehousesList, nil)
	if err != nil {
		return nil, err
	}

	var result *model.WarehouseResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

func (cli *warehouseClient) UpdateInvoieRequest(input *WarehouseUpdateInvoiceRequest) error {
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Put, cli.headers, nil, input, pathInvoiceRequest, &[]string{fmt.Sprintf("%d", input.AdminID)})
	if err != nil {
		return err
	}
	var result *model.WarehouseResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return err
	}
	if result.Status != common.APIStatus.Ok {
		if result.Status == common.APIStatus.NotFound {
			return nil
		}
		return fmt.Errorf("%v", result.Message)
	}
	return nil
}

func (cli *warehouseClient) GetDeliveryOrders(query *model.DeliveryOrderReq) ([]*model.DeliveryOrder, error) {
	params := map[string]string{
		"offset": strconv.Itoa(0),
		"limit":  strconv.Itoa(100),
	}
	if query != nil {
		bytes, _ := json.Marshal(query)
		params["q"] = string(bytes)
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetDeliveryOrders, nil)
	if err != nil {
		return nil, err
	}

	var result *model.DeliveryOrderResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

func (cli *warehouseClient) GetLatestPutTicket(query *GetLatestPutTicketReq) (*LatestPutInfo, error) {
	params := make(map[string]string)
	// q
	if query != nil {
		bytes, _ := json.Marshal(query)
		params["q"] = string(bytes)
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetLastPut, &[]string{"RETURN_TICKET"})
	if err != nil {
		return nil, err
	}

	var result *LatestPutResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	if len(result.Data) <= 0 {
		return nil, fmt.Errorf("%v", "Not found")
	}

	data := result.Data[0]

	putInfos, err := cli.GetPutTicket(data.ReferenceCode, 0, 1)
	if err != nil {
		return &data, nil
	}

	putTicket := putInfos[0]
	data.InboundCode = putTicket.InboundCode

	return &data, nil

}

func (cli *warehouseClient) GetPutTicket(code string, offset, limit int) ([]PutTicket, error) {
	params := make(map[string]string)

	// q
	if code != "" {
		type Input struct {
			Code string `json:"code"`
		}

		input := Input{
			Code: code,
		}

		bytes, _ := json.Marshal(input)
		params["q"] = string(bytes)
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetPutTicket, nil)
	if err != nil {
		return nil, err
	}

	var result *PutTicketResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	if len(result.Data) > 0 {
		return result.Data, nil
	}

	return nil, fmt.Errorf("%v", "Not found")
}

func (cli *warehouseClient) GetLeadTimeByLocationCode(provinceCode, districtCode, wardCode string, customerID int64) (*WarehouseLeadTimeResponse, error) {
	params := map[string]string{
		"provinceCode": provinceCode,
		"districtCode": districtCode,
		"wardCode":     wardCode,
		"customer":     strconv.Itoa(int(customerID)),
		"orderTime":    strconv.Itoa(int(time.Now().Unix())),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetLeadTime, nil)
	if err != nil {
		return nil, err
	}

	var result *WarehouseLeadTimeResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}
	return result, nil
}
