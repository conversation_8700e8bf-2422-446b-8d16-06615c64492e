package worker

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/tool"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/conf"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

/*
- recognize time range by schedule
- get all seller => recognize <seller - schedule> should be applied by level & apply level time
- get all order completed in time range => get orders include seller's products
- get all spec revenue by schedule
- get all category
- calculate revenue
*/
func ProcessReconciliation(now *time.Time, config *schedule.Config) (err error, note string, nextRun *time.Time) {
	if now == nil {
		_t := time.Now()
		now = &_t
	}
	_t := now.In(model.VNTimeZone)
	nowInVN := &_t

	y, m, d := nowInVN.Date()

	next := time.Date(y, m, d, conf.Config.ReconciliationTime, 0, 0, 0, model.VNTimeZone).AddDate(0, 0, 1)
	if conf.Config.NextSchedule != 0 {
		_n := nowInVN.Add(conf.Config.NextSchedule)
		if _n.Before(next) {
			next = _n
		}
	}
	nextRun = &next

	note, err = tool.ProcessBySchedule(config.Topic, *nowInVN, nil)
	if err != nil {
		println(config.Topic, "err:", err.Error())
	}

	return
}
