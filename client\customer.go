package client

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

const (
	pathGetCustomerByCode                   = "/marketplace/customer/v1/account"
	pathGetAccountAddress                   = "/marketplace/customer/v1/account/address"
	pathUpdatePointAndOrderCount            = "/marketplace/customer/v1/account/point"
	pathGetCustomerByPhone                  = "/marketplace/customer/v1/account/search"
	pathGetPICList                          = "/marketplace/customer/v1/pic/list"
	pathCreateOrderRating                   = "/marketplace/customer/v1/rating"
	pathNotificationPartnerList             = "/marketplace/customer/v1/notification-partner/list"
	pathNotificationPartnerListByCustomerID = "/marketplace/customer/v1/notification-partner/by-customer-id"

	pathMigrateUpdateAccumulateProduct = "/marketplace/customer/v1/migrate/update-accumulate-product"
	pathGetSettingConfig               = "/marketplace/customer/v1/customer/setting-config"
	pathUpdateAccumulateProduct        = "/marketplace/customer/v1/customer/accumulate-product"

	pathGetPartnerKiotvietInfoKiotvietParnter = "/marketplace/customer/v1/partner-integration/reference"
)

type customerClient struct {
	svc     *client.RestClient
	headers map[string]string
}

// NewCustomerServiceClient ...
func NewCustomerServiceClient(apiHost, apiKey, logName string, session *mongo.Database) *customerClient {
	if apiHost == "" || apiKey == "" {
		return nil
	}
	customerClient := &customerClient{
		svc: client.NewRESTClient(
			apiHost, logName, 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": apiKey,
		},
	}
	customerClient.svc.SetDBLog(session)
	return customerClient
}

// GetCustomerByAccountID ...
func (cli *customerClient) GetCustomerByAccountID(accountID int64, accountType string) (*model.Customer, error) {
	params := map[string]string{
		"accountID":   fmt.Sprintf("%v", accountID),
		"accountType": accountType,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetCustomerByCode, nil)
	if err != nil {
		return nil, err
	}

	var result *CustomerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

// GetCustomerByCustomerID ...
func (cli *customerClient) GetCustomerByCustomerID(customerID int64) (*model.Customer, error) {
	params := map[string]string{
		"customerID": fmt.Sprintf("%v", customerID),
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetCustomerByCode, nil)
	if err != nil {
		return nil, err
	}

	var result *CustomerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

// GetCustomerByPhone ...
func (cli *customerClient) GetCustomerByPhone(phone string) (*model.Customer, error) {
	body := map[string]string{
		"phone": phone,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, nil, body, pathGetCustomerByPhone, nil)
	if err != nil {
		return nil, err
	}

	var result *CustomerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

// GetCustomerByPhone ...
func (cli *customerClient) GetNotificationPartnerListByCustomerID(in *NotificationPartner, offset, limit int64) ([]*NotificationPartner, error) {

	paramQ, _ := json.Marshal(in)

	query := map[string]string{
		"q":      string(paramQ),
		"offset": strconv.FormatInt(offset, 10),
		"limit":  strconv.FormatInt(limit, 10),
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathNotificationPartnerListByCustomerID, nil)
	if err != nil {
		return nil, err
	}

	var result *NotificationPartnerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

func (cli *customerClient) GetAccountAddress(customerID int64, addressCode string) ([]*model.Address, error) {
	query := map[string]string{
		"customerID":  strconv.FormatInt(customerID, 10),
		"addressCode": addressCode,
	}

	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, query, nil, pathGetAccountAddress, nil)
	if err != nil {
		return nil, err
	}

	var result *AddressResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data, nil
}

// UpdatePointAndOrderCount is func ...
func (cli *customerClient) UpdatePointAndOrderCount(payload *UpdatePointAndOrderCountRequest) *common.APIResponse {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Post, cli.headers, params, &payload, pathUpdatePointAndOrderCount, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UpdatePointAndOrderCount",
		}
	}
	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UpdatePointAndOrderCount",
		}
	}

	return result
}

type PicResp struct {
	common.APIResponse
	Data []*model.Pic `json:"data"`
}

// UpdatePointAndOrderCount is func ...
func (cli *customerClient) GetPicList(params map[string]string) *common.APIResponse {
	res, err := cli.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Get,
		cli.headers, params, nil,
		pathGetPICList, nil)

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_GetPicList",
		}
	}
	var result PicResp
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_GetPicList",
		}
	}

	return &common.APIResponse{
		Status: result.Status,
		Data:   result.Data,
	}
}

func (cli *customerClient) CreateOrderRating(input *model.CustomerRating) *common.APIResponse {
	res, err := cli.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		cli.headers, nil, input,
		pathCreateOrderRating, nil)

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_CreateOrderRating",
		}
	}
	var result common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_CreateOrderRating",
		}
	}

	return &common.APIResponse{
		Status:    result.Status,
		Message:   result.Message,
		ErrorCode: result.ErrorCode,
	}
}

func (cli *customerClient) MigrateUpdateAccumulateProduct(customerID int64, accumulateProductCount int) *common.APIResponse {
	params := map[string]string{}
	payload := struct {
		CustomerID             int64 `json:"customerID"`
		AccumulateProductCount int   `json:"accumulateProductCount"`
	}{
		CustomerID:             customerID,
		AccumulateProductCount: accumulateProductCount,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Put,
		cli.headers, params, payload,
		pathMigrateUpdateAccumulateProduct, nil)

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UpdateAccumulateProduct",
		}
	}
	var result common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UpdateAccumulateProduct",
		}
	}

	return &common.APIResponse{
		Status:    result.Status,
		Message:   result.Message,
		ErrorCode: result.ErrorCode,
	}
}

// GetSettingConfig ...
func (cli *customerClient) GetSettingConfig() (*SettingConfigCustomer, error) {
	params := map[string]string{}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetSettingConfig, nil)
	if err != nil {
		return nil, err
	}

	var result *SettingConfigCustomerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}

	return result.Data[0], nil
}

func (cli *customerClient) UpdateAccumulateProduct(customerID int64, accumulateProductCount int) *common.APIResponse {
	params := map[string]string{}
	payload := struct {
		CustomerID             int64 `json:"customerID"`
		AccumulateProductCount int   `json:"accumulateProductCount"`
	}{
		CustomerID:             customerID,
		AccumulateProductCount: accumulateProductCount,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Put,
		cli.headers, params, payload,
		pathUpdateAccumulateProduct, nil)

	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST_UpdateAccumulateProduct",
		}
	}
	var result common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL_UpdateAccumulateProduct",
		}
	}

	return &common.APIResponse{
		Status:    result.Status,
		Message:   result.Message,
		ErrorCode: result.ErrorCode,
	}
}

func (cli *customerClient) GetPartnerKiotvietInfo(customerID int64, partnerName string) *common.APIResponse {
	params := map[string]string{
		"customerId": strconv.FormatInt(customerID, 10),
		"partner":    partnerName,
	}
	res, err := cli.svc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, params, nil, pathGetPartnerKiotvietInfoKiotvietParnter, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "MAKE_REQUEST",
		}
	}

	var result *common.APIResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "UNMARSHAL",
		}
	}

	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return &common.APIResponse{
			Status:    result.Status,
			Message:   result.Message,
			ErrorCode: result.ErrorCode,
		}
	}

	return result
}
