package enum

type InvoiceMethodForVoucherValue string

type invoiceMethodForVoucherValue struct {
	TRADE_DISCOUNT           InvoiceMethodForVoucherValue // Xuất dưới dạng CK thương mại
	PRODUCT_DISCOUNT         InvoiceMethodForVoucherValue // Xuất dưới dạng chiết khấu sản phẩm
	ALLOCATING_TO_UNIT_PRICE InvoiceMethodForVoucherValue // Xuất dưới dạng phân bổ vào đơn giá
}

var InvoiceMethodForVoucher = &invoiceMethodForVoucherValue{
	"TRADE_DISCOUNT",
	"PRODUCT_DISCOUNT",
	"ALLOCATING_TO_UNIT_PRICE",
}

var InvoiceMethodForVoucherMap = map[InvoiceMethodForVoucherValue]*InvoiceMethodForVoucherValue{
	InvoiceMethodForVoucher.TRADE_DISCOUNT:           &InvoiceMethodForVoucher.TRADE_DISCOUNT,
	InvoiceMethodForVoucher.PRODUCT_DISCOUNT:         &InvoiceMethodForVoucher.PRODUCT_DISCOUNT,
	InvoiceMethodForVoucher.ALLOCATING_TO_UNIT_PRICE: &InvoiceMethodForVoucher.ALLOCATING_TO_UNIT_PRICE,
}

type ReceiveInvoiceInfoByValue string

type receiveInvoiceInfoByValue struct {
	INVOICE_BY_SO ReceiveInvoiceInfoByValue
	INVOICE_BY_DO ReceiveInvoiceInfoByValue
}

var ReceiveInvoiceInfoBy = &receiveInvoiceInfoByValue{
	"INVOICE_BY_SO",
	"INVOICE_BY_DO",
}

var ReceiveInvoiceInfoByMap = map[ReceiveInvoiceInfoByValue]*ReceiveInvoiceInfoByValue{
	ReceiveInvoiceInfoBy.INVOICE_BY_SO: &ReceiveInvoiceInfoBy.INVOICE_BY_SO,
	ReceiveInvoiceInfoBy.INVOICE_BY_DO: &ReceiveInvoiceInfoBy.INVOICE_BY_DO,
}
