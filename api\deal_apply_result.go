package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

// DealApplyResultList is handler get list deal apply result with pagination
func DealApplyResultList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q        = req.GetParam("q")
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	var query = model.DealApplyResult{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			})
		}
	}

	return resp.Respond(action.GetDealApplyResultList(getActionSource(req), &query, offset, limit, getTotal))
}

// DealApplyResultUpdate ...
func DealApplyResultUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.DealApplyResult
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.UpdateDealApplyResult(input.SKU, &input))
}

// DealApplyResultCreate ...
func DealApplyResultCreate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.DealApplyResult
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	return resp.Respond(action.CreateDealApplyResult(&input))
}

// DealApplyResultDelete ...
func DealApplyResultDelete(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sku      = req.GetParam("sku")
		dealCode = req.GetParam("dealCode")
	)
	if len(sku) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Sku invalid",
			ErrorCode: "SKU_INVALID",
		})
	}

	if len(dealCode) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Deal code invalid",
			ErrorCode: "DEAL_CODE_INVALID",
		})
	}

	return resp.Respond(action.DeleteDealApplyResult(sku, dealCode))
}
