package reconcile_action

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ProcessCalculateBizHouseholdTax(sellerCode, reconcileScheduleTimeIndex string) error {
	err := ProcessCalculateBizHouseholdTaxWithOrderList(sellerCode, reconcileScheduleTimeIndex, nil)
	if err != nil {
		return err
	}

	value, err := CalculateReconciliation(sellerCode, reconcileScheduleTimeIndex)
	if err != nil {
		return err
	}

	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}
	result := model.ReconciliationDB.UpdateOne(model.Reconciliation{SellerCode: sellerCode, ReconcileScheduleTimeIndex: reconcileScheduleTimeIndex}, updater)
	if result.Status != common.APIStatus.Ok {
		return fmt.Errorf("update reconciliation error: %s", result.Message)
	}

	return nil
}

func ProcessCalculateBizHouseholdTaxWithOrderList(sellerCode, reconcileScheduleTimeIndex string, orderIDs []int64) error {
	taxRevenueItems := make([]*client.TaxReportItem, 0)
	removeDuplicateItems := make([]*client.TaxReportItem, 0)
	IdForm := ""

	for {
		items := client.Services.SellerReporting.GetListTaxReportItem(sellerCode, reconcileScheduleTimeIndex, IdForm)

		if len(items) == 0 {
			break
		}

		for _, item := range items {
			if item.Type != enum.TaxReport.DeliveryOrder {
				taxRevenueItems = append(taxRevenueItems, item)
			}
		}

		if len(items) < 1000 {
			break
		}

		lastID := items[len(items)-1].ID
		IdForm = lastID.String()
		time.Sleep(100 * time.Millisecond)
	}

	distinctOrderIDs := getDistinctOrderIDs(orderIDs)
	if len(distinctOrderIDs) > 0 {
		for _, orderID := range distinctOrderIDs {
			items := client.Services.SellerReporting.GetListTaxReportItemByOrder(sellerCode, orderID)

			if len(items) == 0 {
				continue
			}

			// check if order is being reconciled in this period
			reconciliationItem := model.ReconciliationItemDB.QueryOne(model.ReconciliationItem{
				SellerCode:                 sellerCode,
				ReconcileScheduleTimeIndex: reconcileScheduleTimeIndex,
				FeeType:                    enum.FeeType.REVENUE,
				OrderID:                    orderID,
			})
			if reconciliationItem.Status != common.APIStatus.Ok {
				continue
			}

			calcDate := time.Date(2023, 12, 31, 23, 59, 59, 999, utils.VNTimeZone)
			for _, item := range items {
				if item.TrackingDate.After(calcDate) {
					taxRevenueItems = append(taxRevenueItems, item)
				} else {
					removeDuplicateItems = append(removeDuplicateItems, item)
				}
			}
			time.Sleep(100 * time.Millisecond)
		}
	}

	if len(removeDuplicateItems) > 0 {
		for _, item := range removeDuplicateItems {
			filter := model.ReconciliationItem{
				SellerCode:                 sellerCode,
				ReconcileScheduleTimeIndex: reconcileScheduleTimeIndex,
				FeeType:                    enum.FeeType.BIZ_HOUSEHOLD_TAX,
				SaleOrderCode:              item.SaleOrderCode,
				TaxType:                    item.Type,
			}

			model.ReconciliationItemDB.Delete(filter)
			time.Sleep(10 * time.Millisecond)
		}
	}

	if len(taxRevenueItems) == 0 {
		return nil
	}

	mapSODO := make(map[string]model.ReconciliationItem, 0)
	creditItems := make([]model.ReconciliationItem, 0)

	for _, item := range taxRevenueItems {
		if item.SaleOrderCode == "" || item.TotalTax == nil {
			continue
		}

		var debit, credit int64
		var vatRate, pitRate float64

		if item.Debit != nil {
			debit = *item.Debit
		}

		if item.Credit != nil {
			credit = *item.Credit
		}

		if item.VATRate != nil {
			vatRate = *item.VATRate
		}

		if item.PITRate != nil {
			pitRate = *item.PITRate
		}

		switch item.Type {
		case enum.TaxReport.DeliveryOrder:
			if soItem, ok := mapSODO[item.SaleOrderCode]; !ok {
				reconcileItem := model.ReconciliationItem{
					SellerCode:                 sellerCode,
					ReconcileScheduleTimeIndex: reconcileScheduleTimeIndex,
					OrderID:                    item.OrderId,
					SaleOrderCode:              item.SaleOrderCode,
					FeeType:                    enum.FeeType.BIZ_HOUSEHOLD_TAX,

					TotalTaxRevenue: debit,
					VATRate:         vatRate,
					PITRate:         pitRate,

					TaxType:           enum.TaxReport.DeliveryOrder,
					DeliveryOrderCode: []string{item.TrackingCode},
					PenaltyBMFee:      int(*item.TotalTax),
				}

				mapSODO[item.SaleOrderCode] = reconcileItem
			} else {
				soItem.DeliveryOrderCode = append(soItem.DeliveryOrderCode, item.TrackingCode)
				soItem.TotalTaxRevenue += debit
				soItem.PenaltyBMFee += int(*item.TotalTax)
				mapSODO[item.SaleOrderCode] = soItem
			}
		case enum.TaxReport.CreditNote:
			reconcileItem := model.ReconciliationItem{
				SellerCode:                 sellerCode,
				ReconcileScheduleTimeIndex: reconcileScheduleTimeIndex,
				OrderID:                    item.OrderId,
				SaleOrderCode:              item.SaleOrderCode,
				FeeType:                    enum.FeeType.REFUND_BIZ_HOUSEHOLD_TAX,

				TotalTaxRevenue: credit,
				VATRate:         vatRate,
				PITRate:         pitRate,

				TaxType:        enum.TaxReport.CreditNote,
				CreditNoteCode: item.TrackingCode,
				BonusBMAmount:  int(*item.TotalTax),
			}

			creditItems = append(creditItems, reconcileItem)
		case enum.TaxReport.CancelledCreditNote:
			reconcileItem := model.ReconciliationItem{
				SellerCode:                 sellerCode,
				ReconcileScheduleTimeIndex: reconcileScheduleTimeIndex,
				OrderID:                    item.OrderId,
				SaleOrderCode:              item.SaleOrderCode,
				FeeType:                    enum.FeeType.BIZ_HOUSEHOLD_TAX,

				TotalTaxRevenue: debit,
				VATRate:         vatRate,
				PITRate:         pitRate,

				TaxType:        enum.TaxReport.CancelledCreditNote,
				CreditNoteCode: item.TrackingCode,
				PenaltyBMFee:   int(*item.TotalTax),
			}
			creditItems = append(creditItems, reconcileItem)
		}
	}

	for _, item := range mapSODO {
		filter := model.ReconciliationItem{
			SellerCode:                 item.SellerCode,
			ReconcileScheduleTimeIndex: item.ReconcileScheduleTimeIndex,
			FeeType:                    item.FeeType,
			SaleOrderCode:              item.SaleOrderCode,
			TaxType:                    item.TaxType,
		}

		model.ReconciliationItemDB.Upsert(filter, item)

		time.Sleep(10 * time.Millisecond)
	}

	for _, item := range creditItems {
		filter := model.ReconciliationItem{
			SellerCode:                 item.SellerCode,
			ReconcileScheduleTimeIndex: item.ReconcileScheduleTimeIndex,
			FeeType:                    item.FeeType,
			SaleOrderCode:              item.SaleOrderCode,
			CreditNoteCode:             item.CreditNoteCode,
		}

		model.ReconciliationItemDB.Upsert(filter, item)
		time.Sleep(10 * time.Millisecond)
	}

	return nil
}

func getDistinctOrderIDs(orderIDs []int64) []int64 {
	orderIDMap := make(map[int64]bool, 0)

	for _, orderID := range orderIDs {
		orderIDMap[orderID] = true
	}

	result := make([]int64, 0)
	for orderID := range orderIDMap {
		result = append(result, orderID)
	}

	return result
}

func ProcessMigrateDeliveryOrderTax() {
	defer fmt.Println("MigrateDeliveryOrderTax finished")
	offset := 0
	limit := 1000
	_, total, errGetSellers := client.Services.Seller.GetSellerList("EXTERNAL", offset, 1)
	sellers := make([]*client.Seller, 0, total)
	var data []*client.Seller
	for errGetSellers == nil && offset < total {
		data, _, errGetSellers = client.Services.Seller.GetSellerList("EXTERNAL", offset, limit)
		offset += limit

		for _, seller := range data {
			if seller.SellerType == client.SellerTypes.MARKET || seller.SellerType == client.SellerTypes.BIZ_HOUSEHOLD {
				sellers = append(sellers, seller)
			}
		}
	}

	for _, seller := range sellers {
		taxRevenueItems := make([]*client.TaxReportItem, 0)

		ProcessMigrateBizHouseHoldTaxBySeller(seller.Code, "2024-01", &taxRevenueItems)
		fmt.Println("Done migrate for seller", seller.Code, "2024-01")

		ProcessMigrateBizHouseHoldTaxBySeller(seller.Code, "2024-02", &taxRevenueItems)
		fmt.Println("Done migrate for seller", seller.Code, "2024-02")

		if len(taxRevenueItems) == 0 {
			continue
		}

		// checkOrder := make([]int64, 0, len(taxRevenueItems))
		// for _, item := range taxRevenueItems {
		// 	checkOrder = append(checkOrder, item.OrderId)
		// }

		// fmt.Println("Process orders", checkOrder)

		var reconciliation *model.Reconciliation
		var err error

		recRes := model.ReconciliationDB.Query(model.Reconciliation{
			SellerCode:           seller.Code,
			ReconciliationStatus: model.ReconciliationStatus.Waiting,
		}, 0, 1, &primitive.M{"_id": -1})
		if recRes.Status == common.APIStatus.Ok {
			reconciliation = recRes.Data.([]*model.Reconciliation)[0]
		} else if recRes.Status == common.APIStatus.NotFound {
			reconciliation, err = CreateReconciliationFromTime(seller.Code, time.Now())
		}

		if err != nil || reconciliation == nil {
			model.LogReqDb.Create(&model.LogReq{
				ReqMethod:    "CREATE_RECONCILE",
				ReqURL:       "ERROR",
				ActionSource: nil,
				Data:         bson.M{"seller_code": seller.Code},
			})
			return
		}

		err = HandleReconcile(seller.Code, reconciliation.ReconcileScheduleTimeIndex, taxRevenueItems)
		if err != nil {
			// save log error
			model.LogReqDb.Create(&model.LogReq{
				ReqMethod:    "UPDATE_RECONCILE",
				ReqURL:       err.Error(),
				ActionSource: nil,
				Data:         bson.M{"seller_code": seller.Code},
			})
			return
		}
		fmt.Println("Done migrate for seller", seller.Code, "Final")
	}
}

func ProcessMigrateBizHouseHoldTaxBySeller(sellerCode, quarter string, taxRevenueItems *[]*client.TaxReportItem) {
	IdForm := ""

	for {
		items := client.Services.SellerReporting.GetListTaxReportItemByPage(sellerCode, quarter, IdForm)
		if len(items) == 0 {
			break
		}

		for _, item := range items {
			if item.Type != enum.TaxReport.DeliveryOrder {
				continue
			}

			// check if order is being reconciled or not
			reconciliationItem := model.ReconciliationItemDB.QueryOne(
				bson.M{
					"seller_code":                   sellerCode,
					"reconcile_schedule_time_index": bson.M{"$ne": nil},
					"fee_type":                      enum.FeeType.REVENUE,
					"order_id":                      item.OrderId,
				},
			)
			// if not reconcile yet -> do nothing
			if reconciliationItem.Status != common.APIStatus.Ok {
				continue
			}
			// if had
			// check if this tax item is already in reconciliation item or not
			reconciliationItem = model.ReconciliationItemDB.QueryOne(
				bson.M{
					"seller_code":                   sellerCode,
					"reconcile_schedule_time_index": bson.M{"$ne": nil},
					"fee_type":                      enum.FeeType.BIZ_HOUSEHOLD_TAX,
					"order_id":                      item.OrderId,
					"tax_type":                      "DELIVERY_ORDER",
				},
			)

			// if yes -> do nothing
			if reconciliationItem.Status == common.APIStatus.Ok {
				continue
			}
			// if not -> insert to reconciliation item
			*taxRevenueItems = append(*taxRevenueItems, item)
		}

		if len(items) < 1000 {
			break
		}

		lastID := items[len(items)-1].ID
		IdForm = lastID.String()
		time.Sleep(100 * time.Millisecond)
	}
}

func HandleReconcile(sellerCode, reconcileScheduleTimeIndex string, taxRevenueItems []*client.TaxReportItem) error {
	mapSODO := make(map[string]model.ReconciliationItem, 0)

	for _, item := range taxRevenueItems {
		if item.SaleOrderCode == "" || item.TotalTax == nil || item.Type != enum.TaxReport.DeliveryOrder {
			continue
		}

		var debit int64
		var vatRate, pitRate float64

		if item.Debit != nil {
			debit = *item.Debit
		}

		if item.VATRate != nil {
			vatRate = *item.VATRate
		}

		if item.PITRate != nil {
			pitRate = *item.PITRate
		}

		if soItem, ok := mapSODO[item.SaleOrderCode]; !ok {
			reconcileItem := model.ReconciliationItem{
				SellerCode:                 sellerCode,
				ReconcileScheduleTimeIndex: reconcileScheduleTimeIndex,
				OrderID:                    item.OrderId,
				SaleOrderCode:              item.SaleOrderCode,
				FeeType:                    enum.FeeType.BIZ_HOUSEHOLD_TAX,

				TotalTaxRevenue: debit,
				VATRate:         vatRate,
				PITRate:         pitRate,

				TaxType:           enum.TaxReport.DeliveryOrder,
				DeliveryOrderCode: []string{item.TrackingCode},
				PenaltyBMFee:      int(*item.TotalTax),
			}

			mapSODO[item.SaleOrderCode] = reconcileItem
		} else {
			soItem.DeliveryOrderCode = append(soItem.DeliveryOrderCode, item.TrackingCode)
			soItem.TotalTaxRevenue += debit
			soItem.PenaltyBMFee += int(*item.TotalTax)
			mapSODO[item.SaleOrderCode] = soItem
		}
	}

	for _, item := range mapSODO {
		filter := model.ReconciliationItem{
			SellerCode:                 item.SellerCode,
			ReconcileScheduleTimeIndex: item.ReconcileScheduleTimeIndex,
			FeeType:                    item.FeeType,
			SaleOrderCode:              item.SaleOrderCode,
			TaxType:                    item.TaxType,
		}

		model.ReconciliationItemDB.Upsert(filter, item)

		time.Sleep(10 * time.Millisecond)
	}

	value, err := CalculateReconciliation(sellerCode, reconcileScheduleTimeIndex)
	if err != nil {
		// save log
		return err
	}

	updater := model.Reconciliation{
		TotalBuyerFee:  &value.TotalBuyerFee,
		TotalRevenue:   &value.TotalRevenue,
		ListingFee:     &value.ListingFee,
		FulfillmentFee: &value.FulfillmentFee,
		PenaltyFee:     &value.PenaltyFee,
		BonusAmount:    &value.BonusAmount,
		TotalPayment:   &value.TotalPayment,
	}
	result := model.ReconciliationDB.UpdateOne(model.Reconciliation{SellerCode: sellerCode, ReconcileScheduleTimeIndex: reconcileScheduleTimeIndex}, updater)
	if result.Status != common.APIStatus.Ok {
		// save log
		return fmt.Errorf("update reconciliation error: %s", result.Message)
	}
	time.Sleep(10 * time.Millisecond)

	return nil
}
