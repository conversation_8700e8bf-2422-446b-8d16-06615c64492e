package api

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
)

func GetInvoicesForAdmin(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *model.ReqGetInvoiceList
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	response := action.GetInvoicesBySeller(input)
	return res.Respond(response)
}

func GetInvoicesBySeller(req sdk.APIRequest, res sdk.APIResponder) error {
	sellerCode := strings.ToUpper(req.GetVar("sellerCode"))
	var input *model.ReqGetInvoiceList
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	input.SellerCode = sellerCode
	response := action.GetInvoicesBySeller(input)
	return res.Respond(response)
}

func GetInvoiceDetailBySeller(req sdk.APIRequest, res sdk.APIResponder) error {
	sellerCode := strings.ToUpper(req.GetVar("sellerCode"))
	getInvoiceItems := req.GetParam("getInvoiceItems") == "true"
	var input *model.Invoice
	err := json.Unmarshal([]byte(req.GetParam("q")), &input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}
	input.SellerCode = sellerCode

	response := action.GetInvoiceDetail(input, getInvoiceItems)
	return res.Respond(response)
}

func GetSellerInvoiceByMailProcessor(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *model.Invoice
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	response := action.GetSellerInvoiceByMailProcessor(input)
	return res.Respond(response)
}

func UploadInvoiceDocumentByMailProcessor(req sdk.APIRequest, res sdk.APIResponder) error {
	var input model.Invoice
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	response := action.UploadInvoiceDocumentByMailProcessor(&input)
	return res.Respond(response)
}

func InvoiceCallbackFromMailProcessor(req sdk.APIRequest, res sdk.APIResponder) error {
	var input model.Invoice
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	response := action.InvoiceCallbackFromMailProcessor(&input)
	return res.Respond(response)
}

func DownloadFile(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *model.ReqGetInvoiceList
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	response := action.DownloadFile(input)
	return res.Respond(response)
}

func SellerDownloadFile(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *model.ReqGetInvoiceList
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	response := action.SellerDownloadFile(input)
	return res.Respond(response)
}

func UpdateInvoiceToCompleted(req sdk.APIRequest, resp sdk.APIResponder) error {
	var invoiceIDStr = req.GetParam("invoiceID")
	if invoiceIDStr == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "InvoiceID must not be empty",
			ErrorCode: "MISSING_INVOICEID",
		})
	}

	invoiceID, err := strconv.Atoi(invoiceIDStr)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Error converting %s: %s", invoiceIDStr, err),
			ErrorCode: "INVALID_INVOICEID",
		})
	}

	return resp.Respond(action.UpdateInvoiceToCompleted(int64(invoiceID)))
}

func DeleteInvoice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var invoiceIDStr = req.GetParam("invoiceID")
	if invoiceIDStr == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "InvoiceID must not be empty",
			ErrorCode: "MISSING_INVOICEID",
		})
	}

	invoiceID, err := strconv.Atoi(invoiceIDStr)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Error converting %s: %s", invoiceIDStr, err),
			ErrorCode: "INVALID_INVOICEID",
		})
	}

	return resp.Respond(action.DeleteInvoice(int64(invoiceID)))
}

func SellerInvoiceReturnTicket(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InvoiceReturnTicket
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "PAYLOAD_IVALID",
		})
	}

	return resp.Respond(action.UpdateInvoiceReturnTickets(&input))
}

func FineLateInvoice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Invoice
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	as := action.GetActionSource(req)
	if as == nil || as.Account == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Missing X-Source",
			ErrorCode: "MISSING_SOURCE",
		})
	}

	return resp.Respond(action.FineLateInvoice(&input, as))
}

func ForceUpdateOrderInvoice(req sdk.APIRequest, resp sdk.APIResponder) error {
	areYouSure := req.GetParam("areYouSure") == "true"
	if !areYouSure {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Are you sure?",
			ErrorCode: "ARE_YOU_SURE",
		})
	}

	var input model.UpdateInvoiceRequest
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vui lòng kiểm tra lại thông tin " + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}
	if err := model.Checker.Validate(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   err.Error(),
			ErrorCode: "PAYLOAD_VALIDATE",
		})
	}

	return resp.Respond(action.ForceUpdateOrderInvoice(&input))
}

func InvoiceV2Callback(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InvoiceV2Callback
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: "PAYLOAD_INVALID",
		})
	}

	// log request
	model.LogReqDb.Create(model.LogReq{
		ReqMethod: "PUT",
		ReqURL:    "/marketplace/order/v2/seller/invoice/callback",
		Data:      req.GetContentText(),
	})

	return resp.Respond(action.CallbackInvoiceV2(input))
}
