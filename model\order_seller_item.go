package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// OrderSellerItem ...
type OrderSellerItem struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// reference data
	OrderSellerID     int64                  `json:"orderSellerId,omitempty" bson:"order_seller_id,omitempty"`
	OrderSellerCode   string                 `json:"orderSellerCode,omitempty" bson:"order_seller_code,omitempty"`
	Sku               string                 `json:"sku,omitempty" bson:"sku,omitempty"`
	Quantity          int                    `json:"quantity,omitempty" bson:"quantity,omitempty"`
	ReservedQuantity  *int                   `json:"reservedQuantity,omitempty" bson:"reserved_quantity,omitempty"`
	OutboundQuantity  *int                   `json:"outboundQuantity,omitempty" bson:"outbound_quantity,omitempty"`
	CompletedQuantity *int                   `json:"completedQuantity,omitempty" bson:"completed_quantity,omitempty"`
	ReturnQuantity    *int                   `json:"returnQuantity,omitempty" bson:"return_quantity,omitempty"`
	MaxQuantity       int                    `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`
	IsImportant       *bool                  `json:"isImportant,omitempty" bson:"is_important,omitempty"`
	Type              enum.ItemTypeValue     `json:"type,omitempty" bson:"type,omitempty"`
	DealCode          *string                `json:"dealCode,omitempty" bson:"deal_code,omitempty"`
	SellerCode        string                 `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerClass       *enum.SellerClassValue `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`
	ProductCode       string                 `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID         int64                  `json:"productID,omitempty" bson:"product_id,omitempty"`
	Price             int                    `json:"price,omitempty" bson:"price,omitempty"`
	TotalPrice        int                    `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
	ActualPrice       *int                   `json:"actualPrice,omitempty" bson:"actual_price,omitempty"`
	SellerPrice       int                    `json:"sellerPrice,omitempty" bson:"seller_price,omitempty"`
	TotalSellerPrice  int                    `json:"totalSellerPrice,omitempty" bson:"total_seller_price,omitempty"`
	ActualSellerPrice *int                   `json:"actualSellerPrice,omitempty" bson:"actual_seller_price,omitempty"`
	TotalActualPrice  int                    `json:"totalActualPrice,omitempty" bson:"total_actual_price,omitempty"`
	VAT               *float64               `json:"vat,omitempty" bson:"vat,omitempty"`

	UpdatedProcessingQuantity *bool `json:"-" bson:"updated_processing_quantity,omitempty"` // tracking if missing processing-status

	ComplexQuery   []*bson.M `json:"-" bson:"$and,omitempty"`
	ComplexQueryOr []*bson.M `json:"-" bson:"$or,omitempty"`
}
