package reconcile_action

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
)

func Apply3DaysReconcile(seller *client.Seller, appliedBefore time.Time) bool {
	if seller.ReconcileInfo != nil &&
		seller.ReconcileInfo.ThreeDaysReconcile &&
		seller.ReconcileInfo.ApplyThreeDaysReconcile != nil &&
		seller.ReconcileInfo.ApplyThreeDaysReconcile.Before(appliedBefore) {
		return true
	}

	return false
}
