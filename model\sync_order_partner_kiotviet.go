package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

type PartnerKiotvietConfigs struct {
	MyAccountPartnerTab          int    `json:"myAccountPartnerTab,omitempty" bson:"my_account_partner_tab,omitempty"`
	KiotVietKey                  string `json:"kiotvietKey,omitempty" bson:"kiotviet_key,omitempty"`
	JWTSecretKeyForKiotViet      string `json:"jwtSecretKeyForKiotviet,omitempty" bson:"jwt_secret_key_for_kiotviet,omitempty"`
	ScrectKeySH256ForKiotViet    string `json:"screctKeySH256ForKiotviet,omitempty" bson:"screct_key_sh256_for_kiotviet,omitempty"`
	URLKiotViet                  string `json:"urlKiotViet,omitempty" bson:"url_kiotviet,omitempty"`
	URLKiotVietRedirectLogin     string `json:"urlKiotVietRedirectLogin,omitempty" bson:"url_kiotviet_redirect_login,omitempty"`
	AllowMappingInfoKiotViet     bool   `json:"allowMappingInfoKiotViet,omitempty" bson:"allow_mapping_info_kiotviet,omitempty"`
	AllowSyncCreateOrderKiotviet bool   `json:"allowSyncCreateOrderKiotviet,omitempty" bson:"allow_sync_create_order_kiotviet,omitempty"`
	AllowSyncUpdateOrderKiotviet bool   `json:"allowSyncUpdateOrderKiotviet,omitempty" bson:"allow_sync_update_order_kiotviet,omitempty"`
}

type Combo struct {
	SubProductSKU          string `json:"sub_product_sku,omitempty" bson:"sub_product_sku,omitempty"`
	SubProductCode         string `json:"sub_product_code,omitempty" bson:"sub_product_code,omitempty"`
	SubProductName         string `json:"sub_product_name,omitempty" bson:"sub_product_name,omitempty"`
	SubProductUnit         string `json:"sub_product_unit,omitempty" bson:"sub_product_unit,omitempty"`
	SubProductQuantity     int    `json:"sub_product_quantity,omitempty" bson:"sub_product_quantity,omitempty"`
	SubProductPrice        int    `json:"sub_product_price,omitempty" bson:"sub_product_price,omitempty"`
	SubProductDeliveredQty *int   `json:"sub_product_delivered_qty,omitempty" bson:"sub_product_delivered_qty,omitempty"`
	SubProductReturnedQty  *int   `json:"sub_product_returned_qty,omitempty" bson:"sub_product_returned_qty,omitempty"`
	SubProductLotDate      string `json:"sub_product_lot_date,omitempty" bson:"sub_product_lot_date,omitempty"`
}

type OrderItemCreate struct {
	ProductSKU   string  `json:"product_sku" bson:"product_sku"`
	ProductCode  string  `json:"product_code" bson:"product_code"`
	ProductName  string  `json:"product_name" bson:"product_name"`
	Quantity     int     `json:"quantity" bson:"quantity"`
	Unit         string  `json:"unit" bson:"unit"`
	RawItemPrice int     `json:"raw_item_price" bson:"raw_item_price"` // Giá trước khuyến mãi
	ItemPrice    int     `json:"item_price" bson:"item_price"`         // Giá sau khuyến mãi (giá sau chiết khấu)
	Discount     int     `json:"discount" bson:"discount"`
	Combo        []Combo `json:"combo" bson:"combo"`
}

type OrderItemUpdate struct {
	ProductSKU   string  `json:"product_sku" bson:"product_sku"`
	ProductCode  string  `json:"product_code" bson:"product_code"`
	DeliveredQty *int    `json:"delivered_qty" bson:"delivered_qty"`
	ReturnedQty  *int    `json:"returned_qty" bson:"returned_qty"`
	LotDate      string  `json:"lot_date" bson:"lot_date"`
	Combo        []Combo `json:"combo" bson:"combo"`
}

type OrderCreateData struct {
	OrderID       string            `json:"order_id" bson:"order_id"`
	BmID          string            `json:"bm_id" bson:"bm_id"`
	Note          string            `json:"note" bson:"note"`
	TotalPrice    int               `json:"total_price" bson:"total_price"`       // Tổng tiền thanh toán đơn hàng (chưa bao gồm phụ phí)
	TotalDiscount int               `json:"total_discount" bson:"total_discount"` // Tổng giá trị giảm giá đơn hàng = Giảm giá voucher (quy về hết voucher)
	RawTotal      int               `json:"raw_total" bson:"raw_total"`           // Tổng tiền trước khi trừ khuyến mãi
	CreatedTime   time.Time         `json:"created_time" bson:"created_time"`
	Province      string            `json:"province" bson:"province"`
	District      string            `json:"district" bson:"district"`
	Items         []OrderItemCreate `json:"items" bson:"items"`
	Signature     string            `json:"signature" bson:"signature"`
}

type OrderUpdateData struct {
	OrderID         string               `json:"order_id" bson:"order_id"`
	BmID            string               `json:"bm_id" bson:"bm_id"`
	DeliveryStatus  string               `json:"delivery_status" bson:"delivery_status"`
	OrderStatus     enum.OrderStateValue `json:"order_status" bson:"order_status"`
	LastUpdatedTime string               `json:"last_updated_time" bson:"last_updated_time"`
	Items           []OrderItemUpdate    `json:"items" bson:"items"`
	Signature       string               `json:"signature" bson:"signature"`
}

type SyncOrderCreatePayload struct {
	KvID        string          `json:"kv_id" bson:"kv_id"`
	PartnerCode string          `json:"partner_code" bson:"partner_code"`
	Action      string          `json:"action" bson:"action"`
	Data        OrderCreateData `json:"data" bson:"data"`
	Time        string          `json:"time" bson:"time"`
}

type SyncOrderUpdatePayload struct {
	KvID        string          `json:"kv_id" bson:"kv_id"`
	PartnerCode string          `json:"partner_code" bson:"partner_code"`
	Action      string          `json:"action" bson:"action"`
	Data        OrderUpdateData `json:"data" bson:"data"`
	Time        string          `json:"time" bson:"time"`
}

type SyncOrderCreateInfoLog struct {
	ID          *primitive.ObjectID     `json:"id,omitempty" bson:"_id,omitempty"`
	CreatedTime *time.Time              `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CustomerID  int64                   `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	OrderID     int64                   `json:"orderID,omitempty" bson:"order_id,omitempty"`
	RequestData *SyncOrderCreatePayload `json:"requestData,omitempty" bson:"request_data,omitempty"`
	Action      string                  `json:"action,omitempty" bson:"action,omitempty"`

	//Response
	RequestID string `json:"request_id,omitempty" bson:"request_id,omitempty"`
	Success   bool   `json:"success,omitempty" bson:"success"`
	ErrorCode string `json:"error_code,omitempty" bson:"error_code,omitempty"`
}

type SyncOrderUpdateInfoLog struct {
	ID          *primitive.ObjectID     `json:"id,omitempty" bson:"_id,omitempty"`
	CreatedTime *time.Time              `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	CustomerID  int64                   `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	OrderID     int64                   `json:"orderID,omitempty" bson:"order_id,omitempty"`
	RequestData *SyncOrderUpdatePayload `json:"requestData,omitempty" bson:"request_data,omitempty"`
	Action      string                  `json:"action,omitempty" bson:"action,omitempty"`

	//Response
	RequestID string `json:"request_id,omitempty" bson:"request_id,omitempty"`
	Success   bool   `json:"success,omitempty" bson:"success"`
	ErrorCode string `json:"error_code,omitempty" bson:"error_code,omitempty"`
}

type ReconciliationOrderKiotviet struct {
	OrderID      int64              `json:"orderId,omitempty" bson:"-"`
	CustomerID   int64              `json:"customerId,omitempty" bson:"-"`
	LastID       primitive.ObjectID `json:"lastID,omitempty" bson:"-"`
	StartTime    string             `json:"startTime,omitempty" bson:"-"`
	EndTime      string             `json:"endTime,omitempty" bson:"-"`
	ComplexQuery []*bson.M          `json:"-" bson:"$and,omitempty"`
	SortType     string             `json:"sortType,omitempty" bson:"-"`
}

type LatestOrderData struct {
	OrderID         string                `json:"order_id" bson:"order_id"`
	BmID            string                `json:"bm_id" bson:"bm_id"`
	Note            string                `json:"note" bson:"note"`
	TotalPrice      int                   `json:"total_price" bson:"total_price"`       // Tổng tiền thanh toán đơn hàng (chưa bao gồm phụ phí)
	TotalDiscount   int                   `json:"total_discount" bson:"total_discount"` // Tổng giá trị giảm giá đơn hàng = Giảm giá voucher (quy về hết voucher)
	RawTotal        int                   `json:"raw_total" bson:"raw_total"`           // Tổng tiền trước khi trừ khuyến mãi
	CreatedTime     time.Time             `json:"created_time" bson:"created_time"`
	Province        string                `json:"province" bson:"province"`
	District        string                `json:"district" bson:"district"`
	DeliveryStatus  string                `json:"delivery_status" bson:"delivery_status"`
	OrderStatus     enum.OrderStateValue  `json:"order_status" bson:"order_status"`
	LastUpdatedTime string                `json:"last_updated_time" bson:"last_updated_time"`
	Items           []LatestOrderItemInfo `json:"items" bson:"items"`
	Signature       string                `json:"signature" bson:"signature"`
}

type LatestOrderItemInfo struct {
	ProductSKU   string  `json:"product_sku" bson:"product_sku"`
	ProductCode  string  `json:"product_code" bson:"product_code"`
	ProductName  string  `json:"product_name" bson:"product_name"`
	Quantity     int     `json:"quantity" bson:"quantity"`
	Unit         string  `json:"unit" bson:"unit"`
	RawItemPrice int     `json:"raw_item_price" bson:"raw_item_price"` // Giá trước khuyến mãi
	ItemPrice    int     `json:"item_price" bson:"item_price"`         // Giá sau khuyến mãi (giá sau chiết khấu)
	Discount     int     `json:"discount" bson:"discount"`
	DeliveredQty *int    `json:"delivered_qty" bson:"delivered_qty"`
	ReturnedQty  *int    `json:"returned_qty" bson:"returned_qty"`
	LotDate      string  `json:"lot_date" bson:"lot_date"`
	Combo        []Combo `json:"combo" bson:"combo"`
}

type LatestOrderStatusPartnerKiotviet struct {
	ID         *primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	Action     string              `json:"action,omitempty" bson:"action,omitempty"`
	OrderID    int64               `json:"orderId,omitempty" bson:"order_id,omitempty"`
	CustomerID int64               `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	KvID       int64               `json:"kvId,omitempty" bson:"kv_id,omitempty"`
	Data       *LatestOrderData    `json:"data,omitempty" bson:"data,omitempty"`
}

var SyncOrderPartnerKiotvietDB = &db.Instance{
	ColName:        "sync_order_partnser_kiotviet",
	TemplateObject: &SyncOrderCreateInfoLog{},
}

var LatestOrderStatusKiotvietDB = &db.Instance{
	ColName:        "latest_order_status_kiotviet",
	TemplateObject: &LatestOrderStatusPartnerKiotviet{},
}

func InitSyncOrderPartnerKiotvietModel(s *mongo.Database) {
	SyncOrderPartnerKiotvietDB.ApplyDatabase(s)
	LatestOrderStatusKiotvietDB.ApplyDatabase(s)
	// t := true

	// _ = SyncOrderPartnerKiotvietDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "customer_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = SyncOrderPartnerKiotvietDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "customer_id", Value: 1},
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = SyncOrderPartnerKiotvietDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
