package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type RunOne struct {
	ID          *primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	CreatedTime *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`

	Key string `json:"key,omitempty" bson:"key,omitempty"`
}

// // RunOneDB represent DB repo of this model
var RunOneDB = &db.Instance{
	ColName:        "run_one",
	TemplateObject: &RunOne{},
}

func InitRunOneDBModel(s *mongo.Database) {
	RunOneDB.ApplyDatabase(s)

	// t := true
	// RunOneDB.CreateIndex(bson.D{
	// 	primitive.E{"key", 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// expiredTime := int32(60 * 60 * 24 * 30)
	// RunOneDB.CreateIndex(bson.D{
	// 	primitive.E{"created_time", 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &expiredTime,
	// })
}
