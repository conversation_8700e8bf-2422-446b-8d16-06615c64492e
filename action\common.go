package action

import "gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"

var (
	PARTNER_PAYMENT_METHOD_HARDCODE = []string{
		string(enum.PaymentMethod.ATM),
		string(enum.PaymentMethod.MASTERCARD),
		string(enum.PaymentMethod.VISA),
		string(enum.PaymentMethod.CREDIT_DEBIT),

		string(enum.PaymentMethod.QR),

		string(enum.PaymentMethod.MOMO),
		string(enum.PaymentMethod.VIETQR),
		string(enum.PaymentMethod.ZALOPAY),
	}
	PAYMENT_METHOD_ORDER_TRANSFER_HARDCODE = []string{
		string(enum.PaymentMethod.BANK),

		string(enum.PaymentMethod.ATM),
		string(enum.PaymentMethod.MASTERCARD),
		string(enum.PaymentMethod.VISA),
		string(enum.PaymentMethod.CREDIT_DEBIT),

		string(enum.PaymentMethod.QR),

		string(enum.PaymentMethod.MOMO),
		string(enum.PaymentMethod.VIETQR),
		string(enum.PaymentMethod.ZALOPAY),
	}

	PARTNER_PAYMENT_METHOD        = PARTNER_PAYMENT_METHOD_HARDCODE
	PAYMENT_METHOD_ORDER_TRANSFER = PAYMENT_METHOD_ORDER_TRANSFER_HARDCODE
)

func IsOrderPaymentMethodContains(arr []enum.PaymentMethodValue, key enum.PaymentMethodValue) bool {
	if len(arr) == 0 {
		return false
	}

	for i := range arr {
		if arr[i] == key {
			return true
		}
	}

	return false
}

func IsContainsT[T comparable](arr []T, key T) bool {
	if len(arr) == 0 {
		return false
	}

	for i := range arr {
		if arr[i] == key {
			return true
		}
	}

	return false
}

func IsValidPaymentMethodEnumValue(value string) bool {
	temp := append(PAYMENT_METHOD_ORDER_TRANSFER, string(enum.PaymentMethod.COD), string(enum.PaymentMethod.CREDIT))
	for i := range temp {
		if temp[i] == value {
			return true
		}
	}
	return false
}
