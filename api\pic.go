package api

import (
	"encoding/json"
	"strconv"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

func GetCartByPic(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q          = req.GetParam("q")
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		skuCode    = req.GetParam("skuCode")
		getTotal   = req.GetParam("getTotal") == "true"
		hasVoucher = req.GetParam("hasVoucher") == "true"
	)
	if limit < 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}
	var query = model.Cart{}
	if len(q) > 0 {
		json.Unmarshal([]byte(q), &query)
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetCartListByPic(&query, skuCode, offset, limit, getTotal, hasVoucher, *acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Your account is not authorized to perform this action",
	})
}

func GetOrderByPic(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		q          = req.GetParam("q")
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
		skuCode    = req.GetParam("skuCode")
		sort       = req.GetParam("sort")
		hasVoucher = req.GetParam("hasVoucher") == "true"
		ids        = req.GetParam("ids")
	)

	if limit < 0 {
		limit = 20
	}

	if limit > 100 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	checkOffsetResp := verifyOffset(req, offset)
	if checkOffsetResp.Status != common.APIStatus.Ok {
		return resp.Respond(checkOffsetResp)
	}

	var query = model.Order{}
	if len(q) > 0 {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			})
		}
	}

	if len(ids) > 0 {
		idsArr := strings.Split(ids, ",")
		listIDs := []int64{}
		for _, id := range idsArr {
			intID, _ := strconv.Atoi(id)
			if intID > 0 {
				listIDs = append(listIDs, int64(intID))
			}
		}
		query.ComplexQuery = []*bson.M{
			{
				"order_id": &bson.M{
					"$in": listIDs,
				},
			},
		}
	}

	if string(query.Status) == "-WAIT_TO_CONFIRM" {
		query.Status = enum.OrderState.WaitConfirm
	}

	if query.StartLastUpdatedTime != nil && query.EndLastUpdatedTime != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"last_updated_time": bson.M{
					"$gte": query.StartLastUpdatedTime,
					"$lte": query.EndLastUpdatedTime,
				},
			})
	}

	if query.StartCompletedTime != nil && query.EndCompletedTime != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"completed_time": bson.M{
					"$gte": query.StartCompletedTime,
					"$lte": query.EndCompletedTime,
				},
			})
	}

	if query.StartCreatedTime != nil {
		query.ComplexQuery = append(
			query.ComplexQuery,
			&bson.M{
				"created_time": bson.M{
					"$gte": query.StartCreatedTime,
				},
			})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.GetOrderListByPic(getActionSource(req), &query, skuCode, offset, limit, getTotal, hasVoucher, sort))

	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Your account is not authorized to perform this action",
	})
}
