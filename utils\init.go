package utils

import (
	"time"
)

var VNTimeZone = time.FixedZone("", +7*60*60)

func GetTimeVersionYYYYMMDD(t time.Time) string {
	// parse to time zone +7
	tz := time.FixedZone("UTC+7", +7*60*60)
	ictTime := t.In(tz)
	return ictTime.Format("2006-01-02")
}

// from YYYY-MM-DD to DD/MM/YYYY
func FormatTimeString(str string) string {
	time, _ := ParseToTime(str)
	return TransferToString(time)
}

// result as DD/MM/YYYY
func TransferToString(time time.Time) string {
	return time.Format("02/01/2006")
}

// str format YYYY-MM-DD
func ParseToTime(str string) (time.Time, error) {
	return time.Parse("2006-01-02", str)
}
