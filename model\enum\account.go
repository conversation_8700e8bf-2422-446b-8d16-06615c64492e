package enum

type accountType struct {
	CUSTOMER    string
	EMPLOYEE    string
	SELLER      string
	PARTNER     string
	BRAND_SALES string
}

var AccountType = &accountType{
	"CUSTOMER",
	"EMPLOYEE",
	"SELLER",
	"PARTNER",
	"BRAND_SALES",
}

// ColorValue ...
type CustomerColorValue string

type color struct {
	Purple CustomerColorValue //Khach hang VIP - Cong no
	Blue   CustomerColorValue //Khach hang VIP
	Green  CustomerColorValue //Khach hang binh thuong
	Yellow CustomerColorValue //Khach hang co lich su lam dung he thong
	Orange CustomerColorValue //Khach hang boom hang hoac huy don nhieu lan
	Red    CustomerColorValue //Khach hang quay roi he thong hoac tuyen truyen thong tin sai lech ve Thuocsi...
}

// CustomerColor ...
var CustomerColor = &color{
	"PURPLE",
	"BLUE",
	"GRE<PERSON>",
	"Y<PERSON>L<PERSON>",
	"ORANGE",
	"RED",
}

// CustomerTagValue ...
type CustomerTagValue string

type customerTag struct {
	VipDebt   CustomerTagValue
	Vip       CustomerTagValue
	Complain  CustomerTagValue
	BlockCod  CustomerTagValue
	BlockBank CustomerTagValue
	Ban       CustomerTagValue
	Circa     CustomerTagValue
	Clinic    CustomerTagValue
}

// CustomerTag ...
var CustomerTag = &customerTag{
	"VIP_DEBT",
	"VIP",
	"COMPLAIN",
	"BLOCK_COD",
	"BLOCK_BANK",
	"BAN",
	"CIRCA",
	"CLINIC",
}
