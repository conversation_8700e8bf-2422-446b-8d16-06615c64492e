package tool

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func AddScheduleConfig(req sdk.APIRequest, res sdk.APIResponder) error {
	var configs []bson.M
	err := req.GetContent(&configs)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.<PERSON>rror(),
		})
	}

	results := make([]*common.APIResponse, 0, len(configs))
	db := model.ConfigSchedule.GetConfigDB()
	for _, config := range configs {
		topic := config["topic"].(string)
		if topic == "" {
			continue
		}

		filter := schedule.Config{
			Topic: topic,
		}
		updater := schedule.Config{
			Topic: topic,
		}
		if next := config["next_run"].(string); next != "" {
			t, _ := time.Parse(time.RFC3339, next)
			updater.NextRun = &t
		}
		result := db.Upsert(filter, updater)
		if result.Status == common.APIStatus.Ok {
			results = append(results, result)
		}
	}

	return res.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   results,
	})
}

func RemoveScheduleConfig(req sdk.APIRequest, res sdk.APIResponder) error {
	var configs []bson.M
	err := req.GetContent(&configs)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	results := make([]*common.APIResponse, 0, len(configs))
	db := model.ConfigSchedule.GetConfigDB()
	for _, config := range configs {
		result := db.Delete(config)
		if result.Status == common.APIStatus.Ok {
			results = append(results, result)
		}
	}

	return res.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   results,
	})
}

type InputTriggerSchedule struct {
	Topic                      string     `json:"topic,omitempty"`
	Time                       *time.Time `json:"time,omitempty"`
	ReconcileScheduleTimeIndex string     `json:"reconcile_schedule_time_index,omitempty"`

	SpecSellers []string `json:"spec_sellers"`
}

func TriggerSchedule(req sdk.APIRequest, res sdk.APIResponder) error {
	var input *InputTriggerSchedule
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	if input.Time == nil || input.Time.IsZero() {
		now := time.Now()
		input.Time = &now
	}

	go sdk.Execute(
		func() {
			if input.ReconcileScheduleTimeIndex != "" {
				topic, startDate := reconcile_action.ConvertTimeIndexToSchedule(input.ReconcileScheduleTimeIndex)
				input.Topic = topic
				input.Time = startDate
			}

			note, prcErr := ProcessBySchedule(input.Topic, *input.Time, input.SpecSellers)
			if prcErr != nil {
				println("err:", prcErr.Error())
			}
			if note != "" {
				println("note:", note)
			}
		},
	)

	return res.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Trigger process successfully",
	})
}

func AddSpecRevenue(req sdk.APIRequest, res sdk.APIResponder) error {
	var input []model.ReconciliationSpecRevenue
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	s := req.GetParam("schedule")
	if s == "" {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "schedule",
		})
	}

	for _, line := range input {
		line.Schedule = s
		model.ReconciliationSpecRevenueDB.Upsert(model.ReconciliationSpecRevenue{
			OrderID:    line.OrderID,
			ProductID:  line.ProductID,
			Sku:        line.Sku,
			SellerCode: line.SellerCode,
			Schedule:   line.Schedule,
		}, line)
	}

	return GetSpecRevenue(req, res)
}

func RemoveSpecRevenue(req sdk.APIRequest, res sdk.APIResponder) error {
	var input []model.ReconciliationSpecRevenue
	err := req.GetContent(&input)
	if err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	s := req.GetParam("schedule")
	if s == "" {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "schedule",
		})
	}

	all := req.GetParam("all") == "true"
	if all {
		model.ReconciliationSpecRevenueDB.Delete(model.ReconciliationSpecRevenue{Schedule: s})
	} else {
		for _, line := range input {
			model.ReconciliationSpecRevenueDB.Delete(line)
		}
	}

	return GetSpecRevenue(req, res)
}

func GetSpecRevenue(req sdk.APIRequest, res sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
		s        = req.GetParam("schedule")
	)

	var query model.ReconciliationSpecRevenue
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return res.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})

		}
	}

	if s != "" {
		query.Schedule = s
	}

	result := model.ReconciliationSpecRevenueDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal == true {
		result.Total = model.ReconciliationSpecRevenueDB.Count(query).Total
	}
	return res.Respond(result)
}
