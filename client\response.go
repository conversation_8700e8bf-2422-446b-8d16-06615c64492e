package client

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// BaseAPIResponse ...
type BaseAPIResponse struct {
	Status    string `json:"status"`
	Message   string `json:"message"`
	ErrorCode string `json:"errorCode,omitempty"`
	Total     int64  `json:"total,omitempty"`
}

// PromoResponse is resp from promo client
type PromoResponse struct {
	BaseAPIResponse
	Data []*model.PromoApply `json:"data"`
}

// VoucherActiveResponse ...
type VoucherActiveResponse struct {
	BaseAPIResponse
	Data []*model.VoucherActive `json:"data"`
}

// CustomerResponse is resp from customer client
type CustomerResponse struct {
	BaseAPIResponse
	Data []*model.Customer `json:"data"`
}

// SellerResponse is resp from customer client
type SellerResponse struct {
	BaseAPIResponse
	Data []*Seller `json:"data"`
}

type SellerStoreResponse struct {
	BaseAPIResponse
	Data []*SellerStore `json:"data"`
}

type SellerConfigResponse struct {
	BaseAPIResponse
	Data []*SellerConfig `json:"data"`
}

type SellerReconciliationFeeConfigResponse struct {
	BaseAPIResponse
	Data []SellerReconciliationFeeConfig `json:"data"`
}

type LatestPutResponse struct {
	BaseAPIResponse
	Data []LatestPutInfo `json:"data"`
}

type PutTicketResponse struct {
	BaseAPIResponse
	Data []PutTicket `json:"data"`
}

// Seller ...
type Seller struct {
	SellerID     int64                  `json:"sellerID" bson:"seller_id,omitempty"`
	AccountID    int64                  `json:"accountID,omitempty"`
	Code         string                 `json:"code" bson:"code,omitempty"`
	AppliedLevel string                 `json:"-" bson:"applied_level,omitempty"`
	Level        *enum.LevelSellerValue `json:"level" bson:"level,omitempty"`
	LevelInfo    LevelInfo              `json:"levelInfo" bson:"level_info,omitempty"`
	HiloInfo     *HiloInfo              `json:"hiloInfo,omitempty" bson:"hilo_info,omitempty"`
	IsVip        bool                   `json:"isVip,omitempty" bson:"is_vip,omitempty"`
	DisplayName  string                 `json:"displayName,omitempty" bson:"display_name,omitempty"`
	Email        string                 `json:"email,omitempty" bson:"email,omitempty"`
	Name         string                 `json:"name,omitempty" bson:"name,omitempty"`
	EntityID     int64                  `json:"entityID,omitempty" bson:"entity_id,omitempty"`

	SellerType SellerType `json:"sellerType" bson:"seller_type,omitempty"`

	InvoiceInfo   *InvoiceInfo   `json:"invoiceInfo,omitempty" bson:"-"`
	ConfigInfo    *SellerConfig  `json:"configInfo,omitempty" bson:"-"`
	ReconcileInfo *ReconcileInfo `json:"reconcileInfo,omitempty" bson:"-"`
}

type LevelInfo struct {
	Date      time.Time `json:"date" bson:"date,omitempty"`
	LevelPrev string    `json:"levelPrev" bson:"level_prev,omitempty"`
}

type SellerType string
type sellerTypes struct {
	INDIVIDUAL    SellerType
	ENTERPRISE    SellerType
	MARKET        SellerType
	BIZ_HOUSEHOLD SellerType
	// vendor
	TRADING     SellerType
	NON_TRADING SellerType
}

var SellerTypes = sellerTypes{
	INDIVIDUAL:    "INDIVIDUAL",
	ENTERPRISE:    "ENTERPRISE",
	MARKET:        "MARKET",
	BIZ_HOUSEHOLD: "BIZ_HOUSEHOLD",

	TRADING:     "TRADING",
	NON_TRADING: "NON-TRADING",
}

type HiloInfo struct {
	IsActive            bool                 `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Status              enum.HiloStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	SignedBy            enum.SignedByValue   `json:"signedBy,omitempty" bson:"signed_by,omitempty"`
	Pattern             string               `json:"pattern,omitempty" bson:"pattern,omitempty"`
	Series              string               `json:"series,omitempty" bson:"series,omitempty"`
	InvoiceTemplateCode string               `json:"invoiceTemplateCode,omitempty" bson:"invoice_template_code,omitempty"`

	ExportBuyerNoRequest bool `json:"exportBuyerNoRequest,omitempty" bson:"export_buyer_no_request,omitempty"`
}

type SellerReconciliationFeeConfig struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SellerID              int64   `json:"sellerID" bson:"seller_id,omitempty"`
	SellerCode            string  `json:"sellerCode" bson:"seller_code,omitempty"`
	SaleFeePercent        float64 `json:"saleFeePercent,omitempty" bson:"sale_fee_percent,omitempty"`
	FulfillmentFeePercent float64 `json:"fulfillmentFeePercent,omitempty" bson:"fulfillment_fee_percent,omitempty"`
}

func (conf *SellerReconciliationFeeConfig) CanApply(seller *Seller) bool {
	// if conf.SaleFeePercent == 0 && conf.FulfillmentFeePercent == 0 {
	// 	return false
	// }
	return conf.SellerCode == seller.Code || (conf.SellerCode == "OTHER_VIP" && seller.IsVip)
}

// setting_for_sc
type SettingForSC struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code      string                   `json:"code,omitempty" bson:"code,omitempty"`
	Data      []map[string]interface{} `json:"data,omitempty" bson:"data,omitempty"`
	IsEnabled *bool                    `json:"isEnabled,omitempty" bson:"is_enabled,omitempty"`
}

type SettingForSCResponse struct {
	BaseAPIResponse
	Data []SettingForSC `json:"data"`
}

type ReconciliationScheduleSettingResponse struct {
	BaseAPIResponse
	Data []*ReconciliationScheduleSetting `json:"data"`
}

type ReconciliationScheduleSetting struct {
	Index        string                 `json:"index,omitempty" bson:"index,omitempty"`
	Schedule     ReconciliationSchedule `json:"schedule,omitempty" bson:"schedule,omitempty"`
	Setting      ScheduleSetting        `json:"setting,omitempty" bson:"setting,omitempty"`
	NextSchedule ReconciliationSchedule `json:"nextSchedule,omitempty" bson:"nextSchedule,omitempty"`
}

type ReconciliationSchedule struct {
	Year     int64                  `json:"year,omitempty" bson:"year,omitempty"`
	Month    int64                  `json:"month,omitempty" bson:"month,omitempty"`
	Type     enum.ScheduleTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	Index    string                 `json:"index,omitempty" bson:"index,omitempty"`
	TimeFrom *time.Time             `json:"timeFrom,omitempty" bson:"timeFrom,omitempty"`
	TimeTo   *time.Time             `json:"timeTo,omitempty" bson:"timeTo,omitempty"`
}

type ScheduleSetting struct {
	Type        enum.ScheduleSettingTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	NewTimeFrom *time.Time                    `json:"newTimeFrom,omitempty" bson:"newTimeFrom,omitempty"`
	NewTimeTo   *time.Time                    `json:"newTimeTo,omitempty" bson:"newTimeTo,omitempty"`
}

// Seller store...
type SellerStore struct {
	SellerCode string `json:"sellerCode"`
}

type InvoiceInfo struct {
	ID           primitive.ObjectID `json:"id" bson:"_id,omitempty" `
	SellerID     int64              `json:"sellerID,omitempty" bson:"seller_id,omitempty"`
	SellerCode   string             `json:"sellerCode" bson:"seller_code,omitempty"`
	Name         *string            `json:"name" bson:"name,omitempty"`
	Email        *string            `json:"email" bson:"email,omitempty"`
	Phone        *string            `json:"phone,omitempty" bson:"phone,omitempty"`
	Address      *string            `json:"address,omitempty" bson:"address,omitempty"`
	ProvinceCode *string            `json:"provinceCode" bson:"province_code,omitempty"`
	DistrictCode *string            `json:"districtCode" bson:"district_code,omitempty"`
	WardCode     *string            `json:"wardCode" bson:"ward_code,omitempty"`
	Tax          *string            `json:"tax" bson:"tax,omitempty"`
}

type SellerConfig struct {
	MinInvoiceValue     int     `json:"minInvoiceValue" bson:"min_invoice_value,omitempty"`
	InvoiceSellingPrice *string `json:"invoiceSellingPrice" bson:"invoice_selling_price,omitempty"`
	SellerCode          string  `json:"sellerCode" bson:"seller_code,omitempty"`

	InvoiceMethodForVoucher *enum.InvoiceMethodForVoucherValue `json:"invoiceMethodForVoucher,omitempty" bson:"-"`
	ReceiveInvoiceInfoBy    *enum.ReceiveInvoiceInfoByValue    `json:"receiveInvoiceInfoBy,omitempty" bson:"-"`
}

type ReconcileInfo struct {
	SellerID   int64  `json:"sellerId" bson:"seller_id,omitempty"`
	SellerCode string `json:"sellerCode" bson:"seller_code,omitempty"`

	ThreeDaysReconcile bool `json:"threeDaysReconcile" bson:"three_days_reconcile,omitempty"`
	SkipInboundPenalty bool `json:"skipInboundPenalty,omitempty" bson:"skip_inbound_penalty,omitempty"`

	ApplyThreeDaysReconcile *time.Time `json:"applyThreeDaysReconcile,omitempty" bson:"apply_three_days_reconcile,omitempty"`
}

// Sku ...
type Sku struct {
	// basic info
	SKU         string               `json:"sku,omitempty" bson:"sku,omitempty"` // sku code
	Code        string               `json:"code,omitempty"`
	ItemCode    string               `json:"itemCode,omitempty"`
	ProductCode string               `json:"productCode,omitempty"`
	ProductID   int64                `json:"productID"`
	SellerCode  string               `json:"sellerCode,omitempty"`
	Type        *enum.SkuTypeValue   `json:"type,omitempty"`
	Status      *enum.SkuStatusValue `json:"status,omitempty"`
	StatusData  *model.SkuStatusData `json:"statusData,omitempty"`
	IsActive    *bool                `json:"isActive,omitempty"`

	// configuration
	DealCode              string               `json:"dealCode,omitempty"`
	MaxQuantityPerOrder   int64                `json:"maxQuantityPerOrder,omitempty"`
	RetailPriceType       *enum.PriceTypeValue `json:"retailPriceType,omitempty"`
	RetailPriceValue      int64                `json:"retailPriceValue,omitempty"`
	VAT                   *float64             `json:"vat,omitempty"`
	IsDynamicPricingLevel *bool                `json:"isDynamicPricingLevel,omitempty"`
	DynamicPricingLevel   *int64               `json:"dynamicPricingLevel,omitempty"`
	PricingStrategy       *float64             `json:"pricingStrategy,omitempty"`
	FeeCodes              *[]string            `json:"feeCodes,omitempty"`
	SKUs                  *[]*model.SubSku     `json:"skus,omitempty"`
	Tags                  []string             `json:"tags,omitempty"`
	NoneVat               *bool                `json:"noneVat,omitempty"`
	Version               string               `json:"version,omitempty"`
	Level                 enum.LevelSKUValue   `json:"level,omitempty" bson:"level,omitempty"`
	LevelSpecial          *LevelSpecial        `json:"levelSpecial,omitempty" bson:"level_special,omitempty"`

	// combo
	IsCombo *bool `json:"isCombo,omitempty"`
	// combo price
	UseSKUsPrice          *bool             `json:"useSKUsPrice,omitempty"`
	ComboDiscountType     string            `json:"comboDiscountType,omitempty"`
	ComboDiscountValue    *int64            `json:"comboDiscountValue,omitempty"`
	ComboMaxDiscountValue *int64            `json:"comboMaxDiscountValue,omitempty"`
	Point                 int64             `json:"point" bson:"point"`
	PointMultiplier       int64             `json:"pointMultiplier" bson:"point_multiplier"`
	SkuContractCode       string            `json:"skuContract,omitempty" bson:"-"`
	SkuContractDetailCode string            `json:"skuContractDetailCode,omitempty" bson:"-"`
	LotDates              *[]model.LotDates `json:"lotDates,omitempty" bson:"lot_dates,omitempty"`

	// location
	LocationCodes *[]string `json:"locationCodes,omitempty" bson:"location_codes,omitempty"`

	ApprovedDate *time.Time `json:"approvedDate,omitempty" bson:"approved_date,omitempty"` // the time that sku was approved to sell on the app
	CampaignCode *string    `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`

	RequiredCertificates []string `json:"requiredCertificates,omitempty" bson:"-"`

	IsTrading    *bool          `json:"isTrading,omitempty" bson:"is_trading,omitempty"`
	SellerClass  string         `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`
	Slug         string         `json:"slug,omitempty" bson:"slug,omitempty"`
	ImageUrls    []string       `json:"imageUrls,omitempty" bson:"image_urls,omitempty"`
	VoucherApply []VoucherApply `json:"voucherApply,omitempty" bson:"-"`

	IsSpecialControl bool `json:"isSpecialControl,omitempty" bson:"is_special_control,omitempty"`
}

type VoucherApply struct {
	Code string `json:"code,omitempty" bson:"code,omitempty"`
}

type LevelSpecial struct {
	Level               *enum.LevelSKUValue `json:"level,omitempty" bson:"level,omitempty"`
	TimeStartApplyLevel *time.Time          `json:"timeStartApplyLevel,omitempty" bson:"time_start_apply_level,omitempty"`
	TimeEndApplyLevel   *time.Time          `json:"timeEndApplyLevel,omitempty" bson:"time_end_apply_level,omitempty"`
	TimeApplyLevel      *int                `json:"timeApplyLevel,omitempty" bson:"time_apply_level,omitempty"`
}

// Deal is model define struct Deal
type Deal struct {
	Code     string                `json:"code,omitempty"`
	Name     string                `json:"name,omitempty"`
	DealType *enum.DealTypeValue   `json:"dealType"`
	Status   *enum.DealStatusValue `json:"status"`

	MaxQuantity            int `json:"maxQuantity"`
	MaxQuantityPerCustomer int `json:"maxQuantityPerCustomer"`
	TotalDealQuantity      int `json:"totalDealQuantity"`
	CurrentQuantity        int `json:"quantity"`
	Price                  int `json:"price,omitempty"`

	StartTime        *time.Time `json:"startTime"`
	EndTime          *time.Time `json:"endTime"`
	ReadyTime        *time.Time `json:"-"`
	DiscountPercent  *float64   `json:"discountPercent,omitempty"`
	MaxDiscountValue *int       `json:"maxDiscountValue,omitempty"`
	ChargeDealFee    string     `json:"chargeDealFee,omitempty"` // MARKETPLACE . SELLER . SELLER_MARKETPLACE
	Owner            string     `json:"owner,omitempty"`
	PricingType      *string    `json:"pricingType,omitempty"`

	VendorPromoInfo  *VendorPromoInfo `json:"vendorPromoInfo,omitempty" bson:"vendor_promo_info,omitempty"`
	DealTags         []string         `json:"dealTags,omitempty" bson:"deal_tags,omitempty"`
	SegmentationCode string           `json:"segmentationCode,omitempty" bson:"segmentation_code,omitempty"`
}

type VendorPromoInfo struct {
	PromoCode string  `json:"promoCode,omitempty" bson:"promo_code,omitempty"`
	PromoName string  `json:"promoName,omitempty" bson:"promo_name,omitempty"`
	Price     float64 `json:"price,omitempty" bson:"price,omitempty"`
}

type Campaign struct {
	CampaignCode          string           `json:"campaignCode,omitempty"`
	CampaignType          string           `json:"campaignType,omitempty"`
	Status                string           `json:"status,omitempty"`
	CampaignName          string           `json:"campaignName,omitempty"`
	RegistrationStartTime time.Time        `json:"registrationStartTime,omitempty"`
	RegistrationEndTime   time.Time        `json:"registrationEndTime,omitempty"`
	StartTime             time.Time        `json:"startTime,omitempty"`
	EndTime               time.Time        `json:"endTime,omitempty"`
	FlashSaleTimes        *[]FlashSaleTime `json:"flashSaleTimes,omitempty"`
	Reward                *CampaignReward  `json:"reward,omitempty"`

	SaleType       string    `json:"saleType,omitempty"`
	IsActive       *bool     `json:"isActive,omitempty"`
	CustomerScopes *[]string `json:"customerScopes,omitempty"`
	Regions        *[]string `json:"regions,omitempty"`
	SubsidyType    string    `json:"subsidyType,omitempty" bson:"subsidy_type,omitempty"`
	SubsidyValue   int64     `json:"subsidyValue,omitempty" bson:"subsidy_value,omitempty"`
}

type FlashSaleTime struct {
	Code       string    `json:"code,omitempty"`
	StartTime  time.Time `json:"startTime,omitempty"`
	EndTime    time.Time `json:"endTime,omitempty"`
	ProductIDs *[]int64  `json:"productIDs,omitempty"`
}

type CampaignReward struct {
	PercentageDiscount *int64 `json:"percentageDiscount,omitempty"`
	AbsoluteDiscount   *int64 `json:"absoluteDiscount,omitempty"`
	MaxDiscount        *int64 `json:"maxDiscount,omitempty"`
}

// used for response for display on web/app
type ProductData struct {
	SKU                    *Sku             `json:"sku,omitempty"`
	Deal                   *Deal            `json:"deal,omitempty"`
	Product                *Product         `json:"product,omitempty"`
	Campaign               *ProductCampaign `json:"campaign,omitempty"`
	IsAvailable            bool             `json:"isAvailable,omitempty"`
	Owner                  string           `json:"owner,omitempty"`
	QuantityPurchasedToday int64            `json:"quantityPurchasedToday"`

	LimitPerOrder int `json:"limitPerOrder,omitempty"`
}

type ProductCampaign struct {
	CampaignID             int64                `json:"campaignID,omitempty"`
	CampaignCode           string               `json:"campaignCode,omitempty"`
	CampaignProductCode    string               `json:"campaignProductCode,omitempty"`
	ProductID              int64                `json:"productID,omitempty"`
	ProductCode            string               `json:"productCode,omitempty"`
	Sku                    string               `json:"sku,omitempty"`
	SellerCode             string               `json:"sellerCode,omitempty"`
	SalePrice              int64                `json:"retailPriceValue,omitempty"`
	CampaignPrice          int64                `json:"campaignPrice,omitempty"`
	PercentageDiscount     *int64               `json:"percentageDiscount"`
	AbsoluteDiscount       *int64               `json:"absoluteDiscount"`
	SaleType               string               `json:"saleType,omitempty"`
	MaxDiscount            *int64               `json:"maxDiscount,omitempty"`
	ChargeFee              string               `json:"chargeFee,omitempty"`
	Quantity               int64                `json:"quantity,omitempty"`
	Price                  int64                `json:"price,omitempty"`
	SoldQuantity           int64                `json:"soldQuantity,omitempty"`
	MaxQuantityPerOrder    int64                `json:"maxQuantityPerOrder,omitempty"`
	MaxQuantityPerCustomer int                  `json:"maxQuantityPerCustomer"`
	TotalQuantity          int                  `json:"totalQuantity"`
	FlashSaleTime          *[]*CampaignSaleTime `json:"flashSaleTime,omitempty"`
	IsActive               bool                 `json:"isActive,omitempty"`
	Campaign               *Campaign            `json:"campaign,omitempty"`
	Slug                   string               `json:"slug,omitempty"`
}

type CampaignSaleTime struct {
	StartTime    *time.Time           `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime      *time.Time           `json:"endTime,omitempty" bson:"end_time,omitempty"`
	Code         string               `json:"code,omitempty" bson:"code,omitempty"`
	NumDay       int                  `json:"numDay,omitempty" bson:"num_day,omitempty"`
	Kind         string               `json:"kind,omitempty" bson:"kind,omitempty"`
	CampaignID   int64                `json:"campaignID,omitempty" bson:"campaign_id,omitempty"`
	CampaignCode string               `json:"campaignCode,omitempty" bson:"campaign_code,omitempty"`
	ProductID    *int64               `json:"productID,omitempty" bson:"product_id,omitempty"`
	SaleTime     []*FlashSaleTimeItem `json:"saleTime,omitempty" bson:"sale_time,omitempty"`
}

type FlashSaleTimeItem struct {
	Code      string     `json:"code,omitempty" bson:"code,omitempty"`
	Ref       string     `json:"ref,omitempty" bson:"ref,omitempty"`
	Name      string     `json:"name,omitempty" bson:"name,omitempty"`
	StartTime *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`
}

// ProductResponse is resp from product client
type ProductResponse struct {
	BaseAPIResponse
	Data []*ProductData `json:"data"`
}

// SKUPrice ..
type SKUPrice struct {
	*SKUInformation
	FeesApply *model.FeesApply `json:"feesApply"`
}

// ProductResponse is resp from product client
type PriceResponse struct {
	BaseAPIResponse
	Data []*map[string]*SKUPrice `json:"data"`
}

// Region ...
type Region struct {
	Code          string   `json:"code,omitempty"`
	Scope         string   `json:"scope,omitempty"`
	EstThuocSi    float64  `json:"estThuocSi,omitempty"`
	EstLogistic   float64  `json:"estLogistic,omitempty"`
	ProvinceCodes []string `json:"provinceCodes,omitempty"`

	SplitOrderDistrictCodes []string `json:"splitOrderDistrictCodes,omitempty" bson:"split_order_district_codes,omitempty"`
}

type RegionDeliveryTime struct {
	EstThuocSi  float64 `json:"estThuocSi,omitempty"`
	EstLogistic float64 `json:"estLogistic,omitempty"`

	Type string `json:"type,omitempty"`

	ProvinceCode string `json:"provinceCode,omitempty"`
	DistrictCode string `json:"districtCode,omitempty"`
	RegionCode   string `json:"regionCode,omitempty"`
}

type Ward struct {
	Code         string `json:"code" bson:"code,omitempty"`
	Name         string `json:"name" bson:"name,omitempty"`
	ProvinceCode string `json:"provinceCode" bson:"province_code,omitempty"`
	ProvinceName string `json:"provinceName" bson:"province_name,omitempty"`
	DistrictCode string `json:"districtCode" bson:"district_code,omitempty"`
	DistrictName string `json:"districtName" bson:"district_name,omitempty"`
}

// RegionResponse
type RegionResponse struct {
	BaseAPIResponse
	Data []*Region `json:"data"`
}

// RegionGetDeliveryTimeResponse
type RegionGetDeliveryTimeResponse struct {
	BaseAPIResponse
	Data []*RegionDeliveryTime `json:"data"`
}

// WardResponse
type WardResponse struct {
	BaseAPIResponse
	Data []*Ward `json:"data"`
}

// SMSResponse ...
type SMSResponse struct {
	BaseAPIResponse
	Data []*SMS `json:"data"`
}

// NotifyResponse ...
type NotifyResponse struct {
	BaseAPIResponse
	Data []*Notify `json:"data"`
}

// ===// NotificationPartnerResponse ...
type NotificationPartnerResponse struct {
	BaseAPIResponse
	Data []*NotificationPartner `json:"data"`
}

type AddressResponse struct {
	BaseAPIResponse
	Data []*model.Address `json:"data"`
}

// =============================================================================================

// PaymentFeeConfig ...
type PaymentFeeConfig struct {
	ID              primitive.ObjectID `json:"-"`
	CreatedTime     *time.Time         `json:"-"`
	LastUpdatedTime *time.Time         `json:"-"`

	Code        string  `json:"code,omitempty"`
	Name        string  `json:"name,omitempty"`
	SubTitle    *string `json:"subTitle,omitempty"`
	URL         *string `json:"url,omitempty"`
	Description *string `json:"description,omitempty"`
	Priority    *int64  `json:"priority"`
	HashTag     string  `json:"-"`
	Status      string  `json:"status,omitempty"`

	PaymentLocations                  []*PaymentLocation                  `json:"paymentLocations,omitempty"`
	PartnerPaymentServiceLocationFees []*PartnerPaymentServiceLocationFee `json:"partnerPaymentServiceLocationFees,omitempty" bson:"partner_payment_service_location_fees,omitempty"`

	CustomerTags []string `json:"customerTags,omitempty" bson:"-"`

	ComplexQuery []*bson.M `json:"-"`
}

type PaymentLocation struct {
	LocationCodes         []*string `json:"locationCodes,omitempty"`
	FeeDiscountPercentage *float64  `json:"feeDiscountPercentage,omitempty"`
}

type PartnerPaymentServiceLocationFee struct {
	LocationCodes    []string                                `json:"locationCodes,omitempty" bson:"location_codes,omitempty"`
	FixedFeeConfig   *PartnerPaymentServiceLocationFeeConfig `json:"fixedFeeConfig,omitempty" bson:"fixed_fee_config,omitempty"`
	DynamicFeeConfig *PartnerPaymentServiceLocationFeeConfig `json:"dynamicFeeConfig,omitempty" bson:"dynamic_fee_config,omitempty"`
}
type PartnerPaymentServiceLocationFeeConfig struct {
	IsActive         bool    `json:"isActive,omitempty" bson:"is_active,omitempty"`
	BuymedFeeValue   float64 `json:"buymedFeeValue,omitempty" bson:"buymed_fee_value,omitempty"`
	CustomerFeeValue float64 `json:"customerFeeValue,omitempty" bson:"customer_fee_value,omitempty"`
}

// DeliveryFeeConfig ...
type DeliveryFeeConfig struct {
	ID              primitive.ObjectID `json:"-"`
	CreatedTime     *time.Time         `json:"-"`
	LastUpdatedTime *time.Time         `json:"-"`

	Code        string  `json:"code,omitempty"`
	Name        string  `json:"name,omitempty"`
	SubTitle    *string `json:"subTitle,omitempty"`
	URL         *string `json:"url,omitempty"`
	Description *string `json:"description,omitempty"`
	Priority    *int64  `json:"priority"`
	HashTag     string  `json:"-"`
	Status      string  `json:"status,omitempty"`

	Condition         *DeliveryCondition  `json:"condition,omitempty"`
	DeliveryLocations []*DeliveryLocation `json:"deliveryLocations,omitempty"`

	ComplexQuery []*bson.M `json:"-"`
}

type DeliveryLocation struct {
	LocationCodes []*string `json:"locationCodes,omitempty"`
	FeeValue      *int64    `json:"feeValue,omitempty"`
}

type DeliveryCondition struct {
	MinPrice                *int64    `json:"minPrice,omitempty"`
	MaxPrice                *int64    `json:"maxPrice,omitempty"`
	TimeToDeliver           *int      `json:"timeToDeliver,omitempty"`
	NotSupportYMDs          []*string `json:"notSupportYYYYMMDDs,omitempty"`
	NotSupportLocationCodes []*string `json:"notSupportLocationCodes,omitempty"`
	Tags                    []*string `json:"tags,omitempty"`
}

// PaymentFeeConfigResponse
type PaymentFeeConfigResponse struct {
	BaseAPIResponse
	Data []*PaymentFeeConfig `json:"data"`
}

// DeliveryFeeConfigResponse
type DeliveryFeeConfigResponse struct {
	BaseAPIResponse
	Data []*DeliveryFeeConfig `json:"data"`
}

type SyncCartWithCampaign struct {
	SKU          string `json:"sku"`
	Type         string `json:"type"`
	ErrorMessage string `json:"errorMessage"`
	ErrorCode    string `json:"errorCode"`
	IsValid      bool   `json:"isValid"`
	CampaignCode string `json:"campaignCode"`
}

type SyncCartWithCampaignResponse struct {
	BaseAPIResponse
	Data []*SyncCartWithCampaign `json:"data"`
}

type SkuLimitResponse struct {
	BaseAPIResponse
	Data []*model.SkuLimit `json:"data"`
}

type SkuBrandContract struct {
	SKU           string `json:"sku,omitempty" bson:"sku,omitempty"`
	ProductID     int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode   string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	Source        string `json:"source,omitempty" bson:"source,omitempty"`
	CustomerLevel int    `json:"customerLevel,omitempty" bson:"customer_level,omitempty"`
	ContractPrice *int64 `json:"contractPrice,omitempty" bson:"contract_price,omitempty"`

	RetailPrice     *int64  `json:"retailPrice,omitempty" bson:"retail_price,omitempty"`
	RetailPriceType *string `json:"retailPriceType,omitempty" bson:"retail_price_type,omitempty"`
	// if NoFees == true : item not charge fee
	// if NoFees == false || nil : item charge fee
	NoFees *bool `json:"noFees,omitempty" bson:"no_fees,omitempty"`

	LimitQuantityPerMonth *int   `json:"limitQuantityPerMonth,omitempty" bson:"limit_quantity_per_month,omitempty"`
	IsActive              bool   `json:"isActive,omitempty" bson:"is_active,omitempty"`
	SalesTypeCode         string `json:"salesTypeCode,omitempty" bson:"sales_type_code,omitempty"`
}

type SkuBrandContractResponse struct {
	BaseAPIResponse
	Data []*SkuBrandContract `json:"data"`
}

type Debt struct {
	DebtID   int64  `json:"debtId,omitempty"`
	DebtCode string `json:"debtCode,omitempty"`

	WalletCode         string `json:"walletCode,omitempty" `
	InterestWalletCode string `json:"interestWalletCode,omitempty"`

	Status enum.DebtStatusValue `json:"status,omitempty"`

	CustomerID      int64 `json:"customerId,omitempty"`
	BusinessStaffID int64 `json:"businessStaffId,omitempty"`

	DebtLimit    int64   `json:"debtLimit,omitempty"`
	InterestRate float64 `json:"interestRate,omitempty"`

	OutstandingBalance           int64 `json:"outstandingBalance,omitempty"`           // số tiền chưa thanh toán thực tế(Công nợ tạm tính)
	AccountingOutstandingBalance int64 `json:"accountingOutstandingBalance,omitempty"` // số tiền chưa thanh toán dự kiến cho kế toán(Công nợ thực tế)

	Balance           int64 `json:"balance,omitempty"`           // số tiền còn lại(Dư nợ tạm tính)
	AccountingBalance int64 `json:"accountingBalance,omitempty"` // số tiền còn lại cho kế toán(Dư nợ thực tế)

	// ActualOrder   *[]int64 `json:"actualOrder,omitempty" bson:"actual_order,omitempty"`
	// ExpectedOrder *[]int64 `json:"expectedOrder,omitempty" bson:"expected_order,omitempty"`

	RebatePerMonth float64 `json:"rebatePerMonth,omitempty"`
	RebatePerYear  float64 `json:"rebatePerYear,omitempty"`

	IsValid               bool `json:"isValid,omitempty"`
	HasOutstandingBalance bool `json:"hasOutstandingBalance,omitempty" bson:"-"`

	ContractStatus       enum.ContractStatusValue `json:"contractStatus,omitempty"`
	ContractStartTime    *time.Time               `json:"contractStartTime,omitempty"`
	ContractPaymentTerms int64                    `json:"contractPaymentTerms,omitempty"`
}

type Transaction struct {
	TransactionID   int64  `json:"transactionId,omitempty" bson:"transaction_id,omitempty"`
	TransactionCode string `json:"transactionCode,omitempty" bson:"transaction_code,omitempty"`

	RequestID int64 `json:"requestId" bson:"request_id,omitempty"`

	Status     enum.TransactionStatusValue     `json:"status,omitempty" bson:"status,omitempty"`
	ActionType enum.TransactionActionTypeValue `json:"actionType,omitempty" bson:"action_type,omitempty"`

	OwnerID   int64  `json:"ownerId,omitempty" bson:"owner_id,omitempty"`
	OwnerType string `json:"ownerType,omitempty" bson:"owner_type,omitempty"`

	WalletCode string `json:"walletCode,omitempty" bson:"wallet_code,omitempty"`

	Source      string `json:"source,omitempty" bson:"source,omitempty"`
	Description string `json:"description,omitempty" bson:"description,omitempty"`

	Amount  int64 `json:"amount,omitempty" bson:"amount,omitempty"`
	Balance int64 `json:"balance,omitempty" bson:"balance,omitempty"`

	Ref []string `json:"ref,omitempty" bson:"ref,omitempty"` // ["ORDER/152221"] || ["PAYMENT/PAYCG14FW96", "ORDER/152221"] || ["PAYMENT/PAYCG14FW96"]

	MoneySource enum.WalletOptionValue `json:"moneySource,omitempty" bson:"money_source,omitempty"`

	IsVerify  bool `json:"isVerify,omitempty" bson:"is_verify,omitempty"`
	IsMigrate bool `json:"isMigrate,omitempty" bson:"is_migrate,omitempty"`
}

type CustomerPurchaseConfiguration struct {
	IsActive   bool  `json:"isActive,omitempty" bson:"is_active,omitempty"`
	CustomerID int64 `json:"customerID,omitempty" bson:"customer_id,omitempty"`
}

type CustomerPurchaseConfigurationResponse struct {
	BaseAPIResponse
	Data []*CustomerPurchaseConfiguration `json:"data"`
}

type CustomerPurchaseConfigurationDetail struct {
	CustomerID  int64  `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	SellerCode  string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`

	LimitQuantityPerMonth *int   `json:"limitQuantityPerMonth,omitempty" bson:"limit_quantity_per_month,omitempty"`
	ContractPrice         *int64 `json:"contractPrice,omitempty" bson:"contract_price,omitempty"`
	IsActive              bool   `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Source                string `json:"source,omitempty" bson:"source,omitempty"`
	ManufacturerCode      string `json:"manufacturerCode,omitempty" bson:"manufacturer_code,omitempty"`
}

type CustomerPurchaseConfigurationDetailResponse struct {
	BaseAPIResponse
	Data []*CustomerPurchaseConfigurationDetail `json:"data"`
}

type SalesType struct {
	Name        string `json:"name,omitempty" bson:"name,omitempty"`
	SalesTypeID int64  `json:"salesTypeID,omitempty" bson:"sales_type_id,omitempty"`
	Code        string `json:"code,omitempty" bson:"code,omitempty"`

	Type             string   `json:"type,omitempty" bson:"type,omitempty"`
	SellerCodes      []string `json:"sellerCodes,omitempty" bson:"seller_codes,omitempty"`
	ManufactureCodes []string `json:"manufactureCodes,omitempty" bson:"manufacture_codes,omitempty"`

	SystemDisplay string `json:"systemDisplay,omitempty" bson:"system_display,omitempty"`
}

type LatestPutInfo struct {
	TransferFinishTime *time.Time `json:"transferFinishTime,omitempty" bson:"transfer_finish_time,omitempty"`
	WarehouseCode      string     `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	Sku                string     `json:"sku,omitempty" bson:"sku,omitempty"`
	ReferenceCode      string     `json:"reference,omitempty" bson:"reference,omitempty"`
	Status             string     `json:"status,omitempty" bson:"status,omitempty"`
	InboundCode        string     `json:"inboundCode,omitempty" bson:"inbound_code,omitempty"`
}

type PutTicket struct {
	Code          string `json:"code,omitempty"`
	InboundCode   string `json:"inboundCode,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty"`
}

type VendorStore struct {
	Code          string `json:"code,omitempty"`
	Name          string `json:"name,omitempty"`
	SellerCode    string `json:"sellerCode,omitempty"`
	AllProductTag string `json:"allProductTag,omitempty"`
	Tags          []struct {
		Code     string `json:"code,omitempty"`
		IsActive bool   `json:"isActive,omitempty"`
	} `json:"tags,omitempty"`
}

type VendorStoreResponse struct {
	BaseAPIResponse
	Data []*VendorStore `json:"data"`
}

type WarehouseLeadTime struct {
	AvgTotalTime int `json:"avgTotalTime,omitempty"`
}

type WarehouseLeadTimeResponse struct {
	BaseAPIResponse
	Data []*WarehouseLeadTime `json:"data"`
}

type SettingConfigCustomer struct {
	NumberOfMonthsCalLevel int `json:"numberOfMonthsCalLevel,omitempty"`
}

type SettingConfigCustomerResponse struct {
	BaseAPIResponse
	Data []*SettingConfigCustomer `json:"data"`
}

type PartnerIntegration struct {
	PartnerName string `json:"partnerName,omitempty"`
	PartnerID   int64  `json:"partnerID,omitempty"`
	IdentityID  string `json:"identityId,omitempty"`
	CustomerID  int64  `json:"customerId,omitempty"`
}

type PartnerIntegrationResponse struct {
	BaseAPIResponse
	Data []*PartnerIntegration `json:"data"`
}

type TaxGOVResponse struct {
	BaseAPIResponse
	Data []*model.CustomerTaxGOV `json:"data"`
}

type BillingInvoiceResponse struct {
	BaseAPIResponse
	Data []*BillingInvoice `json:"data"`
}
type BillingInvoice struct {
	ID              *primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	InvoiceNo string `json:"invoiceNo,omitempty" bson:"invoice_no,omitempty"` // Số hóa đơn

	// time
	OutboundTime *time.Time `json:"outboundTime,omitempty" bson:"outbound_time,omitempty"` // Ngày xuất kho
	ArisingTime  *time.Time `json:"arisingTime,omitempty" bson:"arising_time,omitempty"`   // Thời gian phát sinh hóa đơn
	ArisingDate  string     `json:"arisingDate,omitempty" bson:"arising_date,omitempty"`   // Ngày phát sinh hóa đơn

	// permission
	OrgID       int64  `json:"orgId,omitempty" bson:"org_id,omitempty"`
	CompanyCode string `json:"companyCode,omitempty" bson:"company_code,omitempty"` // Nguồn hóa đơn -> mã công ty

	Code string               `json:"code,omitempty" bson:"code,omitempty"` // Tự sinh / fkey
	Type enum.InvoiceTypeEnum `json:"type,omitempty" bson:"type,omitempty"`

	// ref
	InvoiceTemplateCode string `json:"invoiceTemplateCode,omitempty" bson:"invoice_template_code,omitempty"` // Mã mẫu hóa đơn
	OrderID             int64  `json:"orderId,omitempty" bson:"order_id,omitempty"`                          // mã đặt hàng
	OrderCode           string `json:"orderCode,omitempty" bson:"order_code,omitempty"`
	SaleOrderCode       string `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"` // mã đơn hàng

	// template
	Series  string `json:"series,omitempty" bson:"series,omitempty"`
	Pattern string `json:"pattern" bson:"pattern,omitempty"` // mẫu hóa đơn

	PurchaserName *string `json:"purchaserName,omitempty" bson:"purchaser_name,omitempty"` // Tên đơn vị mua hàng
	PaymentMethod string  `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"` // Hình thức thanh toán

	// buyer info
	BuyerName    *string `json:"buyerName,omitempty" bson:"buyer_name,omitempty"`        // Họ tên người mua hàng
	BuyerAddress *string `json:"buyerAddress,omitempty" bson:"buyer_address,omitempty"`  // Địa chỉ người mua hàng
	BuyerTaxCode *string `json:"buyerTaxCode,omitempty" bson:"buyer_tax_code,omitempty"` // bắt buộc nếu là KH doanh nghiệp
	BuyerRequest *bool   `json:"buyerRequest" bson:"buyer_request,omitempty"`            // Đánh dấu xuất hóa đơn với thông tin người mua hàng/Khách hàng không lấy hóa đơn

	RequestDraft       *bool      `json:"requestDraft,omitempty" bson:"request_draft,omitempty"`              // Đánh dấu yêu cầu xuất hóa đơn nháp
	ExportDeadlineTime *time.Time `json:"exportDeadlineTime,omitempty" bson:"export_deadline_time,omitempty"` // Thời gian hết hạn xuất hóa đơn

	// seller info
	SellerCode        string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerEmail       string `json:"sellerEmail,omitempty" bson:"seller_email,omitempty"`              // Email
	SellerPhoneNumber string `json:"sellerPhoneNumber,omitempty" bson:"seller_phone_number,omitempty"` // Số điện thoại

	// amount
	AmountWithOutVAT *float64 `json:"amountWithoutVat,omitempty" bson:"amount_without_vat,omitempty"` // Tổng tiền trước thuế
	VATAmount        *float64 `json:"vatAmount,omitempty" bson:"vat_amount,omitempty"`                // Tổng thuế VAT
	Amount           *float64 `json:"amount,omitempty" bson:"amount,omitempty"`                       // Tổng tiền (sau thuế)
	AmountInWords    string   `json:"amountInWords,omitempty" bson:"amount_in_words,omitempty"`       // Tổng tiền (sau thuế) viết bằng chữ
	CurrencyCode     string   `json:"currencyCode" bson:"currency_code,omitempty"`                    // Loại tiền tệ

	// 5
	AmountWithout5PercentVAT *float64 `json:"amountWithout5PercentVAT,omitempty" bson:"amount_without_5_percent_vat,omitempty"` // Tổng tiền sản phẩm chịu thuế 5% (chưa tính thuế)
	VAT5PercentAmount        *float64 `json:"vat5PercentAmount,omitempty" bson:"vat_5_percent_amount,omitempty"`                // Tổng tiền thuế 5%

	// Tracking data
	Provider     string                 `json:"provider,omitempty" bson:"provider,omitempty"` // Tên nhà cung cấp: HILO/...
	Status       enum.InvoiceStatusType `json:"status,omitempty" bson:"status,omitempty"`
	OriginSource string                 `json:"originSource,omitempty" bson:"origin_source,omitempty"` // Tên nguồn xuất hóa đơn: OMS,...
	Source       string                 `json:"source,omitempty" bson:"source,omitempty"`              // Nguồn hóa đơn

	// Log
	SyncToken string `json:"syncToken,omitempty" bson:"sync_token,omitempty"` // Ghi nhận thông tin syncToken, mỗi lần push queue sync token sẽ cập nhật sync token mới

	// items
	InvoiceData *[]*InvoiceData `json:"invoiceData,omitempty" bson:"invoice_data,omitempty"` // Lưu danh sách các chứng từ hóa đơn
}

type InvoiceData struct {
	PDFUrl        string `json:"pdfUrl,omitempty" bson:"pdf_url,omitempty"`
	InvoiceNo     string `json:"invoiceNo" bson:"invoice_no,omitempty"`
	Type          string `json:"type,omitempty" bson:"type,omitempty"`
	DocNo         string `json:"docNo,omitempty" bson:"docNo,omitempty"` // Số hóa đơn
	DisplayName   string `json:"displayName,omitempty" bson:"-"`
	CreatedByID   int64  `json:"createdById,omitempty" bson:"created_by_id,omitempty"`
	CreatedByName string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
}
