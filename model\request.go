package model

import (
	"time"

	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var SELLER_INTERNALS = []string{
	"MEDX",
	"MEDX-HN",
	"MEDX_E",
	"MARKETING",
	"MARKET",
	"DENX",
	"MED_NT",
	"TAM_GIA",
	"MEDC",
	"",
}

// UpdateOrderStatusRequest ...
type UpdateOrderStatusRequest struct {
	OrderID                int64   `json:"orderId" validate:"required"`                 // id đơn hàng
	Status                 string  `json:"status" validate:"required,orderStatus"`      // trạng thái đơn hàng
	Note                   *string `json:"note" validate:"omitempty"`                   // ghi chú đơn hàng
	SaleOrderCode          *string `json:"saleOrderCode" validate:"omitempty"`          // so
	DeliveryTrackingNumber *string `json:"deliveryTrackingNumber" validate:"omitempty"` // mã tracking giao hàng
}

// ConfirmOrderData ...
type ConfirmOrderData struct {
	OrderID     int64      `json:"orderId"`
	CreatedTime *time.Time `json:"createdTime"`
}

// CartAddItem
type CartAddItem struct {
	Sku             string             `json:"sku" validate:"required"`
	Quantity        int                `json:"quantity" validate:"required,gt=0"`
	Type            enum.ItemTypeValue `json:"type"`
	Source          *enum.SourceValue  `json:"source,omitempty"`
	IsSelected      *bool              `json:"isSelected,omitempty"`
	Page            string             `json:"page,omitempty"`
	SearchKey       string             `json:"searchKey,omitempty"`
	ProductID       int64              `json:"productID,omitempty"`
	SellerID        int64              `json:"sellerID,omitempty"`
	SellerCode      string             `json:"sellerCode,omitempty"`
	EventSource     string             `json:"eventSource,omitempty"`
	EventScreen     string             `json:"eventScreen,omitempty"`
	Host            string             `json:"host,omitempty"`
	SearchIndex     string             `json:"searchIndex,omitempty"`
	SearchPageIndex string             `json:"searchPageIndex,omitempty"`
	SystemDisplay   string             `json:"systemDisplay,omitempty"`
	SalesTypeCode   string             `json:"salesTypeCode,omitempty"`
	Price           int                `json:"price,omitempty"`
	// brand
	CustomerID    int64             `json:"customerId"`
	RecommendSKUs string            `json:"recommendSKUs,omitempty"`
	Metric        string            `json:"metric,omitempty"`
	BlockCode     string            `json:"blockCode,omitempty"`
	Metadata      map[string]string `json:"metadata,omitempty"`
}

// CartRemoveItem
type CartRemoveItem struct {
	Sku           string             `json:"sku,omitempty"`
	Type          enum.ItemTypeValue `json:"type,omitempty"`
	ProductID     int64              `json:"productID,omitempty"`
	SellerID      int64              `json:"sellerID,omitempty"`
	SellerCode    string             `json:"sellerCode,omitempty"`
	IsDeleteAll   bool               `json:"isDeleteAll,omitempty"`
	Skus          []string           `json:"skus,omitempty"`
	Host          string             `json:"host,omitempty"`
	SalesTypeCode string             `json:"salesTypeCode,omitempty"`
	// brand
	CustomerID int64             `json:"customerId"`
	BlockCode  string            `json:"blockCode,omitempty"`
	Metadata   map[string]string `json:"metadata,omitempty"`
}

// CartItemUpdate
type CartItemUpdate struct {
	Sku         string             `json:"sku" validate:"required"`
	Type        enum.ItemTypeValue `json:"type" validate:"omitempty,oneof=NORMAL DEAL COMBO CAMPAIGN" default:"NORMAL"`
	IsImportant *bool              `json:"isImportant"`
}

// CartUpdateVoucher
type CartUpdateVoucher struct {
	RedeemCode *[]*string `json:"redeemCode" validate:"required"`
}

// CartConfirm
type CartConfirm struct {
	Skus []string `json:"skus" validate:"required"`
}

// CartUpdateInfo
type CartUpdateInfo struct {
	// customer info
	CustomerName            string  `json:"customerName,omitempty" bson:"customer_name,omitempty"`                        // tên người nhận
	CustomerPhone           string  `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`                      // điện thoại người nhận
	CustomerEmail           *string `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`                      // email người nhận
	CustomerShippingAddress string  `json:"customerShippingAddress,omitempty" bson:"customer_shipping_address,omitempty"` // địa chỉ người nhận
	CustomerDistrictCode    string  `json:"customerDistrictCode,omitempty" bson:"customer_district_code,omitempty"`       // khu vực nhận
	CustomerWardCode        string  `json:"customerWardCode,omitempty" bson:"customer_ward_code,omitempty"`               //
	CustomerProvinceCode    string  `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`       //
	CustomerRegionCode      string  `json:"customerRegionCode,omitempty" bson:"customer_region_code,omitempty"`           //
	CustomerAddressCode     string  `json:"customerAddressCode,omitempty" bson:"customer_address_code,omitempty"`         //

	// fee
	PaymentMethod  string `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`   // phương thức thanh toán cod/chuyển khoản
	DeliveryMethod string `json:"deliveryMethod,omitempty" bson:"delivery_method,omitempty"` // hình thức giao hàng

	// note
	Note *string `json:"note,omitempty" bson:"note,omitempty"` // ghi chú giỏ hàng

	//invoice
	Invoice *InvoiceRequest `json:"invoice,omitempty" bson:"invoice,omitempty"`

	IsRefuseSplitOrder *bool `json:"isRefuseSplitOrder,omitempty" bson:"is_refuse_split_order,omitempty"`

	//log time
	LastActionTime *time.Time `json:"-" bson:"last_action_time,omitempty"`

	// brand
	CustomerID    int64        `json:"customerId,omitempty" bson:"customer_id,omitempty"`
	SalesTypeCode string       `json:"salesTypeCode,omitempty" bson:"sales_type_code,omitempty"`
	BrandGifts    *[]BrandGift `json:"brandGifts,omitempty" bson:"brand_gifts,omitempty"`

	// price
	TotalPrice              int      `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
	ExtraFee                *int64   `json:"extraFee,omitempty" bson:"extra_fee,omitempty"`
	PaymentMethodFee        *int64   `json:"paymentMethodFee,omitempty" bson:"payment_method_fee,omitempty"`               // phí phương thức thanh toán cod/chuyển khoản
	PaymentMethodPercentage *float64 `json:"paymentMethodPercentage,omitempty" bson:"payment_method_percentage,omitempty"` // phần trăm giảm giá cho hình thức thanh toán

	PartnerPaymentMethod *PartnerPaymentMethod `json:"partnerPaymentMethod,omitempty" bson:"partner_payment_method,omitempty"` // Phương thức thanh toán qua cổng thanh toán online
	// delivery fee
	DeliveryMethodFee                 *int64 `json:"deliveryMethodFee,omitempty" bson:"delivery_method_fee,omitempty"`                                    // phí hình thức giao hàng
	TotalPriceBeforePartnerPaymentFee *int   `json:"totalPriceBeforePartnerPaymentFee,omitempty" bson:"total_price_before_partner_payment_fee,omitempty"` // tổng tiền trước khi trừ phí thanh toán online
}

type BrandGift struct {
	Sku      string `json:"sku" validate:"required"`
	ItemCode string `json:"itemCode" validate:"required"`
	Quantity int    `json:"quantity" validate:"required,gt=0"`
}

// CartApplyVoucher
type CartApplyVoucher struct {
	RedeemCode []*string `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"` // mã giảm giá
}

// CartCheckout
type CartCheckoutData struct {
	Source        *enum.SourceValue       `json:"source,omitempty"`
	SourceDetail  OrderSourceDetail       `json:"-"`
	PaymentMethod enum.PaymentMethodValue `json:"paymentMethod,omitempty"`

	// brand
	CustomerID    int64  `json:"customerId,omitempty"`
	SalesTypeCode string `json:"salesTypeCode,omitempty"`

	AcceptAdvancePolicies bool `json:"acceptAdvancePolicies,omitempty" bson:"-"` // xác nhận sự đồng ý đối với các điều kiện và điều khoản về tạm ứng
}

// OrderRemoveItem
type OrderRemoveItem struct {
	Sku        string             `json:"sku" validate:"required"`
	OrderId    int64              `json:"orderId" validate:"required"`
	Type       enum.ItemTypeValue `json:"type" validate:"omitempty,oneof=NORMAL DEAL COMBO CAMPAIGN"`
	NoExtraFee bool               `json:"noExtraFee" validate:"omitempty"`
}

// OrderUpdateItem
type OrderUpdateItem struct {
	Sku        string             `json:"sku" validate:"required"`
	OrderId    int64              `json:"orderId" validate:"required"`
	Type       enum.ItemTypeValue `json:"type" validate:"omitempty,oneof=NORMAL DEAL COMBO CAMPAIGN"`
	Quantity   int                `json:"quantity" validate:"required,gt=0"`
	NoExtraFee bool               `json:"noExtraFee" validate:"omitempty"`
}

// OrderUpdateInfo
type OrderUpdateInfo struct {
	OrderID   int64  `json:"orderId,omitempty" bson:"order_id,omitempty" validate:"required"` //
	OrderCode string `json:"orderCode" bson:"order_code,omitempty"`                           //
	// customer info
	CustomerName            string               `json:"customerName,omitempty" bson:"customer_name,omitempty"`                        // tên người nhận
	CustomerPhone           string               `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`                      // điện thoại người nhận
	CustomerEmail           *string              `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`                      // email người nhận
	CustomerShippingAddress string               `json:"customerShippingAddress,omitempty" bson:"customer_shipping_address,omitempty"` // địa chỉ người nhận
	CustomerDistrictCode    string               `json:"customerDistrictCode,omitempty" bson:"customer_district_code,omitempty"`       // khu vực nhận
	CustomerWardCode        *string              `json:"customerWardCode,omitempty" bson:"customer_ward_code,omitempty"`               //
	CustomerProvinceCode    string               `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`       //
	CustomerRegionCode      string               `json:"customerRegionCode,omitempty" bson:"customer_region_code,omitempty"`           //
	CustomerAddressCode     string               `json:"customerAddressCode,omitempty" bson:"customer_address_code,omitempty"`         //
	PaymentMethod           string               `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`                      // phương thức thanh toán cod/chuyển khoản
	PaymentMethodPercentage *float64             `json:"paymentMethodPercentage,omitempty" bson:"payment_method_percentage,omitempty"` // phần trăm giảm giá cho hình thức thanh toán
	PaymentMethodFee        *int                 `json:"-" bson:"payment_method_fee,omitempty"`                                        // phí phương thức thanh toán cod/chuyển khoản
	TotalPrice              *int                 `json:"-" bson:"total_price,omitempty"`
	TotalDiscount           *int                 `json:"-" bson:"total_discount,omitempty"`
	RedeemCode              *[]*string           `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"`                // mã giảm giá
	RedeemApplyResult       *[]*PromoApplyResult `json:"redeemApplyResult,omitempty" bson:"redeem_apply_result,omitempty"` // kết quả áp dụng mã giảm giá

	Status enum.OrderStateValue `json:"status,omitempty" bson:"-"` // trạng thái đơn hàng
	// note
	Note                *string `json:"note,omitempty" bson:"note,omitempty"`                                  // ghi chú giỏ hàng
	PrivateNote         string  `json:"privateNote,omitempty" bson:"private_note,omitempty"`                   // ghi chú nội bộ đơn hàng
	WaitForTransfer     *bool   `json:"waitForTransfer,omitempty" bson:"wait_for_transfer,omitempty"`          // đánh dấu không được huỷ khi là đơn chuyển khoản
	WaitForTransferTime int64   `json:"waitForTransferTime,omitempty" bson:"wait_for_transfer_time,omitempty"` // thời gian chờ chuyển khoản (phút) lưu lại vì cài đặt có thể thay đổi

	Invoice *InvoiceRequest `json:"invoice,omitempty" bson:"invoice,omitempty"`

	CustomerDistrictName string  `json:"customerDistrictName,omitempty" bson:"-"`
	CustomerWardName     *string `json:"customerWardName,omitempty" bson:"-"`
	CustomerProvinceName string  `json:"customerProvinceName,omitempty" bson:"-"`

	DeliveryDate                    *time.Time `json:"deliveryDate,omitempty" bson:"delivery_date,omitempty"`  // ngày giao mong muốn
	AutoSendPaymentNotificationUnix int64      `json:"-" bson:"auto_send_payment_notification_unix,omitempty"` // token để gửi thông báo tự động
	AutoCancelTransferPaymentUnix   int64      `json:"-" bson:"auto_cancel_transfer_payment_unix,omitempty"`   // ts để tự động huỷ đơn thanh toán online
}

type CustomerOrderUpdateDetailRequest struct {
	OrderID   int64  `json:"orderId,omitempty" bson:"order_id,omitempty"` //
	OrderCode string `json:"orderCode" bson:"order_code,omitempty"`       //

	IsRefuseSplitOrder bool `json:"isRefuseSplitOrder,omitempty" bson:"-"`

	PaymentMethod string `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"` // phương thức thanh toán cod/chuyển khoản
}

type UpdateOrderOmitempty struct {
	Tags *[]enum.TagValue `json:"tags,omitempty" bson:"tags,omitempty"`
}

// OrderUpdateStatus
type OrderUpdateStatus struct {
	OrderID                int64                       `json:"orderId,omitempty" bson:"order_id,omitempty" validate:"required"`            //
	OrderCode              string                      `json:"orderCode,omitempty" bson:"order_code,omitempty"`                            //
	DeliveryStatus         string                      `json:"deliveryStatus,omitempty" bson:"delivery_status,omitempty"`                  // trạng thái nhà vận chuyển: đang lấy,...
	DeliveryTrackingNumber string                      `json:"deliveryTrackingNumber,omitempty" bson:"delivery_tracking_number,omitempty"` // mã tracking
	DeliveryCarrier        string                      `json:"deliveryCarrier,omitempty" bson:"delivery_carrier,omitempty"`                // đơn vị vận chuyển
	DeliveryCarrierCode    string                      `json:"deliveryCarrierCode,omitempty" bson:"delivery_carrier_code,omitempty"`       // code - đơn vị vận chuyển
	DeliveryDate           *time.Time                  `json:"deliveryDate,omitempty" bson:"delivery_date,omitempty"`                      // ngày giao mong muốn
	Status                 enum.OrderStateValue        `json:"status,omitempty" bson:"status,omitempty"`                                   // trạng thái đơn hàng
	PrivateNote            string                      `json:"privateNote,omitempty" bson:"private_note,omitempty"`                        // ghi chú nội bộ đơn hàng
	Note                   *string                     `json:"note,omitempty" bson:"note,omitempty"`                                       // ghi chú nội bộ đơn hàng
	SaleOrderCode          string                      `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`                   // so
	ConfirmType            *enum.OrderConfirmTypeValue `json:"-" bson:"confirm_type,omitempty"`
	ConfirmationDate       *time.Time                  `json:"-" bson:"confirmation_date,omitempty"`                  // ngày xác nhận
	CompletedTime          *time.Time                  `json:"-" bson:"completed_time,omitempty"`                     // thời gian hoàn tất đơn hàng -- status = completed
	OutboundDate           *time.Time                  `json:"outboundDate,omitempty" bson:"outbound_date,omitempty"` // ngày xuất kho
	ActionTime             *time.Time                  `json:"actionTime" bson:"action_time,omitempty"`               // thời gian thực hiện thao tác
	Source                 string                      `json:"source,omitempty" bson:"-"`
	// estime
	ProcessStartTime          *time.Time `json:"processStartTime,omitempty" bson:"process_start_time,omitempty"` // ngày bắt đầu xử lý
	DeliveredTime             *time.Time `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"`        // ngày giao hàng
	CancelTime                *time.Time `json:"cancelTime,omitempty" bson:"cancel_time,omitempty"`              // ngày huỷ
	AutoCancelOverdueTransfer *bool      `json:"autoCancelOverdueTransfer,omitempty" bson:"auto_cancel_overdue_transfer,omitempty"`
	CancelRemainDO            bool       `json:"cancelRemainDO,omitempty" bson:"cancel_remain_do,omitempty"`
}

// OrderSellerUpdateStatus
type OrderSellerUpdateStatus struct {
	OrderSellerID        int64                          `json:"orderSellerId,omitempty" bson:"order_seller_id,omitempty" validate:"required"` //
	OrderSellerCode      string                         `json:"orderSellerCode,omitempty" bson:"order_seller_code,omitempty"`                 //
	ReconciliationStatus enum.ReconciliationStatusValue `json:"reconciliationStatus,omitempty" bson:"reconciliation_status,omitempty"`
	Status               enum.OrderSellerStateValue     `json:"status,omitempty"`
	VAT                  float64                        `json:"vat,omitempty" bson:"vat,omitempty"`
}

// countingField

type CountingField struct {
	FieldName string `json:"fieldName"`
	Condition string `json:"condition"`
	Result    int64  `json:"result"`
}

// OrderCounting
type OrderCounting struct {
	CountingField []*CountingField `json:"countingField"`
}

// OrderCounting
type OrderItemCounting struct {
	CountingField []*CountingField `json:"countingField"`
}

type ReqGetInvoiceList struct {
	Offset          int64 `json:"offset,omitempty"`
	Limit           int64 `json:"limit,omitempty"`
	GetTotal        bool  `json:"getTotal,omitempty"`
	GetInvoiceItems bool  `json:"getInvoiceItems,omitempty"`

	InvoiceStatus      InvoiceStatusValue            `json:"invoiceStatus,omitempty"`
	InvoiceCode        string                        `json:"invoiceCode,omitempty"`
	InvoiceID          int64                         `json:"invoiceID,omitempty"`
	OrderID            int64                         `json:"orderID,omitempty"`
	SellerCode         string                        `json:"sellerCode,omitempty"`
	OrderCodes         []string                      `json:"orderCodes,omitempty"`
	FromDate           *time.Time                    `json:"fromDate,omitempty"`
	ToDate             *time.Time                    `json:"toDate,omitempty"`
	Request            bool                          `json:"request,omitempty"`
	IsFined            bool                          `json:"isFined,omitempty"`
	GetAll             bool                          `json:"getAll,omitempty"`
	DownloadedFile     bool                          `json:"downloadedFile,omitempty"`
	GetDownloadFile    bool                          `json:"getDownloadFile,omitempty"`
	SkipTimeRange      bool                          `json:"skipTimeRange,omitempty"`
	InvoiceIDs         []*int                        `json:"invoiceIDs,omitempty"`
	ReturnStatus       InvoiceReturnStatusValue      `json:"returnStatus,omitempty"`
	InvoicePartnerCode string                        `json:"invoicePartnerCode,omitempty"`
	SignedBy           string                        `json:"signedBy,omitempty"`
	OrderCompletedFrom *time.Time                    `json:"orderCompletedFrom,omitempty"`
	OrderCompletedTo   *time.Time                    `json:"orderCompletedTo,omitempty"`
	OrderDeliveredFrom *time.Time                    `json:"orderDeliveredFrom,omitempty"`
	OrderDeliveredTo   *time.Time                    `json:"orderDeliveredTo,omitempty"`
	ExcludeTags        []enum.InvoiceExcludeTagValue `json:"excludeTags,omitempty"`
}

type RequestUpdateDeliverStatus struct {
	OrderID      int64        `json:"orderID"`
	UpdateAtTime time.Time    `json:"updateAtTime"`
	Status       string       `json:"status"` // RESERVING | DELIVERING | DELIVERED
	OrderItems   []*OrderItem `json:"orderItems"`
}

type RequestDelayDelivery struct {
	OrderID int64 `json:"orderId"`
}

// UpdateInvoiceRequest ...
type UpdateInvoiceRequest struct {
	OrderID int64 `json:"orderId,omitempty" bson:"-" validate:"required"`
	InvoiceRequest
}

type RequestCountOrderPoint struct {
	DateFrom *time.Time `json:"timeFrom" validate:"required"`
	DateTo   *time.Time `json:"timeTo" validate:"required"`
}

type RequestCountOrderValue struct {
	ConfirmFrom             *time.Time `json:"confirmFrom"`
	ConfirmTo               *time.Time `json:"confirmTo"`
	SellerCode              string     `json:"sellerCode"`
	LogSyncGamificationCode string     `json:"logSyncGamificationCode"`
	ValueType               string     `json:"valueType"`
	SystemDisplay           string     `json:"systemDisplay"`
}

type AccumalateProductRequest struct {
	DateFrom   *time.Time `json:"timeFrom" validate:"required"`
	DateTo     *time.Time `json:"timeTo" validate:"required"`
	CustomerId int64      `json:"customerId"`
}

type ReOrderRequest struct {
	OrderID int64 `json:"orderID"`
}

type BrandReOrderRequest struct {
	OrderID    int64 `json:"orderID" validate:"required"`
	CustomerID int64 `json:"customerID" validate:"required"`
	// SalesTypeCode string `json:"salesTypeCode"`
}

type SellerStore struct {
	SellerStores *[]SellerStores `json:"sellerStores,omitempty" bson:"seller_stores,omitempty"`
}

type SellerStores struct {
	SellerCode string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	Revenue    *int   `json:"revenue,omitempty" bson:"revenue,omitempty"`
}

type UpdateOrderItemDataRequest struct {
	OrderId          int64               `json:"orderId,omitempty" validate:"required"`
	ProductId        int64               `json:"productId,omitempty" validate:"required"`
	OutboundQuantity *int                `json:"outboundQuantity,omitempty" validate:"required"`
	SkuLevel         *enum.LevelSKUValue `json:"skuLevel,omitempty" bson:"sku_level,omitempty"`
}
type CancelOrderByCustomerRequest struct {
	OrderId int64                `json:"orderId,omitempty" validate:"required"`
	Status  enum.OrderStateValue `json:"-" bson:"status,omitempty"`
}

type OrderNotDeliveryRequest struct {
	CustomerID              int64  `json:"customerID"`
	CustomerCode            string `json:"customerCode"`
	CustomerPhone           string `json:"customerPhone" validate:"required"`
	CustomerName            string `json:"customerName" validate:"required"`
	CustomerEmail           string `json:"customerEmail" validate:"required"`
	CustomerShippingAddress string `json:"customerShippingAddress" validate:"required"`
	CustomerDistrictCode    string `json:"customerDistrictCode" validate:"required"`
	CustomerWardCode        string `json:"customerWardCode" validate:"required"`
	CustomerProvinceCode    string `json:"customerProvinceCode" validate:"required"`

	OrderID              int64               `json:"orderID" validate:"required"`
	TotalPrice           int                 `json:"totalPrice" validate:"required"`
	CreatedAt            *time.Time          `json:"createdAt" validate:"required"`
	Items                []*OrderItemRequest `json:"items" validate:"required,dive"`
	TotalFee             int64               `json:"totalFee"`
	DeliveryMethodFee    int64               `json:"deliveryMethodFee"`
	PaymentMethodFee     int64               `json:"paymentMethodFee"`
	PaymentMethodPercent float64             `json:"paymentMethodPercent"`
	ExtraFee             int64               `json:"extraFee"`
	TotalDiscount        int64               `json:"totalDiscount"`
}

type OrderItemRequest struct {
	ProductID           int64  `json:"productID" validate:"required"`
	Sku                 string `json:"sku"`
	ProductName         string `json:"productName"`
	Price               int    `json:"price" validate:"required"`
	Quantity            int    `json:"quantity" validate:"required"`
	NotOutboundQuantity int    `json:"notOutboundQuantity"`
	OutboundQuantity    int    `json:"outboundQuantity" validate:"required"`
	TotalPrice          int    `json:"totalPrice" validate:"required"`
}

type OrderCalcAmountRequest struct {
	OrderID              int64                   `json:"orderId" bson:"order_id" validate:"required"`
	DoCode               string                  `json:"doCode" bson:"do_code"`
	IsSplitDeliveryOrder bool                    `json:"isSplitDeliveryOrder" bson:"is_split_delivery_order"`
	ReCalcDiscountAmount bool                    `json:"reCalcDiscountAmount" bson:"re_calc_discount_amount"`
	OrderItems           []*OrderCalcItemRequest `json:"items" bson:"items" validate:"min=1,dive"`
}

type OrderCalcItemRequest struct {
	Sku      string `json:"sku" bson:"sku" validate:"required"`
	Type     string `json:"type"`
	Quantity int    `json:"quantity" bson:"quantity" validate:"gte=0"`
}

type OrderCalcItemResponse struct {
	Sku              string     `json:"sku"`
	Note             string     `json:"note"`
	Price            int        `json:"price,omitempty"`
	SellerCode       string     `json:"sellerCode,omitempty"`
	TotalPrice       int        `json:"totalPrice,omitempty"`
	Quantity         int        `json:"quantity,omitempty"`
	OutboundQuantity int        `json:"outboundQuantity,omitempty"`
	Discount         int        `json:"discount,omitempty"`
	IsError          bool       `json:"isError"`
	CreatedAt        *time.Time `json:"createdAt"`
	Type             string     `json:"type"`
	StoreCode        string     `json:"-"`
}

type OrderItemProcessRequest struct {
	Sku           string                     `json:"sku,omitempty" validate:"required"`
	Quantity      *int                       `json:"quantity,omitempty"`
	Type          string                     `json:"type,omitempty"`
	ReserveInfos  []*ReserveInfo             `json:"reserveInfos,omitempty"`
	OutboundInfos []*OutboundInfo            `json:"outboundInfos,omitempty"`
	ReturnInfos   []*ReturnInfo              `json:"returnInfos,omitempty"`
	SubItems      []*OrderItemProcessRequest `json:"subItems,omitempty"`
}

type OrderProcessRequest struct {
	OrderId                int64                      `json:"orderId,omitempty" bson:"order_id" validate:"required"`
	SaleOrderCode          string                     `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	Items                  []*OrderItemProcessRequest `json:"items,omitempty" bson:"-"`
	DeliveryStatus         string                     `json:"deliveryStatus,omitempty" bson:"delivery_status,omitempty"`                  // trạng thái nhà vận chuyển: đang lấy,...
	DeliveryTrackingNumber string                     `json:"deliveryTrackingNumber,omitempty" bson:"delivery_tracking_number,omitempty"` // mã tracking
	DeliveryCarrier        string                     `json:"deliveryCarrier,omitempty" bson:"delivery_carrier,omitempty"`                // đơn vị vận chuyển
	DeliveryCarrierCode    string                     `json:"deliveryCarrierCode,omitempty" bson:"delivery_carrier_code,omitempty"`       // code - đơn vị vận chuyển
	Status                 enum.OrderStateValue       `json:"status,omitempty" bson:"-"`
	StatusUpdate           enum.OrderStateValue       `json:"-" bson:"status,omitempty"`
	SaleOrderStatus        enum.SaleOrderStateValue   `json:"saleOrderStatus,omitempty" bson:"sale_order_status,omitempty"`
	PrivateNote            string                     `json:"privateNote,omitempty" bson:"private_note,omitempty"`   // ghi chú nội bộ đơn hàng
	Note                   string                     `json:"note,omitempty" bson:"note,omitempty"`                  // ghi chú nội bộ đơn hàng
	DeliveryDate           *time.Time                 `json:"deliveryDate,omitempty" bson:"delivery_date,omitempty"` // ngày giao mong muốn
	OutboundDate           *time.Time                 `json:"outboundDate,omitempty" bson:"outbound_date,omitempty"` // ngày xuất kho
	ActionTime             *time.Time                 `json:"actionTime,omitempty" bson:"action_time,omitempty"`     // thời gian thực hiện thao tác
	Source                 string                     `json:"source,omitempty" bson:"-"`
	SkipUpdateStatus       bool                       `json:"skipUpdateStatus,omitempty" bson:"-"`
	CompletedTime          *time.Time                 `json:"-" bson:"completed_time,omitempty"`
	ActualTotalPrice       *int                       `json:"-" bson:"actual_total_price,omitempty"` // tổng tiền cuối cùngs
	ActualPrice            *int                       `json:"-" bson:"actual_price,omitempty"`       // tổng tiền hàng thực tế
	ProcessStartTime       *time.Time                 `json:"processStartTime,omitempty" bson:"process_start_time,omitempty"`
	DeliveredTime          *time.Time                 `json:"deliveredTime,omitempty" bson:"delivered_time,omitempty"` // ngày giao hàng
	CancelTime             *time.Time                 `json:"cancelTime,omitempty" bson:"cancel_time,omitempty"`       // ngày huỷ

	RequestProcessCreatedTime *time.Time          `json:"requestProcessCreatedTime,omitempty" bson:"request_process_created_time,omitempty"` // thời gian bấm chờ chờ thêm || đi đơn luôn đầu tiên
	RequestProcessAction      enum.HoldOrderValue `json:"requestProcessAction,omitempty" bson:"request_process_action,omitempty"`            // ACTIVE || HOLD
	RequestProcessHoldCount   int                 `json:"requestProcessHoldCount,omitempty" bson:"request_process_hold_count,omitempty"`     // số lần bấm chờ thêm
	RequestProcessUpdatedTime *time.Time          `json:"requestProcessUpdatedTime,omitempty" bson:"request_process_updated_time,omitempty"` // // thời gian bấm chờ chờ thêm || đi đơn luôn mới nhất

	DeliveryOrderCodes    []string               `json:"deliveryOrderCodes,omitempty" bson:"delivery_order_codes,omitempty"`
	DeliveryOrderCode     string                 `json:"deliveryOrderCode,omitempty" bson:"-"`
	DeliveryOrderStatus   enum.OrderStateValue   `json:"deliveryOrderStatus,omitempty" bson:"-"`
	IsSplitDeliveryOrder  *bool                  `json:"isSplitDeliveryOrder,omitempty" bson:"is_split_delivery_order,omitempty"`
	Tags                  []enum.TagValue        `json:"tags,omitempty" bson:"tags,omitempty"`
	DeliveryOrderStatuses []*DeliveryOrderStatus `json:"deliveryOrderStatuses,omitempty" bson:"delivery_order_statuses,omitempty"`
	CodAmount             int                    `json:"codAmount,omitempty" bson:"cod_amount,omitempty"`
	VoucherDiscount       int                    `json:"voucherDiscount,omitempty" bson:"voucher_discount,omitempty"` // giảm giá voucher
	PaymentDiscount       int                    `json:"paymentDiscount,omitempty" bson:"payment_discount,omitempty"` // giảm giá phí thanh toán
	SplitType             enum.SplitTypeValue    `json:"splitType,omitempty" bson:"split_type,omitempty"`
}

type UpdateShippingInfoRequest struct {
	SaleOrderCode string `json:"saleOrderCode"`
	AdminID       int64  `json:"adminId"`
	Phone         string `json:"phone"`
	Name          string `json:"name"`
	BusinessName  string `json:"businessName"`
	Address       string `json:"address"`
	WardName      string `json:"wardName"`
	WardCode      string `json:"wardCode"`
	ProvinceName  string `json:"provinceName"`
	DistrictName  string `json:"districtName"`
	DistrictCode  string `json:"districtCode"`
	ProvinceCode  string `json:"provinceCode"`
}

// GetListOrderRequest ...
type GetListOrderRequest struct {
	Ids            []int64  `json:"ids,omitempty"`
	Codes          []string `json:"codes,omitempty"`
	SaleOrderCodes []string `json:"saleOrderCodes,omitempty"`
	CustomerIds    []int64  `json:"customerIds,omitempty"`
}

// GetListOrderItemRequest ...
type GetListOrderItemRequest struct {
	Ids    []int64 `json:"ids,omitempty"`
	Offset int64   `json:"offset,omitempty"`
	Limit  int64   `json:"limit,omitempty"`
}

type CartLimitUpdate struct {
	Code     string `json:"code,omitempty" bson:"code,omitempty"`
	IsActive *bool  `json:"isActive,omitempty" bson:"is_active,omitempty"`
	//MaxVolume float64 `json:"maxVolume,omitempty" bson:"max_volume,omitempty"`
	MaxWeight float64 `json:"maxWeight,omitempty" bson:"max_weight,omitempty"`
	Width     float64 `json:"width,omitempty" bson:"width,omitempty"`   // cm
	Height    float64 `json:"height,omitempty" bson:"height,omitempty"` // cm
	Length    float64 `json:"length,omitempty" bson:"length,omitempty"` // cmd
}

type SelectCartItemRequest struct {
	Sku          string   `json:"sku,omitempty"`
	IsSelected   bool     `json:"isSelected,omitempty"`
	IsAppliedAll bool     `json:"isAppliedAll,omitempty"`
	Skus         []string `json:"skus,omitempty"`
}

type SkuQuantity struct {
	Sku      string             `json:"sku,omitempty"`
	Quantity int                `json:"quantity,omitempty"`
	Type     enum.ItemTypeValue `json:"type"`
}

type VerifyCartRequest struct {
	Skus           []SkuQuantity `json:"skus,omitempty"`
	TotalPrice     int           `json:"totalPrice,omitempty"`
	VoucherCode    string        `json:"voucherCode,omitempty"`
	DeliveryMethod string        `json:"deliveryMethod,omitempty"`
	PaymentMethod  string        `json:"paymentMethod,omitempty"`
	Source         string        `json:"source,omitempty"`
}

type SkuLimitUpdateRequest struct {
	Code         string `json:"code,omitempty" bson:"code,omitempty"`
	ItemCode     string `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	Sku          string `json:"sku,omitempty" bson:"sku,omitempty"`
	Quantity     int    `json:"quantity,omitempty" bson:"quantity,omitempty"`
	IsActive     *bool  `json:"isActive,omitempty" bson:"is_active,omitempty"`
	NumberOfDays int    `json:"numberOfDays,omitempty" bson:"number_of_days,omitempty" validate:"omitempty,gt=0"`
}

type SkuLimitHistoryListRequest struct {
	CustomerID *int64   `json:"customerID,omitempty"`
	SkuCodes   []string `json:"skuCodes,omitempty"`
	ItemCodes  []string `json:"itemCodes,omitempty"`
}

// OrderUpdateNoteRequest
type OrderUpdateNoteRequest struct {
	MasterAccountNumber string `json:"masterAccountNumber,omitempty"`
	// VirtualAccountNumber string `json:"virtualAccountNumber,omitempty"`
	// VirtualName          string `json:"virtualName,omitempty"`
	// CallbackId           int64  `json:"callbackId,omitempty"`

	Note          string `json:"note" bson:"note,omitempty" validate:"required"`
	SenderName    string `json:"senderName,omitempty"` // VPB, TCB, VPBANK_ADAPTER
	BalanceAmount int64  `json:"balanceAmount,omitempty"`
	TransactionId string `json:"transactionId,omitempty"` // FT code
	OrderID       int64  `json:"orderId,omitempty" bson:"order_id,omitempty"`
} // @name OrderUpdateNoteRequest

// OrderUpdateNoteRequest
type BulkOrderConfirmRequest struct {
	OrderID int64 `json:"orderId" validate:"required"` // id đơn hàng
}

type HoldOrderConfigRequest struct {
	IsActive *bool               `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Type     enum.HoldOrderValue `json:"type,omitempty" bson:"type,omitempty" validate:"required"`

	DisplayTime             *int64                     `json:"displayTime,omitempty" bson:"display_time,omitempty"`
	ProcessingTime          *int64                     `json:"processingTime,omitempty" bson:"processing_time,omitempty"`
	OpenAgainTime           *int64                     `json:"openAgainTime,omitempty" bson:"open_again_time,omitempty"`
	CreatedTicketTime       *int64                     `json:"createdTicketTime,omitempty" bson:"created_ticket_time,omitempty"`
	DisplayRatio            *int64                     `json:"displayRatio,omitempty" bson:"display_ratio,omitempty"`
	BankTransferWaitingTime *int64                     `json:"bankTransferWaitingTime,omitempty" bson:"bank_transfer_waiting_time,omitempty"`
	NotificationConfig      *RemindPaymentNotification `json:"notificationConfig,omitempty" bson:"notification_config,omitempty"`
}
type OrderProcessRequestFromClient struct {
	OrderID int64               `json:"orderID,omitempty" validate:"required"`
	Action  enum.HoldOrderValue `json:"action,omitempty" validate:"required"` //ACTIVE || HOLD
}
type OrderProcessRequestToWms struct {
	OrderID        int64               `json:"orderID,omitempty"`
	SaleOrderCode  string              `json:"saleOrderCode,omitempty"`
	ActionBy       int64               `json:"actionBy,omitempty"`
	ActionByName   string              `json:"actionByName,omitempty"`
	Note           string              `json:"note,omitempty"`
	Action         enum.HoldOrderValue `json:"action,omitempty"`      //ACTIVE || HOLD
	RequestFrom    string              `json:"requestFrom,omitempty"` //MARKETPLACE
	AdditionalTime *int64              `json:"additionalTime,omitempty"`
}
type CalcTransferingDifferenceRequest struct {
	OrderID                           int64     `json:"orderId"`
	TransferringDifferenceArisingTime time.Time `json:"transferringDifferenceArisingTime"`
	DoCode                            string    `json:"doCode,omitempty"`
}

type SearchOrderQuery struct {
	CustomerID int64  `json:"customerId,omitempty"`
	SellerCode string `json:"sellerCode,omitempty"`
	Search     string `json:"search,omitempty"`

	AccountType string `json:"accountType,omitempty"`
}

type MigratePaymentMethodRequest struct {
	CustomerID        int64      `json:"customerID"`
	ContractStartTime *time.Time `json:"contractStartTime"`
}

type Pic struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code      string `json:"code,omitempty" bson:"code,omitempty"`
	AccountID int64  `json:"accountID,omitempty" bson:"account_id,omitempty"`
	Username  string `json:"username,omitempty" bson:"username,omitempty"` // deprecated

	Scopes   []string `json:"scopes,omitempty" bson:"scopes,omitempty"`
	IsActive *bool    `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Type     string   `json:"type,omitempty" bson:"type,omitempty"`

	Provinces *[]*ProvinceInfo `json:"provinces,omitempty" bson:"provinces,omitempty"`

	FirstProvinceCode string          `json:"provinceCode,omitempty" bson:"-"`
	FirstDistricts    *[]*DistrictPIC `json:"districts,omitempty" bson:"-"`

	ProvinceCode_  string          `json:"-" bson:"province_code,omitempty"`
	DistrictCodes_ *[]*DistrictPIC `json:"-" bson:"districts,omitempty"`

	DistrictCode   string   `json:"districtCode,omitempty" bson:"-"`
	WardCode       string   `json:"wardCode,omitempty" bson:"-"`
	ComplexQuery   []bson.M `json:"-" bson:"$and,omitempty"`
	ComplexQueryOr []bson.M `json:"-" bson:"$or,omitempty"`
}

type DistrictPIC struct {
	DistrictCode string    `json:"districtCode,omitempty" bson:"district_code,omitempty"`
	WardCodes    *[]string `json:"wardCodes,omitempty" bson:"ward_codes,omitempty"`
}

type ProvinceInfo struct {
	ProvinceCode string          `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	Districts    *[]*DistrictPIC `json:"districts,omitempty" bson:"districts,omitempty"`
}

type AppValueResponse struct {
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Data    []AppConfig `json:"data"`
}

type AppConfig struct {
	AppCode      string   `json:"appCode,omitempty" bson:"appCode,omitempty"`
	AppValueCode string   `json:"appValueCode,omitempty" bson:"appValueCode,omitempty"`
	Value        AppValue `json:"value,omitempty" bson:"value,omitempty"`
}
type AppValue struct {
	Key         string `json:"key,omitempty" bson:"key,omitempty"`
	ValueInt    int64  `json:"valueInt,omitempty" bson:"valueInt,omitempty"`
	Provinces   string `json:"provinces,omitempty" bson:"provinces,omitempty"`
	Districts   string `json:"districts,omitempty" bson:"districts,omitempty"`
	Wards       string `json:"wards,omitempty" bson:"wards,omitempty"`
	CustomerIds string `json:"customerIds,omitempty" bson:"customer_ids,omitempty"`
	ValString   string `json:"valString,omitempty" bson:"val_string,omitempty"`

	SellerCodeSyncInvoiceToCirca string `json:"sellerCodeSyncInvoiceToCirca,omitempty" bson:"seller_code_sync_invoice_to_circa,omitempty"`
}

type RemoveVoucherRequest struct {
	RedeemCodes       *[]*string           `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"`
	RedeemApplyResult *[]*PromoApplyResult `json:"-" bson:"redeem_apply_result,omitempty"`
	LastActionTime    *time.Time           `json:"-" bson:"last_action_time,omitempty"`
	RedeemCodeRemoved *[]string            `json:"redeemCodeRemoved,omitempty" bson:"redeem_code_removed,omitempty"`
}

type DeliveryInfo struct {
	Name         string `json:"name,omitempty"`
	BusinessName string `json:"businessName,omitempty"`
	Address      string `json:"address,omitempty" `
	Phone        string `json:"phone,omitempty"`
	WardCode     string `json:"wardCode,omitempty"`
	DistrictCode string `json:"districtCode,omitempty"`
	ProvinceCode string `json:"provinceCode,omitempty"`

	IsDropOffAtWarehouse *bool `json:"isDropOffAtWarehouse,omitempty"`
}

type CreateInternalOrderRequest struct {
	CustomerId        int64                        `json:"customerId,omitempty" validate:"required"`
	PaymentMethod     string                       `json:"paymentMethod,omitempty" validate:"required"`
	OrderItems        []*OrderItem                 `json:"orderItems,omitempty" validate:"required"`
	DeliveryInfo      DeliveryInfo                 `json:"deliveryInfo,omitempty" validate:"required"`
	Invoice           *InvoiceRequest              `json:"invoice,omitempty"`
	InternalOrderType *enum.InternalOrderTypeValue `json:"internalOrderType,omitempty" validate:"required"`
}
type RemoveBrandVoucherRequest struct {
	RedeemCodes       *[]*string           `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"`
	RedeemApplyResult *[]*PromoApplyResult `json:"-" bson:"redeem_apply_result,omitempty"`
	LastActionTime    *time.Time           `json:"-" bson:"last_action_time,omitempty"`
	CustomerID        int64                `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	SalesTypeCode     string               `json:"salesTypeCode,omitempty" bson:"sales_type_code,omitempty"`
}

// BrandCartUpdateVoucher
type BrandCartUpdateVoucher struct {
	RedeemCode    *[]*string `json:"redeemCode" validate:"required"`
	CustomerID    int64      `json:"customerID,omitempty" bson:"customer_id,omitempty"`
	SalesTypeCode string     `json:"salesTypeCode,omitempty" bson:"sales_type_code,omitempty"`
}

type CartRemoveItemImportant struct {
	CartNo string   `json:"cartNo,omitempty" bson:"-"` //
	Skus   []string `json:"skus,omitempty"`
}

type AccumulateProductRequest struct {
	DateFrom   *time.Time `json:"timeFrom" validate:"required"`
	DateTo     *time.Time `json:"timeTo" validate:"required"`
	CustomerID int64      `json:"customerID" validate:"required"`
}

type CartItemRequest struct {
	SKUs       []string `json:"skus,omitempty"`
	CustomerID int64    `json:"customerId,omitempty"`
}

type ToolUpdatePointRequest struct {
	OrderID    int64    `json:"orderID,omitempty" bson:"order_id,omitempty" validate:"required"`
	CustomerID int64    `json:"customerID,omitempty" bson:"customer_id,omitempty" validate:"required"`
	Point      *float64 `json:"point,omitempty" bson:"point,omitempty" validate:"required"`
}

type SellerSendInvoice struct {
	OrderID    int64  `json:"orderId" bson:"order_id,omitempty"`
	InvoiceNo  string `json:"invoiceNo" bson:"invoice_no,omitempty"`
	SellerCode string `json:"sellerCode" bson:"seller_code,omitempty"`
	Filename   string `json:"filename" bson:"filename,omitempty"`
	DocUrl     string `json:"docUrl" bson:"doc_url,omitempty"`

	DeliveryOrderCode *string `json:"deliveryOrderCode,omitempty" bson:"delivery_order_code,omitempty"`
}
