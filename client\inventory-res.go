package client

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

type SKULocationRes struct {
	common.APIResponse
	Data []*SKULocation `json:"data,omitempty"`
}

type SKULocation struct {
	AvailableQuantity int    `json:"availableQuantity"`
	LocationCode      string `json:"locationCode,omitempty"`
	OnHoldQuantity    int    `json:"onHoldQuantity"`
	ProductId         int64  `json:"productId"`
	SellerCode        string `json:"sellerCode,omitempty"`
	SKU               string `json:"sku,omitempty"`
	SkuName           string `json:"skuName,omitempty"`
	StockQuantity     int    `json:"stockQuantity"`
}

type InventorySKURes struct {
	common.APIResponse `json:",inline"`
	Data               []*SKUInventory `json:"data"`
}

type SKUInventory struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Name        string `json:"name,omitempty"`
	Productcode string `json:"productCode,omitempty"`
	ProductID   int64  `json:"productId,omitempty"`
	Sku         string `json:"sku,omitempty"`
	Imageurl    string `json:"imageUrl,omitempty"`
	Keyword     string `json:"keyword,omitempty"`
	Packaging   string `json:"packaging,omitempty"`

	AvailableQuantity int64 `json:"availableQuantity,omitempty"`
	MinQuantity       int64 `json:"minQuantity,omitempty"`
	MissingQuantity   int64 `json:"missingQuantity,omitempty"`
	OnholdQuantity    int64 `json:"onHoldQuantity,omitempty"`
	StockQuantity     int64 `json:"stockQuantity,omitempty"`

	VersionNo     string `json:"versionNo,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty"`
	SellerCode    string `json:"sellerCode,omitempty"`
}
