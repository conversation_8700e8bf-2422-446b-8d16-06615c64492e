package action

import (
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/utils"
)

func checkAvailableBySku(skuCode string, customerId int64) bool {
	cacheCustomerSKUs := customerSKUs
	if cacheCustomerSKUs == nil {
		return true
	}
	if data, ok := cacheCustomerSKUs[skuCode]; ok {
		return evaluateDisplayRules(data, customerId)
	}
	return true
}

func evaluateDisplayRules(cacheData *model.CustomerSKUItem, customerId int64) bool {
	isExistCustomerId := checkCustomerIdExistence(customerId, cacheData.CustomerIds)
	isAllowDisplay := checkDisplayRule(cacheData.RuleType)

	// TODO: don't need check condition when customerIds is empty
	if cacheData != nil && len(cacheData.CustomerIds) == 0 {
		return true
	}

	if !isAllowDisplay {
		return !isExistCustomerId
	}

	return isExistCustomerId
}

func checkCustomerIdExistence(customerId int64, customerIds []int64) bool {
	if len(customerIds) > 0 {
		return utils.IsInt64Contains(customerIds, customerId)
	}
	return false
}

func checkDisplayRule(ruleType *enum.CustomerSkusRuleValue) bool {
	return ruleType == nil || *ruleType != enum.CustomerSkuRule.BLOCK_PURCHASE
}
