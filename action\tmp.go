package action

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var skuGamificationTourDL = map[string]bool{}

var skuTourDL = []string{
	"MEDX.HAS-CAL-001",
	"MEDX.DMTS-HAS-NIF-001",
	"MEDX.HAS-GAB-001",
	"MEDX.STA-STA-010",
	"MEDX.DMTS-PHA-GYN-001",
	"MEDX.DMTS-PHA-REC-002",
	"MEDX.KPC-MTP",
	"MEDX.PMP-PYM-003",
	"MEDX.DMTS-OPO-PHY-003",
	"MEDX.ALC-DER-001",
	"MEDX.USP-CEF-001",
	"MEDX.SHE-ECO-001",
	"MEDX.NUL-DER-001",
	"MEDX.COVERAM-5MG5MG-SERVIER-1H30V",
	"MEDX.AMB-SHI-001",
	"MEDX.DMTS-SAN-ALP-001",
	"MEDX.DMTS-GSK-AUG-005",
	"MEDX.GAL-CET-002",
	"MEDX.STA-ORL-008",
	"MEDX.ERT-MKP-001",
	"MEDX.CEF-TIP-300",
	"MEDX.MKP-DEP-001",
	"MEDX.GER84FW9",
	"MEDX.1TU961PG",
	"MEDX.DON-DEL-002",
	"MEDX.MEK-TET-001",
	"MEDX.DON-TOP-001",
	"MEDX.46261PGE",
	"MEDX.MEK-AMO-001",
	"MEDX.ROX-MKP-001",
	"MEDX.AMPICILLIN-500MG-MEKOPHAR-H100V",
	"MEDX.MKP-VIT-002",
	"MEDX.MEK-DOX-001",
	"MEDX.AGI-AGI-023",
	"MEDX.VITAMIN-B1-250MG-MEKOPHAR-H100V",
	"MEDX.TOP-PIR-500",
	"MEDX.MEK-BER-001",
	"MEDX.MEK-MEK-005",
	"MEDX.MEK-KET-001",
	"MEDX.MKP-AUG-009",
	"MEDX.BENTHASONE-DN",
	"MEDX.BETHADESMIN-DN",
	"MEDX.MKP-MET-002",
	"MEDX.HUY-GYT-300",
	"MEDX.MEK-VIT-005",
	"MEDX.TAJPGEQ1",
	"MEDX.U212AXHR",
	"MEDX.MKP-PCT-001",
	"MEDX.FTP-FAM-001",
	"MEDX.STE-DON-001",
	"MEDX.BUD-DON-001",
	"MEDX.DAN-CLO-001",
	"MEDX.NOV-MKP-001",
	"MEDX.BOS-CLO-002",
	"MEDX.G1BR2AXH",
	"MEDX.MKP-AUG-001",
	"MEDX.MEK-DAU-001",
	"MEDX.MEK-MUT-004",
	"MEDX.MEK-GRI-002",
	"MEDX.AMOXICILLIN-250MG-MEKOPHAR-H30G-GOI",
	"MEDX.AGB-MKP-001",
	"MEDX.DON-RHE-001",
	"MEDX.MKP-AUG-002",
	"MEDX.AGI-AGI-111",
	"MEDX.MEK-RUT-001",
	"MEDX.SUL-HUY-200",
	"MEDX.1YCT61PG",
	"MEDX.FTP-MEG-001",
	"MEDX.MEKO-PIRO-001",
	"MEDX.TAN-MKP-040",
	"MEDX.MEK-DIC-002",
	"MEDX.MEK-MUT-003",
	"MEDX.MEK-VIT-009",
	"MEDX.MKP-DIS-001",
	"MEDX.MKP-DIS-001",
	"MEDX.MKP-DIS-002",
	"MEDX.AGI-AGI-016",
	"MEDX.BOS-FLA-001",
	"MEDX.2DWLPGEQ",
	"MEDX.HLCW961P",
	"MEDX.MKP-ROD-009",
	"MEDX.MKP-KIM-004",
	"MEDX.RQAQ1PGE",
	"MEDX.TY784FW9",
	"MEDX.JCKFW961",
	"MEDX.D9GU72AX",
	"MEDX_E.BETHADESMIN-DN",
	"MEDX_E.BENTHASONE-DN",
	"MEDX_E.DON-DEL-002",
	"MEDX_E.STE-DON-001",
	"MEDX_E.TOP-PIR-500",
	"MEDX_E.DON-TOP-001",
	"MEDX_E.DAN-CLO-001",
	"MEDX.46261PGE",
	"MEDX.CEF-TIP-300",
	"MEDX.F8XEQ1PG",
	"MEDX.AgimexP-CB-157-2",
	"MEDX.DMTS-BAO-BAN-002",
	"MEDX.BAO-BON-002",
	"MEDX.BAO-BON-001",
}

func OrderCountValueGamificationTourDL(in *model.RequestCountOrderValue) *common.APIResponse {
	go func() {
		query := &model.Order{}

		// nếu in.SystemDisplay == "" thì gán lại in.SystemDisplay = "BUYMED"
		if in.SystemDisplay == "" {
			in.SystemDisplay = "BUYMED"
		}
		query.SystemDisplay = in.SystemDisplay

		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"status": bson.M{
				"$nin": []enum.OrderStateValue{enum.OrderState.WaitConfirm, enum.OrderState.Canceled},
			},
		})

		if in.ConfirmFrom != nil && in.ConfirmTo != nil {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"created_time": bson.M{
					"$gte": in.ConfirmFrom,
					"$lte": in.ConfirmTo,
				},
			})
		}

		var orders []*client.CountOrderValue
		model.OrderDB.Aggregate([]primitive.M{
			{
				"$match": query,
			},
			{
				"$group": primitive.M{
					"_id":    "$customer_id",
					"orders": bson.M{"$push": "$order_id"},
					"order_values": bson.M{"$push": bson.M{
						"order_id":       "$order_id",
						"status":         "$status",
						"created_time":   "$created_time",
						"completed_time": "$completed_time",
					}},
				},
			},
		}, &orders)
		var finalValues []*client.CountOrderValue
		orderIds := make([]int, 0)
		orderMap := make(map[int]*model.GamificationOrderValue, 0)
		var orderStartTime, orderEndTime *time.Time
		for _, order := range orders {
			for _, value := range order.OrderValues {
				if value.CreatedTime != nil && (orderStartTime == nil || orderStartTime.After(*value.CreatedTime)) {
					orderStartTime = value.CreatedTime
					// fmt.Println("orderStartTime", orderStartTime)
				}
				if value.CreatedTime != nil && (orderEndTime == nil || orderEndTime.Before(*value.CreatedTime)) {
					orderEndTime = value.CreatedTime
					// fmt.Println("orderStartTime", orderStartTime)
				}
				orderIds = append(orderIds, value.OrderID)
				orderMap[value.OrderID] = value
			}
			if len(orderIds) == 500 {
				finalValues = aggOrderItemValueTmp(finalValues, orderMap, orderIds, in.SellerCode, orderStartTime, orderEndTime)
				orderIds = make([]int, 0)
			}
		}
		if len(orderIds) > 0 {
			finalValues = aggOrderItemValueTmp(finalValues, orderMap, orderIds, in.SellerCode, orderStartTime, orderEndTime)
			orderIds = make([]int, 0)
		}
		if saveErr := saveGamificationValue(uniqueOrderValue(finalValues), in.LogSyncGamificationCode); saveErr != nil {
			return
		}
		client.Services.Promotion.SyncGamification([]*client.CountOrderValue{}, in.LogSyncGamificationCode, in.SystemDisplay)
	}()

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Request sync value from order success",
	}
}

func aggOrderItemValueTmp(finalValues []*client.CountOrderValue, orderMap map[int]*model.GamificationOrderValue, orderIds []int, sellerCode string, orderStartTime, orderEndTime *time.Time) []*client.CountOrderValue {
	totalValues := make([]*client.CountOrderValue, 0)
	queryOrderItem := model.OrderItem{
		ComplexQuery: []*bson.M{
			{
				"order_id": bson.M{"$in": orderIds},
				"sku":      bson.M{"$in": skuTourDL},
			},
		},
	}
	listOrderItemDB := make(map[string]*db.Instance, 0)
	fmt.Println(orderEndTime, orderStartTime)
	if orderStartTime != nil && orderEndTime != nil {
		months := ListMonthByStartAndEndTime(*orderStartTime, *orderEndTime)
		for _, date := range months {
			orderItemPartitionDB := model.GetOrderItemPartitionDB(&model.Order{CreatedTime: &date}, "aggOrderItemValue")
			listOrderItemDB[orderItemPartitionDB.ColName] = orderItemPartitionDB
			// fmt.Println("orderItemPartitionDB.ColName", orderItemPartitionDB.ColName)
		}
	}

	for _, db := range listOrderItemDB {
		values := make([]*client.CountOrderValue, 0)
		db.Aggregate([]primitive.M{
			{
				"$match": queryOrderItem,
			},
			{
				"$group": primitive.M{
					"_id": "$customer_id",
					"total_price": primitive.M{
						"$sum": "$total_price",
					},
					"orders": bson.M{"$addToSet": "$order_id"},
					"order_values": bson.M{"$push": bson.M{
						"order_id":           "$order_id",
						"sku":                "$sku",
						"total_price":        "$total_price",
						"total_actual_price": "$actual_price",
					}},
				},
			},
		}, &values)
		if len(values) > 0 {
			totalValues = append(totalValues, values...)
		}
	}
	if len(totalValues) > 0 {
		for _, value := range totalValues {
			orderValues := make([]*model.GamificationOrderValue, 0)
			for _, orderValue := range value.OrderValues {
				if data := orderMap[orderValue.OrderID]; data != nil {
					orderValue.CreatedTime = data.CreatedTime
					orderValue.CompletedTime = data.CompletedTime
					orderValue.Status = data.Status
				}
				orderValues = append(orderValues, orderValue)
			}
			value.OrderValues = orderValues
			finalValues = append(finalValues, value)
		}
	}
	return finalValues
}

func uniqueOrderValue(values []*client.CountOrderValue) []*client.CountOrderValue {
	mapValues := make(map[int]*client.CountOrderValue, 0)
	for _, value := range values {
		if _, ok := mapValues[value.CustomerId]; !ok {
			mapValues[value.CustomerId] = value
		} else {
			mapValues[value.CustomerId].TotalPrice += value.TotalPrice
			mapValues[value.CustomerId].TotalActualPrice += value.TotalActualPrice
			mapValues[value.CustomerId].Orders = append(mapValues[value.CustomerId].Orders, value.Orders...)
			mapValues[value.CustomerId].OrderValues = append(mapValues[value.CustomerId].OrderValues, value.OrderValues...)
		}
	}
	finalValues := make([]*client.CountOrderValue, 0)
	for _, value := range mapValues {
		finalValues = append(finalValues, value)
	}
	return finalValues
}
