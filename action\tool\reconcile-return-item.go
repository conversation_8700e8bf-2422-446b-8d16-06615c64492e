package tool

import (
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/action/reconcile_action"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/client"
)

type ReconcileReturnOutTurnItemData struct {
	SellerCode        string    `json:"sellerCode,omitempty"`
	OrderID           int64     `json:"orderID,omitempty"`
	ScheduleTimeIndex string    `json:"scheduleTimeIndex,omitempty"`
	ReconcileTime     time.Time `json:"reconcileTime,omitempty"`
}

func ReconcileReturnOutTurnItem(req sdk.APIRequest, res sdk.APIResponder) error {
	var input ReconcileReturnOutTurnItemData
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	return res.Respond(ProcessReconcileReturnOutTurnItem(&input))
}

func ProcessReconcileReturnOutTurnItem(input *ReconcileReturnOutTurnItemData) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing input",
			ErrorCode: "MISSING_INPUT",
		}
	}

	if input.SellerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing SellerCode",
			ErrorCode: "MISSING_SELLER_CODE",
		}
	}

	if input.OrderID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Missing OrderID",
			ErrorCode: "MISSING_ORDER_ID",
		}
	}

	switch input.ScheduleTimeIndex {
	case "SCHEDULE_01_TO_15":
	case "SCHEDULE_16_TO_END":

	case "SCHEDULE_01_TO_07":
	case "SCHEDULE_08_TO_15":
	case "SCHEDULE_16_TO_22":
	case "SCHEDULE_23_TO_END":

	case "SCHEDULE_01_TO_03":
	case "SCHEDULE_04_TO_06":
	case "SCHEDULE_07_TO_09":
	case "SCHEDULE_10_TO_12":
	case "SCHEDULE_13_TO_15":
	case "SCHEDULE_16_TO_18":
	case "SCHEDULE_19_TO_21":
	case "SCHEDULE_22_TO_24":
	case "SCHEDULE_25_TO_27":
	case "SCHEDULE_28_TO_END":

	default:
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid schedule time index",
			ErrorCode: "INVALID_SCHEDULE_TIME_INDEX",
		}
	}

	from, to, index, _, err := reconcile_action.GetReconcileTime(input.ScheduleTimeIndex, input.ReconcileTime, nil)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   err.Error(),
			ErrorCode: "GET_TIME_ERROR",
		}
	}

	if from.IsZero() {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "From time is zero",
			ErrorCode: "INVALID_FROM_TIME",
		}
	}

	fromDate := from.Format("2006-01-02")
	toDate := to.AddDate(0, 0, -1).Format("2006-01-02")

	mapOrderArrSKUReturn := getReturnQuantityByTicket([]string{
		strconv.FormatInt(input.OrderID, 10),
	}, from, to)

	processScheduleForSeller(
		nil,
		[]int64{},
		client.Seller{Code: input.SellerCode},
		fromDate,
		toDate,
		index,
		to,
		nil,
		nil,
		nil,
		&mapOrderArrSKUReturn,
		nil,
	)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "ReconcileReturnOutTurnItem complete",
		Data: []interface{}{
			from,
			to,
			index,
			fromDate,
			toDate,
			mapOrderArrSKUReturn,
		},
	}
}
