package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/marketplace/order-v2/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var ReconciliationSpecRevenueDB = &db.Instance{
	ColName:        "reconciliation_spec_revenue",
	TemplateObject: ReconciliationSpecRevenue{},
}

type ReconciliationSpecRevenue struct {
	ID          *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`

	OrderID    int64  `json:"order_id,omitempty" bson:"order_id,omitempty"`
	ProductID  int64  `json:"product_id" bson:"product_id,omitempty"`
	SellerCode string `json:"seller_code,omitempty" bson:"seller_code,omitempty"`
	Sku        string `json:"sku,omitempty" bson:"sku,omitempty"`
	Schedule   string `json:"schedule" bson:"schedule,omitempty"`

	PriceType       *enum.PriceTypeValue `json:"price_type,omitempty" bson:"price_type,omitempty"`
	ChargeBuyerFee  *bool                `json:"charge_buyer_fee,omitempty" bson:"charge_buyer_fee,omitempty"`
	Level           *string              `json:"level,omitempty" bson:"level,omitempty"`
	DealPricing     *string              `json:"deal_pricing,omitempty" bson:"deal_pricing,omitempty"`
	DealCharge      *string              `json:"deal_charge,omitempty" bson:"deal_charge,omitempty"`
	DealChargeValue *int                 `json:"deal_charge_value,omitempty" bson:"deal_charge_value,omitempty"`
	Revenue         *int                 `json:"revenue,omitempty" bson:"revenue,omitempty"`
	ReturnQty       *int                 `json:"return_qty,omitempty" bson:"return_qty,omitempty"`
	MainQty         *int                 `json:"main_qty,omitempty" bson:"main_qty,omitempty"`
	DamageQty       *int                 `json:"damage_qty,omitempty" bson:"damage_qty,omitempty"`
	ListingRate     *float64             `json:"listing_rate,omitempty" bson:"listing_rate,omitempty"`
	FulfillmentRate *float64             `json:"fulfillment_rate,omitempty" bson:"fulfillment_rate,omitempty"`
}

func InitReconciliationSpecRevenue(s *mongo.Database) {
	ReconciliationSpecRevenueDB.ApplyDatabase(s)

	// t := true

	// _ = ReconciliationSpecRevenueDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "schedule", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = ReconciliationSpecRevenueDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "order_id", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "schedule", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
